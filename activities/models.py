from django.db import models
from apps.users.models import User


class Activity(models.Model):
    """活动"""
    ACTIVITY_TYPES = [
        ('online', '线上活动'),
        ('offline', '线下活动'),
        ('dating', '相亲角'),
        ('interest', '兴趣小组'),
        ('skill', '技能分享'),
        ('social', '社交聚会'),
    ]

    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('ongoing', '进行中'),
        ('completed', '已结束'),
        ('cancelled', '已取消'),
    ]

    title = models.CharField(max_length=200, verbose_name='活动标题')
    description = models.TextField(verbose_name='活动描述')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES, verbose_name='活动类型')

    # 活动时间
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    registration_deadline = models.DateTimeField(verbose_name='报名截止时间')

    # 活动地点
    location = models.CharField(max_length=200, blank=True, verbose_name='活动地点')
    address = models.TextField(blank=True, verbose_name='详细地址')
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')

    # 参与设置
    max_participants = models.IntegerField(default=0, verbose_name='最大参与人数')
    min_participants = models.IntegerField(default=1, verbose_name='最小参与人数')
    current_participants = models.IntegerField(default=0, verbose_name='当前参与人数')

    # 费用设置
    is_free = models.BooleanField(default=True, verbose_name='是否免费')
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='活动费用')

    # 限制条件
    gender_limit = models.CharField(max_length=10, blank=True, verbose_name='性别限制')
    age_min = models.IntegerField(null=True, blank=True, verbose_name='最小年龄')
    age_max = models.IntegerField(null=True, blank=True, verbose_name='最大年龄')
    vip_only = models.BooleanField(default=False, verbose_name='仅限VIP')

    # 活动图片
    cover_image = models.URLField(blank=True, verbose_name='封面图片')
    images = models.JSONField(default=list, verbose_name='活动图片')

    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    is_featured = models.BooleanField(default=False, verbose_name='是否推荐')

    # 创建信息
    organizer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organized_activities', verbose_name='组织者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'activities'
        verbose_name = '活动'
        verbose_name_plural = '活动'
        ordering = ['-start_time']

    def __str__(self):
        return self.title
