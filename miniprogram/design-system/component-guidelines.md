# 相亲交友小程序 - 组件设计规范

## 📋 概述

本文档定义了相亲交友小程序的组件设计规范，确保整个应用的视觉一致性和用户体验的统一性。

## 🎨 设计原则

### 1. 一致性 (Consistency)
- 相同功能的组件在不同页面中保持一致的外观和行为
- 遵循统一的颜色、字体、间距和动画规范
- 保持交互模式的一致性

### 2. 可访问性 (Accessibility)
- 确保足够的颜色对比度
- 提供清晰的视觉反馈
- 支持不同屏幕尺寸和分辨率

### 3. 可用性 (Usability)
- 直观的交互设计
- 清晰的信息层次
- 合理的触摸目标大小

## 🧩 基础组件规范

### Button 按钮组件

#### 设计规范
```css
/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  min-height: 64rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(249, 115, 22, 0.3);
}

/* 次要按钮 */
.btn-secondary {
  background: #ffffff;
  color: #f97316;
  border: 2rpx solid #f97316;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-height: 64rpx;
  padding: 16rpx 32rpx;
}

/* 文本按钮 */
.btn-text {
  background: transparent;
  color: #f97316;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  padding: 12rpx 16rpx;
}
```

#### 状态规范
- **默认状态**: 正常显示
- **悬停状态**: 轻微阴影增强
- **按下状态**: 缩放至95%
- **禁用状态**: 透明度50%，不可交互
- **加载状态**: 显示加载指示器

#### 尺寸规范
- **小尺寸**: 高度56rpx，字体24rpx
- **中尺寸**: 高度64rpx，字体28rpx (默认)
- **大尺寸**: 高度80rpx，字体32rpx

### Input 输入框组件

#### 设计规范
```css
.input-container {
  position: relative;
  margin-bottom: 32rpx;
}

.input-field {
  width: 100%;
  height: 64rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #262626;
  background: #ffffff;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #f97316;
  box-shadow: 0 0 0 4rpx rgba(249, 115, 22, 0.1);
}

.input-label {
  position: absolute;
  left: 24rpx;
  top: -12rpx;
  background: #ffffff;
  padding: 0 8rpx;
  font-size: 24rpx;
  color: #8c8c8c;
}
```

#### 状态规范
- **默认状态**: 灰色边框
- **聚焦状态**: 主色边框 + 阴影
- **错误状态**: 红色边框 + 错误提示
- **禁用状态**: 灰色背景，不可编辑

### Card 卡片组件

#### 设计规范
```css
.card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.card-content {
  font-size: 28rpx;
  color: #595959;
  line-height: 1.5;
}
```

### Avatar 头像组件

#### 设计规范
```css
.avatar {
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  background: #f0f0f0;
}

.avatar-sm { width: 64rpx; height: 64rpx; }
.avatar-md { width: 80rpx; height: 80rpx; }
.avatar-lg { width: 120rpx; height: 120rpx; }
.avatar-xl { width: 160rpx; height: 160rpx; }

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: #ffffff;
  font-weight: 600;
}
```

## 🎯 业务组件规范

### UserCard 用户卡片

#### 设计规范
- **布局**: 头像 + 基本信息 + 操作按钮
- **尺寸**: 宽度100%，高度自适应
- **间距**: 内边距32rpx，元素间距24rpx
- **圆角**: 16rpx
- **阴影**: 0 4rpx 12rpx rgba(0, 0, 0, 0.1)

#### 信息层次
1. **主要信息**: 昵称、年龄、职业
2. **次要信息**: 学历、身高、地区
3. **状态信息**: 在线状态、VIP标识
4. **操作区域**: 喜欢、不喜欢、超级喜欢

### ChatBubble 聊天气泡

#### 设计规范
```css
.chat-bubble {
  max-width: 70%;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  position: relative;
}

.chat-bubble.sent {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: #ffffff;
  align-self: flex-end;
  border-bottom-right-radius: 8rpx;
}

.chat-bubble.received {
  background: #f5f5f5;
  color: #262626;
  align-self: flex-start;
  border-bottom-left-radius: 8rpx;
}
```

### VipBadge VIP标识

#### 设计规范
```css
.vip-badge {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
}

.vip-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: #8b4513;
}

.vip-badge.platinum {
  background: linear-gradient(135deg, #e5e4e2, #c0c0c0);
  color: #2f4f4f;
}
```

## 🎬 动画规范

### 页面转场动画
```css
/* 页面进入动画 */
@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 页面退出动画 */
@keyframes pageExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}
```

### 元素动画
- **淡入**: 300ms ease-out
- **滑入**: 300ms ease-out
- **缩放**: 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55)
- **弹跳**: 500ms cubic-bezier(0.175, 0.885, 0.32, 1.275)

## 📱 响应式设计

### 断点定义
- **小屏**: < 576rpx
- **中屏**: 576rpx - 768rpx
- **大屏**: > 768rpx

### 适配原则
1. **内容优先**: 确保核心内容在所有屏幕上都能正常显示
2. **渐进增强**: 大屏幕提供更丰富的交互体验
3. **触摸友好**: 保证最小44rpx的触摸目标

## 🔧 实施指南

### 1. 组件开发流程
1. **设计评审**: 确保符合设计规范
2. **代码实现**: 遵循命名规范和结构规范
3. **测试验证**: 多设备测试和可访问性测试
4. **文档更新**: 更新组件文档和使用示例

### 2. 质量检查清单
- [ ] 符合设计令牌规范
- [ ] 支持所有定义的状态
- [ ] 响应式适配完整
- [ ] 动画流畅自然
- [ ] 可访问性良好
- [ ] 性能表现优秀

### 3. 维护更新
- 定期审查组件使用情况
- 收集用户反馈并优化
- 保持与设计系统的同步更新
- 及时修复发现的问题

## 📚 参考资源

- [设计令牌文档](./design-tokens.js)
- [组件库源码](../components/)
- [使用示例](../examples/)
- [最佳实践指南](./best-practices.md)
