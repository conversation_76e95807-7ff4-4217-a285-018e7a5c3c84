/**
 * 设计系统 - 设计令牌 (Design Tokens)
 * 定义应用的基础设计规范，包括颜色、字体、间距、圆角等
 * 确保整个应用的视觉一致性
 */

const DesignTokens = {
  // ========== 颜色系统 ==========
  colors: {
    // 主色调
    primary: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',  // 主色
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12'
    },

    // 辅助色
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',  // 辅助色
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e'
    },

    // 成功色
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',  // 成功色
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d'
    },

    // 警告色
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',  // 警告色
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },

    // 错误色
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',  // 错误色
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d'
    },

    // 中性色
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717'
    },

    // 语义化颜色
    semantic: {
      // 文本颜色
      textPrimary: '#262626',
      textSecondary: '#595959',
      textTertiary: '#8c8c8c',
      textDisabled: '#bfbfbf',
      textInverse: '#ffffff',

      // 背景颜色
      bgPrimary: '#ffffff',
      bgSecondary: '#fafafa',
      bgTertiary: '#f5f5f5',
      bgDisabled: '#f0f0f0',
      bgOverlay: 'rgba(0, 0, 0, 0.45)',

      // 边框颜色
      borderPrimary: '#d9d9d9',
      borderSecondary: '#f0f0f0',
      borderFocus: '#40a9ff',
      borderError: '#ff4d4f',

      // 链接颜色
      linkPrimary: '#1890ff',
      linkHover: '#40a9ff',
      linkActive: '#096dd9',
      linkVisited: '#722ed1'
    }
  },

  // ========== 字体系统 ==========
  typography: {
    // 字体族
    fontFamily: {
      primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      mono: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace'
    },

    // 字体大小 (rpx)
    fontSize: {
      xs: 20,    // 12px
      sm: 24,    // 14px
      base: 28,  // 16px
      lg: 32,    // 18px
      xl: 36,    // 20px
      '2xl': 40, // 22px
      '3xl': 48, // 24px
      '4xl': 56, // 28px
      '5xl': 64, // 32px
      '6xl': 72, // 36px
      '7xl': 96, // 48px
      '8xl': 128 // 64px
    },

    // 字体权重
    fontWeight: {
      thin: 100,
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800,
      black: 900
    },

    // 行高
    lineHeight: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    },

    // 字母间距
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    }
  },

  // ========== 间距系统 ==========
  spacing: {
    0: 0,
    1: 2,     // 1px
    2: 4,     // 2px
    3: 6,     // 3px
    4: 8,     // 4px
    5: 10,    // 5px
    6: 12,    // 6px
    8: 16,    // 8px
    10: 20,   // 10px
    12: 24,   // 12px
    16: 32,   // 16px
    20: 40,   // 20px
    24: 48,   // 24px
    32: 64,   // 32px
    40: 80,   // 40px
    48: 96,   // 48px
    56: 112,  // 56px
    64: 128,  // 64px
    80: 160,  // 80px
    96: 192,  // 96px
    112: 224, // 112px
    128: 256  // 128px
  },

  // ========== 圆角系统 ==========
  borderRadius: {
    none: 0,
    sm: 4,    // 2px
    base: 8,  // 4px
    md: 12,   // 6px
    lg: 16,   // 8px
    xl: 24,   // 12px
    '2xl': 32, // 16px
    '3xl': 48, // 24px
    full: 9999
  },

  // ========== 阴影系统 ==========
  boxShadow: {
    none: 'none',
    sm: '0 2rpx 4rpx rgba(0, 0, 0, 0.1)',
    base: '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
    md: '0 4rpx 12rpx rgba(0, 0, 0, 0.15)',
    lg: '0 8rpx 24rpx rgba(0, 0, 0, 0.15)',
    xl: '0 12rpx 32rpx rgba(0, 0, 0, 0.2)',
    '2xl': '0 20rpx 48rpx rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1)'
  },

  // ========== 动画系统 ==========
  animation: {
    // 动画时长
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
      slower: 800
    },

    // 缓动函数
    easing: {
      linear: 'linear',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      bounceIn: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      bounceOut: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    },

    // 预定义动画
    presets: {
      fadeIn: {
        duration: 300,
        easing: 'ease-out',
        keyframes: {
          from: { opacity: 0 },
          to: { opacity: 1 }
        }
      },
      slideUp: {
        duration: 300,
        easing: 'ease-out',
        keyframes: {
          from: { transform: 'translateY(100%)', opacity: 0 },
          to: { transform: 'translateY(0)', opacity: 1 }
        }
      },
      scaleIn: {
        duration: 200,
        easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        keyframes: {
          from: { transform: 'scale(0.8)', opacity: 0 },
          to: { transform: 'scale(1)', opacity: 1 }
        }
      }
    }
  },

  // ========== 断点系统 ==========
  breakpoints: {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
  },

  // ========== Z-index 层级 ==========
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800
  },

  // ========== 组件特定令牌 ==========
  components: {
    // 按钮
    button: {
      height: {
        sm: 56,   // 28px
        md: 64,   // 32px
        lg: 80    // 40px
      },
      padding: {
        sm: { x: 24, y: 12 },
        md: { x: 32, y: 16 },
        lg: { x: 40, y: 20 }
      }
    },

    // 输入框
    input: {
      height: {
        sm: 56,
        md: 64,
        lg: 80
      },
      padding: { x: 24, y: 16 }
    },

    // 卡片
    card: {
      padding: 32,
      borderRadius: 16,
      shadow: '0 4rpx 12rpx rgba(0, 0, 0, 0.1)'
    },

    // 导航栏
    navbar: {
      height: 88,
      padding: { x: 30, y: 20 }
    }
  }
}

// 导出设计令牌
module.exports = DesignTokens
