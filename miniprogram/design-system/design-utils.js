/**
 * 设计系统工具函数
 * 提供便捷的设计令牌访问和样式生成功能
 */

const DesignTokens = require('./design-tokens')

class DesignUtils {
  constructor() {
    this.tokens = DesignTokens
  }

  // ========== 颜色工具 ==========

  /**
   * 获取颜色值
   * @param {string} colorPath - 颜色路径，如 'primary.500' 或 'semantic.textPrimary'
   * @returns {string} 颜色值
   */
  getColor(colorPath) {
    const paths = colorPath.split('.')
    let color = this.tokens.colors
    
    for (const path of paths) {
      color = color[path]
      if (!color) {
        console.warn(`颜色路径 ${colorPath} 不存在`)
        return '#000000'
      }
    }
    
    return color
  }

  /**
   * 生成渐变色
   * @param {string} startColor - 起始颜色
   * @param {string} endColor - 结束颜色
   * @param {number} angle - 渐变角度，默认135度
   * @returns {string} CSS渐变值
   */
  createGradient(startColor, endColor, angle = 135) {
    const start = this.getColor(startColor)
    const end = this.getColor(endColor)
    return `linear-gradient(${angle}deg, ${start}, ${end})`
  }

  /**
   * 调整颜色透明度
   * @param {string} colorPath - 颜色路径
   * @param {number} alpha - 透明度 0-1
   * @returns {string} rgba颜色值
   */
  withAlpha(colorPath, alpha) {
    const color = this.getColor(colorPath)
    
    // 如果是hex颜色，转换为rgba
    if (color.startsWith('#')) {
      const hex = color.slice(1)
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)
      return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }
    
    return color
  }

  // ========== 字体工具 ==========

  /**
   * 获取字体大小
   * @param {string} size - 尺寸名称，如 'base', 'lg', 'xl'
   * @returns {string} 字体大小值（rpx）
   */
  getFontSize(size) {
    const fontSize = this.tokens.typography.fontSize[size]
    return fontSize ? `${fontSize}rpx` : '28rpx'
  }

  /**
   * 获取字体权重
   * @param {string} weight - 权重名称，如 'normal', 'bold'
   * @returns {number} 字体权重值
   */
  getFontWeight(weight) {
    return this.tokens.typography.fontWeight[weight] || 400
  }

  /**
   * 生成文本样式
   * @param {Object} options - 样式选项
   * @returns {Object} 样式对象
   */
  createTextStyle(options = {}) {
    const {
      size = 'base',
      weight = 'normal',
      color = 'semantic.textPrimary',
      lineHeight = 'normal',
      letterSpacing = 'normal'
    } = options

    return {
      fontSize: this.getFontSize(size),
      fontWeight: this.getFontWeight(weight),
      color: this.getColor(color),
      lineHeight: this.tokens.typography.lineHeight[lineHeight] || 1.5,
      letterSpacing: this.tokens.typography.letterSpacing[letterSpacing] || '0em'
    }
  }

  // ========== 间距工具 ==========

  /**
   * 获取间距值
   * @param {string|number} space - 间距名称或数值
   * @returns {string} 间距值（rpx）
   */
  getSpacing(space) {
    if (typeof space === 'number') {
      return `${space}rpx`
    }
    
    const spacing = this.tokens.spacing[space]
    return spacing !== undefined ? `${spacing}rpx` : '0rpx'
  }

  /**
   * 生成内边距样式
   * @param {string|Object} padding - 内边距值或对象
   * @returns {Object} 样式对象
   */
  createPadding(padding) {
    if (typeof padding === 'string' || typeof padding === 'number') {
      const value = this.getSpacing(padding)
      return { padding: value }
    }

    const { top, right, bottom, left, x, y } = padding
    const styles = {}

    if (x !== undefined) {
      styles.paddingLeft = this.getSpacing(x)
      styles.paddingRight = this.getSpacing(x)
    }
    if (y !== undefined) {
      styles.paddingTop = this.getSpacing(y)
      styles.paddingBottom = this.getSpacing(y)
    }
    if (top !== undefined) styles.paddingTop = this.getSpacing(top)
    if (right !== undefined) styles.paddingRight = this.getSpacing(right)
    if (bottom !== undefined) styles.paddingBottom = this.getSpacing(bottom)
    if (left !== undefined) styles.paddingLeft = this.getSpacing(left)

    return styles
  }

  /**
   * 生成外边距样式
   * @param {string|Object} margin - 外边距值或对象
   * @returns {Object} 样式对象
   */
  createMargin(margin) {
    if (typeof margin === 'string' || typeof margin === 'number') {
      const value = this.getSpacing(margin)
      return { margin: value }
    }

    const { top, right, bottom, left, x, y } = margin
    const styles = {}

    if (x !== undefined) {
      styles.marginLeft = this.getSpacing(x)
      styles.marginRight = this.getSpacing(x)
    }
    if (y !== undefined) {
      styles.marginTop = this.getSpacing(y)
      styles.marginBottom = this.getSpacing(y)
    }
    if (top !== undefined) styles.marginTop = this.getSpacing(top)
    if (right !== undefined) styles.marginRight = this.getSpacing(right)
    if (bottom !== undefined) styles.marginBottom = this.getSpacing(bottom)
    if (left !== undefined) styles.marginLeft = this.getSpacing(left)

    return styles
  }

  // ========== 圆角工具 ==========

  /**
   * 获取圆角值
   * @param {string} radius - 圆角名称
   * @returns {string} 圆角值（rpx）
   */
  getBorderRadius(radius) {
    const value = this.tokens.borderRadius[radius]
    return value !== undefined ? `${value}rpx` : '0rpx'
  }

  // ========== 阴影工具 ==========

  /**
   * 获取阴影值
   * @param {string} shadow - 阴影名称
   * @returns {string} 阴影值
   */
  getBoxShadow(shadow) {
    return this.tokens.boxShadow[shadow] || 'none'
  }

  // ========== 动画工具 ==========

  /**
   * 创建动画样式
   * @param {string} preset - 预设动画名称
   * @param {Object} options - 自定义选项
   * @returns {Object} 动画样式对象
   */
  createAnimation(preset, options = {}) {
    const presetAnimation = this.tokens.animation.presets[preset]
    if (!presetAnimation) {
      console.warn(`动画预设 ${preset} 不存在`)
      return {}
    }

    const {
      duration = presetAnimation.duration,
      easing = presetAnimation.easing,
      delay = 0,
      fillMode = 'both'
    } = options

    return {
      animationDuration: `${duration}ms`,
      animationTimingFunction: easing,
      animationDelay: `${delay}ms`,
      animationFillMode: fillMode
    }
  }

  // ========== 组件样式生成器 ==========

  /**
   * 生成按钮样式
   * @param {Object} options - 按钮选项
   * @returns {Object} 按钮样式对象
   */
  createButtonStyle(options = {}) {
    const {
      variant = 'primary',
      size = 'md',
      disabled = false,
      loading = false
    } = options

    const baseStyle = {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: 'none',
      borderRadius: this.getBorderRadius('md'),
      fontWeight: this.getFontWeight('semibold'),
      transition: 'all 0.3s ease',
      cursor: disabled ? 'not-allowed' : 'pointer'
    }

    // 尺寸样式
    const sizeStyles = {
      sm: {
        height: `${this.tokens.components.button.height.sm}rpx`,
        fontSize: this.getFontSize('sm'),
        ...this.createPadding(this.tokens.components.button.padding.sm)
      },
      md: {
        height: `${this.tokens.components.button.height.md}rpx`,
        fontSize: this.getFontSize('base'),
        ...this.createPadding(this.tokens.components.button.padding.md)
      },
      lg: {
        height: `${this.tokens.components.button.height.lg}rpx`,
        fontSize: this.getFontSize('lg'),
        ...this.createPadding(this.tokens.components.button.padding.lg)
      }
    }

    // 变体样式
    const variantStyles = {
      primary: {
        background: this.createGradient('primary.500', 'primary.600'),
        color: this.getColor('semantic.textInverse'),
        boxShadow: this.getBoxShadow('md')
      },
      secondary: {
        background: this.getColor('semantic.bgPrimary'),
        color: this.getColor('primary.500'),
        border: `2rpx solid ${this.getColor('primary.500')}`
      },
      text: {
        background: 'transparent',
        color: this.getColor('primary.500')
      }
    }

    // 状态样式
    const stateStyles = {}
    if (disabled) {
      stateStyles.opacity = 0.5
      stateStyles.cursor = 'not-allowed'
    }
    if (loading) {
      stateStyles.cursor = 'wait'
    }

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...stateStyles
    }
  }

  /**
   * 生成卡片样式
   * @param {Object} options - 卡片选项
   * @returns {Object} 卡片样式对象
   */
  createCardStyle(options = {}) {
    const {
      padding = this.tokens.components.card.padding,
      borderRadius = this.tokens.components.card.borderRadius,
      shadow = this.tokens.components.card.shadow,
      background = 'semantic.bgPrimary'
    } = options

    return {
      background: this.getColor(background),
      borderRadius: `${borderRadius}rpx`,
      padding: `${padding}rpx`,
      boxShadow: shadow
    }
  }

  // ========== 响应式工具 ==========

  /**
   * 创建响应式样式
   * @param {Object} breakpointStyles - 断点样式对象
   * @returns {string} 媒体查询CSS
   */
  createResponsiveStyles(breakpointStyles) {
    let css = ''
    
    Object.entries(breakpointStyles).forEach(([breakpoint, styles]) => {
      const minWidth = this.tokens.breakpoints[breakpoint]
      if (minWidth > 0) {
        css += `@media (min-width: ${minWidth}rpx) { ${this.objectToCSS(styles)} }`
      } else {
        css += this.objectToCSS(styles)
      }
    })
    
    return css
  }

  /**
   * 将样式对象转换为CSS字符串
   * @param {Object} styles - 样式对象
   * @returns {string} CSS字符串
   */
  objectToCSS(styles) {
    return Object.entries(styles)
      .map(([key, value]) => `${this.camelToKebab(key)}: ${value};`)
      .join(' ')
  }

  /**
   * 驼峰转短横线
   * @param {string} str - 驼峰字符串
   * @returns {string} 短横线字符串
   */
  camelToKebab(str) {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }
}

// 创建单例实例
const designUtils = new DesignUtils()

module.exports = designUtils
