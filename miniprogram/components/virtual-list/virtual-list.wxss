/* components/virtual-list/virtual-list.wxss - 虚拟列表组件样式 */
/* Task 2.3: 新增实用组件开发 - 虚拟列表样式 */

/* 虚拟列表容器 */
.virtual-list-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* 滚动视图 */
.virtual-scroll-view {
  width: 100%;
  height: 100%;
}

/* 列表内容 */
.virtual-list-content {
  position: relative;
  width: 100%;
}

/* 可视区域 */
.virtual-viewport {
  position: relative;
  width: 100%;
}

/* 列表项 */
.virtual-list-item {
  width: 100%;
  box-sizing: border-box;
  transition: background-color 0.2s ease;
}

.virtual-list-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 列表项内容 */
.virtual-item-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 默认列表项样式 */
.default-item {
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.default-item:last-child {
  border-bottom: none;
}

/* 加载状态 */
.virtual-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #999;
}

.virtual-loading .loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.virtual-loading .loading-text {
  font-size: 28rpx;
}

/* 空状态 */
.virtual-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.virtual-empty .empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.virtual-empty .empty-text {
  font-size: 28rpx;
  text-align: center;
  line-height: 1.5;
}

/* 滚动指示器 */
.virtual-scrollbar {
  position: absolute;
  top: 0;
  right: 4rpx;
  width: 8rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.virtual-list-container.scrolling .virtual-scrollbar {
  opacity: 1;
}

.virtual-scrollbar .scrollbar-thumb {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4rpx;
  transition: background-color 0.2s ease;
}

.virtual-scrollbar:hover .scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 性能优化 */
.virtual-list-item {
  contain: layout style paint;
  will-change: transform;
}

.virtual-viewport {
  contain: layout style paint;
  will-change: transform;
}

/* 滚动性能优化 */
.virtual-scroll-view {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 分割线 */
.virtual-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 0 30rpx;
}

/* 分组标题 */
.virtual-group-title {
  background-color: #f8f9fa;
  color: #666;
  font-size: 26rpx;
  padding: 16rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 5;
}

/* 索引指示器 */
.virtual-index-indicator {
  position: absolute;
  top: 50%;
  right: 30rpx;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 20;
}

.virtual-list-container.scrolling .virtual-index-indicator {
  opacity: 1;
}

/* 快速滚动按钮 */
.virtual-scroll-buttons {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  z-index: 15;
}

.scroll-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #667eea;
  transition: all 0.2s ease;
}

.scroll-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .virtual-item-content {
    padding: 0 20rpx;
  }
  
  .virtual-group-title {
    padding: 12rpx 20rpx;
    font-size: 24rpx;
  }
  
  .scroll-btn {
    width: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
  }
  
  .virtual-scroll-buttons {
    bottom: 20rpx;
    right: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .default-item {
    background-color: #2a2a2a;
    border-bottom-color: #444;
  }
  
  .virtual-group-title {
    background-color: #333;
    color: #ccc;
    border-bottom-color: #444;
  }
  
  .virtual-empty {
    color: #ccc;
  }
  
  .scroll-btn {
    background-color: rgba(42, 42, 42, 0.9);
    color: #667eea;
  }
  
  .virtual-index-indicator {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* 动画增强 */
.virtual-list-item {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.virtual-viewport {
  transition: transform 0.1s ease-out;
}

/* 列表项进入动画 */
.virtual-list-item.entering {
  animation: itemEnter 0.3s ease-out;
}

@keyframes itemEnter {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式优化 */
.virtual-scroll-view::-webkit-scrollbar {
  display: none;
}

/* 触摸反馈 */
.virtual-list-item:active {
  background-color: rgba(102, 126, 234, 0.1);
}
