/**
 * 虚拟列表组件
 * Task 2.3: 新增实用组件开发 - 虚拟列表组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 数据列表
    items: {
      type: Array,
      value: []
    },
    
    // 列表项高度
    itemHeight: {
      type: Number,
      value: 100
    },
    
    // 容器高度
    height: {
      type: String,
      value: '600rpx'
    },
    
    // 缓冲区大小
    bufferSize: {
      type: Number,
      value: 5
    },
    
    // 是否启用虚拟滚动
    virtual: {
      type: Boolean,
      value: true
    },
    
    // 滚动阈值
    scrollThreshold: {
      type: Number,
      value: 50
    },
    
    // 是否显示滚动条
    showScrollbar: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 可视区域开始索引
    startIndex: 0,
    
    // 可视区域结束索引
    endIndex: 0,
    
    // 可视区域数据
    visibleItems: [],
    
    // 滚动位置
    scrollTop: 0,
    
    // 容器尺寸
    containerHeight: 0,
    
    // 可视区域高度
    viewportHeight: 0,
    
    // 总高度
    totalHeight: 0,
    
    // 偏移量
    offsetY: 0,
    
    // 滚动状态
    scrolling: false,
    
    // 滚动定时器
    scrollTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化虚拟列表
     */
    initVirtualList() {
      // 计算容器高度
      this.calculateContainerHeight()
      
      // 计算可视区域
      this.calculateViewport()
      
      // 更新可视数据
      this.updateVisibleItems()
    },

    /**
     * 计算容器高度
     */
    calculateContainerHeight() {
      const query = this.createSelectorQuery()
      query.select('.virtual-list-container').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            containerHeight: rect.height,
            viewportHeight: rect.height
          })
          
          // 重新计算可视区域
          this.calculateViewport()
        }
      }).exec()
    },

    /**
     * 计算可视区域
     */
    calculateViewport() {
      const { items, itemHeight, viewportHeight, bufferSize } = this.data
      
      if (!this.data.virtual || items.length === 0) {
        this.setData({
          startIndex: 0,
          endIndex: items.length - 1,
          totalHeight: items.length * itemHeight,
          offsetY: 0
        })
        return
      }
      
      // 计算可视区域内可显示的项目数量
      const visibleCount = Math.ceil(viewportHeight / itemHeight)
      
      // 计算开始和结束索引
      const scrollTop = this.data.scrollTop
      const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize)
      const endIndex = Math.min(items.length - 1, startIndex + visibleCount + bufferSize * 2)
      
      // 计算偏移量
      const offsetY = startIndex * itemHeight
      
      // 计算总高度
      const totalHeight = items.length * itemHeight
      
      this.setData({
        startIndex,
        endIndex,
        offsetY,
        totalHeight
      })
    },

    /**
     * 更新可视数据
     */
    updateVisibleItems() {
      const { items, startIndex, endIndex } = this.data
      
      if (!this.data.virtual) {
        this.setData({ visibleItems: items })
        return
      }
      
      const visibleItems = []
      
      for (let i = startIndex; i <= endIndex; i++) {
        if (items[i]) {
          visibleItems.push({
            ...items[i],
            index: i,
            top: i * this.data.itemHeight
          })
        }
      }
      
      this.setData({ visibleItems })
    },

    /**
     * 滚动事件处理
     * @param {Event} e 滚动事件
     */
    onScroll(e) {
      const scrollTop = e.detail.scrollTop
      
      this.setData({
        scrollTop,
        scrolling: true
      })
      
      // 清除之前的定时器
      if (this.data.scrollTimer) {
        clearTimeout(this.data.scrollTimer)
      }
      
      // 设置新的定时器
      const timer = setTimeout(() => {
        this.setData({ scrolling: false })
      }, 150)
      
      this.setData({ scrollTimer: timer })
      
      // 更新可视区域
      this.calculateViewport()
      this.updateVisibleItems()
      
      // 触发滚动事件
      this.triggerEvent('scroll', {
        scrollTop: scrollTop,
        scrolling: this.data.scrolling
      })
      
      // 检查是否需要加载更多
      this.checkLoadMore(scrollTop)
    },

    /**
     * 检查是否需要加载更多
     * @param {number} scrollTop 滚动位置
     */
    checkLoadMore(scrollTop) {
      const { totalHeight, viewportHeight, scrollThreshold } = this.data
      const bottomDistance = totalHeight - scrollTop - viewportHeight
      
      if (bottomDistance <= scrollThreshold) {
        this.triggerEvent('loadmore', {
          scrollTop: scrollTop,
          bottomDistance: bottomDistance
        })
      }
    },

    /**
     * 滚动到指定位置
     * @param {number} index 目标索引
     */
    scrollToIndex(index) {
      const scrollTop = index * this.data.itemHeight
      
      this.setData({ scrollTop })
      
      // 更新可视区域
      this.calculateViewport()
      this.updateVisibleItems()
    },

    /**
     * 滚动到顶部
     */
    scrollToTop() {
      this.scrollToIndex(0)
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.scrollToIndex(this.data.items.length - 1)
    },

    /**
     * 刷新列表
     */
    refresh() {
      this.initVirtualList()
    },

    /**
     * 点击列表项
     * @param {Event} e 点击事件
     */
    onItemTap(e) {
      const index = e.currentTarget.dataset.index
      const item = this.data.items[index]
      
      this.triggerEvent('itemtap', {
        index: index,
        item: item
      })
    },

    /**
     * 长按列表项
     * @param {Event} e 长按事件
     */
    onItemLongPress(e) {
      const index = e.currentTarget.dataset.index
      const item = this.data.items[index]
      
      this.triggerEvent('itemlongpress', {
        index: index,
        item: item
      })
    },

    /**
     * 获取列表项样式
     * @param {Object} item 列表项数据
     */
    getItemStyle(item) {
      if (!this.data.virtual) {
        return `height: ${this.data.itemHeight}px;`
      }
      
      return `
        height: ${this.data.itemHeight}px;
        position: absolute;
        top: ${item.top}px;
        left: 0;
        right: 0;
      `
    },

    /**
     * 获取容器样式
     */
    getContainerStyle() {
      return `height: ${this.data.height};`
    },

    /**
     * 获取内容样式
     */
    getContentStyle() {
      if (!this.data.virtual) {
        return ''
      }
      
      return `
        height: ${this.data.totalHeight}px;
        position: relative;
      `
    },

    /**
     * 获取可视区域样式
     */
    getViewportStyle() {
      if (!this.data.virtual) {
        return ''
      }
      
      return `
        transform: translateY(${this.data.offsetY}px);
        position: relative;
      `
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 延迟初始化，确保DOM渲染完成
      setTimeout(() => {
        this.initVirtualList()
      }, 100)
    },
    
    detached() {
      // 清理定时器
      if (this.data.scrollTimer) {
        clearTimeout(this.data.scrollTimer)
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'items, itemHeight, virtual': function() {
      // 数据变化时重新初始化
      setTimeout(() => {
        this.initVirtualList()
      }, 50)
    }
  }
})
