<!--
  虚拟列表组件模板
  Task 2.3: 新增实用组件开发 - 虚拟列表模板
-->

<view 
  class="virtual-list-container {{scrolling ? 'scrolling' : ''}}"
  style="{{getContainerStyle()}}"
>
  <!-- 滚动视图 -->
  <scroll-view
    class="virtual-scroll-view"
    scroll-y="{{true}}"
    scroll-top="{{scrollTop}}"
    show-scrollbar="{{showScrollbar}}"
    bindscroll="onScroll"
    enhanced="{{true}}"
    bounces="{{false}}"
  >
    <!-- 列表内容 -->
    <view class="virtual-list-content" style="{{getContentStyle()}}">
      <!-- 可视区域 -->
      <view class="virtual-viewport" style="{{getViewportStyle()}}">
        <!-- 列表项 -->
        <view
          class="virtual-list-item default-item"
          wx:for="{{visibleItems}}"
          wx:key="index"
          wx:for-item="item"
          data-index="{{item.index}}"
          style="{{getItemStyle(item)}}"
          bindtap="onItemTap"
          bindlongpress="onItemLongPress"
        >
          <!-- 自定义内容插槽 -->
          <slot name="item" item="{{item}}" index="{{item.index}}">
            <!-- 默认列表项内容 -->
            <view class="virtual-item-content">
              <view class="item-title">{{item.title || item.name || '列表项 ' + (item.index + 1)}}</view>
            </view>
          </slot>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{items.length > 0}}" class="virtual-loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载更多...</view>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view wx:if="{{items.length === 0}}" class="virtual-empty">
    <view class="empty-icon">📝</view>
    <view class="empty-text">暂无数据</view>
  </view>

  <!-- 滚动指示器 -->
  <view 
    wx:if="{{showScrollbar && items.length > 0}}"
    class="virtual-scrollbar"
    style="height: {{containerHeight}}px"
  >
    <view 
      class="scrollbar-thumb"
      style="height: {{(containerHeight / totalHeight) * containerHeight}}px; transform: translateY({{(scrollTop / totalHeight) * containerHeight}}px)"
    ></view>
  </view>

  <!-- 索引指示器 -->
  <view wx:if="{{scrolling && items.length > 0}}" class="virtual-index-indicator">
    {{Math.floor(scrollTop / itemHeight) + 1}} / {{items.length}}
  </view>

  <!-- 快速滚动按钮 -->
  <view wx:if="{{items.length > 20}}" class="virtual-scroll-buttons">
    <view class="scroll-btn" bindtap="scrollToTop">↑</view>
    <view class="scroll-btn" bindtap="scrollToBottom">↓</view>
  </view>
</view>

<wxs module="utils">
  var getItemStyle = function(item, virtual, itemHeight) {
    if (!virtual) {
      return 'height: ' + itemHeight + 'px;'
    }
    
    return 'height: ' + itemHeight + 'px; position: absolute; top: ' + item.top + 'px; left: 0; right: 0;'
  }
  
  var getContainerStyle = function(height) {
    return 'height: ' + height + ';'
  }
  
  var getContentStyle = function(virtual, totalHeight) {
    if (!virtual) {
      return ''
    }
    
    return 'height: ' + totalHeight + 'px; position: relative;'
  }
  
  var getViewportStyle = function(virtual, offsetY) {
    if (!virtual) {
      return ''
    }
    
    return 'transform: translateY(' + offsetY + 'px); position: relative;'
  }
  
  module.exports = {
    getItemStyle: getItemStyle,
    getContainerStyle: getContainerStyle,
    getContentStyle: getContentStyle,
    getViewportStyle: getViewportStyle
  }
</wxs>
