/**
 * 图片预览组件
 * Task 2.3: 新增实用组件开发 - 图片预览组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    
    // 图片列表
    images: {
      type: Array,
      value: []
    },
    
    // 当前索引
    current: {
      type: Number,
      value: 0
    },
    
    // 是否显示指示器
    showIndicator: {
      type: Boolean,
      value: true
    },
    
    // 是否显示删除按钮
    showDelete: {
      type: Boolean,
      value: false
    },
    
    // 是否显示保存按钮
    showSave: {
      type: Boolean,
      value: true
    },
    
    // 是否支持缩放
    zoomable: {
      type: Boolean,
      value: true
    },
    
    // 最大缩放倍数
    maxZoom: {
      type: Number,
      value: 3
    },
    
    // 最小缩放倍数
    minZoom: {
      type: Number,
      value: 0.5
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前显示的图片索引
    currentIndex: 0,
    
    // 缩放相关
    scale: 1,
    translateX: 0,
    translateY: 0,
    
    // 触摸状态
    touching: false,
    touchCount: 0,
    
    // 双指触摸
    lastTouchDistance: 0,
    lastTouchCenter: { x: 0, y: 0 },
    
    // 单指拖拽
    lastTouchPoint: { x: 0, y: 0 },
    
    // 动画状态
    animating: false,
    
    // 图片加载状态
    imageLoading: {},
    imageError: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示预览
     */
    show() {
      this.setData({
        currentIndex: this.data.current,
        scale: 1,
        translateX: 0,
        translateY: 0
      })
      
      // 预加载当前图片
      this.preloadImage(this.data.currentIndex)
      
      // 触发显示事件
      this.triggerEvent('show', {
        current: this.data.currentIndex
      })
    },

    /**
     * 隐藏预览
     */
    hide() {
      this.setData({
        scale: 1,
        translateX: 0,
        translateY: 0,
        animating: false
      })
      
      // 触发隐藏事件
      this.triggerEvent('hide', {
        current: this.data.currentIndex
      })
    },

    /**
     * 切换到指定图片
     * @param {number} index 图片索引
     */
    switchTo(index) {
      if (index < 0 || index >= this.data.images.length) {
        return
      }
      
      this.setData({
        currentIndex: index,
        scale: 1,
        translateX: 0,
        translateY: 0
      })
      
      // 预加载相邻图片
      this.preloadImage(index)
      this.preloadImage(index - 1)
      this.preloadImage(index + 1)
      
      // 触发切换事件
      this.triggerEvent('change', {
        current: index,
        image: this.data.images[index]
      })
    },

    /**
     * 上一张图片
     */
    prev() {
      const prevIndex = this.data.currentIndex - 1
      if (prevIndex >= 0) {
        this.switchTo(prevIndex)
      }
    },

    /**
     * 下一张图片
     */
    next() {
      const nextIndex = this.data.currentIndex + 1
      if (nextIndex < this.data.images.length) {
        this.switchTo(nextIndex)
      }
    },

    /**
     * 预加载图片
     * @param {number} index 图片索引
     */
    preloadImage(index) {
      if (index < 0 || index >= this.data.images.length) {
        return
      }
      
      const image = this.data.images[index]
      if (!image || this.data.imageLoading[index]) {
        return
      }
      
      this.setData({
        [`imageLoading[${index}]`]: true
      })
      
      // 创建图片对象预加载
      const img = wx.createImage ? wx.createImage() : new Image()
      
      img.onload = () => {
        this.setData({
          [`imageLoading[${index}]`]: false
        })
      }
      
      img.onerror = () => {
        this.setData({
          [`imageLoading[${index}]`]: false,
          [`imageError[${index}]`]: true
        })
      }
      
      img.src = typeof image === 'string' ? image : image.url
    },

    /**
     * 触摸开始
     * @param {Event} e 触摸事件
     */
    onTouchStart(e) {
      if (!this.data.zoomable) return
      
      const touches = e.touches
      this.setData({
        touching: true,
        touchCount: touches.length,
        animating: false
      })
      
      if (touches.length === 1) {
        // 单指触摸
        this.setData({
          lastTouchPoint: {
            x: touches[0].clientX,
            y: touches[0].clientY
          }
        })
      } else if (touches.length === 2) {
        // 双指触摸
        const distance = this.getTouchDistance(touches[0], touches[1])
        const center = this.getTouchCenter(touches[0], touches[1])
        
        this.setData({
          lastTouchDistance: distance,
          lastTouchCenter: center
        })
      }
    },

    /**
     * 触摸移动
     * @param {Event} e 触摸事件
     */
    onTouchMove(e) {
      if (!this.data.touching || !this.data.zoomable) return
      
      const touches = e.touches
      
      if (touches.length === 1 && this.data.scale > 1) {
        // 单指拖拽（仅在放大状态下）
        const touch = touches[0]
        const deltaX = touch.clientX - this.data.lastTouchPoint.x
        const deltaY = touch.clientY - this.data.lastTouchPoint.y
        
        this.setData({
          translateX: this.data.translateX + deltaX,
          translateY: this.data.translateY + deltaY,
          lastTouchPoint: {
            x: touch.clientX,
            y: touch.clientY
          }
        })
      } else if (touches.length === 2) {
        // 双指缩放
        const distance = this.getTouchDistance(touches[0], touches[1])
        const center = this.getTouchCenter(touches[0], touches[1])
        
        const scaleRatio = distance / this.data.lastTouchDistance
        let newScale = this.data.scale * scaleRatio
        
        // 限制缩放范围
        newScale = Math.max(this.data.minZoom, Math.min(this.data.maxZoom, newScale))
        
        this.setData({
          scale: newScale,
          lastTouchDistance: distance,
          lastTouchCenter: center
        })
      }
      
      // 阻止默认行为
      e.preventDefault && e.preventDefault()
    },

    /**
     * 触摸结束
     * @param {Event} e 触摸事件
     */
    onTouchEnd(e) {
      this.setData({
        touching: false,
        touchCount: 0
      })
      
      // 边界检查和回弹
      this.checkBounds()
    },

    /**
     * 双击缩放
     * @param {Event} e 双击事件
     */
    onDoubleTap(e) {
      if (!this.data.zoomable) return
      
      const newScale = this.data.scale > 1 ? 1 : 2
      
      this.setData({
        scale: newScale,
        translateX: newScale === 1 ? 0 : this.data.translateX,
        translateY: newScale === 1 ? 0 : this.data.translateY,
        animating: true
      })
      
      setTimeout(() => {
        this.setData({ animating: false })
      }, 300)
    },

    /**
     * 获取两点间距离
     * @param {Object} touch1 触摸点1
     * @param {Object} touch2 触摸点2
     */
    getTouchDistance(touch1, touch2) {
      const dx = touch1.clientX - touch2.clientX
      const dy = touch1.clientY - touch2.clientY
      return Math.sqrt(dx * dx + dy * dy)
    },

    /**
     * 获取两点中心
     * @param {Object} touch1 触摸点1
     * @param {Object} touch2 触摸点2
     */
    getTouchCenter(touch1, touch2) {
      return {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      }
    },

    /**
     * 检查边界并回弹
     */
    checkBounds() {
      let { translateX, translateY, scale } = this.data
      let needUpdate = false
      
      if (scale <= 1) {
        // 缩放小于等于1时，重置位移
        if (translateX !== 0 || translateY !== 0) {
          translateX = 0
          translateY = 0
          needUpdate = true
        }
      } else {
        // 缩放大于1时，检查边界
        const maxTranslate = 100 * (scale - 1)
        
        if (Math.abs(translateX) > maxTranslate) {
          translateX = translateX > 0 ? maxTranslate : -maxTranslate
          needUpdate = true
        }
        
        if (Math.abs(translateY) > maxTranslate) {
          translateY = translateY > 0 ? maxTranslate : -maxTranslate
          needUpdate = true
        }
      }
      
      if (needUpdate) {
        this.setData({
          translateX,
          translateY,
          animating: true
        })
        
        setTimeout(() => {
          this.setData({ animating: false })
        }, 300)
      }
    },

    /**
     * 保存图片
     */
    saveImage() {
      const currentImage = this.data.images[this.data.currentIndex]
      const imageUrl = typeof currentImage === 'string' ? currentImage : currentImage.url
      
      wx.saveImageToPhotosAlbum({
        filePath: imageUrl,
        success: () => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          // 触发保存事件
          this.triggerEvent('save', {
            index: this.data.currentIndex,
            image: currentImage
          })
        },
        fail: (err) => {
          if (err.errMsg.includes('auth')) {
            wx.showModal({
              title: '提示',
              content: '需要授权访问相册才能保存图片',
              confirmText: '去授权',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting()
                }
              }
            })
          } else {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        }
      })
    },

    /**
     * 删除图片
     */
    deleteImage() {
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            // 触发删除事件
            this.triggerEvent('delete', {
              index: this.data.currentIndex,
              image: this.data.images[this.data.currentIndex]
            })
          }
        }
      })
    },

    /**
     * 点击遮罩关闭
     */
    onMaskTap() {
      this.hide()
    },

    /**
     * 点击指示器
     * @param {Event} e 点击事件
     */
    onIndicatorTap(e) {
      const index = e.currentTarget.dataset.index
      this.switchTo(parseInt(index))
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.show()
      } else {
        this.hide()
      }
    },
    
    'current': function(current) {
      if (this.data.visible) {
        this.switchTo(current)
      }
    }
  }
})
