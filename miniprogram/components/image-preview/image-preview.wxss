/* components/image-preview/image-preview.wxss - 图片预览组件样式 */
/* Task 2.3: 新增实用组件开发 - 图片预览样式 */

/* 预览容器 */
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 图片容器 */
.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片主体 */
.preview-image {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.3s ease;
}

.preview-image.animating {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 加载状态 */
.preview-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.preview-loading .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-loading .loading-text {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 错误状态 */
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  opacity: 0.6;
}

.preview-error .error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.preview-error .error-text {
  font-size: 28rpx;
}

/* 顶部工具栏 */
.preview-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(rgba(0, 0, 0, 0.6), transparent);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 10;
}

.preview-toolbar .toolbar-left,
.preview-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.toolbar-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  transition: all 0.2s ease;
}

.toolbar-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

/* 底部指示器 */
.preview-indicators {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 10;
}

.indicator-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.indicator-dot.active {
  background-color: #fff;
  transform: scale(1.2);
}

/* 图片计数 */
.preview-counter {
  position: absolute;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  z-index: 10;
}

/* 切换按钮 */
.preview-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.preview-nav.prev {
  left: 30rpx;
}

.preview-nav.next {
  right: 30rpx;
}

.nav-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #fff;
  transition: all 0.2s ease;
}

.nav-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

.nav-btn.disabled {
  opacity: 0.3;
  pointer-events: none;
}

/* 手势提示 */
.preview-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
  text-align: center;
  pointer-events: none;
  z-index: 5;
}

/* 缩放信息 */
.preview-zoom-info {
  position: absolute;
  top: 120rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-zoom-info.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .preview-toolbar {
    height: 80rpx;
    padding: 0 20rpx;
  }
  
  .toolbar-btn {
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }
  
  .nav-btn {
    width: 60rpx;
    height: 60rpx;
    font-size: 32rpx;
  }
  
  .preview-nav.prev {
    left: 20rpx;
  }
  
  .preview-nav.next {
    right: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .preview-overlay {
    background-color: rgba(0, 0, 0, 0.95);
  }
}

/* 动画增强 */
.preview-image {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.toolbar-btn,
.nav-btn {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 毛玻璃效果 */
.preview-toolbar,
.preview-counter,
.preview-zoom-info {
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}
