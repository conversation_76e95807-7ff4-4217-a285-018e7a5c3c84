<!--
  图片预览组件模板
  Task 2.3: 新增实用组件开发 - 图片预览模板
-->

<view class="preview-overlay" wx:if="{{visible}}" bindtap="onMaskTap">
  <!-- 顶部工具栏 -->
  <view class="preview-toolbar" catchtap="">
    <view class="toolbar-left">
      <view class="toolbar-btn" bindtap="hide">×</view>
    </view>
    
    <view class="toolbar-right">
      <view wx:if="{{showDelete}}" class="toolbar-btn" bindtap="deleteImage">🗑</view>
      <view wx:if="{{showSave}}" class="toolbar-btn" bindtap="saveImage">💾</view>
    </view>
  </view>

  <!-- 图片容器 -->
  <view class="preview-container" catchtap="">
    <!-- 当前图片 -->
    <image
      wx:if="{{images[currentIndex] && !imageError[currentIndex]}}"
      class="preview-image {{animating ? 'animating' : ''}}"
      src="{{typeof images[currentIndex] === 'string' ? images[currentIndex] : images[currentIndex].url}}"
      mode="aspectFit"
      style="transform: scale({{scale}}) translate({{translateX}}px, {{translateY}}px)"
      bindtouchstart="onTouchStart"
      bindtouchmove="onTouchMove"
      bindtouchend="onTouchEnd"
      bindtap="onDoubleTap"
      binderror="onImageError"
    />

    <!-- 加载状态 -->
    <view wx:if="{{imageLoading[currentIndex]}}" class="preview-loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:if="{{imageError[currentIndex]}}" class="preview-error">
      <view class="error-icon">❌</view>
      <view class="error-text">图片加载失败</view>
    </view>

    <!-- 手势提示 -->
    <view wx:if="{{zoomable && !touching}}" class="preview-hint">
      双击缩放 · 双指缩放 · 拖拽移动
    </view>
  </view>

  <!-- 切换按钮 -->
  <view wx:if="{{images.length > 1}}">
    <!-- 上一张 -->
    <view 
      class="preview-nav prev"
      wx:if="{{currentIndex > 0}}"
      catchtap="prev"
    >
      <view class="nav-btn">‹</view>
    </view>

    <!-- 下一张 -->
    <view 
      class="preview-nav next"
      wx:if="{{currentIndex < images.length - 1}}"
      catchtap="next"
    >
      <view class="nav-btn">›</view>
    </view>
  </view>

  <!-- 底部指示器 -->
  <view wx:if="{{showIndicator && images.length > 1}}" class="preview-indicators" catchtap="">
    <view 
      class="indicator-dot {{index === currentIndex ? 'active' : ''}}"
      wx:for="{{images}}"
      wx:key="index"
      wx:for-index="index"
      data-index="{{index}}"
      bindtap="onIndicatorTap"
    ></view>
  </view>

  <!-- 图片计数 -->
  <view wx:if="{{!showIndicator && images.length > 1}}" class="preview-counter" catchtap="">
    {{currentIndex + 1}} / {{images.length}}
  </view>

  <!-- 缩放信息 -->
  <view 
    wx:if="{{zoomable && scale !== 1}}" 
    class="preview-zoom-info {{scale !== 1 ? 'show' : ''}}"
    catchtap=""
  >
    {{(scale * 100).toFixed(0)}}%
  </view>
</view>

<wxs module="utils">
  var getImageUrl = function(image) {
    if (typeof image === 'string') {
      return image
    }
    return image.url || image.src || ''
  }
  
  module.exports = {
    getImageUrl: getImageUrl
  }
</wxs>
