/**
 * 渐进式图片组件
 * Task 2.5: 图片加载性能优化 - 渐进式加载组件
 */

const imageLoader = require('../../utils/image-loader')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图片源
    src: {
      type: String,
      value: ''
    },
    
    // 占位图
    placeholder: {
      type: String,
      value: '/images/placeholder.png'
    },
    
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFill'
    },
    
    // 是否启用渐进式加载
    progressive: {
      type: Boolean,
      value: true
    },
    
    // 是否启用懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    
    // 图片宽度
    width: {
      type: String,
      value: '100%'
    },
    
    // 图片高度
    height: {
      type: String,
      value: 'auto'
    },
    
    // 圆角
    borderRadius: {
      type: String,
      value: '0'
    },
    
    // 是否显示加载进度
    showProgress: {
      type: Boolean,
      value: false
    },
    
    // 加载优先级
    priority: {
      type: String,
      value: 'normal' // high, normal, low
    },
    
    // 图片质量
    quality: {
      type: String,
      value: 'normal' // thumbnail, normal, high
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前显示的图片
    currentSrc: '',
    
    // 加载状态
    loadingState: 'idle', // idle, loading, loaded, error
    
    // 加载进度
    loadProgress: 0,
    
    // 加载阶段
    loadStage: '', // placeholder, thumbnail, medium, complete
    
    // 是否在视口内
    inViewport: false,
    
    // 错误信息
    errorMessage: '',
    
    // 动画状态
    animating: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 开始加载图片
     */
    async startLoad() {
      if (!this.data.src || this.data.loadingState === 'loading') {
        return
      }
      
      this.setData({
        loadingState: 'loading',
        loadProgress: 0,
        errorMessage: ''
      })
      
      try {
        if (this.data.progressive) {
          await this.progressiveLoad()
        } else {
          await this.normalLoad()
        }
      } catch (error) {
        this.handleLoadError(error)
      }
    },

    /**
     * 渐进式加载
     */
    async progressiveLoad() {
      await imageLoader.progressiveLoad(this.data.src, {
        placeholder: this.data.placeholder,
        onProgress: (progress) => {
          this.handleLoadProgress(progress)
        }
      })
    },

    /**
     * 普通加载
     */
    async normalLoad() {
      const optimizedSrc = imageLoader.optimizeImageUrl(this.data.src, {
        quality: this.data.quality,
        width: this.getImageWidth(),
        height: this.getImageHeight()
      })
      
      const imageData = await imageLoader.loadImage(optimizedSrc, {
        priority: this.data.priority
      })
      
      this.setData({
        currentSrc: optimizedSrc,
        loadingState: 'loaded',
        loadProgress: 100,
        loadStage: 'complete'
      })
      
      this.triggerLoadComplete(imageData)
    },

    /**
     * 处理加载进度
     * @param {Object} progress 进度信息
     */
    handleLoadProgress(progress) {
      const { stage, progress: percent, data } = progress
      
      this.setData({
        loadProgress: percent,
        loadStage: stage,
        animating: true
      })
      
      // 更新显示的图片
      if (data && data.url) {
        this.updateCurrentSrc(data.url)
      }
      
      // 触发进度事件
      this.triggerEvent('progress', {
        stage: stage,
        progress: percent,
        data: data
      })
      
      // 重置动画状态
      setTimeout(() => {
        this.setData({ animating: false })
      }, 300)
    },

    /**
     * 更新当前显示的图片
     * @param {string} src 图片源
     */
    updateCurrentSrc(src) {
      this.setData({ currentSrc: src })
    },

    /**
     * 处理加载错误
     * @param {Error} error 错误对象
     */
    handleLoadError(error) {
      console.error('图片加载失败:', error)
      
      this.setData({
        loadingState: 'error',
        errorMessage: error.message || '图片加载失败',
        currentSrc: this.data.placeholder
      })
      
      // 触发错误事件
      this.triggerEvent('error', {
        error: error,
        src: this.data.src
      })
    },

    /**
     * 触发加载完成事件
     * @param {Object} imageData 图片数据
     */
    triggerLoadComplete(imageData) {
      this.triggerEvent('load', {
        src: this.data.src,
        imageData: imageData,
        loadTime: imageData.loadTime
      })
    },

    /**
     * 重试加载
     */
    retryLoad() {
      this.setData({
        loadingState: 'idle',
        errorMessage: ''
      })
      
      this.startLoad()
    },

    /**
     * 检查是否在视口内
     */
    checkViewport() {
      if (!this.data.lazyLoad) {
        return true
      }
      
      const query = this.createSelectorQuery()
      query.select('.progressive-image').boundingClientRect((rect) => {
        if (rect) {
          const systemInfo = wx.getSystemInfoSync()
          const windowHeight = systemInfo.windowHeight
          
          // 检查是否在视口内或即将进入视口
          const inViewport = rect.top < windowHeight + 100 && rect.bottom > -100
          
          if (inViewport && !this.data.inViewport) {
            this.setData({ inViewport: true })
            this.startLoad()
          }
        }
      }).exec()
    },

    /**
     * 获取图片宽度
     */
    getImageWidth() {
      if (this.data.width.includes('rpx')) {
        return parseInt(this.data.width) / 2 // rpx转px
      }
      return parseInt(this.data.width) || 300
    },

    /**
     * 获取图片高度
     */
    getImageHeight() {
      if (this.data.height === 'auto') {
        return null
      }
      if (this.data.height.includes('rpx')) {
        return parseInt(this.data.height) / 2 // rpx转px
      }
      return parseInt(this.data.height) || 200
    },

    /**
     * 获取容器样式
     */
    getContainerStyle() {
      let style = `width: ${this.data.width}; `
      
      if (this.data.height !== 'auto') {
        style += `height: ${this.data.height}; `
      }
      
      if (this.data.borderRadius !== '0') {
        style += `border-radius: ${this.data.borderRadius}; `
      }
      
      return style
    },

    /**
     * 获取图片样式
     */
    getImageStyle() {
      let style = 'width: 100%; height: 100%; '
      
      // 添加过渡动画
      if (this.data.animating) {
        style += 'transition: opacity 0.3s ease; '
      }
      
      return style
    },

    /**
     * 获取进度条样式
     */
    getProgressStyle() {
      return `width: ${this.data.loadProgress}%`
    },

    /**
     * 点击图片
     */
    onImageTap() {
      this.triggerEvent('tap', {
        src: this.data.src,
        currentSrc: this.data.currentSrc
      })
    },

    /**
     * 图片加载完成
     */
    onImageLoad(e) {
      if (this.data.loadingState !== 'loaded') {
        this.setData({
          loadingState: 'loaded',
          loadProgress: 100
        })
      }
    },

    /**
     * 图片加载错误
     */
    onImageError(e) {
      this.handleLoadError(new Error('图片加载失败'))
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 设置初始占位图
      this.setData({
        currentSrc: this.data.placeholder
      })
      
      // 检查是否需要立即加载
      if (!this.data.lazyLoad) {
        this.startLoad()
      } else {
        // 延迟检查视口
        setTimeout(() => {
          this.checkViewport()
        }, 100)
      }
    },
    
    detached() {
      // 清理资源
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'src': function(newSrc) {
      if (newSrc && newSrc !== this.data.currentSrc) {
        // 重置状态
        this.setData({
          loadingState: 'idle',
          loadProgress: 0,
          currentSrc: this.data.placeholder
        })
        
        // 开始加载新图片
        if (!this.data.lazyLoad || this.data.inViewport) {
          this.startLoad()
        }
      }
    }
  },

  /**
   * 页面滚动监听
   */
  pageLifetimes: {
    show() {
      // 页面显示时检查视口
      if (this.data.lazyLoad && !this.data.inViewport) {
        this.checkViewport()
      }
    }
  }
})
