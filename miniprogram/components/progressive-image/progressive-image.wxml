<!--
  渐进式图片组件模板
  Task 2.5: 图片加载性能优化 - 渐进式图片模板
-->

<view 
  class="progressive-image {{loadingState}} stage-{{loadStage}} {{animating ? 'animating' : ''}}"
  style="{{getContainerStyle()}}"
  bindtap="onImageTap"
>
  <!-- 图片主体 -->
  <view class="progressive-image-main">
    <!-- 占位图 -->
    <view 
      wx:if="{{loadingState === 'idle' || loadingState === 'loading'}}"
      class="progressive-image-placeholder"
    ></view>
    
    <!-- 实际图片 -->
    <image
      wx:if="{{currentSrc}}"
      class="progressive-image-img"
      src="{{currentSrc}}"
      mode="{{mode}}"
      style="{{getImageStyle()}}"
      lazy-load="{{false}}"
      bindload="onImageLoad"
      binderror="onImageError"
    />
  </view>

  <!-- 加载进度条 -->
  <view 
    wx:if="{{showProgress && loadingState === 'loading'}}"
    class="progressive-image-progress"
  >
    <view 
      class="progressive-image-progress-bar"
      style="{{getProgressStyle()}}"
    ></view>
  </view>

  <!-- 加载指示器 -->
  <view 
    wx:if="{{loadingState === 'loading'}}"
    class="progressive-image-loading"
  >
    <view class="progressive-image-spinner"></view>
    <view class="progressive-image-loading-text">
      {{loadStage === 'thumbnail' ? '加载缩略图...' : 
        loadStage === 'medium' ? '加载中等质量...' : 
        loadStage === 'complete' ? '加载高清图...' : '加载中...'}}
    </view>
  </view>

  <!-- 错误状态 -->
  <view 
    wx:if="{{loadingState === 'error'}}"
    class="progressive-image-error"
  >
    <view class="progressive-image-error-icon">❌</view>
    <view class="progressive-image-error-text">{{errorMessage}}</view>
    <button 
      class="progressive-image-retry"
      bindtap="retryLoad"
      catchtap=""
    >
      重试
    </button>
  </view>

  <!-- 质量指示器 -->
  <view 
    wx:if="{{loadingState === 'loading' && loadStage}}"
    class="progressive-image-quality"
  >
    {{loadStage === 'thumbnail' ? '缩略图' : 
      loadStage === 'medium' ? '中等' : 
      loadStage === 'complete' ? '高清' : '加载中'}}
  </view>

  <!-- 网络状态指示 -->
  <view 
    wx:if="{{loadingState === 'loading'}}"
    class="progressive-image-network wifi"
  ></view>

  <!-- 图片信息 -->
  <view 
    wx:if="{{loadingState === 'loaded'}}"
    class="progressive-image-info"
  >
    {{quality}}
  </view>
</view>

<wxs module="utils">
  var getContainerStyle = function(width, height, borderRadius) {
    var style = 'width: ' + width + '; '
    
    if (height !== 'auto') {
      style += 'height: ' + height + '; '
    }
    
    if (borderRadius !== '0') {
      style += 'border-radius: ' + borderRadius + '; '
    }
    
    return style
  }
  
  var getImageStyle = function(animating) {
    var style = 'width: 100%; height: 100%; '
    
    if (animating) {
      style += 'transition: opacity 0.3s ease; '
    }
    
    return style
  }
  
  var getProgressStyle = function(loadProgress) {
    return 'width: ' + loadProgress + '%'
  }
  
  module.exports = {
    getContainerStyle: getContainerStyle,
    getImageStyle: getImageStyle,
    getProgressStyle: getProgressStyle
  }
</wxs>
