/* components/progressive-image/progressive-image.wxss - 渐进式图片组件样式 */
/* Task 2.5: 图片加载性能优化 - 渐进式图片样式 */

/* 图片容器 */
.progressive-image {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片主体 */
.progressive-image-main {
  width: 100%;
  height: 100%;
  position: relative;
}

.progressive-image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease, filter 0.3s ease;
}

/* 加载状态 */
.progressive-image.loading .progressive-image-img {
  filter: blur(2px);
}

.progressive-image.loaded .progressive-image-img {
  filter: none;
}

/* 占位图样式 */
.progressive-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 加载进度条 */
.progressive-image-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.3);
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image.loading .progressive-image-progress {
  opacity: 1;
}

.progressive-image-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  position: relative;
}

.progressive-image-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShimmer 1s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 加载指示器 */
.progressive-image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image.loading .progressive-image-loading {
  opacity: 1;
}

.progressive-image-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(102, 126, 234, 0.3);
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progressive-image-loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 错误状态 */
.progressive-image-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image.error .progressive-image-error {
  opacity: 1;
}

.progressive-image-error-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.progressive-image-error-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-bottom: 20rpx;
}

.progressive-image-retry {
  padding: 12rpx 24rpx;
  background-color: #667eea;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: background-color 0.2s ease;
}

.progressive-image-retry:active {
  background-color: #5a6fd8;
}

/* 质量指示器 */
.progressive-image-quality {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 4rpx 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  border-radius: 8rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image.loading .progressive-image-quality {
  opacity: 1;
}

/* 不同加载阶段的样式 */
.progressive-image.stage-placeholder .progressive-image-img {
  filter: blur(8px) brightness(1.1);
}

.progressive-image.stage-thumbnail .progressive-image-img {
  filter: blur(4px) brightness(1.05);
}

.progressive-image.stage-medium .progressive-image-img {
  filter: blur(1px);
}

.progressive-image.stage-complete .progressive-image-img {
  filter: none;
}

/* 懒加载状态 */
.progressive-image.lazy-loading {
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20rpx 20rpx;
  background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0rpx;
}

/* 圆形图片 */
.progressive-image.circle {
  border-radius: 50%;
}

.progressive-image.circle .progressive-image-img {
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .progressive-image-loading-text {
    font-size: 22rpx;
  }
  
  .progressive-image-error-text {
    font-size: 22rpx;
  }
  
  .progressive-image-retry {
    padding: 10rpx 20rpx;
    font-size: 22rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .progressive-image {
    background-color: #2a2a2a;
  }
  
  .progressive-image-placeholder {
    background: linear-gradient(90deg, #3a3a3a 25%, #4a4a4a 50%, #3a3a3a 75%);
    background-size: 200% 100%;
  }
  
  .progressive-image-loading,
  .progressive-image-error {
    color: #ccc;
  }
  
  .progressive-image-error-text {
    color: #ccc;
  }
}

/* 动画增强 */
.progressive-image-img {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progressive-image-progress-bar {
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 高性能优化 */
.progressive-image {
  contain: layout style paint;
  will-change: transform;
}

.progressive-image-img {
  contain: layout style paint;
  will-change: filter, opacity;
}

/* 加载成功动画 */
.progressive-image.loaded .progressive-image-img {
  animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 网络状态指示 */
.progressive-image-network {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image.loading .progressive-image-network {
  opacity: 1;
}

.progressive-image-network.wifi {
  background-color: #2ed573;
}

.progressive-image-network.cellular {
  background-color: #ffa502;
}

.progressive-image-network.slow {
  background-color: #ff4757;
}

/* 图片信息提示 */
.progressive-image-info {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  padding: 4rpx 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  border-radius: 8rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progressive-image:hover .progressive-image-info {
  opacity: 1;
}
