/* components/icon/icon.wxss */

/* 图标容器 */
.icon-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 尺寸变体 */
.icon-container.small {
  width: 48rpx;
  height: 48rpx;
}

.icon-container.medium {
  width: 64rpx;
  height: 64rpx;
}

.icon-container.large {
  width: 96rpx;
  height: 96rpx;
}

.icon-container.xlarge {
  width: 128rpx;
  height: 128rpx;
}

/* 类型变体 */
.icon-container.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-container.secondary {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
}

.icon-container.success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.icon-container.warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.icon-container.danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.icon-container.gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

.icon-container.transparent {
  background: transparent;
}

/* 渐变背景 */
.icon-background {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  opacity: 0.1;
}

.icon-background.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-background.secondary {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
}

.icon-background.success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
}

.icon-background.warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.icon-background.danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.icon-background.gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

/* 图标内容 */
.icon-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* SVG图标 */
.icon-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.icon-path {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 尺寸对应的图标大小 */
.small .icon-path {
  font-size: 20rpx;
}

.medium .icon-path {
  font-size: 24rpx;
}

.large .icon-path {
  font-size: 32rpx;
}

.xlarge .icon-path {
  font-size: 48rpx;
}

/* 文字图标 */
.text-icon {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

.small .text-icon {
  font-size: 20rpx;
}

.medium .text-icon {
  font-size: 24rpx;
}

.large .text-icon {
  font-size: 32rpx;
}

.xlarge .text-icon {
  font-size: 48rpx;
}

/* 图片图标 */
.image-icon {
  width: 60%;
  height: 60%;
}

/* 徽章 */
.icon-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: bold;
  border: 2rpx solid white;
  z-index: 2;
}

.badge-text {
  font-size: 18rpx;
  line-height: 1;
}

/* 悬停效果 */
.icon-container:active {
  transform: scale(0.95);
}

/* 特殊图标样式 */
.icon-container.vip {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  box-shadow: 0 8rpx 16rpx rgba(255, 215, 0, 0.3);
  animation: vipGlow 2s ease-in-out infinite alternate;
}

@keyframes vipGlow {
  0% {
    box-shadow: 0 8rpx 16rpx rgba(255, 215, 0, 0.3);
  }
  100% {
    box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.6), 0 0 32rpx rgba(255, 215, 0, 0.4);
  }
}

.icon-container.online {
  background: linear-gradient(135deg, #00d4aa 0%, #01a3a4 100%);
  animation: onlinePulse 2s ease-in-out infinite;
}

@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.icon-container.verified {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2rpx solid white;
}

/* 毛玻璃效果图标 */
.icon-container.glass {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
}

.glass .icon-path,
.glass .text-icon {
  color: #333;
}

/* 渐变边框效果 */
.icon-container.gradient-border {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rpx;
}

.gradient-border .icon-content {
  background: white;
  border-radius: 50%;
}

.gradient-border .icon-path,
.gradient-border .text-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
