// components/icon/icon.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图标名称
    name: {
      type: String,
      value: 'home'
    },
    
    // 图标类型：svg, text, image
    iconType: {
      type: String,
      value: 'svg'
    },
    
    // 图标尺寸：small, medium, large, xlarge
    size: {
      type: String,
      value: 'medium'
    },
    
    // 图标类型：primary, secondary, success, warning, danger, gold, transparent
    type: {
      type: String,
      value: 'primary'
    },
    
    // 渐变类型
    gradient: {
      type: String,
      value: 'primary'
    },
    
    // 是否显示背景
    showBackground: {
      type: Boolean,
      value: true
    },
    
    // 图片源（当iconType为image时使用）
    src: {
      type: String,
      value: ''
    },
    
    // 徽章文字
    badge: {
      type: String,
      value: ''
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击事件
    onIconTap() {
      this.triggerEvent('tap', {
        name: this.data.name,
        type: this.data.type
      })
    },
    
    // 长按事件
    onIconLongPress() {
      this.triggerEvent('longpress', {
        name: this.data.name,
        type: this.data.type
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  }
})
