<!-- components/icon/icon.wxml -->
<view class="icon-container {{size}} {{type}}" style="{{customStyle}}">
  <!-- 渐变背景 -->
  <view class="icon-background {{gradient}}" wx:if="{{showBackground}}"></view>
  
  <!-- 图标内容 -->
  <view class="icon-content">
    <!-- SVG图标 -->
    <view class="svg-icon" wx:if="{{iconType === 'svg'}}">
      <!-- 首页图标 -->
      <view wx:if="{{name === 'home'}}" class="icon-svg">
        <text class="icon-path">🏠</text>
      </view>
      
      <!-- 发现图标 -->
      <view wx:elif="{{name === 'discover'}}" class="icon-svg">
        <text class="icon-path">🔍</text>
      </view>
      
      <!-- 消息图标 -->
      <view wx:elif="{{name === 'message'}}" class="icon-svg">
        <text class="icon-path">💬</text>
      </view>
      
      <!-- 动态图标 -->
      <view wx:elif="{{name === 'moments'}}" class="icon-svg">
        <text class="icon-path">📸</text>
      </view>
      
      <!-- 个人中心图标 -->
      <view wx:elif="{{name === 'profile'}}" class="icon-svg">
        <text class="icon-path">👤</text>
      </view>
      
      <!-- 喜欢图标 -->
      <view wx:elif="{{name === 'heart'}}" class="icon-svg">
        <text class="icon-path">❤️</text>
      </view>
      
      <!-- 超级喜欢图标 -->
      <view wx:elif="{{name === 'star'}}" class="icon-svg">
        <text class="icon-path">⭐</text>
      </view>
      
      <!-- 跳过图标 -->
      <view wx:elif="{{name === 'close'}}" class="icon-svg">
        <text class="icon-path">✕</text>
      </view>
      
      <!-- 位置图标 -->
      <view wx:elif="{{name === 'location'}}" class="icon-svg">
        <text class="icon-path">📍</text>
      </view>
      
      <!-- 设置图标 -->
      <view wx:elif="{{name === 'settings'}}" class="icon-svg">
        <text class="icon-path">⚙️</text>
      </view>
      
      <!-- 通知图标 -->
      <view wx:elif="{{name === 'notification'}}" class="icon-svg">
        <text class="icon-path">🔔</text>
      </view>
      
      <!-- VIP图标 -->
      <view wx:elif="{{name === 'vip'}}" class="icon-svg">
        <text class="icon-path">👑</text>
      </view>
      
      <!-- 认证图标 -->
      <view wx:elif="{{name === 'verified'}}" class="icon-svg">
        <text class="icon-path">✓</text>
      </view>
      
      <!-- 在线图标 -->
      <view wx:elif="{{name === 'online'}}" class="icon-svg">
        <text class="icon-path">●</text>
      </view>
      
      <!-- 礼物图标 -->
      <view wx:elif="{{name === 'gift'}}" class="icon-svg">
        <text class="icon-path">🎁</text>
      </view>
      
      <!-- 活动图标 -->
      <view wx:elif="{{name === 'activity'}}" class="icon-svg">
        <text class="icon-path">🎉</text>
      </view>
      
      <!-- 家长图标 -->
      <view wx:elif="{{name === 'parent'}}" class="icon-svg">
        <text class="icon-path">👨‍👩‍👧‍👦</text>
      </view>
      
      <!-- 红娘图标 -->
      <view wx:elif="{{name === 'matchmaker'}}" class="icon-svg">
        <text class="icon-path">👔</text>
      </view>
      
      <!-- 安全图标 -->
      <view wx:elif="{{name === 'security'}}" class="icon-svg">
        <text class="icon-path">🛡️</text>
      </view>
      
      <!-- 帮助图标 -->
      <view wx:elif="{{name === 'help'}}" class="icon-svg">
        <text class="icon-path">❓</text>
      </view>
      
      <!-- 默认图标 -->
      <view wx:else class="icon-svg">
        <text class="icon-path">●</text>
      </view>
    </view>
    
    <!-- 文字图标 -->
    <text wx:elif="{{iconType === 'text'}}" class="text-icon">{{name}}</text>
    
    <!-- 图片图标 -->
    <image wx:elif="{{iconType === 'image'}}" src="{{src}}" class="image-icon" mode="aspectFit" />
  </view>
  
  <!-- 徽章 -->
  <view class="icon-badge" wx:if="{{badge}}">
    <text class="badge-text">{{badge}}</text>
  </view>
</view>
