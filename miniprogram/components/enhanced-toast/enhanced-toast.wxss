/* components/enhanced-toast/enhanced-toast.wxss - 增强版Toast组件样式 */
/* Task 2.1: Toast组件功能增强 - 组件样式 */

/* 遮罩层 */
.toast-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background-color: transparent;
}

.toast-mask.with-background {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Toast容器 */
.toast-container {
  position: fixed;
  left: 50%;
  max-width: 80%;
  min-width: 200rpx;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

/* Toast内容 */
.toast-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

/* 图标 */
.toast-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  line-height: 1;
}

.toast-icon.small {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  margin-right: 12rpx;
}

/* 消息文本 */
.toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  word-break: break-all;
  max-width: 100%;
}

/* 水平布局 */
.toast-content.horizontal {
  flex-direction: row;
  align-items: center;
}

.toast-content.horizontal .toast-icon {
  margin-bottom: 0;
  margin-right: 12rpx;
}

/* 不同类型的样式 */
.toast-container.default {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.toast-container.success {
  background-color: rgba(46, 213, 115, 0.95);
  color: #fff;
}

.toast-container.error {
  background-color: rgba(255, 71, 87, 0.95);
  color: #fff;
}

.toast-container.warning {
  background-color: rgba(255, 165, 2, 0.95);
  color: #fff;
}

.toast-container.info {
  background-color: rgba(102, 126, 234, 0.95);
  color: #fff;
}

.toast-container.loading {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  min-width: 240rpx;
  min-height: 120rpx;
}

/* 不同位置的样式 */
.toast-container.position-top {
  top: 20%;
  transform: translateX(-50%);
}

.toast-container.position-center {
  top: 50%;
  transform: translate(-50%, -50%);
}

.toast-container.position-bottom {
  bottom: 20%;
  transform: translateX(-50%);
}

/* 加载动画 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条 */
.toast-progress {
  width: 100%;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2rpx;
  margin-top: 16rpx;
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  background-color: #fff;
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

/* 关闭按钮 */
.toast-close {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #fff;
  cursor: pointer;
}

.toast-close:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 多行文本 */
.toast-message.multiline {
  text-align: left;
  max-height: 200rpx;
  overflow-y: auto;
}

/* 操作按钮 */
.toast-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
  width: 100%;
}

.toast-action-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  background-color: transparent;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toast-action-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.toast-action-btn.primary {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .toast-container {
    max-width: 85%;
    min-width: 180rpx;
    padding: 20rpx 28rpx;
  }
  
  .toast-message {
    font-size: 26rpx;
  }
  
  .toast-icon {
    font-size: 40rpx;
    margin-bottom: 12rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .toast-container.default {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
  }
  
  .toast-mask.with-background {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

/* 动画增强 */
.toast-container {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 震动效果 */
.toast-container.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  25% { transform: translateX(-50%) translateY(-5rpx); }
  75% { transform: translateX(-50%) translateY(5rpx); }
}

/* 弹跳效果 */
.toast-container.bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0% { transform: translateX(-50%) scale(0.3); }
  50% { transform: translateX(-50%) scale(1.05); }
  70% { transform: translateX(-50%) scale(0.9); }
  100% { transform: translateX(-50%) scale(1); }
}

/* 渐变背景 */
.toast-container.gradient {
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.9) 0%, 
    rgba(118, 75, 162, 0.9) 100%);
}

/* 毛玻璃效果增强 */
.toast-container.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 阴影效果 */
.toast-container.shadow-large {
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.25);
}

/* 圆角变化 */
.toast-container.rounded-large {
  border-radius: 24rpx;
}

.toast-container.rounded-small {
  border-radius: 8rpx;
}
