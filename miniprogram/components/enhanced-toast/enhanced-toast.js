/**
 * 增强版Toast组件
 * Task 2.1: Toast组件功能增强 - Toast组件
 */

const toastManager = require('../../utils/toast-manager')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    
    // 消息内容
    message: {
      type: String,
      value: ''
    },
    
    // Toast类型
    type: {
      type: String,
      value: 'default'
    },
    
    // 显示位置
    position: {
      type: String,
      value: 'center'
    },
    
    // 显示时长
    duration: {
      type: Number,
      value: 2000
    },
    
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: false
    },
    
    // 层级
    zIndex: {
      type: Number,
      value: 9999
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画状态
    animationData: {},
    
    // 显示状态
    showToast: false,
    
    // 样式配置
    toastStyle: '',
    
    // 图标
    icon: '',
    
    // 背景色
    backgroundColor: '',
    
    // 文字颜色
    color: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示Toast
     */
    show() {
      this.setData({ showToast: true })
      
      // 创建进入动画
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })
      
      // 根据位置设置不同的进入动画
      switch (this.data.position) {
        case 'top':
          animation.translateY(0).opacity(1).step()
          break
        case 'bottom':
          animation.translateY(0).opacity(1).step()
          break
        case 'center':
        default:
          animation.scale(1).opacity(1).step()
          break
      }
      
      this.setData({
        animationData: animation.export()
      })
      
      // 触发显示事件
      this.triggerEvent('show', {
        message: this.data.message,
        type: this.data.type
      })
    },

    /**
     * 隐藏Toast
     */
    hide() {
      // 创建退出动画
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-in'
      })
      
      // 根据位置设置不同的退出动画
      switch (this.data.position) {
        case 'top':
          animation.translateY(-100).opacity(0).step()
          break
        case 'bottom':
          animation.translateY(100).opacity(0).step()
          break
        case 'center':
        default:
          animation.scale(0.8).opacity(0).step()
          break
      }
      
      this.setData({
        animationData: animation.export()
      })
      
      // 延迟隐藏
      setTimeout(() => {
        this.setData({ showToast: false })
        
        // 触发隐藏事件
        this.triggerEvent('hide', {
          message: this.data.message,
          type: this.data.type
        })
      }, 300)
    },

    /**
     * 更新样式
     */
    updateStyle() {
      const typeConfig = toastManager.config.types[this.data.type] || toastManager.config.types.default
      const positionConfig = toastManager.config.positions[this.data.position] || toastManager.config.positions.center
      
      // 构建样式字符串
      let toastStyle = `z-index: ${this.data.zIndex}; `
      
      // 位置样式
      Object.entries(positionConfig).forEach(([key, value]) => {
        toastStyle += `${key}: ${value}; `
      })
      
      // 背景和颜色
      toastStyle += `background-color: ${typeConfig.backgroundColor}; `
      toastStyle += `color: ${typeConfig.color}; `
      
      this.setData({
        toastStyle: toastStyle,
        icon: typeConfig.icon,
        backgroundColor: typeConfig.backgroundColor,
        color: typeConfig.color
      })
    },

    /**
     * 点击遮罩
     */
    onMaskTap() {
      // 如果允许点击遮罩关闭
      if (this.data.type !== 'loading') {
        this.hide()
      }
    },

    /**
     * 点击Toast
     */
    onToastTap() {
      // 触发点击事件
      this.triggerEvent('tap', {
        message: this.data.message,
        type: this.data.type
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化样式
      this.updateStyle()
      
      // 设置初始动画状态
      const animation = wx.createAnimation({
        duration: 0
      })
      
      switch (this.data.position) {
        case 'top':
          animation.translateY(-100).opacity(0).step()
          break
        case 'bottom':
          animation.translateY(100).opacity(0).step()
          break
        case 'center':
        default:
          animation.scale(0.8).opacity(0).step()
          break
      }
      
      this.setData({
        animationData: animation.export()
      })
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.show()
      } else {
        this.hide()
      }
    },
    
    'type, position': function() {
      this.updateStyle()
    }
  }
})
