<!--
  增强版Toast组件模板
  Task 2.1: Toast组件功能增强 - 组件模板
-->

<view wx:if="{{showToast}}">
  <!-- 遮罩层 -->
  <view 
    class="toast-mask {{mask ? 'with-background' : ''}}"
    bindtap="onMaskTap"
  ></view>

  <!-- Toast容器 -->
  <view 
    class="toast-container {{type}} position-{{position}}"
    style="{{toastStyle}}"
    animation="{{animationData}}"
    bindtap="onToastTap"
    catchtap=""
  >
    <!-- Toast内容 -->
    <view class="toast-content {{icon && message ? 'horizontal' : ''}}">
      <!-- 加载动画 -->
      <view wx:if="{{type === 'loading'}}" class="loading-spinner"></view>
      
      <!-- 图标 -->
      <view 
        wx:elif="{{icon}}" 
        class="toast-icon {{message ? 'small' : ''}}"
      >
        {{icon}}
      </view>
      
      <!-- 消息文本 -->
      <view 
        wx:if="{{message}}" 
        class="toast-message {{message.length > 20 ? 'multiline' : ''}}"
      >
        {{message}}
      </view>
    </view>
    
    <!-- 进度条 (可选) -->
    <view wx:if="{{type === 'loading' && progress !== undefined}}" class="toast-progress">
      <view 
        class="toast-progress-bar"
        style="width: {{progress}}%"
      ></view>
    </view>
    
    <!-- 操作按钮 (可选) -->
    <view wx:if="{{actions && actions.length > 0}}" class="toast-actions">
      <view 
        wx:for="{{actions}}"
        wx:key="text"
        class="toast-action-btn {{item.primary ? 'primary' : ''}}"
        data-action="{{item}}"
        bindtap="onActionTap"
      >
        {{item.text}}
      </view>
    </view>
    
    <!-- 关闭按钮 (可选) -->
    <view wx:if="{{closable}}" class="toast-close" bindtap="hide">
      ×
    </view>
  </view>
</view>
