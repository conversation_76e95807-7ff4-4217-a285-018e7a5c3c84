/**
 * 标准化卡片组件
 * 基于设计系统的统一卡片组件，支持多种布局和样式
 */

const designUtils = require('../../../design-system/design-utils')

Component({
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: ''
    },
    
    // 卡片副标题
    subtitle: {
      type: String,
      value: ''
    },
    
    // 卡片描述
    description: {
      type: String,
      value: ''
    },
    
    // 卡片图片
    image: {
      type: String,
      value: ''
    },
    
    // 图片位置
    imagePosition: {
      type: String,
      value: 'top' // top, left, right, background
    },
    
    // 卡片变体
    variant: {
      type: String,
      value: 'default', // default, outlined, elevated, filled
      observer: 'updateCardStyle'
    },
    
    // 卡片尺寸
    size: {
      type: String,
      value: 'md', // sm, md, lg
      observer: 'updateCardStyle'
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false,
      observer: 'updateCardStyle'
    },
    
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true,
      observer: 'updateCardStyle'
    },
    
    // 圆角大小
    borderRadius: {
      type: String,
      value: 'md', // sm, md, lg, xl
      observer: 'updateCardStyle'
    },
    
    // 内边距
    padding: {
      type: String,
      value: 'md', // sm, md, lg
      observer: 'updateCardStyle'
    },
    
    // 背景颜色
    backgroundColor: {
      type: String,
      value: 'semantic.bgPrimary',
      observer: 'updateCardStyle'
    },
    
    // 边框颜色
    borderColor: {
      type: String,
      value: 'semantic.borderSecondary',
      observer: 'updateCardStyle'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    
    // 是否显示骨架屏
    skeleton: {
      type: Boolean,
      value: false
    }
  },

  data: {
    cardStyle: '',
    cardClass: '',
    imageStyle: '',
    contentStyle: ''
  },

  lifetimes: {
    attached() {
      this.updateCardStyle()
    }
  },

  methods: {
    /**
     * 更新卡片样式
     */
    updateCardStyle() {
      const {
        variant,
        size,
        clickable,
        shadow,
        borderRadius,
        padding,
        backgroundColor,
        borderColor,
        imagePosition
      } = this.data
      
      // 生成基础样式
      const baseStyle = designUtils.createCardStyle({
        padding: this.getPaddingValue(padding),
        borderRadius: this.getBorderRadiusValue(borderRadius),
        shadow: shadow ? designUtils.getBoxShadow('md') : 'none',
        background: backgroundColor
      })
      
      // 变体样式
      let variantStyle = {}
      switch (variant) {
        case 'outlined':
          variantStyle.border = `2rpx solid ${designUtils.getColor(borderColor)}`
          variantStyle.boxShadow = 'none'
          break
        case 'elevated':
          variantStyle.boxShadow = designUtils.getBoxShadow('lg')
          break
        case 'filled':
          variantStyle.background = designUtils.getColor('neutral.100')
          break
      }
      
      // 可点击样式
      if (clickable) {
        variantStyle.cursor = 'pointer'
        variantStyle.transition = 'all 0.3s ease'
      }
      
      // 图片样式
      const imageStyle = this.getImageStyle(imagePosition)
      
      // 内容样式
      const contentStyle = this.getContentStyle(imagePosition)
      
      // 合并样式
      const finalStyle = { ...baseStyle, ...variantStyle }
      
      // 转换为样式字符串
      const styleString = Object.entries(finalStyle)
        .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
        .join('; ')
      
      // 生成样式类
      const cardClass = [
        'standard-card',
        `card-${variant}`,
        `card-${size}`,
        `card-image-${imagePosition}`,
        clickable ? 'card-clickable' : '',
        shadow ? 'card-shadow' : '',
        this.data.customClass
      ].filter(Boolean).join(' ')
      
      this.setData({
        cardStyle: styleString + '; ' + this.data.customStyle,
        cardClass,
        imageStyle: this.objectToStyle(imageStyle),
        contentStyle: this.objectToStyle(contentStyle)
      })
    },

    /**
     * 获取内边距值
     */
    getPaddingValue(padding) {
      const paddingMap = {
        sm: 24,
        md: 32,
        lg: 40
      }
      return paddingMap[padding] || 32
    },

    /**
     * 获取圆角值
     */
    getBorderRadiusValue(borderRadius) {
      const radiusMap = {
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32
      }
      return radiusMap[borderRadius] || 16
    },

    /**
     * 获取图片样式
     */
    getImageStyle(position) {
      const baseStyle = {
        width: '100%',
        objectFit: 'cover',
        borderRadius: 'inherit'
      }
      
      switch (position) {
        case 'top':
          return {
            ...baseStyle,
            height: '400rpx',
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0
          }
        case 'left':
          return {
            ...baseStyle,
            width: '200rpx',
            height: '200rpx',
            flexShrink: 0,
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0
          }
        case 'right':
          return {
            ...baseStyle,
            width: '200rpx',
            height: '200rpx',
            flexShrink: 0,
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0
          }
        case 'background':
          return {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0
          }
        default:
          return baseStyle
      }
    },

    /**
     * 获取内容样式
     */
    getContentStyle(imagePosition) {
      const baseStyle = {
        position: 'relative',
        zIndex: 1
      }
      
      switch (imagePosition) {
        case 'left':
        case 'right':
          return {
            ...baseStyle,
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          }
        case 'background':
          return {
            ...baseStyle,
            color: '#ffffff',
            textShadow: '0 2rpx 4rpx rgba(0, 0, 0, 0.5)'
          }
        default:
          return baseStyle
      }
    },

    /**
     * 卡片点击事件
     */
    onCardTap(e) {
      if (!this.data.clickable || this.data.loading) {
        return
      }
      
      this.triggerEvent('tap', {
        title: this.data.title,
        timestamp: Date.now()
      })
    },

    /**
     * 卡片长按事件
     */
    onCardLongPress(e) {
      if (!this.data.clickable || this.data.loading) {
        return
      }
      
      this.triggerEvent('longpress', {
        title: this.data.title,
        timestamp: Date.now()
      })
    },

    /**
     * 图片加载完成
     */
    onImageLoad(e) {
      this.triggerEvent('imageload', e.detail)
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      this.triggerEvent('imageerror', e.detail)
    },

    /**
     * 对象转样式字符串
     */
    objectToStyle(obj) {
      return Object.entries(obj)
        .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
        .join('; ')
    },

    /**
     * 驼峰转短横线
     */
    camelToKebab(str) {
      return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
    }
  }
})
