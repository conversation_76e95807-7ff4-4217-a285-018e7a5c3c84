/**
 * 标准化按钮组件
 * 基于设计系统的统一按钮组件，支持多种变体、尺寸和状态
 */

const designUtils = require('../../../design-system/design-utils')

Component({
  properties: {
    // 按钮变体
    variant: {
      type: String,
      value: 'primary',
      observer: 'updateButtonStyle'
    },
    
    // 按钮尺寸
    size: {
      type: String,
      value: 'md',
      observer: 'updateButtonStyle'
    },
    
    // 按钮类型
    type: {
      type: String,
      value: 'button'
    },
    
    // 禁用状态
    disabled: {
      type: <PERSON>olean,
      value: false,
      observer: 'updateButtonStyle'
    },
    
    // 加载状态
    loading: {
      type: Bo<PERSON>an,
      value: false,
      observer: 'updateButtonStyle'
    },
    
    // 按钮文本
    text: {
      type: String,
      value: ''
    },
    
    // 图标
    icon: {
      type: String,
      value: ''
    },
    
    // 图标位置
    iconPosition: {
      type: String,
      value: 'left'
    },
    
    // 块级按钮
    block: {
      type: Boolean,
      value: false,
      observer: 'updateButtonStyle'
    },
    
    // 圆形按钮
    round: {
      type: Boolean,
      value: false,
      observer: 'updateButtonStyle'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    buttonStyle: '',
    buttonClass: '',
    rippleStyle: '',
    showRipple: false
  },

  lifetimes: {
    attached() {
      this.updateButtonStyle()
    }
  },

  methods: {
    /**
     * 更新按钮样式
     */
    updateButtonStyle() {
      const { variant, size, disabled, loading, block, round } = this.data
      
      // 生成基础样式
      const baseStyle = designUtils.createButtonStyle({
        variant,
        size,
        disabled,
        loading
      })
      
      // 添加额外样式
      let additionalStyle = {}
      
      if (block) {
        additionalStyle.width = '100%'
      }
      
      if (round) {
        additionalStyle.borderRadius = '9999rpx'
      }
      
      // 合并样式
      const finalStyle = { ...baseStyle, ...additionalStyle }
      
      // 转换为样式字符串
      const styleString = Object.entries(finalStyle)
        .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
        .join('; ')
      
      // 生成样式类
      const buttonClass = [
        'standard-button',
        `button-${variant}`,
        `button-${size}`,
        disabled ? 'button-disabled' : '',
        loading ? 'button-loading' : '',
        block ? 'button-block' : '',
        round ? 'button-round' : '',
        this.data.customClass
      ].filter(Boolean).join(' ')
      
      this.setData({
        buttonStyle: styleString + '; ' + this.data.customStyle,
        buttonClass
      })
    },

    /**
     * 按钮点击事件
     */
    onButtonTap(e) {
      if (this.data.disabled || this.data.loading) {
        return
      }
      
      // 创建波纹效果
      this.createRippleEffect(e)
      
      // 触发自定义事件
      this.triggerEvent('tap', {
        variant: this.data.variant,
        size: this.data.size,
        timestamp: Date.now()
      })
    },

    /**
     * 创建波纹效果
     */
    createRippleEffect(e) {
      const { clientX, clientY } = e.detail
      const query = this.createSelectorQuery()
      
      query.select('.standard-button').boundingClientRect((rect) => {
        if (!rect) return
        
        const x = clientX - rect.left
        const y = clientY - rect.top
        const size = Math.max(rect.width, rect.height) * 2
        
        const rippleStyle = `
          left: ${x - size / 2}px;
          top: ${y - size / 2}px;
          width: ${size}px;
          height: ${size}px;
        `
        
        this.setData({
          rippleStyle,
          showRipple: true
        })
        
        // 动画结束后隐藏波纹
        setTimeout(() => {
          this.setData({ showRipple: false })
        }, 600)
      }).exec()
    },

    /**
     * 长按事件
     */
    onButtonLongPress(e) {
      if (this.data.disabled || this.data.loading) {
        return
      }
      
      this.triggerEvent('longpress', {
        variant: this.data.variant,
        size: this.data.size,
        timestamp: Date.now()
      })
    },

    /**
     * 触摸开始
     */
    onTouchStart(e) {
      if (this.data.disabled || this.data.loading) {
        return
      }
      
      this.setData({
        buttonClass: this.data.buttonClass + ' button-active'
      })
    },

    /**
     * 触摸结束
     */
    onTouchEnd(e) {
      if (this.data.disabled || this.data.loading) {
        return
      }
      
      this.setData({
        buttonClass: this.data.buttonClass.replace(' button-active', '')
      })
    },

    /**
     * 驼峰转短横线
     */
    camelToKebab(str) {
      return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
    },

    /**
     * 获取按钮状态
     */
    getButtonState() {
      return {
        variant: this.data.variant,
        size: this.data.size,
        disabled: this.data.disabled,
        loading: this.data.loading,
        block: this.data.block,
        round: this.data.round
      }
    },

    /**
     * 设置加载状态
     */
    setLoading(loading) {
      this.setData({ loading })
    },

    /**
     * 设置禁用状态
     */
    setDisabled(disabled) {
      this.setData({ disabled })
    },

    /**
     * 模拟点击
     */
    simulateClick() {
      if (!this.data.disabled && !this.data.loading) {
        this.triggerEvent('tap', {
          variant: this.data.variant,
          size: this.data.size,
          timestamp: Date.now(),
          simulated: true
        })
      }
    }
  }
})
