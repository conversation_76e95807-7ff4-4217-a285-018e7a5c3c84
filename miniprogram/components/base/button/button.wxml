<!--components/base/button/button.wxml-->
<button 
  class="{{buttonClass}}"
  style="{{buttonStyle}}"
  disabled="{{disabled}}"
  loading="{{loading}}"
  form-type="{{type}}"
  bindtap="onButtonTap"
  bindlongpress="onButtonLongPress"
  bindtouchstart="onTouchStart"
  bindtouchend="onTouchEnd"
>
  <!-- 加载指示器 -->
  <view class="button-loading-indicator" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
  </view>
  
  <!-- 按钮内容 -->
  <view class="button-content" style="opacity: {{loading ? 0.6 : 1}}">
    <!-- 左侧图标 -->
    <view class="button-icon button-icon-left" wx:if="{{icon && iconPosition === 'left'}}">
      <text class="icon">{{icon}}</text>
    </view>
    
    <!-- 按钮文本 -->
    <view class="button-text" wx:if="{{text}}">
      <text>{{text}}</text>
    </view>
    
    <!-- 插槽内容 -->
    <slot wx:if="{{!text}}"></slot>
    
    <!-- 右侧图标 -->
    <view class="button-icon button-icon-right" wx:if="{{icon && iconPosition === 'right'}}">
      <text class="icon">{{icon}}</text>
    </view>
  </view>
  
  <!-- 波纹效果 -->
  <view class="button-ripple {{showRipple ? 'ripple-active' : ''}}" style="{{rippleStyle}}" wx:if="{{showRipple}}"></view>
</button>
