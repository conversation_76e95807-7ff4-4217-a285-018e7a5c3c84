<!--
  下拉刷新组件模板
  Task 2.3: 新增实用组件开发 - 下拉刷新模板
-->

<view 
  class="pull-refresh-container {{status}}"
  style="{{getContainerStyle()}}"
  bindtouchstart="onTouchStart"
  bindtouchmove="onTouchMove"
  bindtouchend="onTouchEnd"
>
  <!-- 刷新头部 -->
  <view class="pull-refresh-header" style="{{getHeaderStyle()}}">
    <view class="refresh-content">
      <!-- 刷新图标 -->
      <view class="refresh-icon {{getAnimationClass()}} {{status}}">
        {{getStatusIcon()}}
      </view>
      
      <!-- 刷新文本 -->
      <view class="refresh-text">{{getStatusText()}}</view>
    </view>
    
    <!-- 波纹效果 -->
    <view class="refresh-ripple"></view>
    
    <!-- 成功动画 -->
    <view wx:if="{{status === 'complete'}}" class="refresh-success">✓</view>
    
    <!-- 下拉提示 -->
    <view class="pull-hint">{{refreshText}}</view>
  </view>

  <!-- 内容区域 -->
  <view class="pull-refresh-content">
    <slot></slot>
  </view>
</view>

<wxs module="utils">
  var getStatusText = function(status, refreshText, releaseText, loadingText, completeText) {
    switch (status) {
      case 'pulling':
        return refreshText
      case 'release':
        return releaseText
      case 'loading':
        return loadingText
      case 'complete':
        return completeText
      default:
        return refreshText
    }
  }
  
  var getStatusIcon = function(status) {
    switch (status) {
      case 'pulling':
        return '↓'
      case 'release':
        return '↑'
      case 'loading':
        return '⟳'
      case 'complete':
        return '✓'
      default:
        return '↓'
    }
  }
  
  var getAnimationClass = function(animationType, status) {
    var className = 'animation-' + animationType
    
    if (status === 'loading') {
      className += ' loading'
    } else if (status === 'release') {
      className += ' release'
    }
    
    return className
  }
  
  var getContainerStyle = function(backgroundColor, textColor, pullDistance, animating) {
    var style = 'background-color: ' + backgroundColor + '; '
    style += 'color: ' + textColor + '; '
    
    if (animating) {
      style += 'transition: transform 0.3s ease; '
    }
    
    style += 'transform: translateY(' + pullDistance + 'px); '
    
    return style
  }
  
  var getHeaderStyle = function(headerHeight, backgroundColor, textColor) {
    var style = 'height: ' + headerHeight + 'px; '
    style += 'margin-top: -' + headerHeight + 'px; '
    style += 'background-color: ' + backgroundColor + '; '
    style += 'color: ' + textColor + '; '
    
    return style
  }
  
  module.exports = {
    getStatusText: getStatusText,
    getStatusIcon: getStatusIcon,
    getAnimationClass: getAnimationClass,
    getContainerStyle: getContainerStyle,
    getHeaderStyle: getHeaderStyle
  }
</wxs>
