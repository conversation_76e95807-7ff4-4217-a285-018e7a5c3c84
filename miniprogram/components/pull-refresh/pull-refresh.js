/**
 * 下拉刷新组件
 * Task 2.3: 新增实用组件开发 - 下拉刷新组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否启用下拉刷新
    enabled: {
      type: Boolean,
      value: true
    },
    
    // 触发刷新的距离
    threshold: {
      type: Number,
      value: 80
    },
    
    // 最大下拉距离
    maxDistance: {
      type: Number,
      value: 200
    },
    
    // 刷新提示文本
    refreshText: {
      type: String,
      value: '下拉刷新'
    },
    
    // 释放提示文本
    releaseText: {
      type: String,
      value: '释放刷新'
    },
    
    // 加载提示文本
    loadingText: {
      type: String,
      value: '正在刷新...'
    },
    
    // 完成提示文本
    completeText: {
      type: String,
      value: '刷新完成'
    },
    
    // 刷新动画类型
    animationType: {
      type: String,
      value: 'spinner' // spinner, dots, arrow
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: '#f8f9fa'
    },
    
    // 文字颜色
    textColor: {
      type: String,
      value: '#666'
    },
    
    // 主题色
    themeColor: {
      type: String,
      value: '#667eea'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 刷新状态
    status: 'normal', // normal, pulling, release, loading, complete
    
    // 下拉距离
    pullDistance: 0,
    
    // 触摸状态
    touching: false,
    touchStartY: 0,
    touchMoveY: 0,
    
    // 动画状态
    animating: false,
    
    // 容器高度
    containerHeight: 0,
    
    // 刷新头部高度
    headerHeight: 80
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 触摸开始
     * @param {Event} e 触摸事件
     */
    onTouchStart(e) {
      if (!this.data.enabled || this.data.status === 'loading') {
        return
      }
      
      const touch = e.touches[0]
      this.setData({
        touching: true,
        touchStartY: touch.clientY,
        touchMoveY: touch.clientY,
        animating: false
      })
    },

    /**
     * 触摸移动
     * @param {Event} e 触摸事件
     */
    onTouchMove(e) {
      if (!this.data.touching || !this.data.enabled) {
        return
      }
      
      const touch = e.touches[0]
      const moveY = touch.clientY - this.data.touchStartY
      
      // 只处理向下拉的情况
      if (moveY <= 0) {
        return
      }
      
      // 检查是否在顶部
      if (!this.isAtTop()) {
        return
      }
      
      // 计算下拉距离（添加阻尼效果）
      let pullDistance = this.calculatePullDistance(moveY)
      
      // 限制最大距离
      pullDistance = Math.min(pullDistance, this.data.maxDistance)
      
      // 更新状态
      let status = 'pulling'
      if (pullDistance >= this.data.threshold) {
        status = 'release'
      }
      
      this.setData({
        touchMoveY: touch.clientY,
        pullDistance: pullDistance,
        status: status
      })
      
      // 阻止默认滚动
      e.preventDefault && e.preventDefault()
    },

    /**
     * 触摸结束
     * @param {Event} e 触摸事件
     */
    onTouchEnd(e) {
      if (!this.data.touching || !this.data.enabled) {
        return
      }
      
      this.setData({
        touching: false,
        animating: true
      })
      
      // 判断是否触发刷新
      if (this.data.status === 'release') {
        this.startRefresh()
      } else {
        this.resetPull()
      }
    },

    /**
     * 开始刷新
     */
    startRefresh() {
      this.setData({
        status: 'loading',
        pullDistance: this.data.headerHeight
      })
      
      // 触发刷新事件
      this.triggerEvent('refresh', {
        complete: this.completeRefresh.bind(this)
      })
    },

    /**
     * 完成刷新
     * @param {boolean} success 是否成功
     */
    completeRefresh(success = true) {
      this.setData({
        status: 'complete'
      })
      
      // 显示完成状态
      setTimeout(() => {
        this.resetPull()
      }, 500)
    },

    /**
     * 重置下拉状态
     */
    resetPull() {
      this.setData({
        status: 'normal',
        pullDistance: 0,
        animating: true
      })
      
      // 重置动画状态
      setTimeout(() => {
        this.setData({ animating: false })
      }, 300)
    },

    /**
     * 计算下拉距离（添加阻尼效果）
     * @param {number} moveY 移动距离
     */
    calculatePullDistance(moveY) {
      // 阻尼系数
      const damping = 0.5
      
      if (moveY <= this.data.threshold) {
        return moveY * damping
      } else {
        // 超过阈值后增加更大的阻尼
        const extraDistance = moveY - this.data.threshold
        return this.data.threshold * damping + extraDistance * damping * 0.3
      }
    },

    /**
     * 检查是否在顶部
     */
    isAtTop() {
      // 这里应该检查内容是否滚动到顶部
      // 由于小程序限制，这里简化处理
      return true
    },

    /**
     * 获取状态文本
     */
    getStatusText() {
      switch (this.data.status) {
        case 'pulling':
          return this.data.refreshText
        case 'release':
          return this.data.releaseText
        case 'loading':
          return this.data.loadingText
        case 'complete':
          return this.data.completeText
        default:
          return this.data.refreshText
      }
    },

    /**
     * 获取状态图标
     */
    getStatusIcon() {
      switch (this.data.status) {
        case 'pulling':
          return '↓'
        case 'release':
          return '↑'
        case 'loading':
          return '⟳'
        case 'complete':
          return '✓'
        default:
          return '↓'
      }
    },

    /**
     * 获取动画类名
     */
    getAnimationClass() {
      let className = `animation-${this.data.animationType}`
      
      if (this.data.status === 'loading') {
        className += ' loading'
      }
      
      return className
    },

    /**
     * 获取容器样式
     */
    getContainerStyle() {
      let style = `background-color: ${this.data.backgroundColor}; `
      style += `color: ${this.data.textColor}; `
      
      if (this.data.animating) {
        style += 'transition: transform 0.3s ease; '
      }
      
      style += `transform: translateY(${this.data.pullDistance}px); `
      
      return style
    },

    /**
     * 获取刷新头部样式
     */
    getHeaderStyle() {
      let style = `height: ${this.data.headerHeight}px; `
      style += `margin-top: -${this.data.headerHeight}px; `
      style += `background-color: ${this.data.backgroundColor}; `
      style += `color: ${this.data.textColor}; `
      
      return style
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 获取容器高度
      const query = this.createSelectorQuery()
      query.select('.pull-refresh-container').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            containerHeight: rect.height
          })
        }
      }).exec()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'threshold, maxDistance': function() {
      // 重置状态
      if (this.data.status !== 'normal') {
        this.resetPull()
      }
    }
  }
})
