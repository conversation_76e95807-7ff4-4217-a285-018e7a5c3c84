/* components/pull-refresh/pull-refresh.wxss - 下拉刷新组件样式 */
/* Task 2.3: 新增实用组件开发 - 下拉刷新样式 */

/* 下拉刷新容器 */
.pull-refresh-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* 刷新头部 */
.pull-refresh-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 刷新内容 */
.refresh-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 20rpx;
}

/* 刷新图标 */
.refresh-icon {
  font-size: 32rpx;
  transition: transform 0.3s ease;
}

/* 不同动画类型的图标 */
.refresh-icon.animation-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(102, 126, 234, 0.3);
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  font-size: 0;
}

.refresh-icon.animation-spinner.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.refresh-icon.animation-dots {
  display: flex;
  gap: 6rpx;
  font-size: 0;
}

.refresh-icon.animation-dots::before,
.refresh-icon.animation-dots::after,
.refresh-icon.animation-dots {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #667eea;
}

.refresh-icon.animation-dots.loading::before,
.refresh-icon.animation-dots.loading::after,
.refresh-icon.animation-dots.loading {
  animation: dotPulse 1.4s ease-in-out infinite;
}

.refresh-icon.animation-dots.loading::before {
  animation-delay: -0.32s;
}

.refresh-icon.animation-dots.loading::after {
  animation-delay: -0.16s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.refresh-icon.animation-arrow {
  transition: transform 0.3s ease;
}

.refresh-icon.animation-arrow.release {
  transform: rotate(180deg);
}

.refresh-icon.animation-arrow.loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 刷新文本 */
.refresh-text {
  font-size: 28rpx;
  color: inherit;
  transition: color 0.3s ease;
}

/* 内容区域 */
.pull-refresh-content {
  position: relative;
  z-index: 1;
  background-color: #fff;
}

/* 状态样式 */
.pull-refresh-container.pulling .refresh-icon {
  color: #667eea;
}

.pull-refresh-container.release .refresh-icon {
  color: #2ed573;
}

.pull-refresh-container.loading .refresh-icon {
  color: #667eea;
}

.pull-refresh-container.complete .refresh-icon {
  color: #2ed573;
}

/* 进度指示器 */
.refresh-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(102, 126, 234, 0.2);
  border-top: 4rpx solid #667eea;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pull-refresh-container.loading .refresh-progress {
  opacity: 1;
  animation: spin 1s linear infinite;
}

/* 波纹效果 */
.refresh-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: rgba(102, 126, 234, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
}

.pull-refresh-container.release .refresh-ripple {
  width: 120rpx;
  height: 120rpx;
  opacity: 1;
}

/* 成功动画 */
.refresh-success {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40rpx;
  color: #2ed573;
  opacity: 0;
  transition: all 0.3s ease;
}

.pull-refresh-container.complete .refresh-success {
  opacity: 1;
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 下拉提示 */
.pull-hint {
  position: absolute;
  top: -60rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 20;
}

.pull-refresh-container.pulling .pull-hint {
  opacity: 1;
  top: -40rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .refresh-content {
    padding: 16rpx;
    gap: 12rpx;
  }
  
  .refresh-icon {
    font-size: 28rpx;
  }
  
  .refresh-text {
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .pull-refresh-content {
    background-color: #2a2a2a;
  }
  
  .refresh-text {
    color: #ccc;
  }
  
  .pull-hint {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* 自定义主题 */
.pull-refresh-container[data-theme="primary"] .refresh-icon {
  color: #667eea;
}

.pull-refresh-container[data-theme="success"] .refresh-icon {
  color: #2ed573;
}

.pull-refresh-container[data-theme="warning"] .refresh-icon {
  color: #ffa502;
}

.pull-refresh-container[data-theme="danger"] .refresh-icon {
  color: #ff4757;
}

/* 动画增强 */
.refresh-content {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.refresh-icon {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 弹性效果 */
.pull-refresh-container.release .refresh-content {
  animation: elasticPull 0.3s ease-out;
}

@keyframes elasticPull {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
