/**
 * 通知列表组件
 * Task 1.2: 消息推送核心系统 - UI组件
 * 用于显示和管理通知列表
 */

const notificationManager = require('../../utils/notification')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 显示类型：all(全部), unread(未读), type(按类型)
    displayType: {
      type: String,
      value: 'all'
    },
    // 通知类型过滤
    notificationType: {
      type: String,
      value: ''
    },
    // 显示数量限制
    limit: {
      type: Number,
      value: 20
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 是否显示时间
    showTime: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    notifications: [],
    unreadCount: 0,
    loading: false,
    isEmpty: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      this.loadNotifications()
      this.setupEventListeners()
    },

    /**
     * 设置事件监听
     */
    setupEventListeners() {
      // 监听通知更新
      notificationManager.on('received', () => {
        this.loadNotifications()
      })

      notificationManager.on('read', () => {
        this.loadNotifications()
      })

      notificationManager.on('deleted', () => {
        this.loadNotifications()
      })

      notificationManager.on('cleared', () => {
        this.loadNotifications()
      })
    },

    /**
     * 加载通知列表
     */
    loadNotifications() {
      this.setData({ loading: true })

      try {
        const options = {
          limit: this.data.limit
        }

        // 根据显示类型设置过滤条件
        if (this.data.displayType === 'unread') {
          options.unreadOnly = true
        } else if (this.data.displayType === 'type' && this.data.notificationType) {
          options.type = this.data.notificationType
        }

        const notifications = notificationManager.getNotifications(options)
        const unreadCount = notificationManager.getUnreadCount()

        // 处理通知数据，添加显示所需的字段
        const processedNotifications = notifications.map(notification => {
          return {
            ...notification,
            displayTime: this.formatTime(notification.timestamp),
            displayContent: this.formatContent(notification),
            iconType: this.getIconType(notification.type),
            isUnread: !notification.read
          }
        })

        this.setData({
          notifications: processedNotifications,
          unreadCount: unreadCount,
          isEmpty: processedNotifications.length === 0,
          loading: false
        })

        // 触发加载完成事件
        this.triggerEvent('loaded', {
          count: processedNotifications.length,
          unreadCount: unreadCount
        })

      } catch (error) {
        console.error('加载通知列表失败:', error)
        this.setData({ loading: false })
        
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    /**
     * 格式化时间显示
     * @param {number} timestamp 时间戳
     */
    formatTime(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      
      // 1分钟内
      if (diff < 60 * 1000) {
        return '刚刚'
      }
      
      // 1小时内
      if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`
      }
      
      // 24小时内
      if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
      }
      
      // 7天内
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
      }
      
      // 超过7天显示具体日期
      const date = new Date(timestamp)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    },

    /**
     * 格式化通知内容
     * @param {Object} notification 通知对象
     */
    formatContent(notification) {
      const { type, data } = notification
      
      switch (type) {
        case 'system':
          return data.message || '系统通知'
        case 'match':
          return `与 ${data.matchedUser?.nickname || '用户'} 匹配成功`
        case 'message':
          return `${data.senderName || '用户'}: ${data.messagePreview || '发来一条消息'}`
        case 'activity':
          return data.title || '新活动通知'
        default:
          return data.message || data.title || '新通知'
      }
    },

    /**
     * 获取图标类型
     * @param {string} type 通知类型
     */
    getIconType(type) {
      const iconMap = {
        system: 'bell',
        match: 'heart',
        message: 'chat',
        activity: 'gift'
      }
      return iconMap[type] || 'bell'
    },

    /**
     * 点击通知项
     * @param {Object} e 事件对象
     */
    onNotificationTap(e) {
      const notification = e.currentTarget.dataset.notification
      
      // 标记为已读
      if (!notification.read) {
        notificationManager.markAsRead(notification.id)
      }
      
      // 触发点击事件
      this.triggerEvent('tap', { notification })
      
      // 根据通知类型进行跳转
      this.handleNotificationNavigation(notification)
    },

    /**
     * 处理通知跳转
     * @param {Object} notification 通知对象
     */
    handleNotificationNavigation(notification) {
      const { type, data } = notification
      
      try {
        switch (type) {
          case 'match':
            if (data.matchId) {
              wx.navigateTo({
                url: `/pages/match/detail?matchId=${data.matchId}`
              })
            }
            break
          case 'message':
            if (data.chatId) {
              wx.navigateTo({
                url: `/pages/chat/chat?chatId=${data.chatId}`
              })
            }
            break
          case 'activity':
            if (data.activityId) {
              wx.navigateTo({
                url: `/pages/activity/detail?activityId=${data.activityId}`
              })
            }
            break
          case 'system':
            // 系统通知可能不需要跳转，或跳转到设置页面
            break
        }
      } catch (error) {
        console.error('通知跳转失败:', error)
      }
    },

    /**
     * 标记为已读
     * @param {Object} e 事件对象
     */
    onMarkAsRead(e) {
      e.stopPropagation() // 阻止冒泡
      
      const notification = e.currentTarget.dataset.notification
      notificationManager.markAsRead(notification.id)
      
      wx.showToast({
        title: '已标记为已读',
        icon: 'success',
        duration: 1000
      })
    },

    /**
     * 删除通知
     * @param {Object} e 事件对象
     */
    onDelete(e) {
      e.stopPropagation() // 阻止冒泡
      
      const notification = e.currentTarget.dataset.notification
      
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条通知吗？',
        success: (res) => {
          if (res.confirm) {
            notificationManager.deleteNotification(notification.id)
            
            wx.showToast({
              title: '已删除',
              icon: 'success',
              duration: 1000
            })
          }
        }
      })
    },

    /**
     * 标记所有为已读
     */
    onMarkAllAsRead() {
      const unreadNotifications = this.data.notifications.filter(n => !n.read)
      
      if (unreadNotifications.length === 0) {
        wx.showToast({
          title: '没有未读通知',
          icon: 'none'
        })
        return
      }
      
      unreadNotifications.forEach(notification => {
        notificationManager.markAsRead(notification.id)
      })
      
      wx.showToast({
        title: `已标记${unreadNotifications.length}条为已读`,
        icon: 'success'
      })
    },

    /**
     * 清空所有通知
     */
    onClearAll() {
      if (this.data.notifications.length === 0) {
        wx.showToast({
          title: '没有通知可清空',
          icon: 'none'
        })
        return
      }
      
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有通知吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            notificationManager.clearAllNotifications()
            
            wx.showToast({
              title: '已清空所有通知',
              icon: 'success'
            })
          }
        }
      })
    },

    /**
     * 下拉刷新
     */
    onRefresh() {
      this.loadNotifications()
      
      // 触发刷新事件
      this.triggerEvent('refresh')
    },

    /**
     * 切换显示类型
     * @param {string} type 显示类型
     */
    switchDisplayType(type) {
      this.setData({ displayType: type })
      this.loadNotifications()
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    },

    detached() {
      // 清理事件监听
      // 注意：实际项目中可能需要更精确的事件清理
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时刷新数据
      this.loadNotifications()
    }
  }
})
