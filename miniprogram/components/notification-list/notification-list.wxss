/**
 * 通知列表组件样式
 * Task 1.2: 消息推送核心系统 - UI样式
 */

.notification-list {
  width: 100%;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #999;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  text-align: center;
  line-height: 1.5;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.action-left {
  display: flex;
  align-items: center;
}

.unread-count {
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  color: #007aff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  background-color: #f0f8ff;
}

.action-btn:active {
  background-color: #e6f3ff;
}

/* 通知项 */
.notification-item {
  background-color: #fff;
  margin-bottom: 2rpx;
  position: relative;
  overflow: hidden;
}

.notification-item.unread {
  border-left: 6rpx solid #007aff;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ff4757;
  border-radius: 50%;
  z-index: 2;
}

.notification-content {
  display: flex;
  padding: 30rpx;
  align-items: flex-start;
}

.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  font-size: 36rpx;
  color: #fff;
}

.notification-icon.system {
  background-color: #007aff;
}

.notification-icon.match {
  background-color: #ff4757;
}

.notification-icon.message {
  background-color: #2ed573;
}

.notification-icon.activity {
  background-color: #ffa502;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 32rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.notification-item.unread .notification-title {
  font-weight: 600;
}

.notification-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-time {
  font-size: 24rpx;
  color: #999;
}

.notification-actions {
  display: flex;
  gap: 20rpx;
}

.action-icon {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx;
  border-radius: 4rpx;
}

.action-icon:active {
  background-color: #f5f5f5;
}

.action-icon.read {
  color: #007aff;
}

.action-icon.delete {
  color: #ff4757;
}

/* 滑动操作 */
.notification-item .movable-area {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.notification-item .movable-view {
  height: 100%;
  width: 100%;
}

.slide-actions {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #ff4757;
}

.slide-btn {
  height: 100%;
  padding: 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
}

.slide-btn.read {
  background-color: #007aff;
}

.slide-btn.delete {
  background-color: #ff4757;
}

/* 类型标签 */
.notification-type {
  display: inline-block;
  font-size: 20rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.notification-type.system {
  background-color: #e6f3ff;
  color: #007aff;
}

.notification-type.match {
  background-color: #ffe6e6;
  color: #ff4757;
}

.notification-type.message {
  background-color: #e6ffe6;
  color: #2ed573;
}

.notification-type.activity {
  background-color: #fff2e6;
  color: #ffa502;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .notification-content {
    padding: 24rpx;
  }
  
  .notification-icon {
    width: 70rpx;
    height: 70rpx;
    font-size: 32rpx;
    margin-right: 20rpx;
  }
  
  .notification-title {
    font-size: 30rpx;
  }
  
  .notification-desc {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.notification-item {
  transition: all 0.3s ease;
}

.notification-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}

.notification-icon {
  transition: transform 0.2s ease;
}

.notification-item:active .notification-icon {
  transform: scale(1.1);
}

/* 加载动画 */
@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-icon {
  animation: loading-spin 1s linear infinite;
}

/* 滑入动画 */
@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-item {
  animation: slide-in 0.3s ease-out;
}

/* 删除动画 */
@keyframes slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
    height: auto;
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
    height: 0;
    padding: 0;
    margin: 0;
  }
}

.notification-item.deleting {
  animation: slide-out 0.3s ease-in forwards;
}

/* 夜间模式支持 */
@media (prefers-color-scheme: dark) {
  .notification-list {
    background-color: #1a1a1a;
  }
  
  .notification-item {
    background-color: #2a2a2a;
    border-bottom-color: #333;
  }
  
  .notification-title {
    color: #fff;
  }
  
  .notification-desc {
    color: #ccc;
  }
  
  .notification-time {
    color: #999;
  }
  
  .action-bar {
    background-color: #2a2a2a;
    border-bottom-color: #333;
  }
  
  .action-btn {
    background-color: #333;
    color: #007aff;
  }
  
  .empty-container {
    color: #999;
  }
}
