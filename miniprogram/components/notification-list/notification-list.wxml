<!--
  通知列表组件模板
  Task 1.2: 消息推送核心系统 - UI模板
-->

<view class="notification-list">
  <!-- 操作栏 -->
  <view class="action-bar" wx:if="{{showActions && !loading}}">
    <view class="action-left">
      <text>通知</text>
      <view class="unread-count" wx:if="{{unreadCount > 0}}">{{unreadCount}}</view>
    </view>
    <view class="action-buttons">
      <view class="action-btn" bindtap="onMarkAllAsRead" wx:if="{{unreadCount > 0}}">
        全部已读
      </view>
      <view class="action-btn" bindtap="onClearAll" wx:if="{{notifications.length > 0}}">
        清空
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <view class="empty-icon">🔔</view>
    <view class="empty-text">
      <view>暂无通知</view>
      <view wx:if="{{displayType === 'unread'}}">所有通知都已读完</view>
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notifications-container" wx:else>
    <view 
      class="notification-item {{item.isUnread ? 'unread' : ''}}"
      wx:for="{{notifications}}"
      wx:key="id"
      data-notification="{{item}}"
      bindtap="onNotificationTap"
    >
      <view class="notification-content">
        <!-- 通知图标 -->
        <view class="notification-icon {{item.type}}">
          <text wx:if="{{item.iconType === 'bell'}}">🔔</text>
          <text wx:elif="{{item.iconType === 'heart'}}">💕</text>
          <text wx:elif="{{item.iconType === 'chat'}}">💬</text>
          <text wx:elif="{{item.iconType === 'gift'}}">🎁</text>
          <text wx:else>📢</text>
        </view>

        <!-- 通知内容 -->
        <view class="notification-body">
          <!-- 类型标签 -->
          <view class="notification-type {{item.type}}" wx:if="{{displayType === 'all'}}">
            <text wx:if="{{item.type === 'system'}}">系统</text>
            <text wx:elif="{{item.type === 'match'}}">匹配</text>
            <text wx:elif="{{item.type === 'message'}}">消息</text>
            <text wx:elif="{{item.type === 'activity'}}">活动</text>
            <text wx:else>通知</text>
          </view>

          <!-- 通知标题/内容 -->
          <view class="notification-title">{{item.displayContent}}</view>

          <!-- 通知详情 -->
          <view class="notification-desc" wx:if="{{item.data.description}}">
            {{item.data.description}}
          </view>

          <!-- 元信息 -->
          <view class="notification-meta">
            <view class="notification-time" wx:if="{{showTime}}">
              {{item.displayTime}}
            </view>
            
            <!-- 操作按钮 -->
            <view class="notification-actions" wx:if="{{showActions}}">
              <view 
                class="action-icon read"
                wx:if="{{item.isUnread}}"
                data-notification="{{item}}"
                bindtap="onMarkAsRead"
                catchtap="onMarkAsRead"
              >
                ✓
              </view>
              <view 
                class="action-icon delete"
                data-notification="{{item}}"
                bindtap="onDelete"
                catchtap="onDelete"
              >
                🗑️
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 滑动操作区域 -->
      <view class="slide-actions" wx:if="{{false}}">
        <view 
          class="slide-btn read"
          wx:if="{{item.isUnread}}"
          data-notification="{{item}}"
          bindtap="onMarkAsRead"
        >
          已读
        </view>
        <view 
          class="slide-btn delete"
          data-notification="{{item}}"
          bindtap="onDelete"
        >
          删除
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{notifications.length >= limit && !loading}}">
    <text class="load-more-text">已显示 {{notifications.length}} 条通知</text>
  </view>
</view>
