/**
 * 举报表单组件
 * Task 1.5: 举报功能模块 - 举报表单
 * 功能：举报入口、原因选择、证据上传
 */

const reportManager = require('../../utils/report')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示表单
    show: {
      type: Boolean,
      value: false
    },
    // 举报目标信息
    targetInfo: {
      type: Object,
      value: null
    },
    // 举报目标类型：user, content, message
    targetType: {
      type: String,
      value: 'user'
    },
    // 举报目标ID
    targetId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      type: '',
      reason: '',
      description: '',
      evidence: []
    },
    
    // 举报类型列表
    reportTypes: [],
    
    // UI状态
    submitting: false,
    showTypePicker: false,
    
    // 证据上传
    uploadedImages: [],
    maxImages: 10,
    
    // 验证状态
    errors: {},
    
    // 当前步骤
    currentStep: 1,
    totalSteps: 3
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      // 加载举报类型列表
      const reportTypes = reportManager.getReportTypes()
      this.setData({ reportTypes })
      
      // 重置表单
      this.resetForm()
    },

    /**
     * 显示类型选择器
     */
    showTypePicker() {
      this.setData({ showTypePicker: true })
    },

    /**
     * 隐藏类型选择器
     */
    hideTypePicker() {
      this.setData({ showTypePicker: false })
    },

    /**
     * 选择举报类型
     * @param {Object} e 事件对象
     */
    onTypeSelect(e) {
      const typeId = e.currentTarget.dataset.type
      
      this.setData({
        'formData.type': typeId,
        showTypePicker: false
      })
      
      // 清除类型相关错误
      this.clearError('type')
      
      // 检查是否需要证据
      const reportType = this.data.reportTypes.find(t => t.id === typeId)
      if (reportType && reportType.evidenceRequired) {
        wx.showToast({
          title: '此类举报需要提供证据',
          icon: 'none',
          duration: 2000
        })
      }
    },

    /**
     * 输入举报原因
     * @param {Object} e 事件对象
     */
    onReasonInput(e) {
      this.setData({
        'formData.reason': e.detail.value
      })
    },

    /**
     * 输入详细描述
     * @param {Object} e 事件对象
     */
    onDescriptionInput(e) {
      this.setData({
        'formData.description': e.detail.value
      })
    },

    /**
     * 选择证据图片
     */
    chooseEvidence() {
      const remainingCount = this.data.maxImages - this.data.uploadedImages.length
      
      if (remainingCount <= 0) {
        wx.showToast({
          title: `最多只能上传${this.data.maxImages}张证据`,
          icon: 'none'
        })
        return
      }
      
      wx.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadEvidence(res.tempFilePaths)
        },
        fail: (error) => {
          console.error('选择证据图片失败:', error)
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 上传证据图片
     * @param {Array} filePaths 图片路径数组
     */
    async uploadEvidence(filePaths) {
      wx.showLoading({ title: '上传中...' })
      
      try {
        const uploadPromises = filePaths.map(filePath => this.uploadSingleEvidence(filePath))
        const uploadedUrls = await Promise.all(uploadPromises)
        
        // 更新上传的图片列表
        const newImages = [...this.data.uploadedImages, ...uploadedUrls]
        this.setData({ 
          uploadedImages: newImages,
          'formData.evidence': newImages
        })
        
        wx.hideLoading()
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } catch (error) {
        wx.hideLoading()
        console.error('上传证据失败:', error)
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    },

    /**
     * 上传单张证据图片
     * @param {string} filePath 图片路径
     */
    uploadSingleEvidence(filePath) {
      return new Promise((resolve, reject) => {
        // 模拟上传过程
        setTimeout(() => {
          // 实际项目中这里应该调用真实的上传API
          resolve(filePath)
        }, 1000)
      })
    },

    /**
     * 删除证据图片
     * @param {Object} e 事件对象
     */
    deleteEvidence(e) {
      const index = e.currentTarget.dataset.index
      const images = [...this.data.uploadedImages]
      
      images.splice(index, 1)
      
      this.setData({
        uploadedImages: images,
        'formData.evidence': images
      })
    },

    /**
     * 预览证据图片
     * @param {Object} e 事件对象
     */
    previewEvidence(e) {
      const index = e.currentTarget.dataset.index
      const current = this.data.uploadedImages[index]
      
      wx.previewImage({
        current: current,
        urls: this.data.uploadedImages
      })
    },

    /**
     * 下一步
     */
    nextStep() {
      if (this.data.currentStep === 1) {
        // 验证第一步：选择类型
        if (!this.validateStep1()) {
          return
        }
        this.setData({ currentStep: 2 })
      } else if (this.data.currentStep === 2) {
        // 验证第二步：填写原因
        if (!this.validateStep2()) {
          return
        }
        this.setData({ currentStep: 3 })
      }
    },

    /**
     * 上一步
     */
    prevStep() {
      if (this.data.currentStep > 1) {
        this.setData({ 
          currentStep: this.data.currentStep - 1 
        })
      }
    },

    /**
     * 验证第一步
     */
    validateStep1() {
      if (!this.data.formData.type) {
        this.setError('type', '请选择举报类型')
        return false
      }
      
      this.clearError('type')
      return true
    },

    /**
     * 验证第二步
     */
    validateStep2() {
      if (!this.data.formData.reason.trim()) {
        this.setError('reason', '请简要说明举报原因')
        return false
      }
      
      this.clearError('reason')
      return true
    },

    /**
     * 验证表单
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}
      
      // 验证类型
      if (!formData.type) {
        errors.type = '请选择举报类型'
      }
      
      // 验证原因
      if (!formData.reason.trim()) {
        errors.reason = '请简要说明举报原因'
      }
      
      // 验证证据（如果需要）
      const reportType = this.data.reportTypes.find(t => t.id === formData.type)
      if (reportType && reportType.evidenceRequired && formData.evidence.length === 0) {
        errors.evidence = '此类举报需要提供证据截图'
      }
      
      // 验证描述长度
      if (formData.description.length > 500) {
        errors.description = '详细描述不能超过500个字符'
      }
      
      this.setData({ errors })
      
      return Object.keys(errors).length === 0
    },

    /**
     * 设置错误信息
     * @param {string} field 字段名
     * @param {string} message 错误信息
     */
    setError(field, message) {
      const errors = { ...this.data.errors }
      errors[field] = message
      this.setData({ errors })
      
      wx.showToast({
        title: message,
        icon: 'none'
      })
    },

    /**
     * 清除错误信息
     * @param {string} field 字段名
     */
    clearError(field) {
      const errors = { ...this.data.errors }
      delete errors[field]
      this.setData({ errors })
    },

    /**
     * 提交举报
     */
    async submitReport() {
      if (this.data.submitting) return
      
      // 验证表单
      if (!this.validateForm()) {
        return
      }
      
      this.setData({ submitting: true })
      
      try {
        // 准备提交数据
        const submitData = {
          ...this.data.formData,
          targetType: this.data.targetType,
          targetId: this.data.targetId,
          targetInfo: this.data.targetInfo || {}
        }
        
        // 提交举报
        const result = await reportManager.submitReport(submitData)
        
        if (result.success) {
          // 提交成功
          wx.showModal({
            title: '举报提交成功',
            content: result.message,
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              // 触发成功事件
              this.triggerEvent('success', {
                reportId: result.reportId,
                message: result.message,
                expectedProcessingTime: result.expectedProcessingTime
              })
              
              // 关闭表单
              this.closeForm()
            }
          })
          
        } else {
          // 提交失败
          wx.showToast({
            title: result.message || '提交失败',
            icon: 'none'
          })
          
          // 触发失败事件
          this.triggerEvent('error', {
            error: result.error,
            message: result.message
          })
        }
      } catch (error) {
        console.error('提交举报失败:', error)
        
        wx.showToast({
          title: '提交失败，请稍后重试',
          icon: 'none'
        })
        
        this.triggerEvent('error', { error: error.message })
      } finally {
        this.setData({ submitting: false })
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.setData({
        formData: {
          type: '',
          reason: '',
          description: '',
          evidence: []
        },
        uploadedImages: [],
        errors: {},
        currentStep: 1
      })
    },

    /**
     * 关闭表单
     */
    closeForm() {
      this.resetForm()
      this.triggerEvent('close')
    },

    /**
     * 点击遮罩层
     */
    onMaskTap() {
      // 可配置是否允许点击遮罩关闭
      // this.closeForm()
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'show': function(show) {
      if (show) {
        this.init()
      }
    }
  }
})
