/**
 * 举报表单组件样式
 * Task 1.5: 举报功能模块 - 表单样式
 */

.report-form {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.report-form.hidden {
  display: none;
}

/* 遮罩层 */
.form-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.report-form:not(.hidden) .form-mask {
  opacity: 1;
}

/* 表单容器 */
.form-container {
  width: 100%;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.report-form:not(.hidden) .form-container {
  transform: translateY(0);
}

/* 表单头部 */
.form-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ddd;
  color: #999;
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.step-number.active {
  background-color: #ff4757;
  color: #fff;
}

.step-number.completed {
  background-color: #2ed573;
  color: #fff;
}

.step-line {
  width: 80rpx;
  height: 4rpx;
  background-color: #ddd;
  margin: 0 20rpx;
  transition: all 0.3s ease;
}

.step-line.completed {
  background-color: #2ed573;
}

/* 表单内容 */
.form-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 30rpx;
}

/* 步骤内容 */
.step-content {
  min-height: 400rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 举报类型选择 */
.type-selector {
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  background-color: #fff;
  transition: all 0.2s ease;
  margin-bottom: 20rpx;
}

.type-selector:active {
  background-color: #f8f9fa;
}

.type-selector.error {
  border-color: #ff4757;
}

.type-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.type-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.type-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.type-text {
  flex: 1;
}

.type-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.type-placeholder {
  font-size: 28rpx;
  color: #999;
}

.type-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 类型选择器弹窗 */
.type-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  width: 85%;
  max-width: 600rpx;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.picker-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.type-list {
  max-height: 50vh;
  overflow-y: auto;
}

.type-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.type-item:last-child {
  border-bottom: none;
}

.type-item:active {
  background-color: #f8f9fa;
}

.type-item .type-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.type-item .type-name {
  font-size: 30rpx;
  margin-bottom: 6rpx;
}

.type-item .type-desc {
  font-size: 24rpx;
}

/* 输入框 */
.form-input {
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  transition: border-color 0.2s ease;
  margin-bottom: 20rpx;
}

.form-input:focus {
  border-color: #ff4757;
}

.form-input.error {
  border-color: #ff4757;
}

.form-textarea {
  min-height: 200rpx;
  resize: none;
}

/* 证据上传 */
.evidence-section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.evidence-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.evidence-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
}

.evidence-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.evidence-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.upload-btn:active {
  border-color: #ff4757;
  color: #ff4757;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

/* 错误信息 */
.error-message {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
  line-height: 1.3;
}

/* 表单底部 */
.form-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.footer-buttons {
  display: flex;
  gap: 20rpx;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.footer-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.footer-btn.primary {
  background: linear-gradient(135deg, #ff4757, #ff3838);
  color: #fff;
}

.footer-btn:active {
  transform: scale(0.98);
}

.footer-btn.primary:active {
  opacity: 0.9;
}

.footer-btn.secondary:active {
  background-color: #e9ecef;
}

.footer-btn.disabled {
  background: #ddd;
  color: #999;
  pointer-events: none;
}

.footer-btn.loading {
  pointer-events: none;
}

.loading-icon {
  margin-right: 16rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 字符计数 */
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.char-count.warning {
  color: #ffa502;
}

.char-count.error {
  color: #ff4757;
}
