<!--
  举报表单组件模板
  Task 1.5: 举报功能模块 - 表单模板
-->

<view class="report-form {{show ? '' : 'hidden'}}">
  <!-- 遮罩层 -->
  <view class="form-mask" bindtap="onMaskTap"></view>
  
  <!-- 表单容器 -->
  <view class="form-container">
    <!-- 表单头部 -->
    <view class="form-header">
      <view class="form-title">举报</view>
      <view class="close-btn" bindtap="closeForm">✕</view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-item">
        <view class="step-number {{currentStep >= 1 ? 'active' : ''}}">1</view>
      </view>
      <view class="step-line {{currentStep > 1 ? 'completed' : ''}}"></view>
      <view class="step-item">
        <view class="step-number {{currentStep >= 2 ? (currentStep === 2 ? 'active' : 'completed') : ''}}">2</view>
      </view>
      <view class="step-line {{currentStep > 2 ? 'completed' : ''}}"></view>
      <view class="step-item">
        <view class="step-number {{currentStep >= 3 ? 'active' : ''}}">3</view>
      </view>
    </view>
    
    <!-- 表单内容 -->
    <scroll-view class="form-content" scroll-y="true">
      <!-- 第一步：选择举报类型 -->
      <view class="step-content" wx:if="{{currentStep === 1}}">
        <view class="step-title">选择举报类型</view>
        <view class="step-desc">请选择最符合的举报类型，我们会根据类型优先级进行处理</view>
        
        <view 
          class="type-selector {{errors.type ? 'error' : ''}}"
          bindtap="showTypePicker"
        >
          <view class="type-display">
            <view class="type-info" wx:if="{{formData.type}}">
              <view class="type-icon" style="color: {{reportTypes.find(t => t.id === formData.type).color}}">
                {{reportTypes.find(t => t.id === formData.type).icon}}
              </view>
              <view class="type-text">
                <view class="type-name">
                  {{reportTypes.find(t => t.id === formData.type).name}}
                </view>
                <view class="type-desc">
                  {{reportTypes.find(t => t.id === formData.type).description}}
                </view>
              </view>
            </view>
            <view class="type-placeholder" wx:else>
              请选择举报类型
            </view>
            <view class="type-arrow">▼</view>
          </view>
        </view>
        
        <view class="error-message" wx:if="{{errors.type}}">
          {{errors.type}}
        </view>
      </view>

      <!-- 第二步：填写举报原因 -->
      <view class="step-content" wx:if="{{currentStep === 2}}">
        <view class="step-title">填写举报原因</view>
        <view class="step-desc">请简要说明举报原因，帮助我们更好地处理</view>
        
        <input 
          class="form-input {{errors.reason ? 'error' : ''}}"
          placeholder="请简要说明举报原因"
          value="{{formData.reason}}"
          maxlength="100"
          bindinput="onReasonInput"
        />
        
        <textarea 
          class="form-input form-textarea {{errors.description ? 'error' : ''}}"
          placeholder="详细描述（选填）"
          value="{{formData.description}}"
          maxlength="500"
          bindinput="onDescriptionInput"
        />
        
        <view class="char-count {{formData.description.length > 450 ? 'warning' : ''}} {{formData.description.length >= 500 ? 'error' : ''}}">
          {{formData.description.length}}/500
        </view>
        
        <view class="error-message" wx:if="{{errors.reason}}">
          {{errors.reason}}
        </view>
        <view class="error-message" wx:if="{{errors.description}}">
          {{errors.description}}
        </view>
      </view>

      <!-- 第三步：上传证据 -->
      <view class="step-content" wx:if="{{currentStep === 3}}">
        <view class="step-title">上传证据</view>
        <view class="step-desc">
          <text wx:if="{{reportTypes.find(t => t.id === formData.type).evidenceRequired}}">
            此类举报需要提供证据截图
          </text>
          <text wx:else>
            可选择上传相关证据截图（选填）
          </text>
        </view>
        
        <view class="evidence-section">
          <view class="section-title">证据截图</view>
          <view class="evidence-upload">
            <!-- 已上传的证据 -->
            <view 
              class="evidence-item"
              wx:for="{{uploadedImages}}"
              wx:key="*this"
            >
              <image 
                class="evidence-preview"
                src="{{item}}"
                mode="aspectFill"
                data-index="{{index}}"
                bindtap="previewEvidence"
              />
              <view 
                class="evidence-delete"
                data-index="{{index}}"
                bindtap="deleteEvidence"
                catchtap="deleteEvidence"
              >
                ✕
              </view>
            </view>
            
            <!-- 上传按钮 -->
            <view 
              class="upload-btn"
              wx:if="{{uploadedImages.length < maxImages}}"
              bindtap="chooseEvidence"
            >
              <view class="upload-icon">📷</view>
              <view>添加证据</view>
            </view>
          </view>
          
          <view class="error-message" style="margin-top: 16rpx; color: #666; font-size: 24rpx;">
            最多可上传{{maxImages}}张证据图片
          </view>
          
          <view class="error-message" wx:if="{{errors.evidence}}">
            {{errors.evidence}}
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 表单底部 -->
    <view class="form-footer">
      <view class="footer-buttons">
        <!-- 上一步按钮 -->
        <view 
          class="footer-btn secondary"
          wx:if="{{currentStep > 1}}"
          bindtap="prevStep"
        >
          上一步
        </view>
        
        <!-- 下一步/提交按钮 -->
        <view 
          class="footer-btn primary {{submitting ? 'loading disabled' : ''}}"
          wx:if="{{currentStep < totalSteps}}"
          bindtap="nextStep"
        >
          下一步
        </view>
        
        <view 
          class="footer-btn primary {{submitting ? 'loading disabled' : ''}}"
          wx:if="{{currentStep === totalSteps}}"
          bindtap="submitReport"
        >
          <view class="loading-icon" wx:if="{{submitting}}">⏳</view>
          <text>{{submitting ? '提交中...' : '提交举报'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 类型选择器 -->
<view class="type-picker" wx:if="{{showTypePicker}}">
  <view class="picker-content">
    <view class="picker-header">
      <view class="picker-title">选择举报类型</view>
    </view>
    <scroll-view class="type-list" scroll-y="true">
      <view 
        class="type-item"
        wx:for="{{reportTypes}}"
        wx:key="id"
        data-type="{{item.id}}"
        bindtap="onTypeSelect"
      >
        <view class="type-icon" style="color: {{item.color}}">
          {{item.icon}}
        </view>
        <view class="type-text">
          <view class="type-name">{{item.name}}</view>
          <view class="type-desc">{{item.description}}</view>
          <view class="type-desc" style="color: {{item.color}}; font-weight: 500;">
            预计{{item.processingTime}}小时内处理
            <text wx:if="{{item.evidenceRequired}}" style="color: #ff4757;">（需要证据）</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view class="form-mask" bindtap="hideTypePicker"></view>
</view>
