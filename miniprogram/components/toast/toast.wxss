/* Toast组件样式 */
.toast-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.toast {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-heavy);
  padding: 24rpx 32rpx;
  max-width: 600rpx;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  pointer-events: auto;
  border-left: 6rpx solid var(--primary-color);
}

/* Toast类型样式 */
.toast.success {
  border-left-color: var(--success-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

.toast.error {
  border-left-color: var(--error-color);
}

.toast.info {
  border-left-color: var(--info-color);
}

/* Toast位置 */
.toast.top {
  align-self: flex-start;
}

.toast.center {
  align-self: center;
}

.toast.bottom {
  align-self: flex-end;
}

/* Toast图标 */
.toast-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.toast.success .toast-icon {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.toast.warning .toast-icon {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.toast.error .toast-icon {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.toast.info .toast-icon {
  background: rgba(24, 144, 255, 0.1);
  color: var(--info-color);
}

.icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* Toast内容 */
.toast-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.toast-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.toast-message {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 关闭按钮 */
.toast-close {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--background-secondary);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.toast-close:active {
  background: var(--border-medium);
  transform: scale(0.9);
}

.close-icon {
  font-size: 20rpx;
  color: var(--text-tertiary);
  font-weight: bold;
}

/* 动画效果 */
.fade-in {
  animation: toastFadeIn 0.3s ease-out;
}

@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .toast-container {
    padding: 100rpx 20rpx;
  }
  
  .toast {
    padding: 20rpx 24rpx;
    max-width: 90vw;
  }
}
