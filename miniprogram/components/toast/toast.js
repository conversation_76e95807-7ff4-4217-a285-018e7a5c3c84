// Toast组件逻辑
Component({
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 消息内容
    message: {
      type: String,
      value: ''
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 类型：success, warning, error, info
    type: {
      type: String,
      value: 'info'
    },
    // 位置：top, center, bottom
    position: {
      type: String,
      value: 'top'
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      value: true
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      value: false
    },
    // 自动关闭时间（毫秒）
    duration: {
      type: Number,
      value: 3000
    }
  },

  data: {
    icon: ''
  },

  observers: {
    'type, showIcon': function(type, showIcon) {
      if (!showIcon) {
        this.setData({ icon: '' });
        return;
      }

      const iconMap = {
        success: '✓',
        warning: '⚠',
        error: '✕',
        info: 'ℹ'
      };

      this.setData({
        icon: iconMap[type] || iconMap.info
      });
    },

    'show': function(show) {
      if (show && this.data.duration > 0) {
        // 自动关闭
        this.autoCloseTimer = setTimeout(() => {
          this.close();
        }, this.data.duration);
      } else if (!show && this.autoCloseTimer) {
        // 清除定时器
        clearTimeout(this.autoCloseTimer);
        this.autoCloseTimer = null;
      }
    }
  },

  methods: {
    // 关闭Toast
    close() {
      this.setData({ show: false });
      
      // 清除定时器
      if (this.autoCloseTimer) {
        clearTimeout(this.autoCloseTimer);
        this.autoCloseTimer = null;
      }

      // 触发关闭事件
      this.triggerEvent('close');
    },

    // 显示Toast
    showToast(options = {}) {
      const {
        message = '',
        title = '',
        type = 'info',
        position = 'top',
        showIcon = true,
        closable = false,
        duration = 3000
      } = options;

      this.setData({
        show: true,
        message,
        title,
        type,
        position,
        showIcon,
        closable,
        duration
      });
    }
  },

  detached() {
    // 组件销毁时清除定时器
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
  }
});
