<view class="toast-container" wx:if="{{show}}">
  <view class="toast {{type}} {{position}} fade-in">
    <view class="toast-icon" wx:if="{{icon}}">
      <text class="icon">{{icon}}</text>
    </view>
    <view class="toast-content">
      <text class="toast-title" wx:if="{{title}}">{{title}}</text>
      <text class="toast-message">{{message}}</text>
    </view>
    <view class="toast-close" wx:if="{{closable}}" bindtap="close">
      <text class="close-icon">×</text>
    </view>
  </view>
</view>
