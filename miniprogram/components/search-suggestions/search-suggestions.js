// 搜索建议组件
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    keyword: {
      type: String,
      value: '',
      observer: 'onKeywordChange'
    }
  },

  data: {
    hotSearches: [
      { id: 1, keyword: '软件工程师', count: '1.2k' },
      { id: 2, keyword: '医生', count: '856' },
      { id: 3, keyword: '教师', count: '743' },
      { id: 4, keyword: '设计师', count: '621' },
      { id: 5, keyword: '金融', count: '534' },
      { id: 6, keyword: '律师', count: '423' }
    ],
    searchHistory: [],
    suggestions: []
  },

  lifetimes: {
    attached() {
      this.loadSearchHistory()
    }
  },

  methods: {
    // 关键词变化时触发搜索建议
    onKeywordChange(newVal) {
      if (newVal && newVal.trim()) {
        this.generateSuggestions(newVal.trim())
      } else {
        this.setData({
          suggestions: []
        })
      }
    },

    // 生成搜索建议
    generateSuggestions(keyword) {
      const suggestions = []
      
      // 模拟职业建议
      const professions = ['软件工程师', '医生', '教师', '设计师', '律师', '会计师', '护士', '销售', '市场营销', '产品经理']
      const matchedProfessions = professions.filter(p => p.includes(keyword))
      matchedProfessions.forEach((profession, index) => {
        suggestions.push({
          id: `profession_${index}`,
          keyword: profession,
          type: '职业'
        })
      })

      // 模拟兴趣建议
      const interests = ['音乐', '电影', '旅行', '摄影', '健身', '阅读', '游戏', '美食', '运动', '绘画']
      const matchedInterests = interests.filter(i => i.includes(keyword))
      matchedInterests.forEach((interest, index) => {
        suggestions.push({
          id: `interest_${index}`,
          keyword: interest,
          type: '兴趣'
        })
      })

      // 模拟地区建议
      const locations = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '重庆']
      const matchedLocations = locations.filter(l => l.includes(keyword))
      matchedLocations.forEach((location, index) => {
        suggestions.push({
          id: `location_${index}`,
          keyword: location,
          type: '地区'
        })
      })

      // 限制建议数量
      this.setData({
        suggestions: suggestions.slice(0, 8)
      })
    },

    // 选择搜索关键词
    selectSearch(e) {
      const keyword = e.currentTarget.dataset.keyword
      this.addToHistory(keyword)
      this.triggerEvent('select', { keyword })
    },

    // 添加到搜索历史
    addToHistory(keyword) {
      let history = this.data.searchHistory
      
      // 移除已存在的相同关键词
      history = history.filter(item => item.keyword !== keyword)
      
      // 添加到开头
      history.unshift({
        id: Date.now(),
        keyword: keyword,
        timestamp: new Date().getTime()
      })
      
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }
      
      this.setData({
        searchHistory: history
      })
      
      // 保存到本地存储
      this.saveSearchHistory(history)
    },

    // 移除历史记录项
    removeHistoryItem(e) {
      const id = e.currentTarget.dataset.id
      const history = this.data.searchHistory.filter(item => item.id !== id)
      
      this.setData({
        searchHistory: history
      })
      
      this.saveSearchHistory(history)
    },

    // 清除搜索历史
    clearHistory() {
      wx.showModal({
        title: '确认清除',
        content: '确定要清除所有搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              searchHistory: []
            })
            this.saveSearchHistory([])
          }
        }
      })
    },

    // 隐藏建议面板
    hideSuggestions() {
      this.triggerEvent('hide')
    },

    // 加载搜索历史
    loadSearchHistory() {
      try {
        const history = wx.getStorageSync('search_history') || []
        this.setData({
          searchHistory: history
        })
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    },

    // 保存搜索历史
    saveSearchHistory(history) {
      try {
        wx.setStorageSync('search_history', history)
      } catch (error) {
        console.error('保存搜索历史失败:', error)
      }
    }
  }
})
