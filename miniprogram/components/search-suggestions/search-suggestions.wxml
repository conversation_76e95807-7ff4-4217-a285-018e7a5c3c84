<!-- 搜索建议组件 -->
<view class="search-suggestions" wx:if="{{visible}}">
  <view class="suggestions-mask" bindtap="hideSuggestions"></view>
  <view class="suggestions-panel">
    <!-- 热门搜索 -->
    <view class="suggestion-section" wx:if="{{hotSearches.length > 0}}">
      <view class="section-header">
        <text class="section-title">热门搜索</text>
        <view class="section-icon">🔥</view>
      </view>
      <view class="hot-searches">
        <view 
          class="hot-search-item" 
          wx:for="{{hotSearches}}" 
          wx:key="id"
          bindtap="selectSearch"
          data-keyword="{{item.keyword}}"
        >
          <text class="search-text">{{item.keyword}}</text>
          <view class="search-count">{{item.count}}</view>
        </view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="suggestion-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-header">
        <text class="section-title">最近搜索</text>
        <view class="clear-history" bindtap="clearHistory">
          <text class="clear-icon">🗑️</text>
          <text class="clear-text">清除</text>
        </view>
      </view>
      <view class="search-history">
        <view 
          class="history-item" 
          wx:for="{{searchHistory}}" 
          wx:key="id"
          bindtap="selectSearch"
          data-keyword="{{item.keyword}}"
        >
          <view class="history-icon">🕐</view>
          <text class="search-text">{{item.keyword}}</text>
          <view 
            class="remove-item" 
            bindtap="removeHistoryItem"
            data-id="{{item.id}}"
            catchtap="true"
          >
            <text class="remove-icon">✕</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索建议 -->
    <view class="suggestion-section" wx:if="{{suggestions.length > 0}}">
      <view class="section-header">
        <text class="section-title">搜索建议</text>
        <view class="section-icon">💡</view>
      </view>
      <view class="search-suggestions-list">
        <view 
          class="suggestion-item" 
          wx:for="{{suggestions}}" 
          wx:key="id"
          bindtap="selectSearch"
          data-keyword="{{item.keyword}}"
        >
          <view class="suggestion-icon">🔍</view>
          <text class="search-text">{{item.keyword}}</text>
          <view class="suggestion-type">{{item.type}}</view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{hotSearches.length === 0 && searchHistory.length === 0 && suggestions.length === 0}}">
      <view class="empty-icon">🔍</view>
      <text class="empty-text">暂无搜索建议</text>
      <text class="empty-tip">输入关键词开始搜索</text>
    </view>
  </view>
</view>
