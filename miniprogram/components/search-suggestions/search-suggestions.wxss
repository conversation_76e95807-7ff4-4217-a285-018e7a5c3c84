/* 搜索建议组件样式 */
.search-suggestions {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.suggestions-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4rpx);
  -webkit-backdrop-filter: blur(4rpx);
}

.suggestions-panel {
  position: absolute;
  top: 200rpx;
  left: 32rpx;
  right: 32rpx;
  max-height: 70vh;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.section-icon {
  font-size: 24rpx;
}

.clear-history {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666666;
}

.clear-icon {
  font-size: 20rpx;
}

.clear-text {
  font-size: 24rpx;
}

/* 热门搜索 */
.hot-searches {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-search-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  color: #ffffff;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.hot-search-item:active {
  transform: scale(0.95);
}

.search-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
}

/* 搜索历史 */
.search-history {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.history-item:active {
  background: #e9ecef;
}

.history-icon {
  font-size: 24rpx;
  color: #999999;
}

.search-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.remove-item {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.remove-item:active {
  background: #dee2e6;
  transform: scale(0.9);
}

.remove-icon {
  font-size: 20rpx;
  color: #666666;
}

/* 搜索建议 */
.search-suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.suggestion-item:active {
  background: #e9ecef;
}

.suggestion-icon {
  font-size: 24rpx;
  color: #667eea;
}

.suggestion-type {
  padding: 4rpx 12rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999999;
}
