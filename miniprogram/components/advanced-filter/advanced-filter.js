// 高级筛选面板组件
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    filters: {
      type: Object,
      value: {}
    }
  },

  data: {
    // 年龄范围
    ageRange: {
      min: 20,
      max: 35
    },

    // 距离选项
    distanceOptions: [
      { value: '1km', label: '1公里内' },
      { value: '3km', label: '3公里内' },
      { value: '5km', label: '5公里内' },
      { value: '10km', label: '10公里内' },
      { value: '20km', label: '20公里内' },
      { value: 'unlimited', label: '不限距离' }
    ],
    selectedDistance: '10km',

    // 学历选项
    educationOptions: [
      { value: 'unlimited', label: '不限学历' },
      { value: 'high_school', label: '高中及以上' },
      { value: 'college', label: '大专及以上' },
      { value: 'bachelor', label: '本科及以上' },
      { value: 'master', label: '硕士及以上' },
      { value: 'doctor', label: '博士及以上' }
    ],
    selectedEducation: 'unlimited',

    // 收入选项
    incomeOptions: [
      { value: 'unlimited', label: '不限收入' },
      { value: '3-5k', label: '3-5千' },
      { value: '5-8k', label: '5-8千' },
      { value: '8-12k', label: '8千-1万2' },
      { value: '12-20k', label: '1万2-2万' },
      { value: '20k+', label: '2万以上' }
    ],
    selectedIncome: 'unlimited',

    // 兴趣选项
    interestOptions: [
      '音乐', '电影', '旅行', '摄影', '健身', '阅读',
      '游戏', '美食', '运动', '绘画', '舞蹈', '瑜伽',
      '烘焙', '宠物', '科技', '投资', '创业', '公益'
    ],
    selectedInterests: [],

    // 其他条件
    onlineOnly: false,
    verifiedOnly: false,
    vipOnly: false,

    // 预计结果
    estimatedResults: 156
  },

  observers: {
    'ageRange, selectedDistance, selectedEducation, selectedIncome, selectedInterests, onlineOnly, verifiedOnly, vipOnly': function() {
      this.calculateEstimatedResults()
    }
  },

  computed: {
    distanceText() {
      const option = this.data.distanceOptions.find(item => item.value === this.data.selectedDistance)
      return option ? option.label : '不限距离'
    },

    educationText() {
      const option = this.data.educationOptions.find(item => item.value === this.data.selectedEducation)
      return option ? option.label : '不限学历'
    },

    incomeText() {
      const option = this.data.incomeOptions.find(item => item.value === this.data.selectedIncome)
      return option ? option.label : '不限收入'
    }
  },

  lifetimes: {
    attached() {
      this.initFilters()
    }
  },

  methods: {
    // 初始化筛选条件
    initFilters() {
      const filters = this.properties.filters || {}
      
      this.setData({
        ageRange: {
          min: filters.minAge || 20,
          max: filters.maxAge || 35
        },
        selectedDistance: filters.distance || '10km',
        selectedEducation: filters.education || 'unlimited',
        selectedIncome: filters.income || 'unlimited',
        selectedInterests: filters.interests || [],
        onlineOnly: filters.onlineOnly || false,
        verifiedOnly: filters.verifiedOnly || false,
        vipOnly: filters.vipOnly || false
      })

      this.updateComputedValues()
    },

    // 更新计算属性
    updateComputedValues() {
      const distanceOption = this.data.distanceOptions.find(item => item.value === this.data.selectedDistance)
      const educationOption = this.data.educationOptions.find(item => item.value === this.data.selectedEducation)
      const incomeOption = this.data.incomeOptions.find(item => item.value === this.data.selectedIncome)

      this.setData({
        distanceText: distanceOption ? distanceOption.label : '不限距离',
        educationText: educationOption ? educationOption.label : '不限学历',
        incomeText: incomeOption ? incomeOption.label : '不限收入'
      })
    },

    // 年龄最小值变化
    onAgeMinChange(e) {
      const minAge = parseInt(e.detail.value)
      this.setData({
        'ageRange.min': minAge
      })
      
      // 确保最小值不大于最大值
      if (minAge >= this.data.ageRange.max) {
        this.setData({
          'ageRange.max': minAge + 1
        })
      }
    },

    // 年龄最大值变化
    onAgeMaxChange(e) {
      const maxAge = parseInt(e.detail.value)
      this.setData({
        'ageRange.max': maxAge
      })
      
      // 确保最大值不小于最小值
      if (maxAge <= this.data.ageRange.min) {
        this.setData({
          'ageRange.min': maxAge - 1
        })
      }
    },

    // 选择距离
    selectDistance(e) {
      const value = e.currentTarget.dataset.value
      this.setData({
        selectedDistance: value
      })
      this.updateComputedValues()
    },

    // 选择学历
    selectEducation(e) {
      const value = e.currentTarget.dataset.value
      this.setData({
        selectedEducation: value
      })
      this.updateComputedValues()
    },

    // 选择收入
    selectIncome(e) {
      const value = e.currentTarget.dataset.value
      this.setData({
        selectedIncome: value
      })
      this.updateComputedValues()
    },

    // 切换兴趣
    toggleInterest(e) {
      const interest = e.currentTarget.dataset.interest
      let selectedInterests = [...this.data.selectedInterests]
      
      const index = selectedInterests.indexOf(interest)
      if (index > -1) {
        selectedInterests.splice(index, 1)
      } else {
        selectedInterests.push(interest)
      }
      
      this.setData({
        selectedInterests
      })
    },

    // 切换仅在线用户
    toggleOnlineOnly(e) {
      this.setData({
        onlineOnly: e.detail.value
      })
    },

    // 切换仅认证用户
    toggleVerifiedOnly(e) {
      this.setData({
        verifiedOnly: e.detail.value
      })
    },

    // 切换仅VIP用户
    toggleVipOnly(e) {
      this.setData({
        vipOnly: e.detail.value
      })
    },

    // 计算预计结果
    calculateEstimatedResults() {
      let baseCount = 1000
      
      // 根据筛选条件调整结果数量
      const ageRange = this.data.ageRange.max - this.data.ageRange.min
      baseCount = Math.floor(baseCount * (ageRange / 40)) // 年龄范围影响
      
      if (this.data.selectedDistance !== 'unlimited') {
        baseCount = Math.floor(baseCount * 0.6) // 距离限制
      }
      
      if (this.data.selectedEducation !== 'unlimited') {
        baseCount = Math.floor(baseCount * 0.7) // 学历要求
      }
      
      if (this.data.selectedIncome !== 'unlimited') {
        baseCount = Math.floor(baseCount * 0.5) // 收入要求
      }
      
      if (this.data.selectedInterests.length > 0) {
        baseCount = Math.floor(baseCount * (1 - this.data.selectedInterests.length * 0.1)) // 兴趣筛选
      }
      
      if (this.data.onlineOnly) {
        baseCount = Math.floor(baseCount * 0.3) // 仅在线
      }
      
      if (this.data.verifiedOnly) {
        baseCount = Math.floor(baseCount * 0.4) // 仅认证
      }
      
      if (this.data.vipOnly) {
        baseCount = Math.floor(baseCount * 0.2) // 仅VIP
      }
      
      this.setData({
        estimatedResults: Math.max(1, baseCount)
      })
    },

    // 重置筛选条件
    resetFilters() {
      this.setData({
        ageRange: { min: 20, max: 35 },
        selectedDistance: '10km',
        selectedEducation: 'unlimited',
        selectedIncome: 'unlimited',
        selectedInterests: [],
        onlineOnly: false,
        verifiedOnly: false,
        vipOnly: false
      })
      this.updateComputedValues()
    },

    // 应用筛选
    applyFilters() {
      const filters = {
        minAge: this.data.ageRange.min,
        maxAge: this.data.ageRange.max,
        distance: this.data.selectedDistance,
        education: this.data.selectedEducation,
        income: this.data.selectedIncome,
        interests: this.data.selectedInterests,
        onlineOnly: this.data.onlineOnly,
        verifiedOnly: this.data.verifiedOnly,
        vipOnly: this.data.vipOnly
      }
      
      this.triggerEvent('apply', { filters, estimatedResults: this.data.estimatedResults })
      this.hideFilter()
    },

    // 隐藏筛选面板
    hideFilter() {
      this.triggerEvent('hide')
    }
  }
})
