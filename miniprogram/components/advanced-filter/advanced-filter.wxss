/* 高级筛选面板样式 */
.advanced-filter {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
}

.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
  -webkit-backdrop-filter: blur(4rpx);
}

.filter-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 头部 */
.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333333;
}

.filter-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.filter-close:active {
  background: #e0e0e0;
  transform: scale(0.9);
}

.close-icon {
  font-size: 24rpx;
  color: #666666;
}

/* 内容区域 */
.filter-content {
  flex: 1;
  padding: 0 32rpx;
  max-height: 60vh;
}

.filter-section {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.filter-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.section-value {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

/* 年龄滑块 */
.age-slider-container {
  padding: 16rpx 0;
}

.slider-wrapper {
  margin-bottom: 24rpx;
  position: relative;
}

.age-slider {
  width: 100%;
  margin: 16rpx 0;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
}

.label-min,
.label-max {
  font-size: 24rpx;
  color: #999999;
}

/* 选项网格 */
.distance-options,
.education-options,
.income-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.option-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  transform: scale(1.05);
}

.option-item:active {
  transform: scale(0.95);
}

.option-text {
  font-size: 28rpx;
  color: #333333;
}

.option-item.active .option-text {
  color: #ffffff;
  font-weight: 600;
}

/* 兴趣标签网格 */
.interests-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.interest-tag {
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.interest-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  transform: scale(1.05);
}

.interest-tag:active {
  transform: scale(0.95);
}

.tag-text {
  font-size: 24rpx;
  color: #333333;
}

.interest-tag.active .tag-text {
  color: #ffffff;
  font-weight: 500;
}

/* 开关选项 */
.other-options {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
}

.switch-label {
  font-size: 28rpx;
  color: #333333;
}

/* 结果预览 */
.result-preview {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.result-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #667eea;
  display: block;
  margin-bottom: 12rpx;
}

.result-tips {
  margin-top: 12rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999999;
}

/* 底部操作 */
.filter-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #ffffff;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.reset-btn {
  background: #f8f9fa;
  color: #666666;
}

.reset-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.apply-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.apply-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}
