<!-- 高级筛选面板组件 -->
<view class="advanced-filter" wx:if="{{visible}}">
  <view class="filter-mask" bindtap="hideFilter"></view>
  <view class="filter-panel">
    <!-- 头部 -->
    <view class="filter-header">
      <text class="filter-title">高级筛选</text>
      <view class="filter-close" bindtap="hideFilter">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 筛选内容 -->
    <scroll-view class="filter-content" scroll-y="true">
      <!-- 年龄范围 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">年龄范围</text>
          <text class="section-value">{{ageRange.min}}岁 - {{ageRange.max}}岁</text>
        </view>
        <view class="age-slider-container">
          <view class="slider-wrapper">
            <slider 
              class="age-slider"
              min="18"
              max="60"
              value="{{ageRange.min}}"
              bindchange="onAgeMinChange"
              activeColor="#667eea"
              backgroundColor="#f0f0f0"
              block-size="24"
              block-color="#667eea"
            />
            <view class="slider-labels">
              <text class="label-min">18岁</text>
              <text class="label-max">60岁</text>
            </view>
          </view>
          <view class="slider-wrapper">
            <slider 
              class="age-slider"
              min="18"
              max="60"
              value="{{ageRange.max}}"
              bindchange="onAgeMaxChange"
              activeColor="#667eea"
              backgroundColor="#f0f0f0"
              block-size="24"
              block-color="#667eea"
            />
          </view>
        </view>
      </view>

      <!-- 距离范围 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">距离范围</text>
          <text class="section-value">{{distanceText}}</text>
        </view>
        <view class="distance-options">
          <view 
            wx:for="{{distanceOptions}}" 
            wx:key="value"
            class="option-item {{selectedDistance === item.value ? 'active' : ''}}"
            data-value="{{item.value}}"
            bindtap="selectDistance"
          >
            <text class="option-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 学历要求 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">学历要求</text>
          <text class="section-value">{{educationText}}</text>
        </view>
        <view class="education-options">
          <view 
            wx:for="{{educationOptions}}" 
            wx:key="value"
            class="option-item {{selectedEducation === item.value ? 'active' : ''}}"
            data-value="{{item.value}}"
            bindtap="selectEducation"
          >
            <text class="option-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 收入范围 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">收入范围</text>
          <text class="section-value">{{incomeText}}</text>
        </view>
        <view class="income-options">
          <view 
            wx:for="{{incomeOptions}}" 
            wx:key="value"
            class="option-item {{selectedIncome === item.value ? 'active' : ''}}"
            data-value="{{item.value}}"
            bindtap="selectIncome"
          >
            <text class="option-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 兴趣爱好 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">兴趣爱好</text>
          <text class="section-value">已选择 {{selectedInterests.length}} 个</text>
        </view>
        <view class="interests-grid">
          <view 
            wx:for="{{interestOptions}}" 
            wx:key="*this"
            class="interest-tag {{selectedInterests.indexOf(item) !== -1 ? 'active' : ''}}"
            data-interest="{{item}}"
            bindtap="toggleInterest"
          >
            <text class="tag-text">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 其他条件 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">其他条件</text>
        </view>
        <view class="other-options">
          <view class="switch-item">
            <text class="switch-label">仅显示在线用户</text>
            <switch 
              checked="{{onlineOnly}}"
              bindchange="toggleOnlineOnly"
              color="#667eea"
            />
          </view>
          <view class="switch-item">
            <text class="switch-label">仅显示认证用户</text>
            <switch 
              checked="{{verifiedOnly}}"
              bindchange="toggleVerifiedOnly"
              color="#667eea"
            />
          </view>
          <view class="switch-item">
            <text class="switch-label">仅显示VIP用户</text>
            <switch 
              checked="{{vipOnly}}"
              bindchange="toggleVipOnly"
              color="#667eea"
            />
          </view>
        </view>
      </view>

      <!-- 筛选结果预览 -->
      <view class="filter-section">
        <view class="section-header">
          <text class="section-title">筛选结果</text>
        </view>
        <view class="result-preview">
          <text class="result-text">预计找到 {{estimatedResults}} 位用户</text>
          <view class="result-tips">
            <text class="tips-text">💡 调整筛选条件可以获得更多结果</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作 -->
    <view class="filter-footer">
      <button class="reset-btn" bindtap="resetFilters">重置</button>
      <button class="apply-btn" bindtap="applyFilters">应用筛选 ({{estimatedResults}})</button>
    </view>
  </view>
</view>
