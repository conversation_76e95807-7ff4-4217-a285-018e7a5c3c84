<!--
  增强版Swiper组件模板
  Task 2.3: 新增实用组件开发 - Swiper模板
-->

<view 
  class="swiper-container"
  style="height: {{height}}; border-radius: {{borderRadius}}"
  bindtouchstart="onTouchStart"
  bindtouchmove="onTouchMove"
  bindtouchend="onTouchEnd"
>
  <!-- 轮播主体 -->
  <view 
    class="swiper-wrapper effect-{{effect}} {{touching ? 'touching' : ''}}"
    style="transform: translateX({{-currentIndex * 100 + (touchMoveX / containerWidth * 100)}}%)"
    wx:if="{{items.length > 0}}"
  >
    <view 
      class="swiper-item {{index === currentIndex ? 'active' : ''}}"
      wx:for="{{items}}"
      wx:key="id"
      wx:for-index="index"
      data-index="{{index}}"
      style="{{getItemStyle(index)}}"
      bindtap="onItemTap"
    >
      <!-- 图片 -->
      <image 
        wx:if="{{item.image}}"
        src="{{item.image}}"
        mode="aspectFill"
        lazy-load="{{true}}"
      />
      
      <!-- 内容覆盖层 -->
      <view wx:if="{{item.title || item.desc}}" class="swiper-item-content">
        <view wx:if="{{item.title}}" class="swiper-item-title">{{item.title}}</view>
        <view wx:if="{{item.desc}}" class="swiper-item-desc">{{item.desc}}</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="swiper-empty">
    <view class="empty-icon">📷</view>
    <view class="empty-text">暂无图片</view>
  </view>

  <!-- 指示器 -->
  <view 
    wx:if="{{showIndicator && items.length > 1}}"
    class="swiper-indicators {{getIndicatorClass()}}"
  >
    <!-- 点状指示器 -->
    <view 
      wx:if="{{indicatorType === 'dots'}}"
      class="indicator-item {{index === currentIndex ? 'active' : ''}}"
      wx:for="{{items}}"
      wx:key="id"
      wx:for-index="index"
      data-index="{{index}}"
      bindtap="onIndicatorTap"
    ></view>

    <!-- 数字指示器 -->
    <view wx:elif="{{indicatorType === 'numbers'}}" class="indicator-numbers">
      {{currentIndex + 1}} / {{items.length}}
    </view>

    <!-- 进度条指示器 -->
    <view wx:elif="{{indicatorType === 'progress'}}" class="indicator-progress">
      <view 
        class="progress-bar"
        style="width: {{((currentIndex + 1) / items.length) * 100}}%"
      ></view>
    </view>

    <!-- 缩略图指示器 -->
    <view 
      wx:elif="{{indicatorType === 'thumbnails'}}"
      class="thumbnail-item {{index === currentIndex ? 'active' : ''}}"
      wx:for="{{items}}"
      wx:key="id"
      wx:for-index="index"
      data-index="{{index}}"
      bindtap="onIndicatorTap"
    >
      <image src="{{item.thumbnail || item.image}}" mode="aspectFill" />
    </view>
  </view>

  <!-- 控制按钮 -->
  <view wx:if="{{showControls && items.length > 1}}">
    <!-- 上一张按钮 -->
    <view 
      class="swiper-controls prev"
      data-direction="prev"
      bindtap="onControlTap"
    >
      <view class="control-btn">‹</view>
    </view>

    <!-- 下一张按钮 -->
    <view 
      class="swiper-controls next"
      data-direction="next"
      bindtap="onControlTap"
    >
      <view class="control-btn">›</view>
    </view>
  </view>
</view>

<wxs module="utils">
  var getItemStyle = function(index, currentIndex, scale, spacing) {
    var style = ''
    
    // 缩放效果
    if (scale !== 1) {
      var isActive = index === currentIndex
      var scaleValue = isActive ? 1 : scale
      style += 'transform: scale(' + scaleValue + '); '
    }
    
    // 间距效果
    if (spacing > 0) {
      style += 'margin: 0 ' + spacing + 'rpx; '
    }
    
    return style
  }
  
  var getIndicatorClass = function(indicatorType) {
    return 'indicator-' + indicatorType
  }
  
  module.exports = {
    getItemStyle: getItemStyle,
    getIndicatorClass: getIndicatorClass
  }
</wxs>
