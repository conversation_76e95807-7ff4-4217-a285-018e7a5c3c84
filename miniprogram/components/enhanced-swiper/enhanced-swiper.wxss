/* components/enhanced-swiper/enhanced-swiper.wxss - 增强版Swiper组件样式 */
/* Task 2.3: 新增实用组件开发 - Swiper样式 */

/* 轮播容器 */
.swiper-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 16rpx;
}

/* 轮播主体 */
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  transition: transform 0.3s ease;
}

/* 轮播项 */
.swiper-item {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.swiper-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 轮播项内容 */
.swiper-item-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
}

.swiper-item-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.swiper-item-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.5;
}

/* 指示器容器 */
.swiper-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 10;
}

/* 点状指示器 */
.indicator-dots .indicator-item {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.indicator-dots .indicator-item.active {
  background-color: #fff;
  transform: scale(1.2);
}

/* 数字指示器 */
.indicator-numbers {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 进度条指示器 */
.indicator-progress {
  width: 200rpx;
  height: 6rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.indicator-progress .progress-bar {
  height: 100%;
  background-color: #fff;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 缩略图指示器 */
.indicator-thumbnails {
  display: flex;
  gap: 8rpx;
}

.indicator-thumbnails .thumbnail-item {
  width: 60rpx;
  height: 40rpx;
  border-radius: 6rpx;
  overflow: hidden;
  opacity: 0.6;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.indicator-thumbnails .thumbnail-item.active {
  opacity: 1;
}

.indicator-thumbnails .thumbnail-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 控制按钮 */
.swiper-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.swiper-controls.prev {
  left: 20rpx;
}

.swiper-controls.next {
  right: 20rpx;
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  cursor: pointer;
}

.control-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.8);
}

/* 切换效果 */
.swiper-wrapper.effect-fade .swiper-item {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.swiper-wrapper.effect-fade .swiper-item.active {
  opacity: 1;
}

.swiper-wrapper.effect-cube {
  perspective: 1200rpx;
}

.swiper-wrapper.effect-cube .swiper-item {
  transform-origin: 0 50%;
  transition: transform 0.3s ease;
}

.swiper-wrapper.effect-flip .swiper-item {
  transform-origin: 50% 50%;
  transition: transform 0.3s ease;
}

.swiper-wrapper.effect-cards .swiper-item {
  transform-origin: 50% 100%;
  transition: transform 0.3s ease;
}

/* 触摸反馈 */
.swiper-wrapper.touching {
  transition: none;
}

/* 加载状态 */
.swiper-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.swiper-loading .loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.swiper-loading .loading-text {
  font-size: 24rpx;
}

/* 空状态 */
.swiper-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  background-color: #f8f9fa;
}

.swiper-empty .empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.swiper-empty .empty-text {
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .swiper-item-content {
    padding: 30rpx 20rpx 20rpx;
  }
  
  .swiper-item-title {
    font-size: 28rpx;
  }
  
  .swiper-item-desc {
    font-size: 24rpx;
  }
  
  .control-btn {
    width: 50rpx;
    height: 50rpx;
    font-size: 20rpx;
  }
  
  .swiper-controls.prev {
    left: 15rpx;
  }
  
  .swiper-controls.next {
    right: 15rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .swiper-item-content {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  }
  
  .indicator-numbers {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .control-btn {
    background-color: rgba(42, 42, 42, 0.9);
    color: #fff;
  }
  
  .swiper-empty {
    background-color: #2a2a2a;
    color: #ccc;
  }
}

/* 动画增强 */
.swiper-item {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.indicator-item {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 3D效果 */
.swiper-wrapper.effect-3d {
  transform-style: preserve-3d;
}

.swiper-wrapper.effect-3d .swiper-item {
  backface-visibility: hidden;
}

/* 视差效果 */
.swiper-item.parallax {
  transform: translateZ(0);
}

.swiper-item.parallax .swiper-item-bg {
  transform: translate3d(0, 0, 0);
  transition: transform 0.3s ease;
}
