<!--
  支付引导组件模板
  Task 1.8: 支付体验优化 - 引导组件模板
-->

<view class="guide-overlay" wx:if="{{visible}}" bindtap="hide">
  <view class="guide-container" catchtap="">
    <!-- 引导头部 -->
    <view class="guide-header">
      <!-- 关闭按钮 -->
      <view class="close-btn" bindtap="hide">×</view>
      
      <!-- 自动播放控制 -->
      <view class="autoplay-control" wx:if="{{autoPlay}}" bindtap="toggleAutoPlay">
        {{autoPlayTimer ? '⏸' : '▶'}}
      </view>
      
      <view class="guide-title">支付引导</view>
      
      <view class="guide-progress">
        <view class="progress-text">{{currentStep + 1}}/{{steps.length}}</view>
        <view class="progress-bar">
          <view 
            class="progress-fill"
            style="width: {{getProgressPercent()}}%"
          ></view>
        </view>
      </view>
    </view>

    <!-- 引导内容 -->
    <view class="guide-content {{animating ? 'animating' : ''}}" wx:if="{{getCurrentStep()}}">
      <view class="step-icon">{{getCurrentStep().icon}}</view>
      <view class="step-title">{{getCurrentStep().title}}</view>
      <view class="step-content">{{getCurrentStep().content}}</view>
      
      <!-- 提示信息 -->
      <view class="step-tips" wx:if="{{getCurrentStep().tips}}">
        <view class="tips-title">💡 小贴士</view>
        <view class="tips-content">{{getCurrentStep().tips}}</view>
      </view>
    </view>

    <!-- 引导底部 -->
    <view class="guide-footer">
      <!-- 操作按钮 -->
      <view class="guide-actions">
        <view 
          class="action-btn secondary"
          wx:if="{{currentStep > 0}}"
          bindtap="prevStep"
        >
          上一步
        </view>
        
        <view 
          class="action-btn primary"
          wx:if="{{currentStep < steps.length - 1}}"
          bindtap="nextStep"
        >
          下一步
        </view>
        
        <view 
          class="action-btn primary"
          wx:if="{{currentStep >= steps.length - 1}}"
          bindtap="hide"
        >
          开始支付
        </view>
        
        <view 
          class="action-btn text"
          bindtap="skipGuide"
        >
          跳过引导
        </view>
      </view>
      
      <!-- 步骤指示器 -->
      <view class="step-indicators">
        <view 
          class="step-dot {{index < currentStep ? 'completed' : index === currentStep ? 'active' : ''}}"
          wx:for="{{steps}}"
          wx:key="id"
          wx:for-index="index"
          data-step="{{index}}"
          bindtap="goToStep"
        ></view>
      </view>
    </view>

    <!-- 反馈区域 -->
    <view class="feedback-section" wx:if="{{currentStep >= steps.length - 1}}">
      <view class="feedback-title">这个引导对您有帮助吗？</view>
      <view class="feedback-actions">
        <view class="feedback-btn helpful" bindtap="markHelpful">
          👍 有帮助
        </view>
        <view class="feedback-btn not-helpful" bindtap="markNotHelpful">
          👎 没帮助
        </view>
      </view>
    </view>
  </view>
</view>

<wxs module="utils">
  var getCurrentStep = function(steps, currentStep) {
    if (steps && steps.length > 0 && currentStep < steps.length) {
      return steps[currentStep]
    }
    return null
  }
  
  var getProgressPercent = function(currentStep, totalSteps) {
    if (totalSteps === 0) return 0
    return ((currentStep + 1) / totalSteps) * 100
  }
  
  module.exports = {
    getCurrentStep: getCurrentStep,
    getProgressPercent: getProgressPercent
  }
</wxs>
