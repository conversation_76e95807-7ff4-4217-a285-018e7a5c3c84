/* components/payment-guide/payment-guide.wxss - 支付引导组件样式 */
/* Task 1.8: 支付体验优化 - 引导组件样式 */

.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 引导容器 */
.guide-container {
  width: 90%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 引导头部 */
.guide-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.guide-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.guide-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  opacity: 0.9;
}

.progress-bar {
  width: 120rpx;
  height: 6rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #fff;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 引导内容 */
.guide-content {
  padding: 40rpx 30rpx;
  min-height: 300rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.step-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #fff;
  margin-bottom: 30rpx;
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.step-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.step-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.step-tips {
  background: linear-gradient(135deg, #f8f9ff, #f0f4ff);
  border: 1rpx solid #e6edff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  width: 100%;
}

.tips-title {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.tips-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 引导底部 */
.guide-footer {
  padding: 20rpx 30rpx 30rpx;
  background-color: #f8f9fa;
}

.guide-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}

.action-btn.secondary {
  background: #fff;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.action-btn.text {
  background: transparent;
  color: #999;
  border: none;
}

/* 步骤指示器 */
.step-indicators {
  display: flex;
  justify-content: center;
  gap: 12rpx;
}

.step-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ddd;
  transition: all 0.3s ease;
  cursor: pointer;
}

.step-dot.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scale(1.2);
}

.step-dot.completed {
  background-color: #2ed573;
}

/* 反馈区域 */
.feedback-section {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.feedback-title {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.feedback-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.feedback-btn {
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.feedback-btn:active {
  transform: scale(0.95);
}

.feedback-btn.helpful {
  background-color: #2ed573;
  color: #fff;
}

.feedback-btn.not-helpful {
  background-color: #ff4757;
  color: #fff;
}

/* 自动播放控制 */
.autoplay-control {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #fff;
  transition: all 0.2s ease;
}

.autoplay-control:active {
  transform: scale(0.9);
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  transition: all 0.2s ease;
}

.close-btn:active {
  transform: scale(0.9);
}

/* 动画效果 */
.guide-content.animating {
  animation: contentSlide 0.3s ease-out;
}

@keyframes contentSlide {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .guide-container {
    width: 95%;
  }
  
  .guide-header,
  .guide-content,
  .guide-footer {
    padding-left: 20rpx;
    padding-right: 20rpx;
  }
  
  .step-icon {
    width: 100rpx;
    height: 100rpx;
    font-size: 50rpx;
  }
  
  .step-title {
    font-size: 32rpx;
  }
  
  .step-content {
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .guide-container {
    background-color: #2a2a2a;
  }
  
  .guide-content {
    background-color: #2a2a2a;
  }
  
  .step-title {
    color: #fff;
  }
  
  .step-content {
    color: #ccc;
  }
  
  .guide-footer {
    background-color: #333;
  }
  
  .action-btn.secondary {
    background-color: #444;
    color: #fff;
    border-color: #555;
  }
  
  .step-tips {
    background: linear-gradient(135deg, #333, #444);
    border-color: #555;
  }
  
  .tips-content {
    color: #ccc;
  }
}
