/**
 * 支付引导组件
 * Task 1.8: 支付体验优化 - 引导组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示引导
    visible: {
      type: Boolean,
      value: false
    },
    
    // 引导步骤
    steps: {
      type: Array,
      value: []
    },
    
    // 当前步骤
    currentStep: {
      type: Number,
      value: 0
    },
    
    // 自动播放
    autoPlay: {
      type: Boolean,
      value: true
    },
    
    // 自动播放间隔
    autoPlayInterval: {
      type: Number,
      value: 3000
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画状态
    animating: false,
    
    // 自动播放定时器
    autoPlayTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示引导
     */
    show() {
      this.setData({ visible: true })
      
      if (this.data.autoPlay) {
        this.startAutoPlay()
      }
      
      // 触发显示事件
      this.triggerEvent('show', {
        currentStep: this.data.currentStep
      })
    },

    /**
     * 隐藏引导
     */
    hide() {
      this.setData({ visible: false })
      this.stopAutoPlay()
      
      // 触发隐藏事件
      this.triggerEvent('hide', {
        currentStep: this.data.currentStep
      })
    },

    /**
     * 下一步
     */
    nextStep() {
      const currentStep = this.data.currentStep
      const totalSteps = this.data.steps.length
      
      if (currentStep < totalSteps - 1) {
        this.setData({
          currentStep: currentStep + 1,
          animating: true
        })
        
        setTimeout(() => {
          this.setData({ animating: false })
        }, 300)
        
        // 触发步骤变化事件
        this.triggerEvent('stepchange', {
          currentStep: currentStep + 1,
          step: this.data.steps[currentStep + 1]
        })
      } else {
        // 最后一步，自动隐藏
        this.hide()
      }
    },

    /**
     * 上一步
     */
    prevStep() {
      const currentStep = this.data.currentStep
      
      if (currentStep > 0) {
        this.setData({
          currentStep: currentStep - 1,
          animating: true
        })
        
        setTimeout(() => {
          this.setData({ animating: false })
        }, 300)
        
        // 触发步骤变化事件
        this.triggerEvent('stepchange', {
          currentStep: currentStep - 1,
          step: this.data.steps[currentStep - 1]
        })
      }
    },

    /**
     * 跳转到指定步骤
     * @param {Event} e 事件对象
     */
    goToStep(e) {
      const step = e.currentTarget.dataset.step
      
      if (step >= 0 && step < this.data.steps.length) {
        this.setData({
          currentStep: step,
          animating: true
        })
        
        setTimeout(() => {
          this.setData({ animating: false })
        }, 300)
        
        // 触发步骤变化事件
        this.triggerEvent('stepchange', {
          currentStep: step,
          step: this.data.steps[step]
        })
      }
    },

    /**
     * 开始自动播放
     */
    startAutoPlay() {
      if (this.data.autoPlayTimer) {
        clearInterval(this.data.autoPlayTimer)
      }
      
      const timer = setInterval(() => {
        this.nextStep()
      }, this.data.autoPlayInterval)
      
      this.setData({ autoPlayTimer: timer })
    },

    /**
     * 停止自动播放
     */
    stopAutoPlay() {
      if (this.data.autoPlayTimer) {
        clearInterval(this.data.autoPlayTimer)
        this.setData({ autoPlayTimer: null })
      }
    },

    /**
     * 切换自动播放
     */
    toggleAutoPlay() {
      if (this.data.autoPlayTimer) {
        this.stopAutoPlay()
      } else {
        this.startAutoPlay()
      }
    },

    /**
     * 跳过引导
     */
    skipGuide() {
      this.hide()
      
      // 触发跳过事件
      this.triggerEvent('skip', {
        currentStep: this.data.currentStep
      })
    },

    /**
     * 标记引导有用
     */
    markHelpful() {
      // 触发有用事件
      this.triggerEvent('helpful', {
        currentStep: this.data.currentStep,
        helpful: true
      })
      
      this.hide()
    },

    /**
     * 标记引导无用
     */
    markNotHelpful() {
      // 触发无用事件
      this.triggerEvent('helpful', {
        currentStep: this.data.currentStep,
        helpful: false
      })
      
      this.hide()
    },

    /**
     * 获取当前步骤数据
     */
    getCurrentStep() {
      const steps = this.data.steps
      const currentStep = this.data.currentStep
      
      if (steps && steps.length > 0 && currentStep < steps.length) {
        return steps[currentStep]
      }
      
      return null
    },

    /**
     * 获取进度百分比
     */
    getProgressPercent() {
      const totalSteps = this.data.steps.length
      const currentStep = this.data.currentStep
      
      if (totalSteps === 0) return 0
      
      return ((currentStep + 1) / totalSteps) * 100
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    },
    
    detached() {
      // 清理定时器
      this.stopAutoPlay()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.show()
      } else {
        this.hide()
      }
    }
  }
})
