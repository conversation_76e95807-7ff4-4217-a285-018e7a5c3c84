/**
 * 骨架屏组件
 * Task 2.2: Loading组件优化升级 - 骨架屏组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    
    // 模板类型
    template: {
      type: String,
      value: 'list'
    },
    
    // 行数
    rows: {
      type: Number,
      value: 5
    },
    
    // 是否显示头像
    avatar: {
      type: Boolean,
      value: true
    },
    
    // 是否显示标题
    title: {
      type: Boolean,
      value: true
    },
    
    // 是否显示内容
    content: {
      type: Boolean,
      value: true
    },
    
    // 是否显示图片
    image: {
      type: Boolean,
      value: false
    },
    
    // 是否显示操作按钮
    actions: {
      type: Boolean,
      value: false
    },
    
    // 动画类型
    animation: {
      type: String,
      value: 'shimmer'
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: '#f0f0f0'
    },
    
    // 高亮色
    highlightColor: {
      type: String,
      value: '#e0e0e0'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 骨架屏项目列表
    skeletonItems: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 生成骨架屏项目
     */
    generateSkeletonItems() {
      const items = []
      
      for (let i = 0; i < this.data.rows; i++) {
        items.push({
          id: i,
          showAvatar: this.data.avatar,
          showTitle: this.data.title,
          showContent: this.data.content,
          showImage: this.data.image,
          showActions: this.data.actions,
          titleWidth: this.getRandomWidth(60, 80),
          contentWidth: this.getRandomWidth(80, 95),
          contentLines: this.getRandomLines()
        })
      }
      
      this.setData({ skeletonItems: items })
    },

    /**
     * 获取随机宽度
     * @param {number} min 最小宽度百分比
     * @param {number} max 最大宽度百分比
     */
    getRandomWidth(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min
    },

    /**
     * 获取随机行数
     */
    getRandomLines() {
      const lines = []
      const lineCount = Math.floor(Math.random() * 3) + 1 // 1-3行
      
      for (let i = 0; i < lineCount; i++) {
        lines.push({
          width: this.getRandomWidth(70, 95)
        })
      }
      
      return lines
    },

    /**
     * 获取模板样式类
     */
    getTemplateClass() {
      return `skeleton-${this.data.template}`
    },

    /**
     * 获取动画样式类
     */
    getAnimationClass() {
      return `animation-${this.data.animation}`
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.generateSkeletonItems()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'rows, avatar, title, content, image, actions': function() {
      this.generateSkeletonItems()
    }
  }
})
