<!--
  骨架屏组件模板
  Task 2.2: Loading组件优化升级 - 骨架屏模板
-->

<view class="skeleton-container {{getTemplateClass()}}" wx:if="{{visible}}">
  <view 
    class="skeleton-item {{getAnimationClass()}}"
    wx:for="{{skeletonItems}}"
    wx:key="id"
  >
    <!-- 头像 -->
    <view wx:if="{{item.showAvatar}}" class="skeleton-avatar"></view>
    
    <!-- 内容区域 -->
    <view class="skeleton-content">
      <!-- 标题 -->
      <view 
        wx:if="{{item.showTitle}}" 
        class="skeleton-title"
        style="width: {{item.titleWidth}}%"
      ></view>
      
      <!-- 文本内容 -->
      <view wx:if="{{item.showContent}}">
        <view 
          class="skeleton-text"
          wx:for="{{item.contentLines}}"
          wx:for-item="line"
          wx:key="width"
          style="width: {{line.width}}%"
        ></view>
      </view>
      
      <!-- 操作按钮 -->
      <view wx:if="{{item.showActions}}" class="skeleton-actions">
        <view class="skeleton-action"></view>
        <view class="skeleton-action"></view>
      </view>
    </view>
    
    <!-- 图片 (卡片模板) -->
    <view wx:if="{{item.showImage && template === 'card'}}" class="skeleton-image"></view>
  </view>
</view>

<wxs module="utils">
  var getTemplateClass = function(template) {
    return 'skeleton-' + template
  }
  
  var getAnimationClass = function(animation) {
    return 'animation-' + animation
  }
  
  module.exports = {
    getTemplateClass: getTemplateClass,
    getAnimationClass: getAnimationClass
  }
</wxs>
