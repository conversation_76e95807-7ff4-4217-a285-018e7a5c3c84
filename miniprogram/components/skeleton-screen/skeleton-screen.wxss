/* components/skeleton-screen/skeleton-screen.wxss - 骨架屏组件样式 */
/* Task 2.2: Loading组件优化升级 - 骨架屏样式 */

/* 骨架屏容器 */
.skeleton-container {
  width: 100%;
  background-color: #fff;
}

/* 骨架屏项目 */
.skeleton-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: flex-start;
}

.skeleton-item:last-child {
  border-bottom: none;
}

/* 头像骨架 */
.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 内容区域 */
.skeleton-content {
  flex: 1;
  min-width: 0;
}

/* 标题骨架 */
.skeleton-title {
  height: 32rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

/* 文本骨架 */
.skeleton-text {
  height: 24rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.skeleton-text:last-child {
  margin-bottom: 0;
}

/* 图片骨架 */
.skeleton-image {
  width: 100%;
  height: 200rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

/* 操作按钮骨架 */
.skeleton-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}

.skeleton-action {
  width: 120rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

/* 列表模板 */
.skeleton-list .skeleton-item {
  padding: 24rpx 20rpx;
}

.skeleton-list .skeleton-avatar {
  width: 88rpx;
  height: 88rpx;
}

/* 卡片模板 */
.skeleton-card {
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.skeleton-card .skeleton-item {
  padding: 24rpx;
  border-bottom: none;
  flex-direction: column;
  align-items: stretch;
}

.skeleton-card .skeleton-image {
  margin: -24rpx -24rpx 20rpx -24rpx;
  border-radius: 0;
  height: 300rpx;
}

/* 个人资料模板 */
.skeleton-profile .skeleton-item {
  padding: 40rpx 20rpx;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.skeleton-profile .skeleton-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 0;
  margin-bottom: 20rpx;
}

.skeleton-profile .skeleton-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skeleton-profile .skeleton-title {
  width: 200rpx;
  margin-bottom: 20rpx;
}

.skeleton-profile .skeleton-text {
  width: 300rpx;
}

/* 文章模板 */
.skeleton-article .skeleton-item {
  padding: 30rpx 20rpx;
  flex-direction: column;
}

.skeleton-article .skeleton-title {
  height: 40rpx;
  margin-bottom: 20rpx;
}

.skeleton-article .skeleton-text {
  height: 28rpx;
  margin-bottom: 16rpx;
}

/* 动画效果 */
.skeleton-item.animation-shimmer .skeleton-avatar,
.skeleton-item.animation-shimmer .skeleton-title,
.skeleton-item.animation-shimmer .skeleton-text,
.skeleton-item.animation-shimmer .skeleton-image,
.skeleton-item.animation-shimmer .skeleton-action {
  position: relative;
  overflow: hidden;
}

.skeleton-item.animation-shimmer .skeleton-avatar::after,
.skeleton-item.animation-shimmer .skeleton-title::after,
.skeleton-item.animation-shimmer .skeleton-text::after,
.skeleton-item.animation-shimmer .skeleton-image::after,
.skeleton-item.animation-shimmer .skeleton-action::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 脉冲动画 */
.skeleton-item.animation-pulse .skeleton-avatar,
.skeleton-item.animation-pulse .skeleton-title,
.skeleton-item.animation-pulse .skeleton-text,
.skeleton-item.animation-pulse .skeleton-image,
.skeleton-item.animation-pulse .skeleton-action {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 波浪动画 */
.skeleton-item.animation-wave .skeleton-avatar,
.skeleton-item.animation-wave .skeleton-title,
.skeleton-item.animation-wave .skeleton-text,
.skeleton-item.animation-wave .skeleton-image,
.skeleton-item.animation-wave .skeleton-action {
  animation: wave 1.5s ease-in-out infinite;
}

.skeleton-item.animation-wave .skeleton-title {
  animation-delay: 0.1s;
}

.skeleton-item.animation-wave .skeleton-text:nth-child(1) {
  animation-delay: 0.2s;
}

.skeleton-item.animation-wave .skeleton-text:nth-child(2) {
  animation-delay: 0.3s;
}

.skeleton-item.animation-wave .skeleton-text:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateY(-4rpx);
    opacity: 0.7;
  }
}

/* 渐变动画 */
.skeleton-item.animation-gradient .skeleton-avatar,
.skeleton-item.animation-gradient .skeleton-title,
.skeleton-item.animation-gradient .skeleton-text,
.skeleton-item.animation-gradient .skeleton-image,
.skeleton-item.animation-gradient .skeleton-action {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: gradient 1.5s ease-in-out infinite;
}

@keyframes gradient {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .skeleton-item {
    padding: 16rpx;
  }
  
  .skeleton-avatar {
    width: 60rpx;
    height: 60rpx;
    margin-right: 16rpx;
  }
  
  .skeleton-title {
    height: 28rpx;
  }
  
  .skeleton-text {
    height: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .skeleton-container {
    background-color: #2a2a2a;
  }
  
  .skeleton-avatar,
  .skeleton-title,
  .skeleton-text,
  .skeleton-image,
  .skeleton-action {
    background-color: #444;
  }
  
  .skeleton-item {
    border-bottom-color: #444;
  }
  
  .skeleton-item.animation-shimmer .skeleton-avatar::after,
  .skeleton-item.animation-shimmer .skeleton-title::after,
  .skeleton-item.animation-shimmer .skeleton-text::after,
  .skeleton-item.animation-shimmer .skeleton-image::after,
  .skeleton-item.animation-shimmer .skeleton-action::after {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
  
  .skeleton-item.animation-gradient .skeleton-avatar,
  .skeleton-item.animation-gradient .skeleton-title,
  .skeleton-item.animation-gradient .skeleton-text,
  .skeleton-item.animation-gradient .skeleton-image,
  .skeleton-item.animation-gradient .skeleton-action {
    background: linear-gradient(
      90deg,
      #444 25%,
      #555 50%,
      #444 75%
    );
  }
}
