/**
 * 增强版Loading组件
 * Task 2.2: Loading组件优化升级 - Loading组件
 */

const loadingManager = require('../../utils/loading-manager')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    
    // Loading类型
    type: {
      type: String,
      value: 'spinner'
    },
    
    // 尺寸
    size: {
      type: String,
      value: 'medium'
    },
    
    // 颜色
    color: {
      type: String,
      value: '#667eea'
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: 'rgba(255, 255, 255, 0.9)'
    },
    
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: true
    },
    
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    
    // 进度百分比
    progress: {
      type: Number,
      value: 0
    },
    
    // 是否显示进度
    showProgress: {
      type: Boolean,
      value: false
    },
    
    // 层级
    zIndex: {
      type: Number,
      value: 9999
    },
    
    // 骨架屏模板
    template: {
      type: String,
      value: 'list'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画数据
    animationData: {},
    
    // 显示状态
    showLoading: false,
    
    // 样式配置
    loadingStyle: '',
    
    // 尺寸配置
    sizeConfig: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示Loading
     */
    show() {
      this.setData({ showLoading: true })
      
      // 创建进入动画
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })
      
      animation.opacity(1).scale(1).step()
      
      this.setData({
        animationData: animation.export()
      })
      
      // 触发显示事件
      this.triggerEvent('show', {
        type: this.data.type,
        text: this.data.text
      })
    },

    /**
     * 隐藏Loading
     */
    hide() {
      // 创建退出动画
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-in'
      })
      
      animation.opacity(0).scale(0.8).step()
      
      this.setData({
        animationData: animation.export()
      })
      
      // 延迟隐藏
      setTimeout(() => {
        this.setData({ showLoading: false })
        
        // 触发隐藏事件
        this.triggerEvent('hide', {
          type: this.data.type,
          text: this.data.text
        })
      }, 300)
    },

    /**
     * 更新样式
     */
    updateStyle() {
      const sizeConfig = loadingManager.config.sizes[this.data.size] || loadingManager.config.sizes.medium
      
      // 构建样式字符串
      let loadingStyle = `z-index: ${this.data.zIndex}; `
      loadingStyle += `background-color: ${this.data.backgroundColor}; `
      loadingStyle += `color: ${this.data.color}; `
      
      this.setData({
        loadingStyle: loadingStyle,
        sizeConfig: sizeConfig
      })
    },

    /**
     * 获取Loading图标
     */
    getLoadingIcon() {
      const typeConfig = loadingManager.config.types[this.data.type]
      return typeConfig ? typeConfig.icon : '⏳'
    },

    /**
     * 获取动画类名
     */
    getAnimationClass() {
      const typeConfig = loadingManager.config.types[this.data.type]
      return typeConfig ? typeConfig.animation : 'spin'
    },

    /**
     * 点击遮罩
     */
    onMaskTap() {
      // 触发遮罩点击事件
      this.triggerEvent('masktap', {
        type: this.data.type
      })
    },

    /**
     * 更新进度
     * @param {number} progress 进度百分比
     */
    updateProgress(progress) {
      this.setData({ progress: Math.max(0, Math.min(100, progress)) })
      
      // 触发进度更新事件
      this.triggerEvent('progress', {
        progress: this.data.progress
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化样式
      this.updateStyle()
      
      // 设置初始动画状态
      const animation = wx.createAnimation({
        duration: 0
      })
      
      animation.opacity(0).scale(0.8).step()
      
      this.setData({
        animationData: animation.export()
      })
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.show()
      } else {
        this.hide()
      }
    },
    
    'type, size, color, backgroundColor, zIndex': function() {
      this.updateStyle()
    },
    
    'progress': function(progress) {
      if (this.data.showProgress) {
        this.updateProgress(progress)
      }
    }
  }
})
