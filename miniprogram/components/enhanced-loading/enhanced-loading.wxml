<!--
  增强版Loading组件模板
  Task 2.2: Loading组件优化升级 - Loading模板
-->

<view wx:if="{{showLoading}}">
  <!-- 遮罩层 -->
  <view 
    wx:if="{{mask}}" 
    class="loading-mask"
    bindtap="onMaskTap"
  ></view>

  <!-- Loading容器 -->
  <view 
    class="loading-container type-{{type}} size-{{size}}"
    style="{{loadingStyle}}"
    animation="{{animationData}}"
  >
    <!-- 骨架屏 -->
    <skeleton-screen
      wx:if="{{type === 'skeleton'}}"
      visible="{{true}}"
      template="{{template}}"
      rows="{{5}}"
      avatar="{{true}}"
      title="{{true}}"
      content="{{true}}"
      animation="shimmer"
    ></skeleton-screen>
    
    <!-- 普通Loading -->
    <view wx:else>
      <!-- Loading图标 -->
      <view class="loading-icon {{getAnimationClass()}}">
        {{getLoadingIcon()}}
      </view>
      
      <!-- Loading文本 -->
      <view wx:if="{{text}}" class="loading-text">{{text}}</view>
      
      <!-- 进度条 -->
      <view wx:if="{{showProgress}}" class="loading-progress">
        <view 
          class="loading-progress-bar"
          style="width: {{progress}}%"
        ></view>
      </view>
      
      <!-- 进度文本 -->
      <view wx:if="{{showProgress}}" class="loading-progress-text">
        {{progress}}%
      </view>
    </view>
  </view>
</view>

<wxs module="utils">
  var getLoadingIcon = function(type) {
    var icons = {
      'spinner': '⏳',
      'dots': '●●●',
      'bars': '|||',
      'circle': '○',
      'progress': '▓'
    }
    return icons[type] || '⏳'
  }
  
  var getAnimationClass = function(type) {
    var animations = {
      'spinner': 'spin',
      'dots': 'pulse',
      'bars': 'wave',
      'circle': 'rotate',
      'progress': 'pulse'
    }
    return animations[type] || 'spin'
  }
  
  module.exports = {
    getLoadingIcon: getLoadingIcon,
    getAnimationClass: getAnimationClass
  }
</wxs>
