/* components/enhanced-loading/enhanced-loading.wxss - 增强版Loading组件样式 */
/* Task 2.2: Loading组件优化升级 - Loading样式 */

/* 遮罩层 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.3);
}

/* Loading容器 */
.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 200rpx;
  min-height: 120rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

/* Loading图标 */
.loading-icon {
  font-size: 48rpx;
  margin-bottom: 20rpx;
  line-height: 1;
}

/* 旋转动画 */
.loading-icon.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.loading-icon.pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* 波浪动画 */
.loading-icon.wave {
  animation: wave 1.5s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: translateY(0); }
  25% { transform: translateY(-8rpx); }
  75% { transform: translateY(8rpx); }
}

/* 旋转动画 */
.loading-icon.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Loading文本 */
.loading-text {
  font-size: 28rpx;
  color: inherit;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

/* 进度条 */
.loading-progress {
  width: 100%;
  max-width: 300rpx;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  position: relative;
}

.loading-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 进度文本 */
.loading-progress-text {
  font-size: 24rpx;
  color: inherit;
  opacity: 0.8;
}

/* 不同尺寸 */
.loading-container.size-small {
  min-width: 160rpx;
  min-height: 100rpx;
  padding: 30rpx;
}

.loading-container.size-small .loading-icon {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.loading-container.size-small .loading-text {
  font-size: 24rpx;
}

.loading-container.size-large {
  min-width: 240rpx;
  min-height: 140rpx;
  padding: 50rpx;
}

.loading-container.size-large .loading-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}

.loading-container.size-large .loading-text {
  font-size: 32rpx;
}

/* 不同类型的特殊样式 */
.loading-container.type-spinner .loading-icon {
  border: 4rpx solid rgba(102, 126, 234, 0.3);
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: block;
  font-size: 0;
}

.loading-container.type-dots .loading-icon {
  display: flex;
  gap: 8rpx;
  font-size: 0;
}

.loading-container.type-dots .loading-icon::before,
.loading-container.type-dots .loading-icon::after,
.loading-container.type-dots .loading-icon {
  content: '';
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #667eea;
  animation: dotPulse 1.4s ease-in-out infinite;
}

.loading-container.type-dots .loading-icon::before {
  animation-delay: -0.32s;
}

.loading-container.type-dots .loading-icon::after {
  animation-delay: -0.16s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-container.type-bars .loading-icon {
  display: flex;
  gap: 4rpx;
  align-items: flex-end;
  font-size: 0;
}

.loading-container.type-bars .loading-icon::before,
.loading-container.type-bars .loading-icon::after,
.loading-container.type-bars .loading-icon {
  content: '';
  width: 8rpx;
  height: 32rpx;
  background-color: #667eea;
  animation: barWave 1.2s ease-in-out infinite;
}

.loading-container.type-bars .loading-icon::before {
  animation-delay: -0.4s;
}

.loading-container.type-bars .loading-icon::after {
  animation-delay: -0.2s;
}

@keyframes barWave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* 圆环加载 */
.loading-container.type-circle .loading-icon {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  position: relative;
  font-size: 0;
}

.loading-container.type-circle .loading-icon::before {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid transparent;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: circleRotate 1s linear infinite;
}

@keyframes circleRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .loading-container {
    min-width: 180rpx;
    min-height: 100rpx;
    padding: 30rpx;
  }
  
  .loading-icon {
    font-size: 40rpx;
    margin-bottom: 16rpx;
  }
  
  .loading-text {
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .loading-mask {
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .loading-container {
    background-color: rgba(42, 42, 42, 0.9);
    color: #fff;
  }
  
  .loading-progress {
    background-color: rgba(255, 255, 255, 0.2);
  }
}
