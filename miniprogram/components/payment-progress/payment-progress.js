/**
 * 支付进度指示器组件
 * Task 1.8: 支付体验优化 - 进度指示器组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前步骤
    currentStep: {
      type: Number,
      value: 0
    },
    
    // 步骤列表
    steps: {
      type: Array,
      value: []
    },
    
    // 是否显示预估时间
    showEstimatedTime: {
      type: Boolean,
      value: true
    },
    
    // 预估时间
    estimatedTime: {
      type: String,
      value: ''
    },
    
    // 主题色
    themeColor: {
      type: String,
      value: '#667eea'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画状态
    animating: false,
    
    // 进度百分比
    progressPercent: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新进度
     * @param {number} step 当前步骤
     */
    updateProgress(step) {
      const totalSteps = this.data.steps.length
      const percent = totalSteps > 0 ? (step / (totalSteps - 1)) * 100 : 0
      
      this.setData({
        animating: true,
        progressPercent: percent
      })
      
      // 触发步骤动画
      this.animateStep(step)
      
      // 动画结束后重置状态
      setTimeout(() => {
        this.setData({ animating: false })
      }, 300)
    },

    /**
     * 动画化步骤
     * @param {number} stepIndex 步骤索引
     */
    animateStep(stepIndex) {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })
      
      // 缩放动画
      animation.scale(1.2).step()
      animation.scale(1).step()
      
      this.setData({
        [`stepAnimation${stepIndex}`]: animation.export()
      })
    },

    /**
     * 获取步骤状态
     * @param {number} index 步骤索引
     */
    getStepStatus(index) {
      const current = this.data.currentStep
      
      if (index < current) {
        return 'completed'
      } else if (index === current) {
        return 'active'
      } else {
        return 'pending'
      }
    },

    /**
     * 获取步骤样式
     * @param {number} index 步骤索引
     */
    getStepStyle(index) {
      const status = this.getStepStatus(index)
      const themeColor = this.data.themeColor
      
      switch (status) {
        case 'completed':
          return `background-color: ${themeColor}; color: #fff;`
        case 'active':
          return `background-color: ${themeColor}; color: #fff; box-shadow: 0 0 0 4px ${themeColor}20;`
        case 'pending':
          return 'background-color: #f0f0f0; color: #999;'
        default:
          return ''
      }
    },

    /**
     * 获取连接线样式
     * @param {number} index 连接线索引
     */
    getLineStyle(index) {
      const current = this.data.currentStep
      const themeColor = this.data.themeColor
      
      if (index < current) {
        return `background-color: ${themeColor};`
      } else {
        return 'background-color: #f0f0f0;'
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始化进度
      this.updateProgress(this.data.currentStep)
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'currentStep': function(newStep) {
      this.updateProgress(newStep)
    }
  }
})
