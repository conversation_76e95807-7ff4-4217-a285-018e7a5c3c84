/* components/payment-progress/payment-progress.wxss - 支付进度指示器样式 */
/* Task 1.8: 支付体验优化 - 进度指示器样式 */

.progress-container {
  padding: 40rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 进度头部 */
.progress-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.progress-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 步骤容器 */
.steps-container {
  position: relative;
  margin-bottom: 30rpx;
}

.steps-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* 进度线 */
.progress-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #f0f0f0;
  transform: translateY(-50%);
  z-index: 1;
}

.progress-line-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

/* 步骤项 */
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
}

.step-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.step-circle.completed {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  transform: scale(1);
}

.step-circle.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.2);
  animation: pulse 2s infinite;
}

.step-circle.pending {
  background-color: #f0f0f0;
  color: #999;
  border: 2rpx solid #e0e0e0;
}

.step-circle.animating {
  animation: stepBounce 0.6s ease-out;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 20rpx rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

@keyframes stepBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 步骤标签 */
.step-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.3;
  min-height: 32rpx;
  transition: color 0.3s ease;
}

.step-item.completed .step-label {
  color: #667eea;
  font-weight: 500;
}

.step-item.active .step-label {
  color: #667eea;
  font-weight: 600;
}

/* 预估时间 */
.estimated-time {
  text-align: center;
  margin-top: 20rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #f8f9ff, #f0f4ff);
  border-radius: 16rpx;
  border: 1rpx solid #e6edff;
}

.time-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.time-value {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}

/* 加载动画 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #667eea;
  margin: 0 4rpx;
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 成功状态 */
.progress-success {
  text-align: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #2ed573, #1dd1a1);
  border-radius: 20rpx;
  color: #fff;
  margin-top: 20rpx;
}

.success-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.success-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 错误状态 */
.progress-error {
  text-align: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4757, #ff3742);
  border-radius: 20rpx;
  color: #fff;
  margin-top: 20rpx;
}

.error-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .progress-container {
    margin: 10rpx;
    padding: 30rpx 20rpx;
  }
  
  .step-circle {
    width: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
  }
  
  .step-label {
    font-size: 22rpx;
  }
  
  .progress-title {
    font-size: 28rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .progress-container {
    background-color: #2a2a2a;
  }
  
  .progress-title {
    color: #fff;
  }
  
  .progress-desc,
  .step-label {
    color: #ccc;
  }
  
  .step-circle.pending {
    background-color: #444;
    color: #999;
    border-color: #555;
  }
  
  .estimated-time {
    background: linear-gradient(135deg, #333, #444);
    border-color: #555;
  }
  
  .time-label {
    color: #ccc;
  }
}
