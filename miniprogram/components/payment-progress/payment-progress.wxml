<!--
  支付进度指示器组件模板
  Task 1.8: 支付体验优化 - 进度指示器模板
-->

<view class="progress-container">
  <!-- 进度头部 -->
  <view class="progress-header">
    <view class="progress-title">支付进行中</view>
    <view class="progress-desc">请稍候，正在为您处理支付...</view>
  </view>

  <!-- 步骤容器 -->
  <view class="steps-container">
    <!-- 进度线 -->
    <view class="progress-line">
      <view 
        class="progress-line-fill"
        style="width: {{progressPercent}}%"
      ></view>
    </view>

    <!-- 步骤列表 -->
    <view class="steps-list">
      <view 
        class="step-item {{getStepStatus(index)}}"
        wx:for="{{steps}}"
        wx:key="id"
        wx:for-index="index"
      >
        <view 
          class="step-circle {{getStepStatus(index)}} {{animating && index === currentStep ? 'animating' : ''}}"
          animation="{{stepAnimation[index]}}"
        >
          {{item.icon}}
        </view>
        <view class="step-label">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 预估时间 -->
  <view class="estimated-time" wx:if="{{showEstimatedTime && estimatedTime}}">
    <view class="time-label">预计剩余时间</view>
    <view class="time-value">{{estimatedTime}}</view>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-indicator" wx:if="{{currentStep < steps.length - 1}}">
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
    <view class="loading-dot"></view>
  </view>

  <!-- 成功状态 -->
  <view class="progress-success" wx:if="{{currentStep >= steps.length - 1}}">
    <view class="success-icon">🎉</view>
    <view class="success-text">支付处理完成</view>
  </view>
</view>

<wxs module="utils">
  var getStepStatus = function(index, currentStep) {
    if (index < currentStep) {
      return 'completed'
    } else if (index === currentStep) {
      return 'active'
    } else {
      return 'pending'
    }
  }
  
  module.exports = {
    getStepStatus: getStepStatus
  }
</wxs>
