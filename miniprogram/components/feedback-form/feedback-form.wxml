<!--
  反馈表单组件模板
  Task 1.4: 用户反馈收集系统 - 表单模板
-->

<view class="feedback-form {{show ? '' : 'hidden'}}">
  <!-- 遮罩层 -->
  <view class="form-mask" bindtap="onMaskTap"></view>
  
  <!-- 表单容器 -->
  <view class="form-container">
    <!-- 表单头部 -->
    <view class="form-header">
      <view class="form-title">问题反馈</view>
      <view class="close-btn" bindtap="closeForm">✕</view>
    </view>
    
    <!-- 表单内容 -->
    <scroll-view class="form-content" scroll-y="true">
      <!-- 问题分类 -->
      <view class="form-item">
        <view class="form-label required">问题分类</view>
        <view 
          class="category-selector {{errors.category ? 'error' : ''}}"
          bindtap="showCategoryPicker"
        >
          <view class="category-display">
            <view class="category-info" wx:if="{{formData.category}}">
              <view class="category-icon">
                {{categories.find(cat => cat.id === formData.category).icon}}
              </view>
              <view class="category-text">
                <view class="category-name">
                  {{categories.find(cat => cat.id === formData.category).name}}
                </view>
                <view class="category-desc">
                  {{categories.find(cat => cat.id === formData.category).description}}
                </view>
              </view>
            </view>
            <view class="category-placeholder" wx:else>
              请选择问题分类
            </view>
            <view class="category-arrow">▼</view>
          </view>
        </view>
        <view class="error-message" wx:if="{{errors.category}}">
          {{errors.category}}
        </view>
      </view>

      <!-- 问题标题 -->
      <view class="form-item">
        <view class="form-label">问题标题</view>
        <input 
          class="form-input {{errors.title ? 'error' : ''}}"
          placeholder="简要描述您遇到的问题"
          value="{{formData.title}}"
          maxlength="50"
          bindinput="onTitleInput"
        />
        <view class="error-message" wx:if="{{errors.title}}">
          {{errors.title}}
        </view>
      </view>

      <!-- 问题描述 -->
      <view class="form-item">
        <view class="form-label required">问题描述</view>
        <textarea 
          class="form-input form-textarea {{errors.description ? 'error' : ''}}"
          placeholder="请详细描述您遇到的问题，包括操作步骤、出现的错误等（至少10个字符）"
          value="{{formData.description}}"
          maxlength="1000"
          bindinput="onDescriptionInput"
        />
        <view class="char-count {{formData.description.length > 900 ? 'warning' : ''}} {{formData.description.length >= 1000 ? 'error' : ''}}">
          {{formData.description.length}}/1000
        </view>
        <view class="error-message" wx:if="{{errors.description}}">
          {{errors.description}}
        </view>
      </view>

      <!-- 满意度评分 -->
      <view class="form-item" wx:if="{{showRating}}">
        <view class="form-label">满意度评分</view>
        <view class="rating-container">
          <view 
            class="rating-star {{index < formData.rating ? 'active' : ''}}"
            wx:for="{{[1,2,3,4,5]}}"
            wx:key="*this"
            data-rating="{{item}}"
            bindtap="onRatingChange"
          >
            ★
          </view>
          <view class="rating-text">
            <text wx:if="{{formData.rating === 0}}">未评分</text>
            <text wx:elif="{{formData.rating === 1}}">很不满意</text>
            <text wx:elif="{{formData.rating === 2}}">不满意</text>
            <text wx:elif="{{formData.rating === 3}}">一般</text>
            <text wx:elif="{{formData.rating === 4}}">满意</text>
            <text wx:elif="{{formData.rating === 5}}">很满意</text>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="form-item" wx:if="{{showContact}}">
        <view class="form-label">联系方式</view>
        <input 
          class="form-input {{errors.contact ? 'error' : ''}}"
          placeholder="邮箱或手机号（选填，便于我们联系您）"
          value="{{formData.contact}}"
          bindinput="onContactInput"
        />
        <view class="error-message" wx:if="{{errors.contact}}">
          {{errors.contact}}
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <view class="form-label">相关截图</view>
        <view class="image-upload">
          <!-- 已上传的图片 -->
          <view 
            class="image-item"
            wx:for="{{uploadedImages}}"
            wx:key="*this"
          >
            <image 
              class="image-preview"
              src="{{item}}"
              mode="aspectFill"
              data-index="{{index}}"
              bindtap="previewImage"
            />
            <view 
              class="image-delete"
              data-index="{{index}}"
              bindtap="deleteImage"
              catchtap="deleteImage"
            >
              ✕
            </view>
          </view>
          
          <!-- 上传按钮 -->
          <view 
            class="upload-btn"
            wx:if="{{uploadedImages.length < maxImages}}"
            bindtap="chooseImage"
          >
            <view class="upload-icon">📷</view>
            <view>添加图片</view>
          </view>
        </view>
        <view class="error-message" style="margin-top: 16rpx; color: #666; font-size: 24rpx;">
          最多可上传{{maxImages}}张图片，支持问题截图
        </view>
      </view>
    </scroll-view>
    
    <!-- 表单底部 -->
    <view class="form-footer">
      <view 
        class="submit-btn {{submitting ? 'loading disabled' : ''}}"
        bindtap="submitFeedback"
      >
        <view class="loading-icon" wx:if="{{submitting}}">⏳</view>
        <text>{{submitting ? '提交中...' : '提交反馈'}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 分类选择器 -->
<view class="category-picker" wx:if="{{showCategoryPicker}}">
  <view class="picker-content">
    <view class="picker-header">
      <view class="picker-title">选择问题分类</view>
    </view>
    <scroll-view class="category-list" scroll-y="true">
      <view 
        class="category-item"
        wx:for="{{categories}}"
        wx:key="id"
        data-category="{{item.id}}"
        bindtap="onCategorySelect"
      >
        <view class="category-icon" style="color: {{item.color}}">
          {{item.icon}}
        </view>
        <view class="category-text">
          <view class="category-name">{{item.name}}</view>
          <view class="category-desc">{{item.description}}</view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view class="form-mask" bindtap="hideCategoryPicker"></view>
</view>
