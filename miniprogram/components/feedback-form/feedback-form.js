/**
 * 反馈表单组件
 * Task 1.4: 用户反馈收集系统 - 表单组件
 * 功能：反馈表单、问题分类、图片上传
 */

const feedbackManager = require('../../utils/feedback')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示表单
    show: {
      type: Boolean,
      value: false
    },
    // 预设分类
    defaultCategory: {
      type: String,
      value: ''
    },
    // 预设页面信息
    pageInfo: {
      type: Object,
      value: null
    },
    // 是否显示评分
    showRating: {
      type: Boolean,
      value: true
    },
    // 是否显示联系方式
    showContact: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      category: '',
      title: '',
      description: '',
      rating: 0,
      contact: '',
      attachments: []
    },
    
    // 分类列表
    categories: [],
    
    // UI状态
    submitting: false,
    showCategoryPicker: false,
    
    // 图片上传
    uploadedImages: [],
    maxImages: 5,
    
    // 验证状态
    errors: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    init() {
      // 加载分类列表
      const categories = feedbackManager.getCategories()
      this.setData({ categories })
      
      // 设置默认分类
      if (this.data.defaultCategory) {
        this.setData({
          'formData.category': this.data.defaultCategory
        })
      }
      
      // 设置页面信息
      if (this.data.pageInfo) {
        this.setData({
          'formData.page': this.data.pageInfo.route || '',
          'formData.metadata': this.data.pageInfo
        })
      }
    },

    /**
     * 显示分类选择器
     */
    showCategoryPicker() {
      this.setData({ showCategoryPicker: true })
    },

    /**
     * 隐藏分类选择器
     */
    hideCategoryPicker() {
      this.setData({ showCategoryPicker: false })
    },

    /**
     * 选择分类
     * @param {Object} e 事件对象
     */
    onCategorySelect(e) {
      const categoryId = e.currentTarget.dataset.category
      
      this.setData({
        'formData.category': categoryId,
        showCategoryPicker: false
      })
      
      // 清除分类相关错误
      this.clearError('category')
    },

    /**
     * 输入标题
     * @param {Object} e 事件对象
     */
    onTitleInput(e) {
      this.setData({
        'formData.title': e.detail.value
      })
      
      this.clearError('title')
    },

    /**
     * 输入描述
     * @param {Object} e 事件对象
     */
    onDescriptionInput(e) {
      const value = e.detail.value
      
      this.setData({
        'formData.description': value
      })
      
      // 实时验证描述长度
      if (value.length >= 10) {
        this.clearError('description')
      }
    },

    /**
     * 输入联系方式
     * @param {Object} e 事件对象
     */
    onContactInput(e) {
      this.setData({
        'formData.contact': e.detail.value
      })
    },

    /**
     * 评分变化
     * @param {Object} e 事件对象
     */
    onRatingChange(e) {
      const rating = parseInt(e.currentTarget.dataset.rating)
      
      this.setData({
        'formData.rating': rating
      })
    },

    /**
     * 选择图片
     */
    chooseImage() {
      const remainingCount = this.data.maxImages - this.data.uploadedImages.length
      
      if (remainingCount <= 0) {
        wx.showToast({
          title: `最多只能上传${this.data.maxImages}张图片`,
          icon: 'none'
        })
        return
      }
      
      wx.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadImages(res.tempFilePaths)
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 上传图片
     * @param {Array} filePaths 图片路径数组
     */
    async uploadImages(filePaths) {
      wx.showLoading({ title: '上传中...' })
      
      try {
        const uploadPromises = filePaths.map(filePath => this.uploadSingleImage(filePath))
        const uploadedUrls = await Promise.all(uploadPromises)
        
        // 更新上传的图片列表
        const newImages = [...this.data.uploadedImages, ...uploadedUrls]
        this.setData({ uploadedImages: newImages })
        
        // 更新表单数据
        this.setData({
          'formData.attachments': newImages
        })
        
        wx.hideLoading()
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } catch (error) {
        wx.hideLoading()
        console.error('上传图片失败:', error)
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    },

    /**
     * 上传单张图片
     * @param {string} filePath 图片路径
     */
    uploadSingleImage(filePath) {
      return new Promise((resolve, reject) => {
        // 模拟上传过程
        setTimeout(() => {
          // 实际项目中这里应该调用真实的上传API
          /*
          wx.uploadFile({
            url: 'https://api.example.com/upload',
            filePath: filePath,
            name: 'file',
            success: (res) => {
              const data = JSON.parse(res.data)
              resolve(data.url)
            },
            fail: reject
          })
          */
          
          // 模拟返回上传后的URL
          resolve(filePath)
        }, 1000)
      })
    },

    /**
     * 删除图片
     * @param {Object} e 事件对象
     */
    deleteImage(e) {
      const index = e.currentTarget.dataset.index
      const images = [...this.data.uploadedImages]
      
      images.splice(index, 1)
      
      this.setData({
        uploadedImages: images,
        'formData.attachments': images
      })
    },

    /**
     * 预览图片
     * @param {Object} e 事件对象
     */
    previewImage(e) {
      const index = e.currentTarget.dataset.index
      const current = this.data.uploadedImages[index]
      
      wx.previewImage({
        current: current,
        urls: this.data.uploadedImages
      })
    },

    /**
     * 验证表单
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}
      
      // 验证分类
      if (!formData.category) {
        errors.category = '请选择问题分类'
      }
      
      // 验证描述
      if (!formData.description || formData.description.trim().length < 10) {
        errors.description = '请详细描述您遇到的问题（至少10个字符）'
      }
      
      if (formData.description.length > 1000) {
        errors.description = '问题描述不能超过1000个字符'
      }
      
      // 验证联系方式格式（如果填写了）
      if (formData.contact && formData.contact.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        const phoneRegex = /^1[3-9]\d{9}$/
        
        if (!emailRegex.test(formData.contact) && !phoneRegex.test(formData.contact)) {
          errors.contact = '请输入有效的邮箱或手机号'
        }
      }
      
      this.setData({ errors })
      
      return Object.keys(errors).length === 0
    },

    /**
     * 清除错误信息
     * @param {string} field 字段名
     */
    clearError(field) {
      const errors = { ...this.data.errors }
      delete errors[field]
      this.setData({ errors })
    },

    /**
     * 提交反馈
     */
    async submitFeedback() {
      if (this.data.submitting) return
      
      // 验证表单
      if (!this.validateForm()) {
        wx.showToast({
          title: '请检查表单信息',
          icon: 'none'
        })
        return
      }
      
      this.setData({ submitting: true })
      
      try {
        // 准备提交数据
        const submitData = {
          ...this.data.formData,
          page: this.data.pageInfo?.route || '',
          metadata: this.data.pageInfo || {}
        }
        
        // 提交反馈
        const result = await feedbackManager.submitFeedback(submitData)
        
        if (result.success) {
          // 提交成功
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          })
          
          // 触发成功事件
          this.triggerEvent('success', {
            feedbackId: result.feedbackId,
            message: result.message
          })
          
          // 重置表单
          this.resetForm()
          
          // 关闭表单
          this.triggerEvent('close')
          
        } else {
          // 提交失败
          wx.showToast({
            title: result.message || '提交失败',
            icon: 'none'
          })
          
          // 触发失败事件
          this.triggerEvent('error', {
            error: result.error,
            message: result.message
          })
        }
      } catch (error) {
        console.error('提交反馈失败:', error)
        
        wx.showToast({
          title: '提交失败，请稍后重试',
          icon: 'none'
        })
        
        this.triggerEvent('error', { error: error.message })
      } finally {
        this.setData({ submitting: false })
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.setData({
        formData: {
          category: this.data.defaultCategory || '',
          title: '',
          description: '',
          rating: 0,
          contact: '',
          attachments: []
        },
        uploadedImages: [],
        errors: {}
      })
    },

    /**
     * 关闭表单
     */
    closeForm() {
      this.triggerEvent('close')
    },

    /**
     * 点击遮罩层
     */
    onMaskTap() {
      // 可配置是否允许点击遮罩关闭
      // this.closeForm()
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init()
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'show': function(show) {
      if (show) {
        this.init()
      }
    }
  }
})
