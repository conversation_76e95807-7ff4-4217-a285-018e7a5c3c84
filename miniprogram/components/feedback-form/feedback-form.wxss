/**
 * 反馈表单组件样式
 * Task 1.4: 用户反馈收集系统 - 表单样式
 */

.feedback-form {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.feedback-form.hidden {
  display: none;
}

/* 遮罩层 */
.form-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feedback-form:not(.hidden) .form-mask {
  opacity: 1;
}

/* 表单容器 */
.form-container {
  width: 100%;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.feedback-form:not(.hidden) .form-container {
  transform: translateY(0);
}

/* 表单头部 */
.form-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

/* 表单内容 */
.form-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 30rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #ff4757;
  margin-left: 4rpx;
}

/* 分类选择 */
.category-selector {
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  background-color: #fff;
  transition: all 0.2s ease;
}

.category-selector:active {
  background-color: #f8f9fa;
}

.category-selector.error {
  border-color: #ff4757;
}

.category-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-text {
  flex: 1;
}

.category-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.category-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.category-placeholder {
  font-size: 28rpx;
  color: #999;
}

.category-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 分类选择器弹窗 */
.category-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.picker-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-list {
  max-height: 60vh;
  overflow-y: auto;
}

.category-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:active {
  background-color: #f8f9fa;
}

.category-item .category-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.category-item .category-name {
  font-size: 30rpx;
  margin-bottom: 6rpx;
}

.category-item .category-desc {
  font-size: 24rpx;
}

/* 输入框 */
.form-input {
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  border-color: #007aff;
}

.form-input.error {
  border-color: #ff4757;
}

.form-textarea {
  min-height: 200rpx;
  resize: none;
}

/* 评分组件 */
.rating-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rating-star {
  font-size: 48rpx;
  color: #ddd;
  transition: all 0.2s ease;
}

.rating-star.active {
  color: #ffa502;
}

.rating-star:active {
  transform: scale(1.2);
}

.rating-text {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* 图片上传 */
.image-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.upload-btn:active {
  border-color: #007aff;
  color: #007aff;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

/* 错误信息 */
.error-message {
  color: #ff4757;
  font-size: 24rpx;
  margin-top: 8rpx;
  line-height: 1.3;
}

/* 表单底部 */
.form-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.submit-btn.disabled {
  background: #ddd;
  color: #999;
  pointer-events: none;
}

.submit-btn.loading {
  pointer-events: none;
}

.loading-icon {
  margin-right: 16rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 字符计数 */
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.char-count.warning {
  color: #ffa502;
}

.char-count.error {
  color: #ff4757;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-content {
    padding: 20rpx;
  }
  
  .form-item {
    margin-bottom: 30rpx;
  }
  
  .image-item,
  .upload-btn {
    width: 120rpx;
    height: 120rpx;
  }
  
  .rating-star {
    font-size: 40rpx;
  }
}

/* 动画效果 */
.form-container {
  will-change: transform;
}

.category-item,
.submit-btn,
.close-btn {
  will-change: transform, background-color;
}

/* 无障碍支持 */
.form-input:focus,
.category-selector:focus,
.submit-btn:focus {
  outline: 2rpx solid #007aff;
  outline-offset: 2rpx;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .form-container,
  .picker-content {
    background-color: #2a2a2a;
  }
  
  .form-title,
  .picker-title,
  .form-label,
  .category-name {
    color: #fff;
  }
  
  .category-desc,
  .rating-text,
  .char-count {
    color: #ccc;
  }
  
  .form-input,
  .category-selector {
    background-color: #333;
    border-color: #555;
    color: #fff;
  }
  
  .close-btn {
    background-color: #333;
    color: #ccc;
  }
  
  .category-item:active,
  .close-btn:active {
    background-color: #444;
  }
}
