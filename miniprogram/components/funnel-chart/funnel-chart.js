/**
 * 转化漏斗图表组件
 * Task 2.9: 转化漏斗分析功能 - 可视化漏斗图表
 */

const funnelAnalyzer = require('../../utils/funnel-analyzer')

Component({
  properties: {
    // 漏斗ID
    funnelId: {
      type: String,
      value: ''
    },
    
    // 图表高度
    height: {
      type: Number,
      value: 400
    },
    
    // 是否显示数据标签
    showLabels: {
      type: Boolean,
      value: true
    },
    
    // 是否显示转化率
    showConversionRate: {
      type: Boolean,
      value: true
    },
    
    // 主题色彩
    theme: {
      type: String,
      value: 'default' // default, blue, green, orange
    }
  },

  data: {
    // 漏斗数据
    funnelData: null,
    
    // 图表配置
    chartConfig: {
      width: 0,
      height: 0,
      stepHeight: 60,
      stepMargin: 20,
      maxWidth: 300,
      minWidth: 100
    },
    
    // 颜色配置
    colors: {
      default: ['#4A90E2', '#5BA0F2', '#6CB0FF', '#7DC0FF', '#8ED0FF'],
      blue: ['#1890FF', '#40A9FF', '#69C0FF', '#91D5FF', '#BAE7FF'],
      green: ['#52C41A', '#73D13D', '#95DE64', '#B7EB8F', '#D9F7BE'],
      orange: ['#FA8C16', '#FFA940', '#FFC069', '#FFD591', '#FFE7BA']
    },
    
    // 动画状态
    animationState: {
      isAnimating: false,
      currentStep: 0
    }
  },

  lifetimes: {
    attached() {
      this.initChart()
    },
    
    ready() {
      this.loadFunnelData()
      this.setupRealTimeUpdate()
    },
    
    detached() {
      this.cleanup()
    }
  },

  observers: {
    'funnelId': function(newFunnelId) {
      if (newFunnelId) {
        this.loadFunnelData()
      }
    }
  },

  methods: {
    /**
     * 初始化图表
     */
    initChart() {
      // 获取容器尺寸
      this.createSelectorQuery()
        .select('.funnel-chart-container')
        .boundingClientRect((rect) => {
          if (rect) {
            this.setData({
              'chartConfig.width': rect.width,
              'chartConfig.height': this.properties.height
            })
          }
        })
        .exec()
    },

    /**
     * 加载漏斗数据
     */
    loadFunnelData() {
      if (!this.properties.funnelId) {
        return
      }
      
      try {
        const analysis = funnelAnalyzer.getFunnelAnalysis(this.properties.funnelId)
        
        if (analysis) {
          this.setData({
            funnelData: analysis
          })
          
          this.renderChart()
        }
      } catch (error) {
        console.error('加载漏斗数据失败:', error)
      }
    },

    /**
     * 渲染图表
     */
    renderChart() {
      if (!this.data.funnelData) {
        return
      }
      
      const steps = this.data.funnelData.data.steps
      const chartSteps = this.calculateChartSteps(steps)
      
      this.setData({
        chartSteps: chartSteps
      })
      
      // 启动动画
      this.startAnimation()
    },

    /**
     * 计算图表步骤
     * @param {Array} steps 步骤数据
     */
    calculateChartSteps(steps) {
      if (!steps || steps.length === 0) {
        return []
      }
      
      const maxUsers = Math.max(...steps.map(step => step.userCount))
      const colors = this.data.colors[this.properties.theme] || this.data.colors.default
      
      return steps.map((step, index) => {
        const widthRatio = maxUsers > 0 ? step.userCount / maxUsers : 0
        const width = this.data.chartConfig.minWidth + 
                     (this.data.chartConfig.maxWidth - this.data.chartConfig.minWidth) * widthRatio
        
        return {
          ...step,
          width: width,
          height: this.data.chartConfig.stepHeight,
          color: colors[index % colors.length],
          x: (this.data.chartConfig.width - width) / 2,
          y: index * (this.data.chartConfig.stepHeight + this.data.chartConfig.stepMargin),
          widthRatio: widthRatio
        }
      })
    },

    /**
     * 启动动画
     */
    startAnimation() {
      if (!this.properties.showLabels) {
        return
      }
      
      this.setData({
        'animationState.isAnimating': true,
        'animationState.currentStep': 0
      })
      
      this.animateNextStep()
    },

    /**
     * 动画下一步
     */
    animateNextStep() {
      const currentStep = this.data.animationState.currentStep
      const totalSteps = this.data.chartSteps?.length || 0
      
      if (currentStep >= totalSteps) {
        this.setData({
          'animationState.isAnimating': false
        })
        return
      }
      
      // 显示当前步骤
      setTimeout(() => {
        this.setData({
          'animationState.currentStep': currentStep + 1
        })
        
        this.animateNextStep()
      }, 300)
    },

    /**
     * 设置实时更新
     */
    setupRealTimeUpdate() {
      // 每分钟更新一次数据
      this.updateTimer = setInterval(() => {
        this.loadFunnelData()
      }, 60000)
    },

    /**
     * 处理步骤点击
     * @param {Object} event 事件对象
     */
    onStepTap(event) {
      const stepIndex = event.currentTarget.dataset.index
      const step = this.data.chartSteps[stepIndex]
      
      if (step) {
        this.triggerEvent('stepclick', {
          stepIndex: stepIndex,
          stepData: step
        })
      }
    },

    /**
     * 处理图表点击
     */
    onChartTap() {
      this.triggerEvent('chartclick', {
        funnelData: this.data.funnelData
      })
    },

    /**
     * 格式化数字
     * @param {number} num 数字
     */
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      } else {
        return num.toString()
      }
    },

    /**
     * 格式化百分比
     * @param {number} rate 比率
     */
    formatPercentage(rate) {
      return rate.toFixed(1) + '%'
    },

    /**
     * 获取步骤样式
     * @param {Object} step 步骤数据
     * @param {number} index 索引
     */
    getStepStyle(step, index) {
      const isVisible = index < this.data.animationState.currentStep
      
      return {
        width: step.width + 'px',
        height: step.height + 'px',
        backgroundColor: step.color,
        transform: `translateX(${step.x}px) translateY(${step.y}px)`,
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.3s ease-in-out'
      }
    },

    /**
     * 获取标签样式
     * @param {Object} step 步骤数据
     * @param {number} index 索引
     */
    getLabelStyle(step, index) {
      const isVisible = index < this.data.animationState.currentStep
      
      return {
        transform: `translateX(${step.x + step.width + 10}px) translateY(${step.y + step.height / 2}px)`,
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.3s ease-in-out 0.1s'
      }
    },

    /**
     * 获取连接线样式
     * @param {Object} step 步骤数据
     * @param {number} index 索引
     */
    getConnectorStyle(step, index) {
      if (index >= this.data.chartSteps.length - 1) {
        return { display: 'none' }
      }
      
      const nextStep = this.data.chartSteps[index + 1]
      const isVisible = index + 1 < this.data.animationState.currentStep
      
      return {
        transform: `translateX(${step.x + step.width / 2}px) translateY(${step.y + step.height}px)`,
        width: '2px',
        height: this.data.chartConfig.stepMargin + 'px',
        backgroundColor: '#E8E8E8',
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.3s ease-in-out 0.2s'
      }
    },

    /**
     * 刷新数据
     */
    refresh() {
      this.loadFunnelData()
    },

    /**
     * 导出图表数据
     */
    exportData() {
      return {
        funnelData: this.data.funnelData,
        chartSteps: this.data.chartSteps,
        exportTime: Date.now()
      }
    },

    /**
     * 清理资源
     */
    cleanup() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    }
  }
})
