/* 转化漏斗图表组件样式 */

.funnel-chart-container {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 图表标题 */
.funnel-title {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  display: block;
  margin-bottom: 8px;
}

.title-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  opacity: 0.9;
}

.stat-item {
  color: rgba(255, 255, 255, 0.9);
}

/* 漏斗图表 */
.funnel-chart {
  position: relative;
  padding: 40px 20px;
  min-height: 300px;
}

/* 漏斗步骤 */
.funnel-step {
  position: absolute;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.funnel-step:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.funnel-step:active {
  transform: scale(0.98);
}

/* 步骤内容 */
.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  text-align: center;
  padding: 8px;
}

.step-number {
  font-size: 12px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  line-height: 20px;
  margin-bottom: 4px;
}

.step-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.step-count {
  font-size: 16px;
  font-weight: 600;
}

/* 转化率标签 */
.conversion-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 连接线 */
.step-connector {
  position: absolute;
  background: #e8e8e8;
  border-radius: 1px;
}

/* 数据标签 */
.step-label {
  position: absolute;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  z-index: 10;
}

.label-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.label-count {
  font-size: 16px;
  font-weight: 600;
  color: #4A90E2;
}

.label-rate {
  font-size: 12px;
  color: #52c41a;
}

.label-dropoff {
  font-size: 12px;
  color: #ff4757;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 异常提示 */
.anomaly-alerts {
  padding: 0 20px 20px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.alert-item.warning {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.alert-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #fa8c16;
  display: block;
  margin-bottom: 4px;
}

.alert-desc {
  font-size: 12px;
  color: #d48806;
  line-height: 1.4;
}

/* 图表操作栏 */
.chart-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
}

.action-btn:hover {
  border-color: #4A90E2;
  color: #4A90E2;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-icon {
  width: 32px;
  height: 32px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .funnel-title {
    padding: 16px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .title-stats {
    flex-direction: column;
    gap: 4px;
  }
  
  .funnel-chart {
    padding: 20px 10px;
  }
  
  .step-name {
    font-size: 12px;
    max-width: 80px;
  }
  
  .step-count {
    font-size: 14px;
  }
  
  .step-label {
    min-width: 100px;
    padding: 6px 8px;
  }
  
  .chart-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-btn {
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.funnel-step {
  animation: fadeInUp 0.3s ease-out;
}

.conversion-badge {
  animation: pulse 2s infinite;
}

/* 主题色彩变体 */
.theme-blue .funnel-title {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.theme-green .funnel-title {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.theme-orange .funnel-title {
  background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
}
