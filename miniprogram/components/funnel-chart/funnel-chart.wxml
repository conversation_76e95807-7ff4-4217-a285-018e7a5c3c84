<!--转化漏斗图表组件模板-->
<view class="funnel-chart-container" style="height: {{height}}px;" bindtap="onChartTap">
  <!-- 图表标题 -->
  <view class="funnel-title" wx:if="{{funnelData}}">
    <text class="title-text">{{funnelData.funnel.name}}</text>
    <view class="title-stats">
      <text class="stat-item">总用户: {{funnelData.data.totalUsers}}</text>
      <text class="stat-item">整体转化率: {{formatPercentage(funnelData.summary.overallConversionRate)}}</text>
    </view>
  </view>

  <!-- 漏斗图表 -->
  <view class="funnel-chart" wx:if="{{chartSteps && chartSteps.length > 0}}">
    <!-- 漏斗步骤 -->
    <view 
      class="funnel-step" 
      wx:for="{{chartSteps}}" 
      wx:key="stepIndex"
      data-index="{{index}}"
      bindtap="onStepTap"
      style="{{getStepStyle(item, index)}}"
    >
      <!-- 步骤内容 -->
      <view class="step-content">
        <view class="step-number">{{index + 1}}</view>
        <view class="step-name">{{item.stepName}}</view>
        <view class="step-count">{{formatNumber(item.userCount)}}</view>
      </view>
      
      <!-- 转化率标签 -->
      <view class="conversion-badge" wx:if="{{showConversionRate && index > 0}}">
        {{formatPercentage(item.conversionRate)}}
      </view>
    </view>

    <!-- 连接线 -->
    <view 
      class="step-connector" 
      wx:for="{{chartSteps}}" 
      wx:key="stepIndex"
      wx:if="{{index < chartSteps.length - 1}}"
      style="{{getConnectorStyle(item, index)}}"
    ></view>

    <!-- 数据标签 -->
    <view 
      class="step-label" 
      wx:if="{{showLabels}}"
      wx:for="{{chartSteps}}" 
      wx:key="stepIndex"
      style="{{getLabelStyle(item, index)}}"
    >
      <view class="label-content">
        <text class="label-name">{{item.stepName}}</text>
        <text class="label-count">{{formatNumber(item.userCount)}}人</text>
        <text class="label-rate" wx:if="{{index > 0}}">
          转化率: {{formatPercentage(item.conversionRate)}}
        </text>
        <text class="label-dropoff" wx:if="{{index > 0 && item.dropoffCount > 0}}">
          流失: {{formatNumber(item.dropoffCount)}}人
        </text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!chartSteps || chartSteps.length === 0}}">
    <image class="empty-icon" src="/images/empty-chart.png"></image>
    <text class="empty-text">暂无漏斗数据</text>
    <text class="empty-desc">请检查漏斗配置或等待数据收集</text>
  </view>

  <!-- 异常提示 -->
  <view class="anomaly-alerts" wx:if="{{funnelData && funnelData.summary.biggestDropoffStep}}">
    <view class="alert-item warning">
      <image class="alert-icon" src="/images/warning.png"></image>
      <view class="alert-content">
        <text class="alert-title">发现异常流失</text>
        <text class="alert-desc">
          步骤"{{funnelData.summary.biggestDropoffStep.stepName}}"流失率达到
          {{formatPercentage(funnelData.summary.biggestDropoffStep.dropoffRate)}}
        </text>
      </view>
    </view>
  </view>

  <!-- 图表操作栏 -->
  <view class="chart-actions">
    <button class="action-btn" bindtap="refresh">
      <image class="btn-icon" src="/images/refresh.png"></image>
      <text>刷新</text>
    </button>
    <button class="action-btn" bindtap="exportData">
      <image class="btn-icon" src="/images/export.png"></image>
      <text>导出</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{animationState.isAnimating}}">
    <view class="loading-content">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">正在加载图表...</text>
    </view>
  </view>
</view>
