/**
 * 通知弹窗组件样式
 * Task 1.3: 通知界面组件开发 - 弹窗样式
 * 目标：界面美观度≥4.5分，交互流畅度60fps
 */

.notification-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
}

.notification-popup.visible {
  pointer-events: auto;
}

/* 遮罩层 */
.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-popup.visible .popup-mask {
  opacity: 1;
}

/* 通知容器 */
.notification-container {
  position: absolute;
  left: 30rpx;
  right: 30rpx;
  z-index: 10000;
}

/* 位置样式 */
.notification-container.top {
  top: 100rpx;
  transform: translateY(-100%);
  opacity: 0;
}

.notification-container.center {
  top: 50%;
  transform: translateY(-50%) scale(0.8);
  opacity: 0;
}

.notification-container.bottom {
  bottom: 100rpx;
  transform: translateY(100%);
  opacity: 0;
}

/* 通知卡片 */
.notification-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20rpx);
  position: relative;
  overflow: hidden;
}

.notification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  pointer-events: none;
}

/* 通知头部 */
.notification-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.notification-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  line-height: 1.4;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #fff;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 通知内容 */
.notification-content {
  position: relative;
  z-index: 1;
  margin-bottom: 24rpx;
}

.notification-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  word-break: break-all;
}

/* 操作按钮 */
.notification-actions {
  display: flex;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid transparent;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary:active {
  background: rgba(255, 255, 255, 0.8);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 不同类型的样式 */
.notification-card.system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.notification-card.match {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.notification-card.message {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.notification-card.activity {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* 动画效果 */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToBottom {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes bounceIn {
  0% {
    transform: translateY(-50%) scale(0.3);
    opacity: 0;
  }
  50% {
    transform: translateY(-50%) scale(1.05);
  }
  70% {
    transform: translateY(-50%) scale(0.9);
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
}

@keyframes bounceOut {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-50%) scale(0.3);
    opacity: 0;
  }
}

/* 进度条动画 */
.auto-close-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  width: 100%;
  transform-origin: left;
  animation: progressShrink linear;
}

@keyframes progressShrink {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* 微交互效果 */
.notification-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-card:active {
  transform: scale(0.98);
}

.notification-icon {
  transition: all 0.3s ease;
}

.notification-card:active .notification-icon {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .notification-container {
    left: 20rpx;
    right: 20rpx;
  }
  
  .notification-card {
    padding: 24rpx;
  }
  
  .notification-icon {
    width: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
  }
  
  .notification-title {
    font-size: 28rpx;
  }
  
  .notification-text {
    font-size: 26rpx;
  }
  
  .action-btn {
    height: 64rpx;
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .popup-mask {
    background-color: rgba(0, 0, 0, 0.3);
  }
  
  .notification-card::before {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .notification-icon {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .close-btn {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* 高性能动画 */
.notification-container,
.notification-card,
.notification-icon,
.action-btn {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 无障碍支持 */
.notification-card {
  outline: none;
}

.notification-card:focus {
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15), 
              0 0 0 4rpx rgba(255, 255, 255, 0.3);
}

.action-btn:focus {
  outline: 2rpx solid rgba(255, 255, 255, 0.5);
  outline-offset: 2rpx;
}
