<!--
  通知弹窗组件模板
  Task 1.3: 通知界面组件开发 - 弹窗模板
-->

<view class="notification-popup {{visible ? 'visible' : ''}}" wx:if="{{visible}}">
  <!-- 遮罩层 -->
  <view class="popup-mask" bindtap="onMaskTap"></view>
  
  <!-- 通知容器 -->
  <view 
    class="notification-container {{position}}"
    animation="{{animationData}}"
  >
    <!-- 通知卡片 -->
    <view 
      class="notification-card {{notificationData.type}}"
      bindtap="onNotificationTap"
    >
      <!-- 通知头部 -->
      <view class="notification-header">
        <!-- 通知图标 -->
        <view 
          class="notification-icon"
          style="color: {{notificationData.iconColor}}"
        >
          {{notificationData.icon}}
        </view>
        
        <!-- 通知标题 -->
        <view class="notification-title">
          {{notificationData.title}}
        </view>
        
        <!-- 关闭按钮 -->
        <view 
          class="close-btn"
          wx:if="{{showClose}}"
          bindtap="onClose"
          catchtap="onClose"
        >
          ✕
        </view>
      </view>
      
      <!-- 通知内容 -->
      <view class="notification-content">
        <view class="notification-text">
          {{notificationData.content}}
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="notification-actions" wx:if="{{showActions && notificationData.actions.length > 0}}">
        <view 
          class="action-btn {{item.type}}"
          wx:for="{{notificationData.actions}}"
          wx:key="action"
          data-action="{{item.action}}"
          bindtap="onActionTap"
          catchtap="onActionTap"
        >
          {{item.text}}
        </view>
      </view>
      
      <!-- 自动关闭进度条 -->
      <view class="auto-close-progress" wx:if="{{autoClose > 0}}">
        <view 
          class="progress-bar"
          style="animation-duration: {{autoClose}}ms"
        ></view>
      </view>
    </view>
  </view>
</view>
