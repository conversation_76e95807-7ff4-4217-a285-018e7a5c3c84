/**
 * 通知弹窗组件
 * Task 1.3: 通知界面组件开发 - 弹窗组件
 * 功能：美观的通知弹窗样式，支持多种通知类型
 */

const notificationManager = require('../../utils/notification')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 通知数据
    notification: {
      type: Object,
      value: null
    },
    // 显示位置：top, center, bottom
    position: {
      type: String,
      value: 'top'
    },
    // 自动关闭时间（毫秒），0表示不自动关闭
    autoClose: {
      type: Number,
      value: 3000
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: true
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 动画类型：slide, fade, bounce
    animationType: {
      type: String,
      value: 'slide'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    visible: false,
    animationData: {},
    notificationData: null,
    autoCloseTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示通知弹窗
     * @param {Object} notification 通知数据
     */
    showNotification(notification) {
      const processedNotification = this.processNotificationData(notification)
      
      this.setData({
        notificationData: processedNotification,
        visible: true
      })
      
      // 执行显示动画
      this.playShowAnimation()
      
      // 设置自动关闭
      if (this.data.autoClose > 0) {
        this.setAutoCloseTimer()
      }
      
      // 触发显示事件
      this.triggerEvent('show', { notification: processedNotification })
    },

    /**
     * 隐藏通知弹窗
     */
    hideNotification() {
      // 执行隐藏动画
      this.playHideAnimation()
      
      // 清除自动关闭定时器
      this.clearAutoCloseTimer()
      
      // 延迟设置visible为false，等待动画完成
      setTimeout(() => {
        this.setData({
          visible: false,
          notificationData: null
        })
        
        // 触发隐藏事件
        this.triggerEvent('hide')
      }, 300)
    },

    /**
     * 处理通知数据
     * @param {Object} notification 原始通知数据
     */
    processNotificationData(notification) {
      const { type, data } = notification
      
      return {
        ...notification,
        title: this.getNotificationTitle(type, data),
        content: this.getNotificationContent(type, data),
        icon: this.getNotificationIcon(type),
        iconColor: this.getNotificationIconColor(type),
        backgroundColor: this.getNotificationBackgroundColor(type),
        actions: this.getNotificationActions(type, data)
      }
    },

    /**
     * 获取通知标题
     * @param {string} type 通知类型
     * @param {Object} data 通知数据
     */
    getNotificationTitle(type, data) {
      switch (type) {
        case 'system':
          return '系统通知'
        case 'match':
          return '新的匹配'
        case 'message':
          return data.senderName || '新消息'
        case 'activity':
          return '活动通知'
        default:
          return '通知'
      }
    },

    /**
     * 获取通知内容
     * @param {string} type 通知类型
     * @param {Object} data 通知数据
     */
    getNotificationContent(type, data) {
      switch (type) {
        case 'system':
          return data.message || '您有新的系统消息'
        case 'match':
          return `与 ${data.matchedUser?.nickname || '用户'} 匹配成功！`
        case 'message':
          return data.messagePreview || '发来一条消息'
        case 'activity':
          return data.description || data.title || '有新的活动等您参与'
        default:
          return data.message || data.content || '您有新的通知'
      }
    },

    /**
     * 获取通知图标
     * @param {string} type 通知类型
     */
    getNotificationIcon(type) {
      const iconMap = {
        system: '🔔',
        match: '💕',
        message: '💬',
        activity: '🎉'
      }
      return iconMap[type] || '📢'
    },

    /**
     * 获取通知图标颜色
     * @param {string} type 通知类型
     */
    getNotificationIconColor(type) {
      const colorMap = {
        system: '#007aff',
        match: '#ff4757',
        message: '#2ed573',
        activity: '#ffa502'
      }
      return colorMap[type] || '#007aff'
    },

    /**
     * 获取通知背景颜色
     * @param {string} type 通知类型
     */
    getNotificationBackgroundColor(type) {
      const colorMap = {
        system: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        match: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        message: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        activity: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
      }
      return colorMap[type] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },

    /**
     * 获取通知操作按钮
     * @param {string} type 通知类型
     * @param {Object} data 通知数据
     */
    getNotificationActions(type, data) {
      const actions = []
      
      switch (type) {
        case 'match':
          actions.push({
            text: '查看',
            type: 'primary',
            action: 'view'
          })
          break
        case 'message':
          actions.push({
            text: '回复',
            type: 'primary',
            action: 'reply'
          })
          break
        case 'activity':
          actions.push({
            text: '参与',
            type: 'primary',
            action: 'join'
          })
          break
      }
      
      // 通用操作
      actions.push({
        text: '稍后',
        type: 'secondary',
        action: 'later'
      })
      
      return actions
    },

    /**
     * 播放显示动画
     */
    playShowAnimation() {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })
      
      switch (this.data.animationType) {
        case 'slide':
          this.playSlideInAnimation(animation)
          break
        case 'fade':
          this.playFadeInAnimation(animation)
          break
        case 'bounce':
          this.playBounceInAnimation(animation)
          break
        default:
          this.playSlideInAnimation(animation)
      }
      
      this.setData({
        animationData: animation.export()
      })
    },

    /**
     * 播放隐藏动画
     */
    playHideAnimation() {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-in'
      })
      
      switch (this.data.animationType) {
        case 'slide':
          this.playSlideOutAnimation(animation)
          break
        case 'fade':
          this.playFadeOutAnimation(animation)
          break
        case 'bounce':
          this.playBounceOutAnimation(animation)
          break
        default:
          this.playSlideOutAnimation(animation)
      }
      
      this.setData({
        animationData: animation.export()
      })
    },

    /**
     * 滑入动画
     */
    playSlideInAnimation(animation) {
      if (this.data.position === 'top') {
        animation.translateY(0).opacity(1)
      } else if (this.data.position === 'bottom') {
        animation.translateY(0).opacity(1)
      } else {
        animation.scale(1).opacity(1)
      }
    },

    /**
     * 滑出动画
     */
    playSlideOutAnimation(animation) {
      if (this.data.position === 'top') {
        animation.translateY(-100).opacity(0)
      } else if (this.data.position === 'bottom') {
        animation.translateY(100).opacity(0)
      } else {
        animation.scale(0.8).opacity(0)
      }
    },

    /**
     * 淡入动画
     */
    playFadeInAnimation(animation) {
      animation.opacity(1)
    },

    /**
     * 淡出动画
     */
    playFadeOutAnimation(animation) {
      animation.opacity(0)
    },

    /**
     * 弹跳进入动画
     */
    playBounceInAnimation(animation) {
      animation.scale(1).opacity(1)
    },

    /**
     * 弹跳退出动画
     */
    playBounceOutAnimation(animation) {
      animation.scale(0.3).opacity(0)
    },

    /**
     * 设置自动关闭定时器
     */
    setAutoCloseTimer() {
      this.clearAutoCloseTimer()
      
      this.data.autoCloseTimer = setTimeout(() => {
        this.hideNotification()
      }, this.data.autoClose)
    },

    /**
     * 清除自动关闭定时器
     */
    clearAutoCloseTimer() {
      if (this.data.autoCloseTimer) {
        clearTimeout(this.data.autoCloseTimer)
        this.data.autoCloseTimer = null
      }
    },

    /**
     * 点击通知
     */
    onNotificationTap() {
      // 触发点击事件
      this.triggerEvent('tap', { 
        notification: this.data.notificationData 
      })
      
      // 处理默认点击行为
      this.handleDefaultTap()
    },

    /**
     * 处理默认点击行为
     */
    handleDefaultTap() {
      const { type, data } = this.data.notificationData
      
      try {
        switch (type) {
          case 'match':
            if (data.matchId) {
              wx.navigateTo({
                url: `/pages/match/detail?matchId=${data.matchId}`
              })
            }
            break
          case 'message':
            if (data.chatId) {
              wx.navigateTo({
                url: `/pages/chat/chat?chatId=${data.chatId}`
              })
            }
            break
          case 'activity':
            if (data.activityId) {
              wx.navigateTo({
                url: `/pages/activity/detail?activityId=${data.activityId}`
              })
            }
            break
        }
      } catch (error) {
        console.error('通知跳转失败:', error)
      }
      
      // 关闭弹窗
      this.hideNotification()
    },

    /**
     * 点击关闭按钮
     */
    onClose() {
      this.hideNotification()
      
      // 触发关闭事件
      this.triggerEvent('close', { 
        notification: this.data.notificationData 
      })
    },

    /**
     * 点击操作按钮
     * @param {Object} e 事件对象
     */
    onActionTap(e) {
      const action = e.currentTarget.dataset.action
      
      // 触发操作事件
      this.triggerEvent('action', {
        action: action,
        notification: this.data.notificationData
      })
      
      // 处理默认操作
      this.handleDefaultAction(action)
    },

    /**
     * 处理默认操作
     * @param {string} action 操作类型
     */
    handleDefaultAction(action) {
      switch (action) {
        case 'view':
        case 'reply':
        case 'join':
          this.handleDefaultTap()
          break
        case 'later':
          this.hideNotification()
          break
      }
    },

    /**
     * 点击遮罩层
     */
    onMaskTap() {
      // 可配置是否允许点击遮罩关闭
      // this.hideNotification()
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 监听通知事件
      notificationManager.on('received', (notification) => {
        // 自动显示新收到的通知
        this.showNotification(notification)
      })
    },

    detached() {
      // 清理定时器
      this.clearAutoCloseTimer()
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'show': function(show) {
      if (show && this.data.notification) {
        this.showNotification(this.data.notification)
      } else if (!show) {
        this.hideNotification()
      }
    }
  }
})
