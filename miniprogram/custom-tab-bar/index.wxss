/* custom-tab-bar/index.wxss */

/* 主容器 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

/* 毛玻璃效果 */
.tab-bar.glassmorphism {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 降级方案 */
.tab-bar.fallback {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* Tab项 */
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100rpx;
  transition: all 0.3s ease;
}

/* 选中状态背景 */
.active-background {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  right: 10rpx;
  bottom: 10rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  z-index: -1;
  animation: activeSlideUp 0.3s ease-out;
}

@keyframes activeSlideUp {
  from {
    transform: translateY(10rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 图标容器 */
.tab-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
}

/* 图片图标 */
.icon-image {
  width: 48rpx;
  height: 48rpx;
}

/* Emoji图标 */
.icon-emoji {
  font-size: 44rpx;
  line-height: 48rpx;
}

/* 文字 */
.tab-text {
  font-size: 20rpx;
  color: #999999;
  transition: all 0.3s ease;
  font-weight: 400;
}

.tab-text.active-text {
  color: white;
  font-weight: 600;
}

/* 角标 */
.tab-badge {
  position: absolute;
  top: 8rpx;
  right: 20rpx;
  z-index: 10;
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid white;
}

.badge-number {
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  border: 2rpx solid white;
  font-weight: 600;
}

/* 波纹效果 */
.ripple-effect {
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
  z-index: 5;
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(4);
    opacity: 0;
  }
}

/* 悬停效果（仅在支持的设备上） */
.tab-item:active {
  transform: scale(0.95);
}

/* 适配不同屏幕 */
@media (max-width: 375px) {
  .tab-text {
    font-size: 18rpx;
  }
  
  .icon-emoji {
    font-size: 40rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .tab-bar.glassmorphism {
    background: rgba(0, 0, 0, 0.8);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .tab-bar.fallback {
    background: rgba(0, 0, 0, 0.95);
  }
  
  .tab-text {
    color: #cccccc;
  }
}
