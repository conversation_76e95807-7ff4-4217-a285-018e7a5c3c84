<!--custom-tab-bar/index.wxml-->
<cover-view class="tab-bar {{useBackdropFilter ? 'glassmorphism' : 'fallback'}}">
  <!-- 波纹效果 -->
  <cover-view 
    wx:if="{{ripple.show}}" 
    class="ripple-effect" 
    style="left: {{ripple.x}}px; top: {{ripple.y}}px;"
  ></cover-view>
  
  <!-- Tab项 -->
  <cover-view 
    wx:for="{{list}}" 
    wx:key="index"
    class="tab-item {{selected === index ? 'active' : ''}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <!-- 选中状态背景 -->
    <cover-view wx:if="{{selected === index}}" class="active-background"></cover-view>
    
    <!-- 图标 -->
    <cover-view class="tab-icon">
      <cover-image 
        wx:if="{{item.iconPath}}"
        src="{{selected === index ? item.selectedIconPath : item.iconPath}}"
        class="icon-image"
      />
      <cover-view wx:else class="icon-emoji">{{item.icon}}</cover-view>
    </cover-view>
    
    <!-- 文字 -->
    <cover-view class="tab-text {{selected === index ? 'active-text' : ''}}">
      {{item.text}}
    </cover-view>
    
    <!-- 消息角标 -->
    <cover-view wx:if="{{item.badge}}" class="tab-badge">
      <cover-view class="badge-dot" wx:if="{{item.badge === true}}"></cover-view>
      <cover-view class="badge-number" wx:else>{{item.badge}}</cover-view>
    </cover-view>
  </cover-view>
</cover-view>
