Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#667eea",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    useBackdropFilter: true,
    ripple: { show: false, x: 0, y: 0 },
    list: [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        iconPath: "/images/tab/home.png",
        selectedIconPath: "/images/tab/home-active.png",
        icon: "🏠"
      },
      {
        pagePath: "/pages/discover/discover",
        text: "发现",
        iconPath: "/images/tab/discover.png",
        selectedIconPath: "/images/tab/discover-active.png",
        icon: "🔍"
      },
      {
        pagePath: "/pages/chat/chat",
        text: "消息",
        iconPath: "/images/tab/chat.png",
        selectedIconPath: "/images/tab/chat-active.png",
        icon: "💬"
      },
      {
        pagePath: "/pages/moments/moments",
        text: "动态",
        iconPath: "/images/tab/moments.png",
        selectedIconPath: "/images/tab/moments-active.png",
        icon: "📸"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "/images/tab/profile.png",
        selectedIconPath: "/images/tab/profile-active.png",
        icon: "👤"
      }
    ]
  },

  attached() {
    // 检测backdrop-filter支持
    this.checkBackdropFilterSupport();

    // 初始化当前页面索引
    this.updateSelectedIndex();
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      // 添加触觉反馈
      wx.vibrateShort({ type: 'light' });
      
      // 触发波纹效果
      this.triggerRippleEffect(e);
      
      wx.switchTab({ url });
      this.setData({
        selected: data.index
      });
    },

    // 检测backdrop-filter支持
    checkBackdropFilterSupport() {
      const systemInfo = wx.getSystemInfoSync();
      const isSupported = this.isBackdropFilterSupported(systemInfo);
      
      this.setData({
        useBackdropFilter: isSupported
      });
    },

    // 判断是否支持backdrop-filter
    isBackdropFilterSupported(systemInfo) {
      const platform = systemInfo.platform;
      const version = systemInfo.version;
      
      // iOS 14.6+ 和 Android 10+ 支持
      if (platform === 'ios') {
        return this.compareVersion(version, '8.0.0') >= 0;
      } else if (platform === 'android') {
        return systemInfo.SDKVersion >= 28;
      }
      
      return false;
    },

    // 版本比较
    compareVersion(v1, v2) {
      const arr1 = v1.split('.');
      const arr2 = v2.split('.');
      const length = Math.max(arr1.length, arr2.length);
      
      for (let i = 0; i < length; i++) {
        const num1 = parseInt(arr1[i] || 0);
        const num2 = parseInt(arr2[i] || 0);
        
        if (num1 > num2) return 1;
        if (num1 < num2) return -1;
      }
      
      return 0;
    },

    // 波纹效果
    triggerRippleEffect(e) {
      const { clientX, clientY } = e.detail || e.touches[0] || {};
      if (!clientX || !clientY) return;
      
      // 创建波纹动画
      const ripple = {
        x: clientX,
        y: clientY,
        show: true
      };
      
      this.setData({ ripple });
      
      // 300ms后隐藏波纹
      setTimeout(() => {
        this.setData({
          ripple: { ...ripple, show: false }
        });
      }, 300);
    },

    /**
     * 更新选中的页面索引
     */
    updateSelectedIndex() {
      try {
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          const route = currentPage.route || '';

          const index = this.data.list.findIndex(item =>
            item.pagePath === `/${route}` || item.pagePath === route
          );

          if (index !== -1) {
            this.setData({ selected: index });
          }
        }
      } catch (error) {
        console.error('更新选中索引失败:', error);
      }
    }
  }
})
