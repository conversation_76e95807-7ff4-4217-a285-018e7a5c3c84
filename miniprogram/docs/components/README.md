# 相亲交友小程序组件库文档

## 📚 组件库概述

本组件库为相亲交友小程序提供了完整的UI组件解决方案，包含通知反馈、加载展示、交互增强等多个类别的组件。

### 🎯 设计理念

- **一致性**: 统一的设计语言和交互规范
- **易用性**: 简单的API设计，开箱即用
- **性能**: 高性能渲染，流畅的用户体验
- **扩展性**: 灵活的配置选项，支持自定义

### 📦 组件分类

#### 1. 反馈组件
- [Toast组件](./toast.md) - 轻量级消息提示
- [Loading组件](./loading.md) - 加载状态展示
- [骨架屏组件](./skeleton.md) - 内容加载占位

#### 2. 展示组件
- [Swiper组件](./swiper.md) - 轮播图展示
- [图片预览组件](./image-preview.md) - 图片查看器
- [虚拟列表组件](./virtual-list.md) - 大数据列表

#### 3. 交互组件
- [下拉刷新组件](./pull-refresh.md) - 下拉刷新功能

### 🚀 快速开始

#### 安装使用

1. **引入组件**
```json
{
  "usingComponents": {
    "enhanced-toast": "/components/enhanced-toast/enhanced-toast",
    "enhanced-loading": "/components/enhanced-loading/enhanced-loading",
    "skeleton-screen": "/components/skeleton-screen/skeleton-screen",
    "enhanced-swiper": "/components/enhanced-swiper/enhanced-swiper",
    "image-preview": "/components/image-preview/image-preview",
    "pull-refresh": "/components/pull-refresh/pull-refresh",
    "virtual-list": "/components/virtual-list/virtual-list"
  }
}
```

2. **使用混入**
```javascript
const toastMixin = require('../../mixins/toast-mixin')
const loadingMixin = require('../../mixins/loading-mixin')

Page({
  mixins: [toastMixin, loadingMixin],
  
  onLoad() {
    // 使用Toast
    this.$success('操作成功')
    
    // 使用Loading
    this.$loading('加载中...')
  }
})
```

### 🎨 主题配置

#### 默认主题
```javascript
const theme = {
  primary: '#667eea',
  success: '#2ed573',
  warning: '#ffa502',
  error: '#ff4757',
  info: '#3742fa'
}
```

#### 深色模式
所有组件都支持深色模式，会根据系统设置自动切换。

### 📱 兼容性

- **微信小程序**: 基础库 2.10.0+
- **设备支持**: iOS 10+, Android 5.0+
- **屏幕适配**: 支持各种屏幕尺寸

### 🔧 开发指南

#### 组件开发规范

1. **文件结构**
```
components/
├── component-name/
│   ├── component-name.js      # 组件逻辑
│   ├── component-name.wxml    # 组件模板
│   ├── component-name.wxss    # 组件样式
│   ├── component-name.json    # 组件配置
│   └── README.md              # 组件文档
```

2. **命名规范**
- 组件名称：kebab-case (如: enhanced-toast)
- 属性名称：camelCase (如: showIndicator)
- 事件名称：小写+连字符 (如: item-tap)

3. **代码规范**
- 使用ES6+语法
- 添加详细注释
- 遵循ESLint规则

#### 最佳实践

1. **性能优化**
- 使用`observers`监听属性变化
- 合理使用`lifetimes`生命周期
- 避免频繁的`setData`调用

2. **用户体验**
- 提供加载状态反馈
- 支持触摸手势操作
- 适配不同屏幕尺寸

3. **错误处理**
- 提供错误状态展示
- 添加降级方案
- 记录错误日志

### 📊 性能指标

| 组件类型 | 渲染时间 | 内存占用 | 兼容性 |
|----------|----------|----------|--------|
| Toast组件 | <50ms | <2MB | 100% |
| Loading组件 | <30ms | <1.5MB | 100% |
| Swiper组件 | <100ms | <5MB | 98% |
| 图片预览 | <80ms | <8MB | 95% |
| 虚拟列表 | <200ms | <10MB | 90% |

### 🤝 贡献指南

1. **提交Issue**
- 使用Issue模板
- 提供详细的复现步骤
- 附上相关截图或代码

2. **提交PR**
- Fork项目到个人仓库
- 创建功能分支
- 提交PR并描述变更内容

3. **代码审查**
- 确保代码质量
- 通过所有测试
- 更新相关文档

### 📞 技术支持

- **文档问题**: 查看各组件详细文档
- **使用问题**: 参考示例代码
- **Bug反馈**: 提交Issue

### 📝 更新日志

#### v1.3.0 (2024-07-08)
- ✨ 新增虚拟列表组件
- ✨ 新增图片预览组件
- ✨ 新增下拉刷新组件
- 🎨 优化Swiper组件交互
- 🐛 修复Toast组件队列问题
- 📚 完善组件文档

#### v1.2.0 (2024-07-07)
- ✨ 新增增强版Swiper组件
- 🎨 优化Loading组件动画
- 🎨 增强Toast组件功能
- 📱 适配更多设备尺寸

#### v1.1.0 (2024-07-06)
- ✨ 新增骨架屏组件
- 🎨 优化Loading组件样式
- 🐛 修复Toast组件显示问题

#### v1.0.0 (2024-07-05)
- 🎉 发布基础组件库
- ✨ 包含Toast和Loading组件
- 📚 提供完整文档

### 📄 许可证

MIT License - 详见 [LICENSE](../LICENSE) 文件
