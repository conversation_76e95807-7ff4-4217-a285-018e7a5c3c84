# Swiper组件

增强版轮播图组件，支持多种切换效果和指示器类型。

## 📱 预览效果

![Swiper组件预览](../images/swiper-preview.png)

## 🚀 快速开始

### 基础用法

```xml
<!-- 基础轮播图 -->
<enhanced-swiper
  items="{{swiperItems}}"
  autoplay="{{true}}"
  interval="{{3000}}"
  bind:change="onSwiperChange"
></enhanced-swiper>
```

```javascript
Page({
  data: {
    swiperItems: [
      {
        id: 1,
        image: 'https://example.com/image1.jpg',
        title: '轮播图1',
        desc: '这是第一张轮播图的描述'
      },
      {
        id: 2,
        image: 'https://example.com/image2.jpg',
        title: '轮播图2',
        desc: '这是第二张轮播图的描述'
      }
    ]
  },
  
  onSwiperChange(e) {
    console.log('当前索引:', e.detail.current)
  }
})
```

## 📋 API文档

### 属性 (Properties)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| items | Array | [] | 轮播数据 |
| autoplay | Boolean | true | 是否自动播放 |
| interval | Number | 3000 | 自动播放间隔(ms) |
| circular | Boolean | true | 是否循环播放 |
| indicatorType | String | 'dots' | 指示器类型 |
| showIndicator | Boolean | true | 是否显示指示器 |
| effect | String | 'slide' | 切换效果 |
| height | String | '400rpx' | 轮播图高度 |
| borderRadius | String | '16rpx' | 圆角大小 |
| showControls | Boolean | false | 是否显示控制按钮 |
| touchable | Boolean | true | 是否支持手势 |
| scale | Number | 1 | 缩放比例 |
| spacing | Number | 0 | 间距大小 |

### 数据格式 (items)

```javascript
const swiperItems = [
  {
    id: 1,                    // 唯一标识
    image: 'image_url',       // 图片地址
    title: '标题',            // 标题文本
    desc: '描述',             // 描述文本
    thumbnail: 'thumb_url',   // 缩略图(用于缩略图指示器)
    link: 'page_url'          // 跳转链接
  }
]
```

### 指示器类型 (indicatorType)

| 类型 | 说明 | 效果 |
|------|------|------|
| dots | 点状指示器 | 圆点显示 |
| numbers | 数字指示器 | 1/5格式 |
| progress | 进度条指示器 | 进度条显示 |
| thumbnails | 缩略图指示器 | 小图预览 |

### 切换效果 (effect)

| 效果 | 说明 | 动画 |
|------|------|------|
| slide | 滑动切换 | 水平滑动 |
| fade | 淡入淡出 | 透明度变化 |
| cube | 立方体 | 3D立方体 |
| flip | 翻转 | 3D翻转 |
| cards | 卡片 | 卡片堆叠 |

### 事件 (Events)

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 轮播切换时触发 | {current, source} |
| tap | 点击轮播项时触发 | {index, item} |

## 🎯 使用示例

### 基础轮播

```xml
<!-- 简单轮播图 -->
<enhanced-swiper
  items="{{banners}}"
  height="300rpx"
  autoplay="{{true}}"
  interval="{{5000}}"
  bind:change="onBannerChange"
  bind:tap="onBannerTap"
></enhanced-swiper>
```

```javascript
Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/banner1.jpg',
        title: '夏日特惠',
        desc: '全场商品8折起'
      },
      {
        id: 2,
        image: '/images/banner2.jpg',
        title: '新品上市',
        desc: '最新款式抢先看'
      }
    ]
  },
  
  onBannerChange(e) {
    console.log('轮播切换:', e.detail)
  },
  
  onBannerTap(e) {
    const { index, item } = e.detail
    wx.navigateTo({
      url: `/pages/detail/detail?id=${item.id}`
    })
  }
})
```

### 高级配置

```xml
<!-- 高级轮播图 -->
<enhanced-swiper
  items="{{products}}"
  height="500rpx"
  effect="cube"
  indicator-type="thumbnails"
  show-controls="{{true}}"
  border-radius="20rpx"
  scale="{{0.9}}"
  spacing="{{20}}"
  bind:change="onProductChange"
></enhanced-swiper>
```

### 不同指示器类型

```xml
<!-- 点状指示器 -->
<enhanced-swiper
  items="{{items}}"
  indicator-type="dots"
></enhanced-swiper>

<!-- 数字指示器 -->
<enhanced-swiper
  items="{{items}}"
  indicator-type="numbers"
></enhanced-swiper>

<!-- 进度条指示器 -->
<enhanced-swiper
  items="{{items}}"
  indicator-type="progress"
></enhanced-swiper>

<!-- 缩略图指示器 -->
<enhanced-swiper
  items="{{items}}"
  indicator-type="thumbnails"
></enhanced-swiper>
```

### 不同切换效果

```xml
<!-- 滑动效果 -->
<enhanced-swiper
  items="{{items}}"
  effect="slide"
></enhanced-swiper>

<!-- 淡入淡出 -->
<enhanced-swiper
  items="{{items}}"
  effect="fade"
></enhanced-swiper>

<!-- 3D立方体 -->
<enhanced-swiper
  items="{{items}}"
  effect="cube"
></enhanced-swiper>

<!-- 3D翻转 -->
<enhanced-swiper
  items="{{items}}"
  effect="flip"
></enhanced-swiper>

<!-- 卡片堆叠 -->
<enhanced-swiper
  items="{{items}}"
  effect="cards"
></enhanced-swiper>
```

## 🎨 样式定制

### CSS变量

```css
/* 自定义轮播图样式 */
.swiper-container {
  --swiper-border-radius: 16rpx;
  --swiper-box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 指示器样式 */
.swiper-indicators {
  --indicator-dot-size: 16rpx;
  --indicator-dot-color: rgba(255, 255, 255, 0.5);
  --indicator-dot-active-color: #fff;
}

/* 控制按钮样式 */
.swiper-controls {
  --control-btn-size: 60rpx;
  --control-btn-bg: rgba(255, 255, 255, 0.9);
  --control-btn-color: #333;
}
```

### 自定义主题

```javascript
// 在页面中自定义主题
Page({
  data: {
    swiperTheme: {
      primary: '#667eea',
      background: '#f8f9fa',
      text: '#333'
    }
  }
})
```

## 📊 性能优化

### 图片懒加载

```xml
<!-- 启用图片懒加载 -->
<enhanced-swiper
  items="{{items}}"
  lazy-load="{{true}}"
></enhanced-swiper>
```

### 预加载策略

```javascript
// 组件内置预加载机制
Page({
  data: {
    items: []
  },
  
  onLoad() {
    // 先加载第一张图片
    this.loadFirstImage()
    
    // 然后预加载其他图片
    this.preloadOtherImages()
  },
  
  loadFirstImage() {
    // 优先加载第一张图片
  },
  
  preloadOtherImages() {
    // 后台预加载其他图片
  }
})
```

## 🐛 常见问题

### Q: 轮播图不自动播放怎么办？

A: 检查以下几点：
1. 确保autoplay设置为true
2. 检查interval时间设置
3. 确认items数组长度大于1

### Q: 如何自定义轮播项内容？

A: 可以通过以下方式：
1. 修改items数据结构
2. 自定义轮播项模板
3. 使用插槽功能

### Q: 触摸滑动不灵敏怎么办？

A: 确保：
1. touchable设置为true
2. 没有其他元素阻止触摸事件
3. 容器尺寸设置正确

## 🔧 最佳实践

### 1. 合理设置轮播数量

```javascript
// ✅ 推荐：3-5张轮播图
const items = [
  { id: 1, image: 'image1.jpg', title: '轮播1' },
  { id: 2, image: 'image2.jpg', title: '轮播2' },
  { id: 3, image: 'image3.jpg', title: '轮播3' }
]

// ❌ 不推荐：过多轮播图影响性能
const items = new Array(20).fill().map((_, i) => ({
  id: i,
  image: `image${i}.jpg`
}))
```

### 2. 优化图片资源

```javascript
// ✅ 推荐：使用适当尺寸的图片
const items = [
  {
    id: 1,
    image: 'https://cdn.example.com/banner_750x400.jpg', // 适当尺寸
    thumbnail: 'https://cdn.example.com/thumb_150x80.jpg' // 缩略图
  }
]

// ❌ 不推荐：使用过大的图片
const items = [
  {
    id: 1,
    image: 'https://cdn.example.com/banner_4k.jpg' // 过大图片
  }
]
```

### 3. 合理设置自动播放间隔

```javascript
// ✅ 推荐：根据内容调整间隔
<enhanced-swiper
  items="{{textItems}}"
  interval="{{5000}}"  // 文字内容需要更长时间
></enhanced-swiper>

<enhanced-swiper
  items="{{imageItems}}"
  interval="{{3000}}"  // 图片内容可以较短
></enhanced-swiper>

// ❌ 不推荐：所有场景使用相同间隔
<enhanced-swiper interval="{{2000}}"></enhanced-swiper>
```

## 📈 更新日志

### v1.3.0
- ✨ 新增cards切换效果
- ✨ 新增缩略图指示器
- 🎨 优化触摸交互体验
- 🐛 修复循环播放问题

### v1.2.0
- ✨ 新增3D切换效果
- ✨ 新增控制按钮功能
- 🎨 优化动画性能
- 📱 适配更多设备

### v1.1.0
- ✨ 新增进度条指示器
- 🎨 优化指示器样式
- 🐛 修复自动播放问题

### v1.0.0
- 🎉 发布Swiper组件
- ✨ 支持5种切换效果
- ✨ 支持4种指示器类型
