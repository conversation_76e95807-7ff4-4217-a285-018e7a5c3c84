# 图片预览组件

全功能图片预览组件，支持缩放、拖拽、保存等操作。

## 📱 预览效果

![图片预览组件预览](../images/image-preview-preview.png)

## 🚀 快速开始

### 基础用法

```javascript
Page({
  data: {
    showPreview: false,
    previewImages: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
      'https://example.com/image3.jpg'
    ],
    currentIndex: 0
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      showPreview: true,
      currentIndex: index
    })
  },

  // 关闭预览
  closePreview() {
    this.setData({ showPreview: false })
  }
})
```

```xml
<!-- 图片网格 -->
<view class="image-grid">
  <image
    wx:for="{{previewImages}}"
    wx:key="*this"
    src="{{item}}"
    data-index="{{index}}"
    bindtap="previewImage"
  ></image>
</view>

<!-- 图片预览 -->
<image-preview
  visible="{{showPreview}}"
  images="{{previewImages}}"
  current="{{currentIndex}}"
  bind:hide="closePreview"
></image-preview>
```

## 📋 API文档

### 属性 (Properties)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示预览 |
| images | Array | [] | 图片列表 |
| current | Number | 0 | 当前图片索引 |
| showIndicator | Boolean | true | 是否显示指示器 |
| showDelete | Boolean | false | 是否显示删除按钮 |
| showSave | Boolean | true | 是否显示保存按钮 |
| zoomable | Boolean | true | 是否支持缩放 |
| maxZoom | Number | 3 | 最大缩放倍数 |
| minZoom | Number | 0.5 | 最小缩放倍数 |

### 图片数据格式

```javascript
// 字符串格式
const images = [
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg'
]

// 对象格式
const images = [
  {
    url: 'https://example.com/image1.jpg',
    title: '图片标题',
    desc: '图片描述'
  }
]
```

### 事件 (Events)

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| show | 预览显示时触发 | {current} |
| hide | 预览隐藏时触发 | {current} |
| change | 图片切换时触发 | {current, image} |
| save | 保存图片时触发 | {index, image} |
| delete | 删除图片时触发 | {index, image} |

## 🎯 使用示例

### 基础预览

```javascript
Page({
  data: {
    photos: [
      { id: 1, url: '/images/photo1.jpg' },
      { id: 2, url: '/images/photo2.jpg' },
      { id: 3, url: '/images/photo3.jpg' }
    ]
  },

  previewPhoto(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.photos.map(photo => photo.url)
    
    this.setData({
      showPreview: true,
      previewImages: images,
      currentIndex: index
    })
  }
})
```

### 高级功能

```javascript
Page({
  data: {
    showPreview: false,
    previewImages: [],
    currentIndex: 0
  },

  // 预览用户相册
  previewAlbum(e) {
    const { images, current } = e.currentTarget.dataset
    
    this.setData({
      showPreview: true,
      previewImages: images,
      currentIndex: current || 0
    })
  },

  // 保存图片
  onImageSave(e) {
    const { index, image } = e.detail
    console.log('保存图片:', image)
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    })
  },

  // 删除图片
  onImageDelete(e) {
    const { index, image } = e.detail
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          // 从数组中删除图片
          const newImages = [...this.data.previewImages]
          newImages.splice(index, 1)
          
          this.setData({
            previewImages: newImages,
            currentIndex: Math.min(this.data.currentIndex, newImages.length - 1)
          })
          
          // 如果没有图片了，关闭预览
          if (newImages.length === 0) {
            this.setData({ showPreview: false })
          }
        }
      }
    })
  }
})
```

### 手势操作

```xml
<!-- 支持缩放和拖拽的图片预览 -->
<image-preview
  visible="{{showPreview}}"
  images="{{previewImages}}"
  current="{{currentIndex}}"
  zoomable="{{true}}"
  max-zoom="{{5}}"
  min-zoom="{{0.2}}"
  bind:change="onImageChange"
></image-preview>
```

## 🎨 样式定制

### CSS变量

```css
/* 自定义预览样式 */
.preview-overlay {
  --preview-bg-color: rgba(0, 0, 0, 0.9);
  --preview-toolbar-bg: linear-gradient(rgba(0, 0, 0, 0.6), transparent);
}

/* 工具栏按钮样式 */
.toolbar-btn {
  --btn-size: 60rpx;
  --btn-bg-color: rgba(255, 255, 255, 0.2);
  --btn-color: #fff;
}

/* 指示器样式 */
.preview-indicators {
  --indicator-dot-size: 16rpx;
  --indicator-dot-color: rgba(255, 255, 255, 0.5);
  --indicator-dot-active-color: #fff;
}
```

### 自定义主题

```javascript
// 深色主题
const darkTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.95)',
  toolbarColor: 'rgba(255, 255, 255, 0.1)',
  textColor: '#fff'
}

// 浅色主题
const lightTheme = {
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  toolbarColor: 'rgba(0, 0, 0, 0.1)',
  textColor: '#333'
}
```

## 📊 性能优化

### 图片预加载

```javascript
Page({
  // 预加载相邻图片
  preloadImages(currentIndex) {
    const { previewImages } = this.data
    
    // 预加载前一张和后一张
    const preloadIndexes = [
      currentIndex - 1,
      currentIndex + 1
    ].filter(index => index >= 0 && index < previewImages.length)
    
    preloadIndexes.forEach(index => {
      const img = wx.createImage()
      img.src = previewImages[index]
    })
  }
})
```

### 内存管理

```javascript
Page({
  onUnload() {
    // 清理图片缓存
    this.setData({
      previewImages: [],
      showPreview: false
    })
  }
})
```

## 🐛 常见问题

### Q: 图片加载失败怎么办？

A: 组件内置错误处理：
1. 显示错误占位图
2. 提供重试机制
3. 跳过损坏的图片

### Q: 如何自定义工具栏？

A: 可以通过以下方式：
1. 修改showSave和showDelete属性
2. 监听事件自定义操作
3. 覆盖工具栏样式

### Q: 缩放手势不响应怎么办？

A: 检查以下几点：
1. 确保zoomable设置为true
2. 检查是否有其他元素阻止触摸
3. 确认在真机上测试

## 🔧 最佳实践

### 1. 合理设置缩放范围

```javascript
// ✅ 推荐：根据图片类型设置合适范围
<image-preview
  max-zoom="{{3}}"    // 普通图片3倍足够
  min-zoom="{{0.5}}"  // 最小0.5倍保持可读性
></image-preview>

// ❌ 不推荐：过大的缩放范围
<image-preview
  max-zoom="{{10}}"   // 过大影响性能
  min-zoom="{{0.1}}"  // 过小影响体验
></image-preview>
```

### 2. 优化图片资源

```javascript
// ✅ 推荐：使用适当尺寸的图片
const optimizedImages = originalImages.map(img => ({
  url: `${img.url}?w=1080&h=1080&q=80`, // 压缩处理
  thumbnail: `${img.url}?w=300&h=300&q=60` // 缩略图
}))

// ❌ 不推荐：使用原始大图
const images = originalImages.map(img => img.originalUrl)
```

### 3. 提供用户反馈

```javascript
// ✅ 推荐：操作后给予反馈
onImageSave(e) {
  wx.showToast({
    title: '保存成功',
    icon: 'success'
  })
}

onImageDelete(e) {
  wx.showToast({
    title: '删除成功',
    icon: 'success'
  })
}

// ❌ 不推荐：静默操作
onImageSave(e) {
  // 没有任何反馈
}
```

## 📈 更新日志

### v1.3.0
- ✨ 新增图片删除功能
- ✨ 新增缩放信息显示
- 🎨 优化手势操作体验
- 🐛 修复边界回弹问题

### v1.2.0
- ✨ 新增双指缩放功能
- ✨ 新增拖拽移动功能
- 🎨 优化动画效果
- 📱 适配更多设备

### v1.1.0
- ✨ 新增保存图片功能
- 🎨 优化工具栏样式
- 🐛 修复图片切换问题

### v1.0.0
- 🎉 发布图片预览组件
- ✨ 支持基础预览功能
- ✨ 支持指示器显示
