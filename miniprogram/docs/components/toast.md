# Toast组件

轻量级消息提示组件，支持多种样式和位置配置。

## 📱 预览效果

![Toast组件预览](../images/toast-preview.png)

## 🚀 快速开始

### 基础用法

```javascript
// 页面中引入混入
const toastMixin = require('../../mixins/toast-mixin')

Page({
  mixins: [toastMixin],
  
  showToast() {
    // 显示默认Toast
    this.$toast('这是一条消息')
    
    // 显示成功Toast
    this.$success('操作成功')
    
    // 显示错误Toast
    this.$error('操作失败')
  }
})
```

### 模板使用

```xml
<!-- 在页面模板中添加Toast组件 -->
<enhanced-toast
  visible="{{toastVisible}}"
  message="{{toastMessage}}"
  type="{{toastType}}"
  position="{{toastPosition}}"
  duration="{{toastDuration}}"
  bind:show="onToastShow"
  bind:hide="onToastHide"
></enhanced-toast>
```

## 📋 API文档

### 属性 (Properties)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示Toast |
| message | String | '' | 消息内容 |
| type | String | 'default' | Toast类型 |
| position | String | 'center' | 显示位置 |
| duration | Number | 2000 | 显示时长(ms) |
| mask | Boolean | false | 是否显示遮罩 |
| zIndex | Number | 9999 | 层级 |

### Toast类型 (type)

| 类型 | 说明 | 图标 | 颜色 |
|------|------|------|------|
| default | 默认样式 | - | 黑色背景 |
| success | 成功提示 | ✅ | 绿色背景 |
| error | 错误提示 | ❌ | 红色背景 |
| warning | 警告提示 | ⚠️ | 橙色背景 |
| info | 信息提示 | ℹ️ | 蓝色背景 |
| loading | 加载提示 | ⏳ | 黑色背景 |

### 显示位置 (position)

| 位置 | 说明 |
|------|------|
| top | 顶部显示 |
| center | 居中显示 |
| bottom | 底部显示 |

### 事件 (Events)

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| show | Toast显示时触发 | {message, type} |
| hide | Toast隐藏时触发 | {message, type} |
| tap | 点击Toast时触发 | {message, type} |

## 🎯 使用示例

### 基础示例

```javascript
Page({
  mixins: [toastMixin],
  
  // 显示不同类型的Toast
  showSuccess() {
    this.$success('操作成功')
  },
  
  showError() {
    this.$error('网络错误，请重试')
  },
  
  showWarning() {
    this.$warning('请填写完整信息')
  },
  
  showInfo() {
    this.$info('这是一条提示信息')
  },
  
  showLoading() {
    const toastId = this.$loading('加载中...')
    
    // 3秒后隐藏
    setTimeout(() => {
      this.$hideToast(toastId)
    }, 3000)
  }
})
```

### 高级用法

```javascript
Page({
  mixins: [toastMixin],
  
  // 自定义配置
  showCustomToast() {
    this.$toast({
      message: '自定义Toast',
      type: 'info',
      position: 'top',
      duration: 5000,
      mask: true
    })
  },
  
  // 确认对话框
  showConfirm() {
    this.$confirm('确定要删除吗？', {
      type: 'warning',
      actions: [
        { text: '取消', primary: false },
        { text: '确定', primary: true }
      ]
    })
  },
  
  // 进度提示
  showProgress() {
    let progress = 0
    const toastId = this.$progress('上传中...', progress)
    
    const timer = setInterval(() => {
      progress += 10
      this.$setProgress(progress, `上传中... ${progress}%`)
      
      if (progress >= 100) {
        clearInterval(timer)
        this.$success('上传完成')
      }
    }, 200)
  }
})
```

### 队列管理

```javascript
Page({
  mixins: [toastMixin],
  
  // 批量显示Toast
  showMultipleToasts() {
    this.$success('第一条消息')
    this.$info('第二条消息')
    this.$warning('第三条消息')
    
    // Toast会按顺序显示，不会重叠
  },
  
  // 隐藏所有Toast
  hideAllToasts() {
    this.$hideAllToast()
  },
  
  // 获取队列状态
  getQueueStatus() {
    const status = this.getToastQueueStatus()
    console.log('队列状态:', status)
  }
})
```

## 🎨 样式定制

### CSS变量

```css
/* 自定义Toast样式 */
.toast-container {
  --toast-bg-color: rgba(0, 0, 0, 0.8);
  --toast-text-color: #fff;
  --toast-border-radius: 16rpx;
  --toast-padding: 24rpx 32rpx;
}

/* 成功Toast样式 */
.toast-container.success {
  --toast-bg-color: rgba(46, 213, 115, 0.95);
}

/* 错误Toast样式 */
.toast-container.error {
  --toast-bg-color: rgba(255, 71, 87, 0.95);
}
```

### 自定义主题

```javascript
// 在app.js中配置全局主题
App({
  globalData: {
    toastTheme: {
      success: {
        backgroundColor: '#2ed573',
        color: '#fff',
        icon: '✅'
      },
      error: {
        backgroundColor: '#ff4757',
        color: '#fff',
        icon: '❌'
      }
    }
  }
})
```

## 📊 性能优化

### 复用率统计

Toast组件具有智能复用机制，相同消息会自动复用：

```javascript
// 获取复用统计
const stats = this.getToastStats()
console.log('复用率:', stats.reuseRate) // 94.2%
console.log('最常用类型:', stats.mostUsedType) // success
```

### 内存优化

- 自动清理过期Toast
- 智能队列管理
- 复用相同消息实例

## 🐛 常见问题

### Q: Toast不显示怎么办？

A: 检查以下几点：
1. 确保引入了toast-mixin
2. 检查zIndex是否被其他元素覆盖
3. 确认message内容不为空

### Q: 如何自定义Toast样式？

A: 可以通过以下方式：
1. 修改CSS变量
2. 覆盖组件样式类
3. 使用自定义主题配置

### Q: Toast队列如何管理？

A: Toast组件内置队列管理：
- 自动排队显示
- 防止重复消息
- 支持批量操作

## 🔧 最佳实践

### 1. 合理使用Toast类型

```javascript
// ✅ 推荐：根据操作结果选择合适类型
this.$success('保存成功')  // 成功操作
this.$error('网络错误')    // 错误提示
this.$warning('请填写必填项') // 警告提示
this.$info('新功能上线')   // 信息通知

// ❌ 不推荐：类型使用不当
this.$error('保存成功')    // 类型与内容不符
```

### 2. 控制显示时长

```javascript
// ✅ 推荐：根据内容长度调整时长
this.$toast({
  message: '短消息',
  duration: 2000  // 2秒
})

this.$toast({
  message: '这是一条比较长的消息内容，需要用户仔细阅读',
  duration: 4000  // 4秒
})

// ❌ 不推荐：所有消息使用相同时长
```

### 3. 避免频繁调用

```javascript
// ✅ 推荐：合并相关消息
this.$success('数据保存成功，页面即将刷新')

// ❌ 不推荐：连续显示多个Toast
this.$success('保存成功')
this.$info('页面刷新中')
```

## 📈 更新日志

### v1.3.0
- ✨ 新增进度Toast功能
- ✨ 新增确认对话框功能
- 🎨 优化队列管理机制
- 🐛 修复复用率统计问题

### v1.2.0
- ✨ 新增智能复用机制
- 🎨 优化动画效果
- 📱 适配更多设备尺寸

### v1.1.0
- ✨ 新增队列管理功能
- 🎨 优化样式配置
- 🐛 修复层级问题

### v1.0.0
- 🎉 发布Toast组件
- ✨ 支持6种类型
- ✨ 支持3种位置
