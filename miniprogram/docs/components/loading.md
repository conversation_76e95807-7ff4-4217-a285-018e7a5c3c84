# Loading组件

增强版加载组件，支持多种样式、骨架屏和进度显示。

## 📱 预览效果

![Loading组件预览](../images/loading-preview.png)

## 🚀 快速开始

### 基础用法

```javascript
// 页面中引入混入
const loadingMixin = require('../../mixins/loading-mixin')

Page({
  mixins: [loadingMixin],
  
  async loadData() {
    // 显示加载
    this.$loading('数据加载中...')
    
    try {
      const data = await api.getData()
      this.setData({ data })
    } finally {
      // 隐藏加载
      this.$hideLoading()
    }
  }
})
```

### 模板使用

```xml
<!-- 在页面模板中添加Loading组件 -->
<enhanced-loading
  visible="{{loadingVisible}}"
  type="{{loadingType}}"
  text="{{loadingText}}"
  size="{{loadingSize}}"
  show-progress="{{loadingShowProgress}}"
  progress="{{loadingProgress}}"
  bind:show="onLoadingShow"
  bind:hide="onLoadingHide"
></enhanced-loading>
```

## 📋 API文档

### 属性 (Properties)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示Loading |
| type | String | 'spinner' | Loading类型 |
| size | String | 'medium' | 尺寸大小 |
| color | String | '#667eea' | 主题颜色 |
| backgroundColor | String | 'rgba(255,255,255,0.9)' | 背景色 |
| mask | Boolean | true | 是否显示遮罩 |
| text | String | '加载中...' | 加载文本 |
| progress | Number | 0 | 进度百分比 |
| showProgress | Boolean | false | 是否显示进度 |
| template | String | 'list' | 骨架屏模板 |

### Loading类型 (type)

| 类型 | 说明 | 动画效果 |
|------|------|----------|
| spinner | 旋转加载 | 圆形旋转 |
| dots | 点状加载 | 点状脉冲 |
| bars | 条状加载 | 条状波浪 |
| circle | 圆环加载 | 圆环旋转 |
| skeleton | 骨架屏 | 闪烁效果 |
| progress | 进度条 | 进度填充 |

### 尺寸规格 (size)

| 尺寸 | 宽高 | 字体大小 | 适用场景 |
|------|------|----------|----------|
| small | 32rpx | 24rpx | 按钮内加载 |
| medium | 48rpx | 28rpx | 页面加载 |
| large | 64rpx | 32rpx | 全屏加载 |

### 骨架屏模板 (template)

| 模板 | 说明 | 适用场景 |
|------|------|----------|
| list | 列表骨架屏 | 用户列表、消息列表 |
| card | 卡片骨架屏 | 动态卡片、商品卡片 |
| profile | 个人资料骨架屏 | 用户详情页 |
| article | 文章骨架屏 | 文章详情页 |

### 事件 (Events)

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| show | Loading显示时触发 | {type, text} |
| hide | Loading隐藏时触发 | {type, duration} |
| progress | 进度更新时触发 | {progress} |

## 🎯 使用示例

### 基础Loading

```javascript
Page({
  mixins: [loadingMixin],
  
  // 简单加载
  async simpleLoad() {
    this.$simpleLoading('加载中...')
    await this.fetchData()
    this.$hideLoading()
  },
  
  // 无遮罩加载
  async lightLoad() {
    this.$lightLoading('请稍候...')
    await this.fetchData()
    this.$hideLoading()
  },
  
  // 自定义配置
  async customLoad() {
    this.$loading({
      type: 'dots',
      text: '数据同步中...',
      size: 'large',
      color: '#2ed573'
    })
    await this.syncData()
    this.$hideLoading()
  }
})
```

### 骨架屏Loading

```javascript
Page({
  mixins: [loadingMixin],
  
  // 列表骨架屏
  async loadUserList() {
    this.$listSkeleton(8) // 显示8行骨架屏
    
    const users = await api.getUserList()
    this.setData({ users })
    this.$hideLoading()
  },
  
  // 卡片骨架屏
  async loadCards() {
    this.$cardSkeleton({
      image: true,
      title: true,
      content: true,
      actions: true
    })
    
    const cards = await api.getCards()
    this.setData({ cards })
    this.$hideLoading()
  },
  
  // 个人资料骨架屏
  async loadProfile() {
    this.$profileSkeleton()
    
    const profile = await api.getProfile()
    this.setData({ profile })
    this.$hideLoading()
  }
})
```

### 进度Loading

```javascript
Page({
  mixins: [loadingMixin],
  
  // 文件上传进度
  async uploadFile(file) {
    this.$progress('上传中...', {
      showProgress: true
    })
    
    const uploadTask = wx.uploadFile({
      url: 'https://api.example.com/upload',
      filePath: file.path,
      name: 'file'
    })
    
    uploadTask.onProgressUpdate((res) => {
      this.$setProgress(res.progress, `上传中... ${res.progress}%`)
    })
    
    try {
      await uploadTask
      this.$success('上传完成')
    } catch (error) {
      this.$error('上传失败')
    }
  },
  
  // 数据处理进度
  async processData(data) {
    this.$progress('处理中...', {
      showProgress: true
    })
    
    const total = data.length
    
    for (let i = 0; i < total; i++) {
      await this.processItem(data[i])
      
      const progress = Math.round((i + 1) / total * 100)
      this.$setProgress(progress, `处理中... ${i + 1}/${total}`)
    }
    
    this.$success('处理完成')
  }
})
```

## 🎨 样式定制

### CSS变量

```css
/* 自定义Loading样式 */
.loading-container {
  --loading-bg-color: rgba(255, 255, 255, 0.9);
  --loading-text-color: #333;
  --loading-border-radius: 16rpx;
  --loading-padding: 40rpx;
}

/* 不同类型的样式 */
.loading-container.type-spinner {
  --spinner-color: #667eea;
  --spinner-size: 48rpx;
}

.loading-container.type-dots {
  --dots-color: #667eea;
  --dots-size: 12rpx;
}
```

### 骨架屏定制

```css
/* 自定义骨架屏样式 */
.skeleton-container {
  --skeleton-bg-color: #f0f0f0;
  --skeleton-highlight-color: #e0e0e0;
  --skeleton-border-radius: 4rpx;
}

/* 动画效果定制 */
.skeleton-item.animation-shimmer::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}
```

## 📊 性能优化

### 体验评分系统

Loading组件内置体验评分系统：

```javascript
// 获取体验统计
const stats = this.getLoadingStats()
console.log('体验评分:', stats.averageExperienceScore) // 4.7分
console.log('平均时长:', stats.averageDuration) // 1850ms
console.log('最常用类型:', stats.mostUsedType) // skeleton
```

### 智能时长估算

```javascript
// 组件会根据历史数据智能估算加载时长
estimateLoadingTime(loadingConfig) {
  let estimatedTime = this.stats.averageDuration || 2000
  
  switch (loadingConfig.type) {
    case 'skeleton':
      estimatedTime = 1500 // 骨架屏通常较快
      break
    case 'progress':
      estimatedTime = 3000 // 进度条通常较慢
      break
  }
  
  return estimatedTime
}
```

## 🐛 常见问题

### Q: 骨架屏不显示怎么办？

A: 检查以下几点：
1. 确保template参数正确
2. 检查骨架屏组件是否正确引入
3. 确认容器高度设置

### Q: 如何自定义骨架屏模板？

A: 可以通过以下方式：
1. 修改现有模板配置
2. 创建新的模板类型
3. 使用自定义骨架屏组件

### Q: 进度更新不及时怎么办？

A: 确保：
1. 及时调用setProgress方法
2. 进度值在0-100范围内
3. 避免频繁更新造成性能问题

## 🔧 最佳实践

### 1. 选择合适的Loading类型

```javascript
// ✅ 推荐：根据场景选择合适类型
this.$skeleton('list')        // 列表加载用骨架屏
this.$progress('上传中...')   // 文件操作用进度条
this.$simpleLoading('请稍候') // 简单操作用spinner

// ❌ 不推荐：所有场景使用相同类型
this.$loading('加载中...')   // 缺乏针对性
```

### 2. 合理设置加载文本

```javascript
// ✅ 推荐：提供有意义的加载文本
this.$loading('正在获取用户信息...')
this.$loading('正在上传图片...')
this.$loading('正在保存数据...')

// ❌ 不推荐：使用模糊的文本
this.$loading('加载中...')
this.$loading('请稍候...')
```

### 3. 及时隐藏Loading

```javascript
// ✅ 推荐：使用try-finally确保隐藏
async loadData() {
  this.$loading('加载中...')
  
  try {
    const data = await api.getData()
    this.setData({ data })
  } finally {
    this.$hideLoading() // 确保一定会隐藏
  }
}

// ❌ 不推荐：可能遗漏隐藏
async loadData() {
  this.$loading('加载中...')
  const data = await api.getData()
  this.setData({ data })
  this.$hideLoading() // 如果出错可能不会执行
}
```

## 📈 更新日志

### v1.3.0
- ✨ 新增虚拟列表骨架屏
- ✨ 新增进度条动画效果
- 🎨 优化体验评分算法
- 🐛 修复深色模式显示问题

### v1.2.0
- ✨ 新增4种骨架屏模板
- ✨ 新增智能时长估算
- 🎨 优化动画性能
- 📱 适配更多设备

### v1.1.0
- ✨ 新增进度显示功能
- 🎨 优化Loading样式
- 🐛 修复遮罩层问题

### v1.0.0
- 🎉 发布Loading组件
- ✨ 支持6种加载类型
- ✨ 支持3种尺寸规格
