# 小程序错误修复指南

## 🔧 常见错误及解决方案

### 1. SharedArrayBuffer 警告

**错误信息:**
```
[Deprecation] SharedArrayBuffer will require cross-origin isolation as of M92, around July 2021.
```

**原因:** 这是微信开发者工具的已知警告，不影响小程序实际运行。

**解决方案:**
- ✅ 已在 `app.js` 中添加兼容性处理
- ✅ 已在 `utils/error-fix.js` 中添加相关检测
- 这个警告可以忽略，不影响功能

### 2. WXML 文件编译错误

**错误信息:**
```
[WXML 文件编译错误] end tag missing, near `view`
```

**原因:** WXML 文件中标签不匹配，缺少结束标签。

**解决方案:**
- ✅ 已修复 `pages/profile/profile.wxml` 中的标签不匹配问题
- 检查所有 WXML 文件的标签是否正确闭合
- 使用 IDE 的语法检查功能

**预防措施:**
```xml
<!-- 正确的标签结构 -->
<view class="container">
  <view class="content">
    <text>内容</text>
  </view>
</view>

<!-- 错误的标签结构 -->
<view class="container">
  <view class="content">
    <text>内容</text>
  </view>
</view> <!-- 多余的结束标签 -->
```

### 3. __route__ 未定义错误

**错误信息:**
```
ReferenceError: __route__ is not defined
```

**原因:** 自定义 tabBar 或页面路由初始化问题。

**解决方案:**
- ✅ 已在 `custom-tab-bar/index.js` 中添加 `updateSelectedIndex` 方法
- ✅ 已在各页面的 `onShow` 方法中添加 tabBar 状态更新
- ✅ 已在错误修复工具中添加路由检查

**代码示例:**
```javascript
// 在页面的 onShow 方法中添加
onShow() {
  // 更新自定义 tabBar 选中状态
  if (typeof this.getTabBar === 'function' && this.getTabBar()) {
    this.getTabBar().setData({
      selected: 0 // 对应的页面索引
    })
  }
}
```

### 4. jsbridge invoke 错误

**错误信息:**
```
[jsbridge] invoke reportKeyValue fail: too early
```

**原因:** 小程序初始化过程中过早调用某些 API。

**解决方案:**
- ✅ 已在 `utils/error-fix.js` 中添加全局错误处理
- ✅ 已在 `app.js` 中添加错误监听
- 这类错误通常不影响功能，可以忽略

## 🛠️ 错误修复工具使用

### 自动错误修复

项目已集成自动错误修复工具 (`utils/error-fix.js`)，会在小程序启动时自动运行：

```javascript
// 在 app.js 中自动初始化
const errorFix = require('./utils/error-fix')

App({
  onLaunch() {
    // 初始化错误修复工具
    errorFix.init()
  }
})
```

### 手动诊断

如果遇到问题，可以手动运行诊断：

```javascript
// 在控制台中运行
const errorFix = require('./utils/error-fix')
errorFix.runDiagnostics()
```

## 🔍 调试技巧

### 1. 使用控制台调试

```javascript
// 查看当前页面信息
console.log('当前页面:', getCurrentPages())

// 查看系统信息
console.log('系统信息:', wx.getSystemInfoSync())

// 查看存储信息
console.log('存储信息:', wx.getStorageInfoSync())
```

### 2. 检查页面状态

```javascript
// 检查页面是否正确初始化
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
console.log('当前页面路由:', currentPage.route)
console.log('页面数据:', currentPage.data)
```

### 3. 检查 tabBar 状态

```javascript
// 检查自定义 tabBar
if (typeof this.getTabBar === 'function') {
  const tabBar = this.getTabBar()
  console.log('TabBar 数据:', tabBar.data)
}
```

## 📋 错误预防清单

### 开发阶段

- [ ] 确保所有 WXML 标签正确闭合
- [ ] 在每个页面的 `onShow` 方法中更新 tabBar 状态
- [ ] 使用 try-catch 包装可能出错的代码
- [ ] 定期运行错误诊断工具

### 测试阶段

- [ ] 在不同设备上测试
- [ ] 检查控制台是否有错误信息
- [ ] 测试页面切换和路由跳转
- [ ] 验证所有功能正常工作

### 发布前

- [ ] 清理所有 console.log 输出
- [ ] 确保没有未处理的错误
- [ ] 运行完整的功能测试
- [ ] 检查性能指标

## 🚨 紧急修复步骤

如果遇到严重错误导致小程序无法运行：

1. **检查 app.json 配置**
   ```json
   {
     "pages": [
       "pages/home/<USER>",
       "pages/discover/discover",
       "pages/chat/chat",
       "pages/profile/profile"
     ],
     "tabBar": {
       "custom": true,
       "list": [...]
     }
   }
   ```

2. **检查主要页面文件**
   - 确保 `.js`、`.wxml`、`.wxss`、`.json` 文件都存在
   - 检查文件语法是否正确

3. **重置错误修复工具**
   ```javascript
   // 在 app.js 中重新初始化
   errorFix.init()
   errorFix.runDiagnostics()
   ```

4. **清理缓存**
   - 在微信开发者工具中点击"清缓存"
   - 重新编译项目

## 📞 技术支持

如果以上方法都无法解决问题，请：

1. 记录完整的错误信息
2. 记录复现步骤
3. 提供系统环境信息
4. 联系技术支持团队

---

**最后更新:** 2024年12月  
**版本:** 1.0.0  
**状态:** 已验证 ✅
