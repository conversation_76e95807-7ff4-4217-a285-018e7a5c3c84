# 快速开始

本指南将帮助您快速上手相亲交友小程序组件库。

## 📦 安装配置

### 1. 项目结构

确保您的项目结构如下：

```
miniprogram/
├── components/           # 组件目录
│   ├── enhanced-toast/
│   ├── enhanced-loading/
│   ├── skeleton-screen/
│   ├── enhanced-swiper/
│   ├── image-preview/
│   ├── pull-refresh/
│   └── virtual-list/
├── mixins/              # 混入目录
│   ├── toast-mixin.js
│   └── loading-mixin.js
├── utils/               # 工具目录
│   ├── toast-manager.js
│   └── loading-manager.js
└── pages/               # 页面目录
```

### 2. 全局配置

在 `app.json` 中配置全局组件：

```json
{
  "usingComponents": {
    "enhanced-toast": "/components/enhanced-toast/enhanced-toast",
    "enhanced-loading": "/components/enhanced-loading/enhanced-loading",
    "skeleton-screen": "/components/skeleton-screen/skeleton-screen",
    "enhanced-swiper": "/components/enhanced-swiper/enhanced-swiper",
    "image-preview": "/components/image-preview/image-preview",
    "pull-refresh": "/components/pull-refresh/pull-refresh",
    "virtual-list": "/components/virtual-list/virtual-list"
  }
}
```

## 🚀 第一个示例

### 创建用户列表页面

让我们创建一个包含多个组件的用户列表页面：

#### 1. 页面配置 (pages/user-list/user-list.json)

```json
{
  "navigationBarTitleText": "用户列表",
  "usingComponents": {
    "enhanced-toast": "/components/enhanced-toast/enhanced-toast",
    "enhanced-loading": "/components/enhanced-loading/enhanced-loading",
    "pull-refresh": "/components/pull-refresh/pull-refresh",
    "virtual-list": "/components/virtual-list/virtual-list"
  }
}
```

#### 2. 页面逻辑 (pages/user-list/user-list.js)

```javascript
const toastMixin = require('../../mixins/toast-mixin')
const loadingMixin = require('../../mixins/loading-mixin')

Page({
  // 引入混入
  mixins: [toastMixin, loadingMixin],
  
  data: {
    userList: [],
    loading: false,
    refreshing: false
  },

  onLoad() {
    this.loadUserList()
  },

  // 加载用户列表
  async loadUserList() {
    try {
      // 显示骨架屏
      this.$listSkeleton(8)
      
      // 模拟API调用
      const users = await this.fetchUsers()
      
      this.setData({ userList: users })
      this.$success('加载完成')
    } catch (error) {
      this.$error('加载失败，请重试')
    } finally {
      this.$hideLoading()
    }
  },

  // 下拉刷新
  async onRefresh(e) {
    const { complete } = e.detail
    
    try {
      this.setData({ refreshing: true })
      
      // 重新加载数据
      const users = await this.fetchUsers()
      this.setData({ userList: users })
      
      this.$success('刷新成功')
      complete(true)
    } catch (error) {
      this.$error('刷新失败')
      complete(false)
    } finally {
      this.setData({ refreshing: false })
    }
  },

  // 点击用户项
  onUserTap(e) {
    const { index, item } = e.detail
    
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?id=${item.id}`
    })
  },

  // 模拟API调用
  fetchUsers() {
    return new Promise((resolve) => {
      setTimeout(() => {
        const users = Array.from({ length: 50 }, (_, i) => ({
          id: i + 1,
          name: `用户${i + 1}`,
          avatar: `https://picsum.photos/100/100?random=${i}`,
          age: 20 + Math.floor(Math.random() * 20),
          location: ['北京', '上海', '广州', '深圳'][Math.floor(Math.random() * 4)]
        }))
        resolve(users)
      }, 1000)
    })
  }
})
```

#### 3. 页面模板 (pages/user-list/user-list.wxml)

```xml
<view class="user-list-page">
  <!-- 下拉刷新容器 -->
  <pull-refresh
    enabled="{{true}}"
    bind:refresh="onRefresh"
  >
    <!-- 虚拟列表 -->
    <virtual-list
      items="{{userList}}"
      item-height="{{120}}"
      height="100vh"
      bind:itemtap="onUserTap"
    >
      <!-- 自定义用户项模板 -->
      <template slot="item">
        <view class="user-item">
          <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="user-info">
            <view class="user-name">{{item.name}}</view>
            <view class="user-details">{{item.age}}岁 · {{item.location}}</view>
          </view>
          <view class="user-action">
            <button class="action-btn">查看</button>
          </view>
        </view>
      </template>
    </virtual-list>
  </pull-refresh>

  <!-- Toast组件 -->
  <enhanced-toast
    visible="{{toastVisible}}"
    message="{{toastMessage}}"
    type="{{toastType}}"
    bind:show="onToastShow"
    bind:hide="onToastHide"
  ></enhanced-toast>

  <!-- Loading组件 -->
  <enhanced-loading
    visible="{{loadingVisible}}"
    type="{{loadingType}}"
    text="{{loadingText}}"
    template="{{loadingTemplate}}"
    bind:show="onLoadingShow"
    bind:hide="onLoadingHide"
  ></enhanced-loading>
</view>
```

#### 4. 页面样式 (pages/user-list/user-list.wxss)

```css
.user-list-page {
  height: 100vh;
  background-color: #f8f9fa;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-details {
  font-size: 26rpx;
  color: #666;
}

.user-action {
  margin-left: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  background-color: #667eea;
  color: #fff;
  border-radius: 20rpx;
  border: none;
}
```

## 🎯 常用场景示例

### 1. 图片轮播展示

```xml
<!-- 首页轮播图 -->
<enhanced-swiper
  items="{{banners}}"
  height="400rpx"
  autoplay="{{true}}"
  interval="{{4000}}"
  indicator-type="dots"
  effect="slide"
  bind:tap="onBannerTap"
></enhanced-swiper>
```

### 2. 图片预览功能

```javascript
Page({
  data: {
    showPreview: false,
    previewImages: [],
    currentIndex: 0
  },

  // 预览图片
  previewImage(e) {
    const { images, current } = e.currentTarget.dataset
    
    this.setData({
      showPreview: true,
      previewImages: images,
      currentIndex: current || 0
    })
  },

  // 关闭预览
  closePreview() {
    this.setData({ showPreview: false })
  }
})
```

```xml
<!-- 图片网格 -->
<view class="image-grid">
  <image
    wx:for="{{photos}}"
    wx:key="id"
    src="{{item.url}}"
    data-images="{{photos}}"
    data-current="{{index}}"
    bindtap="previewImage"
  ></image>
</view>

<!-- 图片预览 -->
<image-preview
  visible="{{showPreview}}"
  images="{{previewImages}}"
  current="{{currentIndex}}"
  bind:hide="closePreview"
></image-preview>
```

### 3. 表单提交反馈

```javascript
Page({
  mixins: [toastMixin, loadingMixin],

  // 提交表单
  async submitForm(formData) {
    // 显示加载
    this.$loading('提交中...')
    
    try {
      await api.submitForm(formData)
      this.$success('提交成功')
      
      // 跳转到成功页面
      wx.redirectTo({
        url: '/pages/success/success'
      })
    } catch (error) {
      this.$error('提交失败，请重试')
    } finally {
      this.$hideLoading()
    }
  }
})
```

## 🎨 主题定制

### 1. 全局主题配置

在 `app.js` 中配置全局主题：

```javascript
App({
  globalData: {
    theme: {
      primary: '#667eea',
      success: '#2ed573',
      warning: '#ffa502',
      error: '#ff4757',
      info: '#3742fa'
    }
  }
})
```

### 2. 组件主题定制

```css
/* 自定义Toast主题 */
.toast-container.success {
  background-color: var(--success-color, #2ed573);
}

/* 自定义Loading主题 */
.loading-container {
  --loading-primary-color: #667eea;
}

/* 自定义Swiper主题 */
.swiper-indicators .indicator-item.active {
  background-color: var(--primary-color, #667eea);
}
```

## 📱 响应式适配

### 1. 屏幕尺寸适配

```css
/* 小屏幕适配 */
@media (max-width: 375px) {
  .user-item {
    padding: 16rpx 20rpx;
  }
  
  .user-avatar {
    width: 60rpx;
    height: 60rpx;
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  .user-item {
    padding: 24rpx 40rpx;
  }
}
```

### 2. 深色模式适配

```css
/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .user-list-page {
    background-color: #1a1a1a;
  }
  
  .user-item {
    background-color: #2a2a2a;
    border-bottom-color: #444;
  }
  
  .user-name {
    color: #fff;
  }
  
  .user-details {
    color: #ccc;
  }
}
```

## 🔧 性能优化建议

### 1. 图片优化

```javascript
// 使用适当尺寸的图片
const optimizedImages = images.map(img => ({
  ...img,
  url: `${img.url}?w=750&h=400&q=80` // 添加图片处理参数
}))
```

### 2. 数据分页加载

```javascript
Page({
  data: {
    userList: [],
    page: 1,
    hasMore: true
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore) return
    
    const newUsers = await this.fetchUsers(this.data.page)
    
    this.setData({
      userList: [...this.data.userList, ...newUsers],
      page: this.data.page + 1,
      hasMore: newUsers.length > 0
    })
  }
})
```

### 3. 组件按需加载

```json
{
  "usingComponents": {
    "enhanced-toast": "/components/enhanced-toast/enhanced-toast"
  },
  "lazyCodeLoading": "requiredComponents"
}
```

## 🐛 常见问题解决

### 1. 组件不显示

检查以下几点：
- 组件是否正确引入
- 数据绑定是否正确
- 样式是否被覆盖

### 2. 性能问题

优化建议：
- 减少不必要的setData调用
- 使用虚拟列表处理大数据
- 优化图片资源

### 3. 兼容性问题

确保：
- 基础库版本符合要求
- 使用兼容的API
- 添加降级方案

## 📚 进阶学习

- [组件开发指南](./component-development.md)
- [性能优化指南](./performance.md)
- [最佳实践](./best-practices.md)
- [常见问题](./faq.md)
