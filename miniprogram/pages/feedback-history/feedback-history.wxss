/**
 * 反馈历史页面样式
 * Task 1.4: 用户反馈收集系统 - 历史页面样式
 */

.feedback-history {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.history-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: #fff;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-desc {
  font-size: 26rpx;
  opacity: 0.8;
  line-height: 1.4;
}

/* 统计卡片 */
.stats-card {
  background: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-value.success {
  color: #2ed573;
}

.stat-value.warning {
  color: #ffa502;
}

.stat-value.primary {
  color: #3742fa;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background-color: #f8f9fa;
  font-size: 26rpx;
  color: #333;
  transition: all 0.2s ease;
}

.filter-btn:active {
  background-color: #e9ecef;
}

.filter-btn.active {
  background-color: #007aff;
  color: #fff;
}

.filter-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.create-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  font-size: 26rpx;
  transition: all 0.2s ease;
}

.create-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 反馈列表 */
.feedback-list {
  padding: 0 30rpx 30rpx;
}

.feedback-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.feedback-item:active {
  transform: scale(0.98);
}

.feedback-content {
  padding: 30rpx;
}

.feedback-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.feedback-info {
  flex: 1;
  min-width: 0;
}

.feedback-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  word-break: break-all;
}

.feedback-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 12rpx;
}

.category-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
}

.feedback-time {
  font-size: 24rpx;
  color: #999;
}

.feedback-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.feedback-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.rating-star {
  font-size: 24rpx;
  color: #ddd;
}

.rating-star.active {
  color: #ffa502;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.feedback-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f8f9fa;
  background-color: #fafbfc;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
}

.action-btn.danger {
  background-color: #ffe6e6;
  color: #ff4757;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 筛选器 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
}

.filter-content {
  width: 100%;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.clear-btn {
  font-size: 26rpx;
  color: #007aff;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  font-size: 26rpx;
  color: #333;
  background-color: #fff;
  transition: all 0.2s ease;
}

.filter-option:active {
  transform: scale(0.95);
}

.filter-option.active {
  border-color: #007aff;
  background-color: #007aff;
  color: #fff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  text-align: center;
  line-height: 1.5;
}

.empty-action {
  margin-top: 30rpx;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #999;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .feedback-list {
    padding: 0 20rpx 20rpx;
  }
  
  .feedback-content {
    padding: 24rpx;
  }
  
  .category-icon {
    width: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
  }
  
  .feedback-title {
    font-size: 28rpx;
  }
  
  .feedback-desc {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.feedback-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .feedback-history {
    background-color: #1a1a1a;
  }
  
  .stats-card,
  .feedback-item,
  .filter-content {
    background-color: #2a2a2a;
  }
  
  .feedback-title,
  .filter-title,
  .filter-section-title {
    color: #fff;
  }
  
  .feedback-desc,
  .stat-label,
  .rating-text {
    color: #ccc;
  }
  
  .action-bar {
    background-color: #2a2a2a;
    border-bottom-color: #333;
  }
  
  .filter-btn {
    background-color: #333;
    color: #ccc;
  }
  
  .filter-option {
    background-color: #333;
    border-color: #555;
    color: #ccc;
  }
}
