<!--
  反馈历史页面模板
  Task 1.4: 用户反馈收集系统 - 历史页面模板
-->

<view class="feedback-history">
  <!-- 页面头部 -->
  <view class="history-header">
    <view class="header-title">反馈历史</view>
    <view class="header-desc">查看您提交的反馈和处理状态</view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-card" bindtap="viewStats">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-value primary">{{stats.totalSubmitted}}</view>
        <view class="stat-label">总提交数</view>
      </view>
      <view class="stat-item">
        <view class="stat-value success">{{stats.totalProcessed}}</view>
        <view class="stat-label">已处理数</view>
      </view>
      <view class="stat-item">
        <view class="stat-value warning">{{stats.averageRating.toFixed(1)}}分</view>
        <view class="stat-label">平均评分</view>
      </view>
      <view class="stat-item">
        <view class="stat-value {{stats.collectionRate >= 80 ? 'success' : 'warning'}}">
          {{stats.collectionRate.toFixed(1)}}%
        </view>
        <view class="stat-label">收集率</view>
      </view>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <view 
      class="filter-btn {{filterCategory || filterStatus ? 'active' : ''}}"
      bindtap="showFilter"
    >
      <view class="filter-icon">🔍</view>
      <text>筛选</text>
    </view>
    
    <view class="create-btn" bindtap="createFeedback">
      <text>新建反馈</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{isEmpty}}">
    <view class="empty-icon">📝</view>
    <view class="empty-text">
      <view>暂无反馈记录</view>
      <view wx:if="{{filterCategory || filterStatus}}">试试调整筛选条件</view>
    </view>
    <view class="empty-action" bindtap="createFeedback" wx:if="{{!filterCategory && !filterStatus}}">
      提交第一个反馈
    </view>
  </view>

  <!-- 反馈列表 -->
  <view class="feedback-list" wx:else>
    <view 
      class="feedback-item"
      wx:for="{{feedbacks}}"
      wx:key="id"
      data-feedback="{{item}}"
      bindtap="onFeedbackTap"
    >
      <view class="feedback-content">
        <view class="feedback-header">
          <view 
            class="category-icon"
            style="background-color: {{item.categoryInfo.color}}20; color: {{item.categoryInfo.color}}"
          >
            {{item.categoryInfo.icon}}
          </view>
          
          <view class="feedback-info">
            <view class="feedback-title">
              {{item.title || item.description.substring(0, 30) + '...'}}
            </view>
            
            <view class="feedback-meta">
              <view 
                class="category-tag"
                style="background-color: {{item.categoryInfo.color}}"
              >
                {{item.categoryInfo.name}}
              </view>
              
              <view 
                class="status-tag"
                style="background-color: {{item.statusColor}}"
              >
                {{item.statusText}}
              </view>
              
              <view class="feedback-time">{{item.displayTime}}</view>
            </view>
            
            <view class="feedback-desc">{{item.description}}</view>
            
            <view class="feedback-rating" wx:if="{{item.rating > 0}}">
              <view class="rating-stars">
                <view 
                  class="rating-star {{index < item.rating ? 'active' : ''}}"
                  wx:for="{{[1,2,3,4,5]}}"
                  wx:key="*this"
                >
                  ★
                </view>
              </view>
              <view class="rating-text">{{item.rating}}分</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="feedback-actions">
        <view 
          class="action-btn secondary"
          data-feedback="{{item}}"
          bindtap="shareFeedback"
          catchtap="shareFeedback"
        >
          分享
        </view>
        <view 
          class="action-btn danger"
          data-feedback="{{item}}"
          bindtap="deleteFeedback"
          catchtap="deleteFeedback"
        >
          删除
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 筛选器 -->
<view class="filter-overlay" wx:if="{{showFilter}}">
  <view class="filter-content">
    <view class="filter-header">
      <view class="filter-title">筛选条件</view>
      <view class="clear-btn" bindtap="clearFilter">清除</view>
    </view>
    
    <!-- 分类筛选 -->
    <view class="filter-section">
      <view class="filter-section-title">问题分类</view>
      <view class="filter-options">
        <view 
          class="filter-option {{filterCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          data-category="{{item.id}}"
          bindtap="onFilterCategoryChange"
        >
          {{item.icon}} {{item.name}}
        </view>
      </view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="filter-section">
      <view class="filter-section-title">处理状态</view>
      <view class="filter-options">
        <view 
          class="filter-option {{filterStatus === 'submitted' ? 'active' : ''}}"
          data-status="submitted"
          bindtap="onFilterStatusChange"
        >
          已提交
        </view>
        <view 
          class="filter-option {{filterStatus === 'processing' ? 'active' : ''}}"
          data-status="processing"
          bindtap="onFilterStatusChange"
        >
          处理中
        </view>
        <view 
          class="filter-option {{filterStatus === 'resolved' ? 'active' : ''}}"
          data-status="resolved"
          bindtap="onFilterStatusChange"
        >
          已解决
        </view>
        <view 
          class="filter-option {{filterStatus === 'closed' ? 'active' : ''}}"
          data-status="closed"
          bindtap="onFilterStatusChange"
        >
          已关闭
        </view>
      </view>
    </view>
  </view>
  
  <view class="filter-mask" bindtap="hideFilter"></view>
</view>
