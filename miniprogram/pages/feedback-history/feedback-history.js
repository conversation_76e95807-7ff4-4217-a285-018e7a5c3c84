/**
 * 反馈历史页面
 * Task 1.4: 用户反馈收集系统 - 历史页面
 * 功能：查看反馈历史、状态跟踪
 */

const feedbackManager = require('../../utils/feedback')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 反馈列表
    feedbacks: [],
    
    // 筛选条件
    filterCategory: '',
    filterStatus: '',
    
    // 分类列表
    categories: [],
    
    // 统计信息
    stats: {
      totalSubmitted: 0,
      totalProcessed: 0,
      averageRating: 0,
      collectionRate: 0
    },
    
    // UI状态
    loading: false,
    isEmpty: false,
    showFilter: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCategories()
    this.loadFeedbacks()
    this.loadStats()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadFeedbacks()
    this.loadStats()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadFeedbacks()
    this.loadStats()
    
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 加载分类列表
   */
  loadCategories() {
    const categories = feedbackManager.getCategories()
    this.setData({ categories })
  },

  /**
   * 加载反馈列表
   */
  loadFeedbacks() {
    this.setData({ loading: true })
    
    try {
      const options = {}
      
      // 应用筛选条件
      if (this.data.filterCategory) {
        options.category = this.data.filterCategory
      }
      
      if (this.data.filterStatus) {
        options.status = this.data.filterStatus
      }
      
      const feedbacks = feedbackManager.getFeedbacks(options)
      
      // 处理反馈数据，添加显示所需的字段
      const processedFeedbacks = feedbacks.map(feedback => {
        const category = this.data.categories.find(cat => cat.id === feedback.category)
        
        return {
          ...feedback,
          categoryInfo: category,
          displayTime: this.formatTime(feedback.timestamp),
          statusText: this.getStatusText(feedback.status),
          statusColor: this.getStatusColor(feedback.status)
        }
      })
      
      this.setData({
        feedbacks: processedFeedbacks,
        isEmpty: processedFeedbacks.length === 0,
        loading: false
      })
    } catch (error) {
      console.error('加载反馈列表失败:', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载统计信息
   */
  loadStats() {
    const stats = feedbackManager.getStats()
    this.setData({ stats })
  },

  /**
   * 格式化时间显示
   * @param {number} timestamp 时间戳
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date
    
    // 今天
    if (date.toDateString() === now.toDateString()) {
      return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
    
    // 昨天
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    if (date.toDateString() === yesterday.toDateString()) {
      return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
    
    // 本年
    if (date.getFullYear() === now.getFullYear()) {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
    
    // 其他年份
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
  },

  /**
   * 获取状态文本
   * @param {string} status 状态
   */
  getStatusText(status) {
    const statusMap = {
      submitted: '已提交',
      uploaded: '已上传',
      pending: '待处理',
      processing: '处理中',
      resolved: '已解决',
      closed: '已关闭'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取状态颜色
   * @param {string} status 状态
   */
  getStatusColor(status) {
    const colorMap = {
      submitted: '#ffa502',
      uploaded: '#3742fa',
      pending: '#ff4757',
      processing: '#2ed573',
      resolved: '#1dd1a1',
      closed: '#747d8c'
    }
    return colorMap[status] || '#747d8c'
  },

  /**
   * 点击反馈项
   * @param {Object} e 事件对象
   */
  onFeedbackTap(e) {
    const feedback = e.currentTarget.dataset.feedback
    
    // 跳转到反馈详情页面
    wx.navigateTo({
      url: `/pages/feedback-detail/feedback-detail?id=${feedback.id}`
    })
  },

  /**
   * 显示筛选器
   */
  showFilter() {
    this.setData({ showFilter: true })
  },

  /**
   * 隐藏筛选器
   */
  hideFilter() {
    this.setData({ showFilter: false })
  },

  /**
   * 选择筛选分类
   * @param {Object} e 事件对象
   */
  onFilterCategoryChange(e) {
    const category = e.currentTarget.dataset.category
    
    this.setData({
      filterCategory: category === this.data.filterCategory ? '' : category
    })
    
    this.loadFeedbacks()
  },

  /**
   * 选择筛选状态
   * @param {Object} e 事件对象
   */
  onFilterStatusChange(e) {
    const status = e.currentTarget.dataset.status
    
    this.setData({
      filterStatus: status === this.data.filterStatus ? '' : status
    })
    
    this.loadFeedbacks()
  },

  /**
   * 清除筛选条件
   */
  clearFilter() {
    this.setData({
      filterCategory: '',
      filterStatus: '',
      showFilter: false
    })
    
    this.loadFeedbacks()
  },

  /**
   * 新建反馈
   */
  createFeedback() {
    wx.navigateTo({
      url: '/pages/feedback-create/feedback-create'
    })
  },

  /**
   * 查看统计详情
   */
  viewStats() {
    const stats = this.data.stats
    
    const content = `
总提交数: ${stats.totalSubmitted}
已处理数: ${stats.totalProcessed}
平均评分: ${stats.averageRating.toFixed(1)}分
收集率: ${stats.collectionRate.toFixed(1)}%
    `.trim()
    
    wx.showModal({
      title: '反馈统计',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 删除反馈
   * @param {Object} e 事件对象
   */
  deleteFeedback(e) {
    e.stopPropagation()
    
    const feedback = e.currentTarget.dataset.feedback
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条反馈吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用删除API
          // feedbackManager.deleteFeedback(feedback.id)
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
          
          // 重新加载列表
          this.loadFeedbacks()
        }
      }
    })
  },

  /**
   * 分享反馈
   * @param {Object} e 事件对象
   */
  shareFeedback(e) {
    e.stopPropagation()
    
    const feedback = e.currentTarget.dataset.feedback
    
    // 生成分享内容
    const shareContent = `我在应用中遇到了${feedback.categoryInfo.name}问题：${feedback.description.substring(0, 50)}...`
    
    wx.setClipboardData({
      data: shareContent,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  }
})
