// pages/dating/feature.js
Page({
  data: {
    features: [
      { id: 1, title: '线下约会', desc: '安排真实约会', icon: '☕', available: true },
      { id: 2, title: '视频通话', desc: '面对面聊天', icon: '📹', available: true },
      { id: 3, title: '约会建议', desc: '智能推荐约会地点', icon: '💡', available: false },
      { id: 4, title: '安全护航', desc: '约会安全保障', icon: '🛡️', available: true }
    ]
  },

  onLoad() {},

  useFeature(e) {
    const { feature } = e.currentTarget.dataset
    if (!feature.available) {
      wx.showToast({ title: '功能即将上线', icon: 'none' })
      return
    }
    wx.showToast({ title: `使用${feature.title}`, icon: 'success' })
  }
})
