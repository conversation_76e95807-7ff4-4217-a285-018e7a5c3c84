<!--pages/dating/feature.wxml-->
<view class="dating-container">
  <!-- 顶部导航 -->
  <view class="dating-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">约会功能</text>
      <view class="header-actions">
        <text class="action-btn" bindtap="goToHistory">📋</text>
        <text class="action-btn" bindtap="createDate">+</text>
      </view>
    </view>

    <!-- 约会统计 -->
    <view class="dating-stats">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{stats.pending_dates}}</text>
          <text class="stat-label">待确认</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{stats.upcoming_dates}}</text>
          <text class="stat-label">即将约会</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{stats.completed_dates}}</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷约会 -->
  <view class="quick-date-section">
    <view class="section-header">
      <text class="section-title">快捷约会</text>
      <text class="section-desc">选择约会类型，快速发起邀请</text>
    </view>
    
    <view class="quick-date-grid">
      <view class="date-type-item" wx:for="{{dateTypes}}" wx:key="id" bindtap="selectDateType" data-type="{{item}}">
        <view class="type-icon {{item.category}}">
          <text class="icon">{{item.icon}}</text>
        </view>
        <text class="type-title">{{item.title}}</text>
        <text class="type-desc">{{item.description}}</text>
        <text class="type-duration">{{item.duration}}</text>
      </view>
    </view>
  </view>

  <!-- 约会邀请 -->
  <view class="date-invitations-section">
    <view class="section-header">
      <text class="section-title">约会邀请</text>
      <view class="filter-tabs">
        <text 
          class="filter-tab {{activeFilter === 'received' ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="received"
        >
          收到的
        </text>
        <text 
          class="filter-tab {{activeFilter === 'sent' ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="sent"
        >
          发出的
        </text>
      </view>
    </view>
    
    <scroll-view class="invitations-list" scroll-y>
      <view class="invitation-item" wx:for="{{invitations}}" wx:key="id">
        <view class="invitation-content" bindtap="viewInvitation" data-invitation="{{item}}">
          <image src="{{item.user.avatar}}" class="invitation-avatar" />
          <view class="invitation-info">
            <view class="invitation-header">
              <text class="invitation-name">{{item.user.nickname}}</text>
              <view class="invitation-badges">
                <view class="vip-badge" wx:if="{{item.user.is_vip}}">
                  <text class="badge-text">VIP</text>
                </view>
                <view class="status-badge {{item.status}}">
                  <text class="badge-text">{{item.status_text}}</text>
                </view>
              </view>
            </view>
            
            <view class="date-details">
              <view class="detail-row">
                <text class="detail-icon">🎯</text>
                <text class="detail-text">{{item.date_type}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-icon">📅</text>
                <text class="detail-text">{{item.proposed_time}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-icon">📍</text>
                <text class="detail-text">{{item.location}}</text>
              </view>
            </view>
            
            <text class="invitation-message" wx:if="{{item.message}}">
              "{{item.message}}"
            </text>
            
            <text class="invitation-time">{{item.created_time}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="invitation-actions" wx:if="{{item.status === 'pending'}}">
          <button 
            class="action-btn accept-btn"
            bindtap="acceptInvitation"
            data-invitation="{{item}}"
            wx:if="{{activeFilter === 'received'}}"
          >
            <text class="btn-icon">✓</text>
            <text class="btn-text">接受</text>
          </button>
          
          <button 
            class="action-btn decline-btn"
            bindtap="declineInvitation"
            data-invitation="{{item}}"
            wx:if="{{activeFilter === 'received'}}"
          >
            <text class="btn-icon">✕</text>
            <text class="btn-text">拒绝</text>
          </button>
          
          <button 
            class="action-btn cancel-btn"
            bindtap="cancelInvitation"
            data-invitation="{{item}}"
            wx:if="{{activeFilter === 'sent'}}"
          >
            <text class="btn-icon">🗑️</text>
            <text class="btn-text">取消</text>
          </button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 即将到来的约会 -->
  <view class="upcoming-dates-section" wx:if="{{upcomingDates.length > 0}}">
    <view class="section-header">
      <text class="section-title">即将到来</text>
      <text class="section-count">{{upcomingDates.length}}个约会</text>
    </view>
    
    <view class="upcoming-dates-list">
      <view class="date-item" wx:for="{{upcomingDates}}" wx:key="id">
        <view class="date-content" bindtap="viewDate" data-date="{{item}}">
          <view class="date-header">
            <image src="{{item.partner.avatar}}" class="partner-avatar" />
            <view class="date-info">
              <text class="partner-name">{{item.partner.nickname}}</text>
              <text class="date-type">{{item.type}}</text>
            </view>
            <view class="date-countdown">
              <text class="countdown-text">{{item.countdown}}</text>
            </view>
          </view>
          
          <view class="date-details">
            <view class="detail-row">
              <text class="detail-icon">📅</text>
              <text class="detail-text">{{item.date_time}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-icon">📍</text>
              <text class="detail-text">{{item.location}}</text>
            </view>
            <view class="detail-row" wx:if="{{item.notes}}">
              <text class="detail-icon">📝</text>
              <text class="detail-text">{{item.notes}}</text>
            </view>
          </view>
        </view>

        <!-- 约会操作 -->
        <view class="date-actions">
          <button 
            class="action-btn chat-btn"
            bindtap="chatWithPartner"
            data-partner="{{item.partner}}"
          >
            <text class="btn-icon">💬</text>
            <text class="btn-text">聊天</text>
          </button>
          
          <button 
            class="action-btn reschedule-btn"
            bindtap="rescheduleDate"
            data-date="{{item}}"
          >
            <text class="btn-icon">📅</text>
            <text class="btn-text">改期</text>
          </button>
          
          <button 
            class="action-btn cancel-btn"
            bindtap="cancelDate"
            data-date="{{item}}"
          >
            <text class="btn-icon">✕</text>
            <text class="btn-text">取消</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 约会建议 -->
  <view class="date-suggestions-section">
    <view class="section-header">
      <text class="section-title">约会建议</text>
      <text class="section-desc">根据你的兴趣推荐</text>
    </view>
    
    <scroll-view class="suggestions-list" scroll-x>
      <view class="suggestion-item" wx:for="{{dateSuggestions}}" wx:key="id" bindtap="viewSuggestion" data-suggestion="{{item}}">
        <image src="{{item.image}}" class="suggestion-image" />
        <view class="suggestion-overlay">
          <text class="suggestion-title">{{item.title}}</text>
          <text class="suggestion-category">{{item.category}}</text>
          <view class="suggestion-rating">
            <text class="rating-stars">{{item.rating_stars}}</text>
            <text class="rating-score">{{item.rating}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 约会记录 -->
  <view class="date-history-section">
    <view class="section-header">
      <text class="section-title">约会记录</text>
      <text class="section-more" bindtap="goToHistory">查看全部</text>
    </view>
    
    <view class="history-list">
      <view class="history-item" wx:for="{{recentHistory}}" wx:key="id">
        <view class="history-content" bindtap="viewHistory" data-history="{{item}}">
          <image src="{{item.partner.avatar}}" class="history-avatar" />
          <view class="history-info">
            <text class="history-partner">{{item.partner.nickname}}</text>
            <text class="history-type">{{item.type}}</text>
            <text class="history-date">{{item.date}}</text>
            <view class="history-rating" wx:if="{{item.rating}}">
              <text class="rating-stars">{{item.rating_stars}}</text>
              <text class="rating-text">{{item.rating_text}}</text>
            </view>
          </view>
        </view>
        
        <button 
          class="rate-btn"
          bindtap="rateDate"
          data-history="{{item}}"
          wx:if="{{!item.rating}}"
        >
          评价
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 创建约会弹窗 -->
<view class="create-date-modal" wx:if="{{showCreateModal}}" bindtap="hideCreateModal">
  <view class="create-content" catchtap="">
    <view class="create-header">
      <text class="create-title">发起约会邀请</text>
      <text class="create-close" bindtap="hideCreateModal">×</text>
    </view>
    
    <view class="create-body">
      <!-- 选择对象 -->
      <view class="create-section">
        <text class="section-label">邀请对象</text>
        <view class="partner-selector" bindtap="selectPartner">
          <image src="{{selectedPartner.avatar}}" class="partner-avatar" wx:if="{{selectedPartner}}" />
          <text class="partner-name">{{selectedPartner ? selectedPartner.nickname : '选择约会对象'}}</text>
          <text class="selector-arrow">></text>
        </view>
      </view>
      
      <!-- 约会类型 -->
      <view class="create-section">
        <text class="section-label">约会类型</text>
        <view class="type-selector">
          <view 
            class="type-option {{selectedType === item.id ? 'selected' : ''}}"
            wx:for="{{dateTypes}}" 
            wx:key="id"
            bindtap="selectType"
            data-type="{{item}}"
          >
            <text class="type-icon">{{item.icon}}</text>
            <text class="type-text">{{item.title}}</text>
          </view>
        </view>
      </view>
      
      <!-- 时间选择 -->
      <view class="create-section">
        <text class="section-label">约会时间</text>
        <picker 
          mode="multiSelector" 
          value="{{dateTimeIndex}}" 
          range="{{dateTimeRange}}"
          bindchange="onDateTimeChange"
        >
          <view class="datetime-picker">
            <text class="datetime-text">{{selectedDateTime || '选择约会时间'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
      
      <!-- 地点选择 -->
      <view class="create-section">
        <text class="section-label">约会地点</text>
        <view class="location-input">
          <input 
            class="location-field" 
            placeholder="输入约会地点"
            value="{{dateLocation}}"
            bindinput="onLocationInput"
          />
          <button class="location-btn" bindtap="selectLocation">📍</button>
        </view>
      </view>
      
      <!-- 邀请留言 -->
      <view class="create-section">
        <text class="section-label">邀请留言</text>
        <textarea 
          class="message-input" 
          placeholder="写一段邀请留言..."
          value="{{inviteMessage}}"
          bindinput="onMessageInput"
          maxlength="200"
        />
        <text class="char-count">{{inviteMessage.length}}/200</text>
      </view>
    </view>
    
    <view class="create-footer">
      <button class="create-cancel" bindtap="hideCreateModal">取消</button>
      <button 
        class="create-confirm"
        bindtap="confirmCreate"
        disabled="{{!canCreate}}"
      >
        发送邀请
      </button>
    </view>
  </view>
</view>
