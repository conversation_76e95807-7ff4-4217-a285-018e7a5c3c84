/* pages/dating/feature.wxss */
.dating-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20rpx;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  position: relative;
}

.feature-item.disabled {
  opacity: 0.6;
}

.feature-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #6b7280;
}

.coming-soon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #f59e0b;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}
