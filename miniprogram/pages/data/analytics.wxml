<!--pages/data/analytics.wxml-->
<view class="analytics-container">
  <!-- 顶部导航 -->
  <view class="analytics-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">数据分析</text>
      <view class="header-actions">
        <text class="action-btn" bindtap="refreshData">🔄</text>
        <text class="action-btn" bindtap="exportData">📊</text>
      </view>
    </view>

    <!-- 时间筛选 -->
    <view class="time-filter">
      <scroll-view class="filter-scroll" scroll-x>
        <view 
          class="filter-item {{activeTimeRange === item.value ? 'active' : ''}}"
          wx:for="{{timeRanges}}" 
          wx:key="value"
          bindtap="switchTimeRange"
          data-range="{{item.value}}"
        >
          <text class="filter-text">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 数据概览 -->
  <scroll-view class="analytics-content" scroll-y>
    <!-- 核心指标 -->
    <view class="metrics-section">
      <view class="section-header">
        <text class="section-title">核心指标</text>
        <text class="section-period">{{currentPeriod}}</text>
      </view>
      
      <view class="metrics-grid">
        <view class="metric-card">
          <view class="metric-icon profile">
            <text class="icon">👁️</text>
          </view>
          <view class="metric-info">
            <text class="metric-value">{{metrics.profile_views}}</text>
            <text class="metric-label">资料浏览</text>
            <view class="metric-change {{metrics.profile_views_change >= 0 ? 'positive' : 'negative'}}">
              <text class="change-icon">{{metrics.profile_views_change >= 0 ? '↗️' : '↘️'}}</text>
              <text class="change-text">{{Math.abs(metrics.profile_views_change)}}%</text>
            </view>
          </view>
        </view>

        <view class="metric-card">
          <view class="metric-icon likes">
            <text class="icon">❤️</text>
          </view>
          <view class="metric-info">
            <text class="metric-value">{{metrics.received_likes}}</text>
            <text class="metric-label">收到喜欢</text>
            <view class="metric-change {{metrics.received_likes_change >= 0 ? 'positive' : 'negative'}}">
              <text class="change-icon">{{metrics.received_likes_change >= 0 ? '↗️' : '↘️'}}</text>
              <text class="change-text">{{Math.abs(metrics.received_likes_change)}}%</text>
            </view>
          </view>
        </view>

        <view class="metric-card">
          <view class="metric-icon matches">
            <text class="icon">💕</text>
          </view>
          <view class="metric-info">
            <text class="metric-value">{{metrics.new_matches}}</text>
            <text class="metric-label">新匹配</text>
            <view class="metric-change {{metrics.new_matches_change >= 0 ? 'positive' : 'negative'}}">
              <text class="change-icon">{{metrics.new_matches_change >= 0 ? '↗️' : '↘️'}}</text>
              <text class="change-text">{{Math.abs(metrics.new_matches_change)}}%</text>
            </view>
          </view>
        </view>

        <view class="metric-card">
          <view class="metric-icon messages">
            <text class="icon">💬</text>
          </view>
          <view class="metric-info">
            <text class="metric-value">{{metrics.messages_sent}}</text>
            <text class="metric-label">发送消息</text>
            <view class="metric-change {{metrics.messages_sent_change >= 0 ? 'positive' : 'negative'}}">
              <text class="change-icon">{{metrics.messages_sent_change >= 0 ? '↗️' : '↘️'}}</text>
              <text class="change-text">{{Math.abs(metrics.messages_sent_change)}}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 活跃度分析 -->
    <view class="activity-section">
      <view class="section-header">
        <text class="section-title">活跃度分析</text>
      </view>
      
      <view class="activity-chart">
        <view class="chart-header">
          <text class="chart-title">每日活跃度</text>
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color active"></view>
              <text class="legend-text">活跃时间</text>
            </view>
          </view>
        </view>
        
        <view class="chart-container">
          <view class="chart-bars">
            <view 
              class="chart-bar"
              wx:for="{{activityData}}" 
              wx:key="date"
              style="height: {{item.percentage}}%"
            >
              <view class="bar-value">{{item.hours}}h</view>
            </view>
          </view>
          <view class="chart-labels">
            <text class="chart-label" wx:for="{{activityData}}" wx:key="date">
              {{item.day}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 匹配分析 -->
    <view class="matching-section">
      <view class="section-header">
        <text class="section-title">匹配分析</text>
      </view>
      
      <view class="matching-stats">
        <view class="stat-row">
          <text class="stat-label">匹配成功率</text>
          <view class="stat-value">
            <text class="value-text">{{matchingStats.success_rate}}%</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{matchingStats.success_rate}}%"></view>
            </view>
          </view>
        </view>
        
        <view class="stat-row">
          <text class="stat-label">回复率</text>
          <view class="stat-value">
            <text class="value-text">{{matchingStats.reply_rate}}%</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{matchingStats.reply_rate}}%"></view>
            </view>
          </view>
        </view>
        
        <view class="stat-row">
          <text class="stat-label">聊天转化率</text>
          <view class="stat-value">
            <text class="value-text">{{matchingStats.chat_conversion_rate}}%</text>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{matchingStats.chat_conversion_rate}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户画像 -->
    <view class="profile-section">
      <view class="section-header">
        <text class="section-title">用户画像</text>
      </view>
      
      <view class="profile-analysis">
        <!-- 年龄分布 -->
        <view class="analysis-item">
          <text class="analysis-title">喜欢你的用户年龄分布</text>
          <view class="age-distribution">
            <view 
              class="age-bar"
              wx:for="{{ageDistribution}}" 
              wx:key="range"
            >
              <text class="age-label">{{item.range}}</text>
              <view class="age-progress">
                <view class="age-fill" style="width: {{item.percentage}}%"></view>
              </view>
              <text class="age-percentage">{{item.percentage}}%</text>
            </view>
          </view>
        </view>

        <!-- 地域分布 -->
        <view class="analysis-item">
          <text class="analysis-title">地域分布</text>
          <view class="location-distribution">
            <view 
              class="location-item"
              wx:for="{{locationDistribution}}" 
              wx:key="city"
            >
              <text class="location-name">{{item.city}}</text>
              <text class="location-count">{{item.count}}人</text>
            </view>
          </view>
        </view>

        <!-- 兴趣标签 -->
        <view class="analysis-item">
          <text class="analysis-title">热门兴趣标签</text>
          <view class="interests-cloud">
            <text 
              class="interest-tag"
              wx:for="{{popularInterests}}" 
              wx:key="name"
              style="font-size: {{item.size}}rpx"
            >
              {{item.name}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用建议 -->
    <view class="suggestions-section">
      <view class="section-header">
        <text class="section-title">使用建议</text>
      </view>
      
      <view class="suggestions-list">
        <view class="suggestion-item" wx:for="{{suggestions}}" wx:key="id">
          <view class="suggestion-icon">{{item.icon}}</view>
          <view class="suggestion-content">
            <text class="suggestion-title">{{item.title}}</text>
            <text class="suggestion-desc">{{item.description}}</text>
          </view>
          <button 
            class="suggestion-btn"
            bindtap="applySuggestion"
            data-suggestion="{{item}}"
            wx:if="{{item.actionable}}"
          >
            {{item.action_text}}
          </button>
        </view>
      </view>
    </view>

    <!-- 数据导出 -->
    <view class="export-section">
      <view class="section-header">
        <text class="section-title">数据导出</text>
      </view>
      
      <view class="export-options">
        <view class="export-item" bindtap="exportReport" data-type="summary">
          <view class="export-icon">📊</view>
          <view class="export-info">
            <text class="export-title">数据报告</text>
            <text class="export-desc">导出完整的数据分析报告</text>
          </view>
          <text class="export-action">导出</text>
        </view>

        <view class="export-item" bindtap="exportReport" data-type="chart">
          <view class="export-icon">📈</view>
          <view class="export-info">
            <text class="export-title">图表数据</text>
            <text class="export-desc">导出图表和统计数据</text>
          </view>
          <text class="export-action">导出</text>
        </view>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 数据详情弹窗 -->
<view class="detail-modal" wx:if="{{showDetailModal}}" bindtap="hideDetailModal">
  <view class="detail-content" catchtap="">
    <view class="detail-header">
      <text class="detail-title">{{detailData.title}}</text>
      <text class="detail-close" bindtap="hideDetailModal">×</text>
    </view>
    
    <view class="detail-body">
      <view class="detail-chart">
        <!-- 详细图表内容 -->
      </view>
      
      <view class="detail-insights">
        <text class="insights-title">数据洞察</text>
        <view class="insights-list">
          <text class="insight-item" wx:for="{{detailData.insights}}" wx:key="*this">
            • {{item}}
          </text>
        </view>
      </view>
    </view>
  </view>
</view>
