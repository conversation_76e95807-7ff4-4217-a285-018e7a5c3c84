// pages/data/analytics.js
const { userAPI, matchingAPI } = require('../../utils/api')

Page({
  data: {
    // 时间范围
    timeRange: 'week', // week, month, year
    timeRangeOptions: [
      { value: 'week', name: '本周' },
      { value: 'month', name: '本月' },
      { value: 'year', name: '今年' }
    ],
    
    // 统计数据
    analytics: {
      profile: {
        views: 0,
        likes: 0,
        matches: 0,
        visitors: 0
      },
      activity: {
        loginDays: 0,
        activeTime: 0,
        messages: 0,
        moments: 0
      },
      social: {
        newFriends: 0,
        chatSessions: 0,
        giftsSent: 0,
        giftsReceived: 0
      }
    },
    
    // 图表数据
    chartData: {
      views: [],
      likes: [],
      matches: [],
      messages: []
    },
    
    // 排行榜
    rankings: {
      popularity: 0,
      activity: 0,
      charm: 0
    },
    
    // 加载状态
    loading: false,
    
    // 详细报告
    showDetailReport: false,
    detailData: null
  },

  onLoad() {
    this.loadAnalyticsData()
  },

  onPullDownRefresh() {
    this.loadAnalyticsData()
    wx.stopPullDownRefresh()
  },

  // 加载分析数据
  async loadAnalyticsData() {
    try {
      this.setData({ loading: true })

      // 获取用户分析概览数据
      const stats = await userAPI.getAnalyticsOverview()

      // 处理分析概览数据
      const analytics = {
        profile: {
          views: stats.profile_views || 0,
          likes: stats.profile_likes || 0,
          matches: stats.total_matches || 0,
          visitors: stats.profile_views || 0  // 使用浏览量作为访客数
        },
        activity: {
          loginDays: 0,  // 从详细分析数据获取
          activeTime: 0,
          messages: 0,
          moments: 0
        },
        social: {
          newFriends: 0,
          chatSessions: 0,
          giftsSent: 0,
          giftsReceived: 0
        },
        today: {
          views: stats.today_views || 0,
          likes: stats.today_likes || 0,
          matches: stats.today_matches || 0
        }
      }

      // 使用真实的趋势数据
      const chartData = {
        views: stats.views_trend || this.generateChartData(7, 5, 15),
        likes: stats.likes_trend || this.generateChartData(7, 2, 8),
        matches: stats.matches_trend || this.generateChartData(7, 0, 3),
        messages: this.generateChartData(7, 10, 30)  // 暂时生成，后续可从详细数据获取
      }

      // 使用真实的排名数据
      const rankings = {
        popularity: stats.popularity_rank || 50,
        activity: stats.activity_rank || 50,
        charm: Math.min(100, Math.max(1, stats.popularity_rank + stats.activity_rank) / 2)
      }

      this.setData({
        analytics,
        chartData,
        rankings,
        loading: false
      })
      
    } catch (error) {
      console.error('加载数据失败:', error)
      this.setData({ loading: false })

      // 降级到模拟数据
      const fallbackAnalytics = {
        profile: { views: 0, likes: 0, matches: 0, visitors: 0 },
        activity: { loginDays: 0, activeTime: 0, messages: 0, moments: 0 },
        social: { newFriends: 0, chatSessions: 0, giftsSent: 0, giftsReceived: 0 }
      }

      const fallbackChartData = {
        views: this.generateChartData(7, 0, 5),
        likes: this.generateChartData(7, 0, 3),
        matches: this.generateChartData(7, 0, 2),
        messages: this.generateChartData(7, 0, 10)
      }

      this.setData({
        analytics: fallbackAnalytics,
        chartData: fallbackChartData,
        rankings: { popularity: 1, activity: 1, charm: 1 }
      })
    }
  },

  // 生成图表数据
  generateChartData(days, min, max) {
    return Array.from({ length: days }, () => 
      Math.floor(Math.random() * (max - min + 1)) + min
    )
  },

  // 切换时间范围
  switchTimeRange(e) {
    const { range } = e.currentTarget.dataset
    this.setData({ timeRange: range })
    this.loadAnalyticsData()
  },

  // 查看详细报告
  viewDetailReport() {
    this.generateDetailReport()
    this.setData({ showDetailReport: true })
  },

  // 生成详细报告
  generateDetailReport() {
    const { analytics, rankings } = this.data
    
    const detailData = {
      summary: {
        totalScore: Math.floor((rankings.popularity + rankings.activity + rankings.charm) / 3),
        improvement: Math.floor(Math.random() * 20) + 5,
        suggestions: [
          '完善个人资料，提升匹配度',
          '增加互动频率，提高活跃度',
          '优化照片质量，增强吸引力'
        ]
      },
      trends: {
        profileViews: {
          current: analytics.profile.views,
          change: Math.floor(Math.random() * 40) - 20,
          trend: 'up'
        },
        matchRate: {
          current: Math.floor(analytics.profile.matches / analytics.profile.likes * 100),
          change: Math.floor(Math.random() * 20) - 10,
          trend: 'up'
        },
        responseRate: {
          current: Math.floor(Math.random() * 30) + 60,
          change: Math.floor(Math.random() * 20) - 10,
          trend: 'down'
        }
      },
      comparisons: {
        sameAge: Math.floor(Math.random() * 50) + 25,
        sameCity: Math.floor(Math.random() * 50) + 25,
        sameEducation: Math.floor(Math.random() * 50) + 25
      }
    }
    
    this.setData({ detailData })
  },

  // 关闭详细报告
  closeDetailReport() {
    this.setData({ showDetailReport: false })
  },

  // 分享数据
  shareAnalytics() {
    const { analytics } = this.data
    return {
      title: `我的交友数据：获得${analytics.profile.likes}个喜欢，${analytics.profile.matches}个匹配`,
      path: '/pages/data/analytics'
    }
  },

  // 导出数据
  exportData() {
    wx.showModal({
      title: '导出数据',
      content: '数据将以邮件形式发送到您的注册邮箱',
      confirmText: '确认导出',
      success: (res) => {
        if (res.confirm) {
          this.performExport()
        }
      }
    })
  },

  // 执行导出
  async performExport() {
    try {
      wx.showLoading({ title: '导出中...' })
      
      // 模拟导出
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      wx.hideLoading()
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
      
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  // 查看数据说明
  viewDataExplanation() {
    wx.showModal({
      title: '数据说明',
      content: '数据统计基于您的使用行为，包括资料浏览、互动记录等，仅供参考',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 设置数据权限
  setDataPermission() {
    wx.navigateTo({
      url: '/pages/settings/data-permission'
    })
  },

  // 分享给好友
  onShareAppMessage() {
    return this.shareAnalytics()
  }
})
