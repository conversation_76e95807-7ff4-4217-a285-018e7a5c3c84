/* pages/data/analytics.wxss */
.analytics-container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 时间范围选择 */
.time-range-section {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e7eb;
}

.time-range-tabs {
  display: flex;
  gap: 20rpx;
}

.time-tab {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  background: #f9fafb;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #6b7280;
  border: 2rpx solid transparent;
}

.time-tab.active {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

/* 概览卡片 */
.overview-section {
  padding: 20rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.overview-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.card-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.card-label {
  font-size: 24rpx;
  color: #6b7280;
}

.card-change {
  font-size: 20rpx;
  margin-top: 8rpx;
}

.change-up {
  color: #10b981;
}

.change-down {
  color: #ef4444;
}

/* 详细统计 */
.stats-section {
  background: white;
  border-radius: 16rpx;
  margin: 0 20rpx 20rpx;
  overflow: hidden;
}

.stats-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
}

.detail-btn {
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  border: none;
}

.stats-content {
  padding: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #8b5cf6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 排行榜 */
.ranking-section {
  background: white;
  border-radius: 16rpx;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.ranking-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24rpx;
  text-align: center;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
}

.ranking-info {
  display: flex;
  align-items: center;
}

.ranking-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.ranking-name {
  font-size: 28rpx;
  color: #1f2937;
}

.ranking-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #8b5cf6;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 16rpx;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.chart-placeholder {
  height: 300rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 26rpx;
}

/* 详细报告弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-content {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 700rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
}

.detail-close {
  font-size: 32rpx;
}

.detail-body {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.report-section {
  margin-bottom: 30rpx;
}

.report-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.summary-score {
  text-align: center;
  padding: 30rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.total-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #8b5cf6;
  margin-bottom: 8rpx;
}

.score-label {
  font-size: 24rpx;
  color: #6b7280;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.suggestion-item {
  padding: 16rpx;
  background: #f0f9ff;
  border-left: 4rpx solid #3b82f6;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #1f2937;
}

.trend-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f9fafb;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.trend-name {
  font-size: 26rpx;
  color: #374151;
}

.trend-value {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.trend-number {
  font-size: 26rpx;
  font-weight: bold;
  color: #1f2937;
}

.trend-change {
  font-size: 22rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.export-btn {
  flex: 1;
  height: 70rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

.share-btn {
  flex: 1;
  height: 70rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  color: #9ca3af;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
}
