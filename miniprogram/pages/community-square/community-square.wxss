/* pages/community-square/community-square.wxss */
.community-container {
  width: 100%;
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 社区头部 */
.community-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 44rpx 32rpx 32rpx;
  color: #fff;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.community-title-section {
  flex: 1;
}

.community-title {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.community-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255,255,255,0.3);
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #fff;
}

.badge-count {
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
  line-height: 1;
}

/* 社区导航标签 */
.community-tabs {
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tabs-scroll {
  width: 100%;
}

.tab-list {
  display: flex;
  padding: 0 32rpx;
  gap: 32rpx;
}

.tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  flex-shrink: 0;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  transform: scale(1.05);
}

.tab-icon {
  font-size: 32rpx;
}

.tab-name {
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
}

.tab-item.active .tab-name {
  color: #fff;
}

.tab-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  display: block;
}

/* 热门话题 */
.trending-topics {
  background: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.more-btn:active {
  background: #e9ecef;
}

.more-text {
  font-size: 24rpx;
  color: #666;
}

.topics-scroll {
  width: 100%;
}

.topic-list {
  display: flex;
  gap: 16rpx;
  padding-bottom: 8rpx;
}

.topic-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-width: 200rpx;
  flex-shrink: 0;
}

.topic-item:active {
  transform: scale(0.98);
  border-color: #667eea;
}

.topic-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.topic-count {
  font-size: 22rpx;
  color: #666;
}

/* 社区内容 */
.community-content {
  flex: 1;
}

.content-section {
  background: #fff;
  margin-bottom: 16rpx;
}

/* 帖子列表 */
.post-list {
  display: flex;
  flex-direction: column;
}

.post-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.post-item:active {
  background: #fafafa;
}

.post-item:last-child {
  border-bottom: none;
}

/* 帖子头部 */
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.post-time {
  font-size: 22rpx;
  color: #999;
}

.user-badge {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #000;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.post-menu {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.post-menu:active {
  background: #f0f0f0;
}

/* 帖子内容 */
.post-content {
  margin-bottom: 16rpx;
}

.post-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 12rpx;
  display: block;
}

.post-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.topic-tag {
  background: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.topic-tag:active {
  background: #bbdefb;
}

/* 帖子图片 */
.post-images {
  margin-bottom: 16rpx;
}

.image-grid {
  display: grid;
  gap: 8rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image-grid.single {
  grid-template-columns: 1fr;
  max-height: 600rpx;
}

.image-grid.double {
  grid-template-columns: 1fr 1fr;
  height: 300rpx;
}

.image-grid.multiple {
  grid-template-columns: repeat(3, 1fr);
  height: 300rpx;
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.post-image:active {
  transform: scale(0.98);
}

/* 帖子视频 */
.post-video {
  margin-bottom: 16rpx;
}

.video-player {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 位置信息 */
.post-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  align-self: flex-start;
  transition: all 0.3s ease;
}

.post-location:active {
  background: #e9ecef;
}

.location-text {
  font-size: 22rpx;
  color: #666;
}

/* 帖子操作 */
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f8f8f8;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.action-item:active {
  background: #f0f0f0;
}

.action-item.liked {
  background: #ffebee;
}

.action-count, .action-text {
  font-size: 24rpx;
  color: #666;
}

.action-item.liked .action-count {
  color: #f44336;
}

/* 关注内容 */
.following-content {
  padding: 40rpx 32rpx;
}

.empty-following {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.discover-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 话题广场 */
.topics-content {
  padding: 24rpx 32rpx;
}

.topic-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.category-chip {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.category-chip.active {
  background: #e3f2fd;
  border-color: #1976d2;
}

.chip-text {
  font-size: 24rpx;
  color: #333;
}

.category-chip.active .chip-text {
  color: #1976d2;
  font-weight: 500;
}

.topic-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.topic-card {
  position: relative;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.topic-card:active {
  transform: scale(0.98);
}

.topic-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.topic-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%);
  padding: 16rpx;
  color: #fff;
}

.topic-name {
  font-size: 28rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.topic-stats {
  font-size: 20rpx;
  opacity: 0.9;
  display: block;
}

.topic-trend {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 附近动态 */
.nearby-content {
  padding: 24rpx 32rpx 0;
}

.location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.current-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.current-location:active {
  opacity: 0.7;
}

.location-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.distance-range {
  font-size: 24rpx;
  color: #666;
}

.nearby-post {
  position: relative;
}

.distance-badge {
  position: absolute;
  top: 24rpx;
  right: 32rpx;
  background: rgba(0,0,0,0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10px);
}

.distance-text {
  font-weight: 500;
}

/* 加载状态 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

/* 发布按钮 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  z-index: 100;
}

.fab-btn {
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.fab-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.6);
}

/* 评论弹窗 */
.comments-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.comments-content {
  width: 100%;
  max-height: 80vh;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  display: flex;
  flex-direction: column;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.comments-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-list {
  flex: 1;
  padding: 0 32rpx;
  max-height: 60vh;
}

.comment-item {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-user {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
  display: block;
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-like {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.comment-like:active {
  background: #f0f0f0;
}

.comment-like.liked {
  background: #ffebee;
}

.like-count {
  font-size: 22rpx;
  color: #666;
}

.comment-like.liked .like-count {
  color: #f44336;
}

.comment-input-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fff;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.send-comment-btn {
  width: 72rpx;
  height: 72rpx;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-comment-btn:active {
  transform: scale(0.95);
  background: #5a6fd8;
}
