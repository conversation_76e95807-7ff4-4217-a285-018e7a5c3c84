// pages/community-square/community-square.js
Page({
  data: {
    // 社区标签
    communityTabs: [
      { id: 'recommend', name: '推荐', icon: '🔥', hasNew: false },
      { id: 'following', name: '关注', icon: '👥', hasNew: true },
      { id: 'topics', name: '话题', icon: '💬', hasNew: false },
      { id: 'nearby', name: '附近', icon: '📍', hasNew: false }
    ],
    selectedTab: 'recommend',
    
    // 通知相关
    unreadCount: 3,
    
    // 热门话题
    trendingTopics: [
      { id: 1, title: '春日出游', postCount: 1234 },
      { id: 2, title: '美食分享', postCount: 856 },
      { id: 3, title: '健身打卡', postCount: 642 },
      { id: 4, title: '读书心得', postCount: 389 }
    ],
    
    // 推荐帖子
    recommendPosts: [],
    
    // 关注用户帖子
    followingPosts: [],
    
    // 话题相关
    selectedTopicCategory: 'all',
    topicCategories: [
      { id: 'all', name: '全部' },
      { id: 'lifestyle', name: '生活' },
      { id: 'food', name: '美食' },
      { id: 'travel', name: '旅行' },
      { id: 'fitness', name: '健身' },
      { id: 'reading', name: '读书' }
    ],
    topicList: [],
    
    // 附近动态
    currentLocation: '北京·朝阳区',
    distanceRange: 5,
    nearbyPosts: [],
    
    // 评论相关
    showComments: false,
    currentPost: null,
    comments: [],
    commentText: '',
    
    // 状态
    isLoading: false,
    hasMore: true
  },

  onLoad(options) {
    console.log('社区广场页面加载', options);
    this.initCommunityData();
    this.loadRecommendPosts();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshCurrentTabData();
  },

  onReachBottom() {
    // 触底加载更多
    this.loadMorePosts();
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshCurrentTabData();
  },

  // 初始化社区数据
  initCommunityData() {
    const mockRecommendPosts = [
      {
        id: 1,
        user: {
          id: 1,
          nickname: '小美',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop',
          isVip: true
        },
        content: '今天天气真好，和朋友们一起去公园野餐了！春天的阳光特别温暖，心情也变得格外美好～',
        topics: ['春日出游', '野餐'],
        images: [
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop'
        ],
        video: null,
        location: { name: '朝阳公园', lat: 39.9, lng: 116.4 },
        timeAgo: '2小时前',
        likeCount: 128,
        commentCount: 23,
        isLiked: false
      },
      {
        id: 2,
        user: {
          id: 2,
          nickname: '健身达人',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop',
          isVip: false
        },
        content: '今天完成了5公里晨跑，感觉整个人都充满了活力！坚持运动真的会让人变得更加自信和快乐。',
        topics: ['健身打卡', '晨跑'],
        images: [
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=600&fit=crop'
        ],
        video: null,
        location: null,
        timeAgo: '4小时前',
        likeCount: 89,
        commentCount: 12,
        isLiked: true
      },
      {
        id: 3,
        user: {
          id: 3,
          nickname: '美食家',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop',
          isVip: true
        },
        content: '自己做的提拉米苏，第一次尝试就成功了！甜而不腻，口感层次丰富，朋友们都说很棒～',
        topics: ['美食分享', '烘焙'],
        images: [
          'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=400&fit=crop',
          'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=400&fit=crop',
          'https://images.unsplash.com/photo-1563729784474-d77dbb933a9e?w=400&h=400&fit=crop'
        ],
        video: null,
        location: { name: '家里厨房', lat: 39.9, lng: 116.4 },
        timeAgo: '6小时前',
        likeCount: 256,
        commentCount: 45,
        isLiked: false
      }
    ];

    const mockTopicList = [
      {
        id: 1,
        title: '春日出游',
        coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
        postCount: 1234,
        participantCount: 856,
        isTrending: true,
        category: 'lifestyle'
      },
      {
        id: 2,
        title: '美食分享',
        coverImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
        postCount: 856,
        participantCount: 642,
        isTrending: true,
        category: 'food'
      },
      {
        id: 3,
        title: '健身打卡',
        coverImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',
        postCount: 642,
        participantCount: 389,
        isTrending: false,
        category: 'fitness'
      },
      {
        id: 4,
        title: '读书心得',
        coverImage: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=200&fit=crop',
        postCount: 389,
        participantCount: 256,
        isTrending: false,
        category: 'reading'
      }
    ];

    this.setData({
      recommendPosts: mockRecommendPosts,
      topicList: mockTopicList,
      nearbyPosts: mockRecommendPosts.map(post => ({
        ...post,
        distance: (Math.random() * 5).toFixed(1)
      }))
    });
  },

  // 选择标签
  selectTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ selectedTab: tab });
    
    // 加载对应标签的数据
    switch (tab) {
      case 'recommend':
        this.loadRecommendPosts();
        break;
      case 'following':
        this.loadFollowingPosts();
        break;
      case 'topics':
        this.loadTopicList();
        break;
      case 'nearby':
        this.loadNearbyPosts();
        break;
    }
  },

  // 加载推荐帖子
  loadRecommendPosts() {
    this.setData({ isLoading: true });
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ isLoading: false });
    }, 1000);
  },

  // 加载关注用户帖子
  loadFollowingPosts() {
    this.setData({ isLoading: true });
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ 
        followingPosts: [],
        isLoading: false 
      });
    }, 1000);
  },

  // 加载话题列表
  loadTopicList() {
    this.setData({ isLoading: true });
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ isLoading: false });
    }, 1000);
  },

  // 加载附近帖子
  loadNearbyPosts() {
    this.setData({ isLoading: true });
    
    // 模拟API调用
    setTimeout(() => {
      this.setData({ isLoading: false });
    }, 1000);
  },

  // 刷新当前标签数据
  refreshCurrentTabData() {
    const { selectedTab } = this.data;
    
    switch (selectedTab) {
      case 'recommend':
        this.loadRecommendPosts();
        break;
      case 'following':
        this.loadFollowingPosts();
        break;
      case 'topics':
        this.loadTopicList();
        break;
      case 'nearby':
        this.loadNearbyPosts();
        break;
    }
    
    wx.stopPullDownRefresh();
  },

  // 加载更多帖子
  loadMorePosts() {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    this.setData({ isLoading: true });
    
    // 模拟加载更多数据
    setTimeout(() => {
      const morePosts = this.generateMorePosts();
      const { selectedTab } = this.data;
      
      if (selectedTab === 'recommend') {
        this.setData({
          recommendPosts: [...this.data.recommendPosts, ...morePosts],
          isLoading: false,
          hasMore: morePosts.length > 0
        });
      }
    }, 1000);
  },

  // 生成更多帖子数据
  generateMorePosts() {
    const posts = [
      {
        id: Date.now(),
        user: {
          id: Date.now(),
          nickname: '新用户',
          avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=100&h=100&fit=crop',
          isVip: Math.random() > 0.7
        },
        content: '分享一些生活中的美好瞬间～',
        topics: ['生活'],
        images: [
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'
        ],
        video: null,
        location: null,
        timeAgo: '刚刚',
        likeCount: Math.floor(Math.random() * 100),
        commentCount: Math.floor(Math.random() * 20),
        isLiked: false
      }
    ];
    
    return Math.random() > 0.3 ? posts : [];
  },

  // 查看帖子详情
  viewPostDetail(e) {
    const post = e.currentTarget.dataset.post;
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${post.id}`
    });
  },

  // 查看用户资料
  viewUserProfile(e) {
    const user = e.currentTarget.dataset.user;
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?userId=${user.id}`
    });
  },

  // 点赞/取消点赞
  toggleLike(e) {
    const post = e.currentTarget.dataset.post;
    const { selectedTab } = this.data;
    
    let posts = [];
    if (selectedTab === 'recommend') {
      posts = this.data.recommendPosts;
    } else if (selectedTab === 'nearby') {
      posts = this.data.nearbyPosts;
    }
    
    const updatedPosts = posts.map(item => {
      if (item.id === post.id) {
        return {
          ...item,
          isLiked: !item.isLiked,
          likeCount: item.isLiked ? item.likeCount - 1 : item.likeCount + 1
        };
      }
      return item;
    });
    
    if (selectedTab === 'recommend') {
      this.setData({ recommendPosts: updatedPosts });
    } else if (selectedTab === 'nearby') {
      this.setData({ nearbyPosts: updatedPosts });
    }
    
    // 点赞动画效果
    if (!post.isLiked) {
      wx.vibrateShort();
    }
  },

  // 打开评论
  openComments(e) {
    const post = e.currentTarget.dataset.post;
    this.setData({ 
      currentPost: post,
      showComments: true 
    });
    this.loadComments(post.id);
  },

  // 关闭评论
  closeComments() {
    this.setData({ 
      showComments: false,
      currentPost: null,
      comments: [],
      commentText: ''
    });
  },

  // 加载评论
  loadComments(postId) {
    const mockComments = [
      {
        id: 1,
        user: { 
          nickname: '小王', 
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop' 
        },
        content: '太棒了！我也想去试试',
        timeAgo: '2分钟前',
        likeCount: 3,
        isLiked: false
      },
      {
        id: 2,
        user: { 
          nickname: '小李', 
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop' 
        },
        content: '好羡慕啊，看起来很有趣',
        timeAgo: '5分钟前',
        likeCount: 1,
        isLiked: true
      }
    ];
    
    this.setData({ comments: mockComments });
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({ commentText: e.detail.value });
  },

  // 发送评论
  sendComment() {
    const { commentText, comments } = this.data;
    if (!commentText.trim()) return;

    const newComment = {
      id: Date.now(),
      user: {
        nickname: '我',
        avatar: '/images/default-avatar.png'
      },
      content: commentText,
      timeAgo: '刚刚',
      likeCount: 0,
      isLiked: false
    };

    this.setData({
      comments: [newComment, ...comments],
      commentText: ''
    });

    wx.showToast({ title: '评论成功', icon: 'success' });
  },

  // 点赞评论
  toggleCommentLike(e) {
    const comment = e.currentTarget.dataset.comment;
    const { comments } = this.data;

    const updatedComments = comments.map(item => {
      if (item.id === comment.id) {
        return {
          ...item,
          isLiked: !item.isLiked,
          likeCount: item.isLiked ? item.likeCount - 1 : item.likeCount + 1
        };
      }
      return item;
    });

    this.setData({ comments: updatedComments });
  },

  // 分享帖子
  sharePost(e) {
    const post = e.currentTarget.dataset.post;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 发送礼物
  sendGift(e) {
    const user = e.currentTarget.dataset.user;
    wx.navigateTo({
      url: `/pages/gift-send/gift-send?userId=${user.id}`
    });
  },

  // 预览图片
  previewImage(e) {
    const { urls, current } = e.currentTarget.dataset;
    wx.previewImage({
      urls: urls,
      current: current
    });
  },

  // 查看位置
  viewLocation(e) {
    const location = e.currentTarget.dataset.location;
    wx.openLocation({
      latitude: location.lat,
      longitude: location.lng,
      name: location.name,
      scale: 18
    });
  },

  // 选择话题
  selectTopic(e) {
    const topic = e.currentTarget.dataset.topic;
    wx.navigateTo({
      url: `/pages/topic-detail/topic-detail?topic=${encodeURIComponent(topic.title || topic)}`
    });
  },

  // 查看所有话题
  viewAllTopics() {
    this.setData({ selectedTab: 'topics' });
    this.loadTopicList();
  },

  // 选择话题分类
  selectTopicCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ selectedTopicCategory: category });
    this.filterTopicsByCategory(category);
  },

  // 按分类筛选话题
  filterTopicsByCategory(category) {
    // 实际项目中这里会调用API
    console.log('按分类筛选话题:', category);
  },

  // 进入话题
  enterTopic(e) {
    const topic = e.currentTarget.dataset.topic;
    wx.navigateTo({
      url: `/pages/topic-detail/topic-detail?id=${topic.id}`
    });
  },

  // 选择位置
  selectLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          currentLocation: res.name || res.address
        });
        this.loadNearbyPosts();
      }
    });
  },

  // 去发现页面
  goToDiscover() {
    wx.switchTab({
      url: '/pages/discover/discover'
    });
  },

  // 创建帖子
  createPost() {
    wx.navigateTo({
      url: '/pages/post-create/post-create'
    });
  },

  // 打开搜索
  openSearch() {
    wx.navigateTo({
      url: '/pages/community-search/community-search'
    });
  },

  // 打开通知
  openNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    });
  },

  // 显示帖子菜单
  showPostMenu(e) {
    const post = e.currentTarget.dataset.post;

    wx.showActionSheet({
      itemList: ['举报', '不感兴趣', '屏蔽用户'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.reportPost(post);
            break;
          case 1:
            this.hidePost(post);
            break;
          case 2:
            this.blockUser(post.user);
            break;
        }
      }
    });
  },

  // 举报帖子
  reportPost(post) {
    wx.showModal({
      title: '举报内容',
      content: '确定要举报这条动态吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({ title: '举报成功', icon: 'success' });
        }
      }
    });
  },

  // 隐藏帖子
  hidePost(post) {
    const { selectedTab } = this.data;

    if (selectedTab === 'recommend') {
      const filteredPosts = this.data.recommendPosts.filter(item => item.id !== post.id);
      this.setData({ recommendPosts: filteredPosts });
    }

    wx.showToast({ title: '已隐藏', icon: 'success' });
  },

  // 屏蔽用户
  blockUser(user) {
    wx.showModal({
      title: '屏蔽用户',
      content: `确定要屏蔽用户"${user.nickname}"吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showToast({ title: '已屏蔽', icon: 'success' });
        }
      }
    });
  }
});
