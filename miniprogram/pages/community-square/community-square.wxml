<!--pages/community-square/community-square.wxml-->
<view class="community-container">
  <!-- 社区头部 -->
  <view class="community-header">
    <view class="header-content">
      <view class="community-title-section">
        <text class="community-title">社区广场</text>
        <text class="community-subtitle">发现有趣的人和事</text>
      </view>
      <view class="header-actions">
        <view class="action-btn search-btn" bindtap="openSearch">
          <custom-icon name="search" type="white" size="medium"></custom-icon>
        </view>
        <view class="action-btn notification-btn" bindtap="openNotifications">
          <custom-icon name="bell" type="white" size="medium"></custom-icon>
          <view class="notification-badge" wx:if="{{unreadCount > 0}}">
            <text class="badge-count">{{unreadCount > 99 ? '99+' : unreadCount}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 社区导航标签 -->
  <view class="community-tabs">
    <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="tab-list">
        <view 
          class="tab-item {{selectedTab === tab.id ? 'active' : ''}}"
          wx:for="{{communityTabs}}" 
          wx:key="id"
          wx:for-item="tab"
          bindtap="selectTab"
          data-tab="{{tab.id}}"
        >
          <text class="tab-icon">{{tab.icon}}</text>
          <text class="tab-name">{{tab.name}}</text>
          <view class="tab-badge" wx:if="{{tab.hasNew}}">
            <text class="badge-dot"></text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 热门话题 -->
  <view class="trending-topics" wx:if="{{selectedTab === 'recommend'}}">
    <view class="section-header">
      <text class="section-title">🔥 热门话题</text>
      <view class="more-btn" bindtap="viewAllTopics">
        <text class="more-text">更多</text>
        <custom-icon name="chevron-right" type="gray" size="small"></custom-icon>
      </view>
    </view>
    
    <scroll-view class="topics-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="topic-list">
        <view 
          class="topic-item"
          wx:for="{{trendingTopics}}" 
          wx:key="id"
          bindtap="selectTopic"
          data-topic="{{item}}"
        >
          <text class="topic-title">#{{item.title}}</text>
          <text class="topic-count">{{item.postCount}}条动态</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 社区内容 -->
  <view class="community-content">
    <!-- 推荐内容 -->
    <view class="content-section" wx:if="{{selectedTab === 'recommend'}}">
      <view class="post-list">
        <view 
          class="post-item"
          wx:for="{{recommendPosts}}" 
          wx:key="id"
          bindtap="viewPostDetail"
          data-post="{{item}}"
        >
          <view class="post-header">
            <view class="user-info" bindtap="viewUserProfile" data-user="{{item.user}}" catchtap="">
              <image class="user-avatar" src="{{item.user.avatar}}" mode="aspectFill"></image>
              <view class="user-details">
                <text class="user-name">{{item.user.nickname}}</text>
                <text class="post-time">{{item.timeAgo}}</text>
              </view>
              <view class="user-badge" wx:if="{{item.user.isVip}}">VIP</view>
            </view>
            <view class="post-menu" bindtap="showPostMenu" data-post="{{item}}" catchtap="">
              <custom-icon name="more" type="gray" size="medium"></custom-icon>
            </view>
          </view>
          
          <view class="post-content">
            <text class="post-text">{{item.content}}</text>
            <view class="post-topics" wx:if="{{item.topics.length > 0}}">
              <text 
                class="topic-tag"
                wx:for="{{item.topics}}" 
                wx:key="*this"
                bindtap="selectTopic"
                data-topic="{{item}}"
                catchtap=""
              >#{{item}}</text>
            </view>
            
            <!-- 图片内容 -->
            <view class="post-images" wx:if="{{item.images.length > 0}}">
              <view class="image-grid {{item.images.length === 1 ? 'single' : item.images.length === 2 ? 'double' : 'multiple'}}">
                <image 
                  class="post-image"
                  wx:for="{{item.images}}" 
                  wx:key="*this"
                  src="{{item}}"
                  mode="aspectFill"
                  bindtap="previewImage"
                  data-urls="{{item.images}}"
                  data-current="{{item}}"
                  catchtap=""
                ></image>
              </view>
            </view>
            
            <!-- 视频内容 -->
            <view class="post-video" wx:if="{{item.video}}">
              <video 
                class="video-player"
                src="{{item.video.url}}"
                poster="{{item.video.cover}}"
                controls="{{true}}"
                object-fit="cover"
              ></video>
            </view>
            
            <!-- 位置信息 -->
            <view class="post-location" wx:if="{{item.location}}" bindtap="viewLocation" data-location="{{item.location}}" catchtap="">
              <custom-icon name="location" type="gray" size="small"></custom-icon>
              <text class="location-text">{{item.location.name}}</text>
            </view>
          </view>
          
          <view class="post-actions">
            <view class="action-item like-action {{item.isLiked ? 'liked' : ''}}" bindtap="toggleLike" data-post="{{item}}" catchtap="">
              <custom-icon name="{{item.isLiked ? 'heart-filled' : 'heart'}}" type="{{item.isLiked ? 'red' : 'gray'}}" size="medium"></custom-icon>
              <text class="action-count">{{item.likeCount}}</text>
            </view>
            
            <view class="action-item comment-action" bindtap="openComments" data-post="{{item}}" catchtap="">
              <custom-icon name="comment" type="gray" size="medium"></custom-icon>
              <text class="action-count">{{item.commentCount}}</text>
            </view>
            
            <view class="action-item share-action" bindtap="sharePost" data-post="{{item}}" catchtap="">
              <custom-icon name="share" type="gray" size="medium"></custom-icon>
              <text class="action-text">分享</text>
            </view>
            
            <view class="action-item gift-action" bindtap="sendGift" data-user="{{item.user}}" catchtap="">
              <custom-icon name="gift" type="gray" size="medium"></custom-icon>
              <text class="action-text">送礼</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 关注内容 -->
    <view class="content-section" wx:if="{{selectedTab === 'following'}}">
      <view class="following-content">
        <view class="empty-following" wx:if="{{followingPosts.length === 0}}">
          <view class="empty-icon">👥</view>
          <text class="empty-title">还没有关注的人</text>
          <text class="empty-desc">去发现页面关注感兴趣的用户吧</text>
          <button class="discover-btn" bindtap="goToDiscover">去发现</button>
        </view>
        
        <view class="post-list" wx:else>
          <!-- 关注用户的动态列表 -->
          <view 
            class="post-item"
            wx:for="{{followingPosts}}" 
            wx:key="id"
          >
            <!-- 复用推荐内容的结构 -->
          </view>
        </view>
      </view>
    </view>

    <!-- 话题广场 -->
    <view class="content-section" wx:if="{{selectedTab === 'topics'}}">
      <view class="topics-content">
        <view class="topic-categories">
          <view 
            class="category-chip {{selectedTopicCategory === category.id ? 'active' : ''}}"
            wx:for="{{topicCategories}}" 
            wx:key="id"
            wx:for-item="category"
            bindtap="selectTopicCategory"
            data-category="{{category.id}}"
          >
            <text class="chip-text">{{category.name}}</text>
          </view>
        </view>
        
        <view class="topic-grid">
          <view 
            class="topic-card"
            wx:for="{{topicList}}" 
            wx:key="id"
            bindtap="enterTopic"
            data-topic="{{item}}"
          >
            <image class="topic-cover" src="{{item.coverImage}}" mode="aspectFill"></image>
            <view class="topic-overlay">
              <text class="topic-name">#{{item.title}}</text>
              <text class="topic-stats">{{item.postCount}}条动态 · {{item.participantCount}}人参与</text>
            </view>
            <view class="topic-trend" wx:if="{{item.isTrending}}">
              <text class="trend-text">热</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 附近动态 -->
    <view class="content-section" wx:if="{{selectedTab === 'nearby'}}">
      <view class="nearby-content">
        <view class="location-header">
          <view class="current-location" bindtap="selectLocation">
            <custom-icon name="location" type="primary" size="medium"></custom-icon>
            <text class="location-name">{{currentLocation}}</text>
            <custom-icon name="chevron-down" type="gray" size="small"></custom-icon>
          </view>
          <text class="distance-range">{{distanceRange}}km内</text>
        </view>
        
        <view class="post-list">
          <view 
            class="post-item nearby-post"
            wx:for="{{nearbyPosts}}" 
            wx:key="id"
          >
            <!-- 复用推荐内容的结构，增加距离显示 -->
            <view class="distance-badge">
              <text class="distance-text">{{item.distance}}km</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载更多内容...</text>
  </view>
</view>

<!-- 发布按钮 -->
<view class="fab-container">
  <view class="fab-btn" bindtap="createPost">
    <custom-icon name="edit" type="white" size="large"></custom-icon>
  </view>
</view>

<!-- 评论弹窗 -->
<view class="comments-modal" wx:if="{{showComments}}" bindtap="closeComments">
  <view class="comments-content" catchtap="">
    <view class="comments-header">
      <text class="comments-title">评论</text>
      <view class="comments-close" bindtap="closeComments">
        <custom-icon name="close" type="gray" size="medium"></custom-icon>
      </view>
    </view>
    
    <scroll-view class="comments-list" scroll-y="true">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <image class="comment-avatar" src="{{item.user.avatar}}" mode="aspectFill"></image>
        <view class="comment-content">
          <text class="comment-user">{{item.user.nickname}}</text>
          <text class="comment-text">{{item.content}}</text>
          <view class="comment-actions">
            <text class="comment-time">{{item.timeAgo}}</text>
            <view class="comment-like {{item.isLiked ? 'liked' : ''}}" bindtap="toggleCommentLike" data-comment="{{item}}">
              <custom-icon name="{{item.isLiked ? 'heart-filled' : 'heart'}}" type="{{item.isLiked ? 'red' : 'gray'}}" size="small"></custom-icon>
              <text class="like-count" wx:if="{{item.likeCount > 0}}">{{item.likeCount}}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <view class="comment-input-section">
      <input 
        class="comment-input" 
        placeholder="写评论..." 
        value="{{commentText}}"
        bindinput="onCommentInput"
        confirm-type="send"
        bindconfirm="sendComment"
      />
      <view class="send-comment-btn" bindtap="sendComment">
        <custom-icon name="send" type="primary" size="medium"></custom-icon>
      </view>
    </view>
  </view>
</view>
