// pages/home/<USER>
const app = getApp()
const { matchingAPI } = require('../../utils/api')

Page({
  data: {
    userList: [],
    loading: true,
    userLocation: '北京',
    isGuestMode: false,
    showEmptyState: false,

    // 统计数据
    todayRecommends: 156,
    mutualLikes: 23,
    matchRate: '89%',
    newMessages: 12,
    notificationCount: 3,

    // 轮播横幅数据
    currentBannerIndex: 0,
    bannerList: [
      {
        id: 1,
        type: 'tips',
        title: '脱单秘籍',
        subtitle: '掌握聊天技巧，快速脱单',
        icon: '💡'
      },
      {
        id: 2,
        type: 'verify',
        title: '真实认证',
        subtitle: '实名认证，安全可靠',
        icon: '✅'
      },
      {
        id: 3,
        type: 'matchmaker',
        title: '专业红娘',
        subtitle: '一对一服务，成功率更高',
        icon: '💕'
      }
    ],

    // 弹窗状态
    showMatchModal: false,
    showFilterModal: false,
    matchedUser: null,

    // 筛选条件
    filters: {
      minAge: 20,
      maxAge: 35,
      distance: '50km内',
      vipOnly: false,
      onlineOnly: false
    },
    distanceOptions: ['10km内', '20km内', '50km内', '100km内', '不限'],

    // 标签导航
    currentTab: 'recommend', // 默认显示推荐页面

    // 标签数量统计
    tabCounts: {
      recommend: 156,
      nearby: 89,
      online: 45,
      new: 23,
      vip: 67
    },

    // 搜索相关
    searchKeyword: '',
    showSearchSuggestions: false,
    currentLocation: '北京',
    hasMore: true,
    page: 1,

    // 高级筛选相关
    showAdvancedFilter: false,
    advancedFilters: {
      minAge: 20,
      maxAge: 35,
      distance: '10km',
      education: 'unlimited',
      income: 'unlimited',
      interests: [],
      onlineOnly: false,
      verifiedOnly: false,
      vipOnly: false
    },

    // 3D卡片相关
    cardTouchStartX: 0,
    cardTouchStartY: 0,
    cardTouchStartTime: 0,
    cardDragging: false,
    cardRotateX: 0,
    cardRotateY: 0,

    // 智能筛选相关
    currentFilter: 'all',
    showAdvancedFilter: false,
    ageOptions: ['18', '20', '22', '25', '28', '30', '32', '35', '40', '45', '50'],
    minAgeIndex: 2, // 22岁
    maxAgeIndex: 8, // 40岁
    distanceFilterOptions: ['不限', '5km内', '10km内', '20km内', '50km内', '同城'],
    distanceIndex: 0,

    // 学历筛选
    educationOptions: ['不限', '高中及以上', '大专及以上', '本科及以上', '硕士及以上'],
    educationIndex: 0,

    // 收入筛选
    incomeOptions: ['不限', '3-5千', '5-8千', '8千-1万', '1万以上'],
    incomeIndex: 0,

    // 状态管理
    isLoading: false,

    // 3D卡片堆叠相关
    useStackMode: true, // 默认使用3D堆叠模式
    stackCards: [], // 3D堆叠卡片数据
    currentCardIndex: 0, // 当前卡片索引
    showSwipeHint: true, // 显示滑动提示

    // 触摸交互相关
    touchStartX: 0,
    touchStartY: 0,
    touchStartTime: 0,
    cardTransform: '',
    isDragging: false,

    // 推荐用户列表
    recommendedUsers: [
      {
        id: 1,
        nickname: '小雨',
        age: 24,
        occupation: '在校学生',
        is_online: true,
        match_score: 95,
        location: '玉林市北流市',
        distance: '2.3km',
        marital_status: '未婚',
        education: '本科',
        height: 175,
        income: '3-5千元/月',
        interests: ['读书', '旅行', '健身'],
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face',
        is_verified: true,
        is_vip: true,
        is_liked: false,
        photo_index: 1,
        photo_total: 6,
        last_active: '1小时前',
        today_views: 23
      },
      {
        id: 2,
        nickname: '阿杰',
        age: 28,
        occupation: '工程师',
        is_online: false,
        match_score: 88,
        location: '广州市天河区',
        distance: '5.8km',
        marital_status: '未婚',
        education: '硕士',
        height: 180,
        income: '8-12千元/月',
        interests: ['摄影', '音乐', '美食'],
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face',
        is_verified: true,
        is_vip: false,
        is_liked: true,
        photo_index: 3,
        photo_total: 8,
        last_active: '3小时前',
        today_views: 15
      },
      {
        id: 3,
        nickname: '晓雯',
        age: 26,
        occupation: '教师',
        is_online: true,
        match_score: 92,
        location: '深圳市南山区',
        distance: '1.2km',
        marital_status: '未婚',
        education: '本科',
        height: 165,
        income: '5-8千元/月',
        interests: ['电影', '瑜伽', '烘焙'],
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=120&h=120&fit=crop&crop=face',
        is_verified: false,
        is_vip: true,
        is_liked: false,
        photo_index: 2,
        photo_total: 5,
        last_active: '30分钟前',
        today_views: 31
      }
    ]
  },

  onLoad() {
    this.loadUserLocation()
    this.loadRecommendations()
    this.loadUserStats()
    this.initVipEnhancements()
  },

  // 轮播横幅切换事件
  onBannerChange(e) {
    this.setData({
      currentBannerIndex: e.detail.current
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索框获得焦点
  onSearchFocus() {
    this.setData({
      showSearchSuggestions: true
    })
  },

  // 搜索框失去焦点
  onSearchBlur() {
    // 延迟隐藏，避免点击建议项时立即隐藏
    setTimeout(() => {
      this.setData({
        showSearchSuggestions: false
      })
    }, 200)
  },

  // 选择搜索建议
  onSearchSelect(e) {
    const keyword = e.detail.keyword
    this.setData({
      searchKeyword: keyword,
      showSearchSuggestions: false
    })
    this.performSearch(keyword)
  },

  // 隐藏搜索建议
  hideSearchSuggestions() {
    this.setData({
      showSearchSuggestions: false
    })
  },

  // 执行搜索
  performSearch(keyword) {
    console.log('搜索关键词:', keyword)
    // 这里可以添加实际的搜索逻辑
    wx.showToast({
      title: `搜索: ${keyword}`,
      icon: 'none'
    })
  },

  // 3D卡片触摸开始
  onCardTouchStart(e) {
    const touch = e.touches[0]
    this.setData({
      cardTouchStartX: touch.clientX,
      cardTouchStartY: touch.clientY,
      cardTouchStartTime: Date.now(),
      cardDragging: true
    })
  },

  // 3D卡片触摸移动
  onCardTouchMove(e) {
    if (!this.data.cardDragging) return

    const touch = e.touches[0]
    const deltaX = touch.clientX - this.data.cardTouchStartX
    const deltaY = touch.clientY - this.data.cardTouchStartY

    // 计算旋转角度
    const rotateY = deltaX * 0.1 // 水平移动影响Y轴旋转
    const rotateX = -deltaY * 0.1 // 垂直移动影响X轴旋转

    this.setData({
      cardRotateX: Math.max(-15, Math.min(15, rotateX)),
      cardRotateY: Math.max(-15, Math.min(15, rotateY))
    })

    // 应用3D变换
    this.applyCardTransform(deltaX, deltaY)
  },

  // 3D卡片触摸结束
  onCardTouchEnd(e) {
    if (!this.data.cardDragging) return

    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - this.data.cardTouchStartX
    const deltaY = touch.clientY - this.data.cardTouchStartY
    const deltaTime = Date.now() - this.data.cardTouchStartTime

    // 判断是否为滑动手势
    const isSwipe = Math.abs(deltaX) > 100 && deltaTime < 500

    if (isSwipe) {
      if (deltaX > 0) {
        // 右滑 - 喜欢
        this.likeCurrentCard()
      } else {
        // 左滑 - 不喜欢
        this.dislikeCurrentCard()
      }
    } else {
      // 重置卡片位置
      this.resetCardPosition()
    }

    this.setData({
      cardDragging: false,
      cardRotateX: 0,
      cardRotateY: 0
    })
  },

  // 应用3D变换
  applyCardTransform(deltaX, deltaY) {
    const query = wx.createSelectorQuery()
    query.select('.user-card-3d.card-0').boundingClientRect()
    query.exec((res) => {
      if (res[0]) {
        const element = res[0]
        const transform = `translateX(${deltaX}px) translateY(${deltaY}px) rotateX(${this.data.cardRotateX}deg) rotateY(${this.data.cardRotateY}deg)`

        // 这里需要通过样式类来实现变换
        // 由于小程序限制，我们使用动画API
        const animation = wx.createAnimation({
          duration: 0,
          timingFunction: 'linear'
        })

        animation.translateX(deltaX).translateY(deltaY)
          .rotateX(this.data.cardRotateX).rotateY(this.data.cardRotateY).step()

        this.setData({
          cardAnimation: animation.export()
        })
      }
    })
  },

  // 重置卡片位置
  resetCardPosition() {
    const animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease-out'
    })

    animation.translateX(0).translateY(0).rotateX(0).rotateY(0).step()

    this.setData({
      cardAnimation: animation.export()
    })
  },

  // 喜欢当前卡片
  likeCurrentCard() {
    const currentUser = this.data.userList[0]
    if (currentUser) {
      this.animateCardExit('right')
      this.likeUser({ currentTarget: { dataset: { user: currentUser } } })
    }
  },

  // 不喜欢当前卡片
  dislikeCurrentCard() {
    const currentUser = this.data.userList[0]
    if (currentUser) {
      this.animateCardExit('left')
      this.dislikeUser({ currentTarget: { dataset: { user: currentUser } } })
    }
  },

  // 卡片退出动画
  animateCardExit(direction) {
    const animation = wx.createAnimation({
      duration: 400,
      timingFunction: 'ease-in'
    })

    const translateX = direction === 'right' ? 1000 : -1000
    animation.translateX(translateX).rotateZ(direction === 'right' ? 30 : -30).opacity(0).step()

    this.setData({
      cardAnimation: animation.export()
    })

    // 延迟移除卡片
    setTimeout(() => {
      this.nextCard()
    }, 400)
  },

  // 刷新推荐
  refreshRecommendations() {
    this.setData({
      loading: true,
      userList: []
    })
    this.loadRecommendations()
  },

  // 显示高级筛选
  showAdvancedFilter() {
    this.setData({
      showAdvancedFilter: true
    })
  },

  // 隐藏高级筛选
  hideAdvancedFilter() {
    this.setData({
      showAdvancedFilter: false
    })
  },

  // 应用高级筛选
  onAdvancedFilterApply(e) {
    const { filters, estimatedResults } = e.detail

    this.setData({
      advancedFilters: filters,
      showAdvancedFilter: false
    })

    // 应用筛选条件重新加载数据
    this.applyAdvancedFilters(filters)

    wx.showToast({
      title: `找到 ${estimatedResults} 位用户`,
      icon: 'none'
    })
  },

  // 应用高级筛选条件
  applyAdvancedFilters(filters) {
    console.log('应用高级筛选:', filters)

    // 这里可以调用API应用筛选条件
    this.setData({
      loading: true,
      userList: []
    })

    // 模拟筛选后的数据加载
    setTimeout(() => {
      this.loadRecommendations()
    }, 500)
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (keyword) {
      this.searchUsers(keyword)
    }
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    })
    this.loadRecommendations()
  },

  // 选择位置
  selectLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          currentLocation: res.name || res.address
        })
        this.loadRecommendations()
      },
      fail: (err) => {
        console.log('选择位置失败:', err)
      }
    })
  },

  // 搜索用户
  searchUsers(keyword) {
    this.setData({ loading: true })

    // 模拟搜索API调用
    setTimeout(() => {
      const filteredUsers = this.data.userList.filter(user =>
        user.nickname.includes(keyword) ||
        (user.tags && user.tags.some(tag => tag.includes(keyword)))
      )

      this.setData({
        userList: filteredUsers,
        loading: false
      })
    }, 500)
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab
    })
    this.loadRecommendations()
  },

  // 查看用户详情
  viewUserDetail(e) {
    const user = e.currentTarget.dataset.user
    wx.navigateTo({
      url: `/pages/user/detail?id=${user.id}`
    })
  },

  // 喜欢用户
  likeUser(e) {
    e.stopPropagation()
    const userId = e.currentTarget.dataset.userId

    // 添加到喜欢列表
    const likedUsers = [...this.data.likedUsers, userId]
    this.setData({ likedUsers })

    // 显示喜欢动画
    this.showLikeAnimation()

    // 调用API
    this.callLikeAPI(userId)
  },

  // 不喜欢用户
  dislikeUser(e) {
    e.stopPropagation()
    const userId = e.currentTarget.dataset.userId

    // 添加到不喜欢列表
    const dislikedUsers = [...this.data.dislikedUsers, userId]
    this.setData({ dislikedUsers })

    // 从列表中移除
    this.removeUserFromList(userId)
  },

  // 超级喜欢
  superLikeUser(e) {
    e.stopPropagation()
    const userId = e.currentTarget.dataset.userId

    // 检查超级喜欢次数
    if (this.data.superLikedUsers.length >= 5) {
      wx.showToast({
        title: '今日超级喜欢次数已用完',
        icon: 'none'
      })
      return
    }

    // 添加到超级喜欢列表
    const superLikedUsers = [...this.data.superLikedUsers, userId]
    this.setData({ superLikedUsers })

    // 显示超级喜欢动画
    this.showSuperLikeAnimation()

    // 调用API
    this.callSuperLikeAPI(userId)
  },

  // 加载更多用户
  loadMoreUsers() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({
      page: this.data.page + 1
    })
    this.loadRecommendations(true)
  },

  // 刷新用户列表
  refreshUsers() {
    this.setData({
      page: 1,
      hasMore: true
    })
    this.loadRecommendations()
  },

  onShow() {
    // 页面显示时重新检查登录状态并刷新数据
    const authManager = app.globalData.authManager
    const isLoggedIn = authManager.isLoggedIn()
    app.globalData.isLogin = isLoggedIn

    if (isLoggedIn) {
      this.loadUserStats()
    }

    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },



  // 加载附近的人
  loadNearbyUsers() {
    this.setData({
      loading: true,
      currentFilter: 'nearby'
    })

    // 这里应该调用API加载附近的用户
    // 暂时使用现有的推荐数据
    this.loadRecommendations()
  },

  // 加载用户位置
  loadUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        // 这里可以调用逆地理编码API获取具体地址
        // 暂时使用默认值
        this.setData({
          userLocation: '北京'
        })
      },
      fail: () => {
        this.setData({
          userLocation: '未知位置'
        })
      }
    })
  },

  // 加载推荐用户
  async loadRecommendations() {
    try {
      this.setData({ loading: true })

      // 检查登录状态，决定加载策略
      const authManager = app.globalData.authManager
      const isLoggedIn = authManager.isLoggedIn()
      app.globalData.isLogin = isLoggedIn

      if (isLoggedIn) {
        // 检查是否是模拟登录
        const token = authManager.getAccessToken()
        const isMockToken = token && token.startsWith('mock_token_')

        if (isMockToken) {
          // 模拟登录：使用示例数据
          console.log('🎭 首页：模拟登录状态，使用示例数据')
          this.loadMockUsers()
          return
        }

        // 真实登录用户：加载个性化推荐
        try {
          console.log('🔐 首页：真实登录状态，调用API')
          const recommendations = await matchingAPI.getRecommendations()
          const processedUsers = recommendations.map(user => ({
            id: user.id,
            nickname: user.nickname || '用户',
            age: user.age || 0,
            avatar: user.avatar || '/images/avatar/default.png',
            occupation: user.occupation || '未填写',
            location: user.location || '未知',
            interests: user.interests || [],
            is_vip: user.is_vip || false,
            is_online: user.is_online || false,
            match_score: user.match_score || 0
          }))

          if (processedUsers.length > 0) {
            this.setData({
              userList: processedUsers,
              currentIndex: 0,
              loading: false
            })
            return
          }
        } catch (error) {
          console.error('加载个性化推荐失败:', error)
          // 继续执行下面的模拟数据逻辑
        }
      }

      // 未登录用户或API失败：显示示例数据
      const mockUsers = [
        {
          id: 1,
          nickname: '小雨',
          age: 25,
          avatar: '/images/avatar/female1.svg',
          occupation: '设计师',
          location: '北京朝阳区',
          interests: ['旅行', '摄影', '美食'],
          is_vip: true,
          is_online: true,
          match_score: 95
        },
        {
          id: 2,
          nickname: '晓明',
          age: 28,
          avatar: '/images/avatar/male1.svg',
          occupation: '工程师',
          location: '北京海淀区',
          interests: ['健身', '电影', '音乐'],
          is_vip: false,
          is_online: false,
          match_score: 88
        },
        {
          id: 3,
          nickname: '小美',
          age: 23,
          avatar: '/images/avatar/female1.svg',
          occupation: '教师',
          location: '北京西城区',
          interests: ['读书', '瑜伽', '咖啡'],
          is_vip: true,
          is_online: true,
          match_score: 92
        }
      ]

      // 为演示数据添加标记
      const processedMockUsers = mockUsers.map(user => ({
        ...user,
        isDemo: !app.globalData.isLogin
      }))

      this.setData({
        userList: processedMockUsers,
        currentIndex: 0,
        loading: false,
        isGuestMode: !app.globalData.isLogin
      })

      // 初始化3D卡片堆叠数据
      this.initStackCards(processedMockUsers)
    } catch (error) {
      console.error('加载推荐用户失败:', error)
      this.setData({
        loading: false,
        userList: [],
        showEmptyState: true
      })
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      if (app.globalData.isLogin) {
        // 已登录用户获取真实统计数据
        // const stats = await userAPI.getUserStats()
        this.setData({
          todayRecommends: 156,
          mutualLikes: 23,
          matchRate: '89%',
          newMessages: 12,
          notificationCount: 3
        })
      } else {
        // 访客模式显示示例数据
        this.setData({
          todayRecommends: 156,
          mutualLikes: 23,
          matchRate: '89%',
          newMessages: 12,
          notificationCount: 0
        })
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 功能按钮点击事件
  goToNews() {
    wx.showToast({ title: '红娘喜讯功能开发中', icon: 'none' })
  },

  goToActivity() {
    wx.navigateTo({ url: '/pages/activity/center' })
  },

  goToPartner() {
    wx.showToast({ title: '找搭子功能开发中', icon: 'none' })
  },

  goToMerchant() {
    wx.showToast({ title: '合作商家功能开发中', icon: 'none' })
  },

  goToClass() {
    wx.showToast({ title: '情感课堂功能开发中', icon: 'none' })
  },

  goToDating() {
    wx.navigateTo({ url: '/pages/dating/feature' })
  },

  goToFollowing() {
    if (!app.globalData.isLogin) {
      this.requireLogin()
      return
    }
    wx.showToast({ title: '我的关注功能开发中', icon: 'none' })
  },

  goToDate() {
    if (!app.globalData.isLogin) {
      this.requireLogin()
      return
    }
    wx.showToast({ title: '我的约会功能开发中', icon: 'none' })
  },

  goToMatchmaker() {
    wx.navigateTo({ url: '/pages/matchmaker/dashboard' })
  },

  goToSafety() {
    wx.showToast({ title: '防骗提醒功能开发中', icon: 'none' })
  },

  // 登录相关
  goToLogin() {
    wx.navigateTo({
      url: '/pages/auth/login'
    })
  },

  requireLogin() {
    wx.showModal({
      title: '需要登录',
      content: '该功能需要登录后使用，是否前往登录？',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin()
        }
      }
    })
  },





  dislikeUser(e) {
    const user = e.currentTarget.dataset.user
    if (user.isDemo) {
      this.requireLogin()
      return
    }

    wx.showToast({
      title: '已跳过',
      icon: 'none'
    })

    // 这里应该调用API
    // await matchingAPI.dislikeUser(user.id)
  },

  // 开始聊天
  startChat(e) {
    e.stopPropagation()
    const user = e.currentTarget.dataset.user

    if (user.isDemo) {
      this.requireLogin()
      return
    }

    // 跳转到聊天页面
    wx.navigateTo({
      url: `/pages/chat/detail?userId=${user.id}&userName=${user.nickname}`
    })
  },

  // 查看用户资料
  viewProfile(e) {
    e.stopPropagation()
    const user = e.currentTarget.dataset.user

    // 跳转到用户详情页
    wx.navigateTo({
      url: `/pages/user/detail?id=${user.id}`
    })
  },



  refreshRecommendations() {
    this.loadRecommendations()
  },

  // 顶部按钮事件
  showNotifications() {
    if (!app.globalData.isLogin) {
      this.requireLogin()
      return
    }
    wx.navigateTo({
      url: '/pages/notification/center'
    })
  },

  showSearch() {
    wx.switchTab({
      url: '/pages/discover/discover'
    })
  },

  // 弹窗控制
  hideMatchModal() {
    this.setData({ showMatchModal: false })
  },

  goToChat() {
    this.hideMatchModal()
    wx.switchTab({
      url: '/pages/chat/chat'
    })
  },

  hideFilterModal() {
    this.setData({ showFilterModal: false })
  },

  // 筛选相关
  onMinAgeChange(e) {
    this.setData({
      'filters.minAge': e.detail.value
    })
  },

  onMaxAgeChange(e) {
    this.setData({
      'filters.maxAge': e.detail.value
    })
  },

  selectDistance(e) {
    const distance = e.currentTarget.dataset.distance
    this.setData({
      'filters.distance': distance
    })
  },

  onVipOnlyChange(e) {
    this.setData({
      'filters.vipOnly': e.detail.value
    })
  },

  onOnlineOnlyChange(e) {
    this.setData({
      'filters.onlineOnly': e.detail.value
    })
  },

  resetFilters() {
    this.setData({
      filters: {
        minAge: 20,
        maxAge: 35,
        distance: '50km内',
        vipOnly: false,
        onlineOnly: false
      }
    })
  },

  applyFilters() {
    this.hideFilterModal()
    wx.showToast({
      title: '筛选条件已应用',
      icon: 'success'
    })
    // 这里应该重新加载推荐数据
    this.loadRecommendations()
  },

  // 触摸开始
  onTouchStart(e) {
    const touch = e.touches[0]
    this.setData({
      startX: touch.clientX,
      startY: touch.clientY,
      isDragging: true
    })
  },

  // 触摸移动
  onTouchMove(e) {
    if (!this.data.isDragging) return
    
    const touch = e.touches[0]
    const deltaX = touch.clientX - this.data.startX
    const deltaY = touch.clientY - this.data.startY
    
    this.setData({
      currentX: deltaX,
      currentY: deltaY
    })
    
    // 这里可以添加卡片跟随手指移动的效果
  },

  // 触摸结束
  onTouchEnd(e) {
    if (!this.data.isDragging) return
    
    const deltaX = this.data.currentX
    const threshold = 100 // 滑动阈值
    
    this.setData({
      isDragging: false,
      currentX: 0,
      currentY: 0
    })
    
    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0) {
        this.onLike() // 右滑喜欢
      } else {
        this.onDislike() // 左滑不喜欢
      }
    }
  },

  // 不喜欢
  async onDislike() {
    const currentUser = this.data.userList[this.data.currentIndex]
    if (!currentUser) return
    
    try {
      await app.request({
        url: '/matching/dislike/',
        method: 'POST',
        data: { target_user_id: currentUser.id }
      })
      
      this.nextCard()
    } catch (error) {
      console.error('操作失败:', error)
      app.showToast('操作失败')
    }
  },

  // 喜欢
  async onLike() {
    const currentUser = this.data.userList[this.data.currentIndex]
    if (!currentUser) return

    try {
      const result = await matchingAPI.likeUser(currentUser.id)

      // 检查是否匹配成功
      if (result.is_match) {
        this.showMatchSuccess(currentUser)
      }

      this.nextCard()
    } catch (error) {
      console.error('喜欢失败:', error)
      app.showToast(error.message || '操作失败')
    }
  },

  // 超级喜欢
  async onSuperLike() {
    if (this.data.superLikesLeft <= 0) {
      app.showToast('今日超级喜欢次数已用完')
      return
    }

    const currentUser = this.data.userList[this.data.currentIndex]
    if (!currentUser) return

    try {
      const result = await matchingAPI.superLikeUser(currentUser.id)

      this.setData({
        superLikesLeft: this.data.superLikesLeft - 1
      })

      // 超级喜欢有更高的匹配概率
      if (result.is_match) {
        this.showMatchSuccess(currentUser)
      }

      this.nextCard()
    } catch (error) {
      console.error('超级喜欢失败:', error)
      app.showToast(error.message || '操作失败')
    }
  },

  // 下一张卡片
  nextCard() {
    const newIndex = this.data.currentIndex + 1
    const newUserList = this.data.userList.slice(1)
    
    this.setData({
      currentIndex: newIndex,
      userList: newUserList
    })
    
    // 如果卡片不足，加载更多
    if (newUserList.length < 2) {
      this.loadRecommendations()
    }
  },

  // 显示匹配成功
  showMatchSuccess(user) {
    this.setData({
      showMatchModal: true,
      matchedUser: user,
      currentUser: app.globalData.userInfo
    })
  },



  // 开始聊天
  goToChat() {
    const user = this.data.matchedUser
    this.hideMatchModal()
    wx.navigateTo({
      url: `/pages/chat/detail?userId=${user.id}&userName=${user.nickname}`
    })
  },

  // 显示筛选弹窗
  showFilterModal() {
    this.setData({
      showFilterModal: true
    })
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({
      showFilterModal: false
    })
  },





  selectDistance(e) {
    this.setData({
      'filters.distance': e.currentTarget.dataset.distance
    })
  },

  selectEducation(e) {
    const index = e.detail.value
    this.setData({
      educationIndex: index
    })
  },

  selectIncome(e) {
    const index = e.detail.value
    this.setData({
      incomeIndex: index
    })
  },

  onVipOnlyChange(e) {
    this.setData({
      'filters.vipOnly': e.detail.value
    })
  },

  onOnlineOnlyChange(e) {
    this.setData({
      'filters.onlineOnly': e.detail.value
    })
  },







  // 加载模拟数据（当后端不可用时）
  loadMockData() {
    console.log('后端服务不可用，加载模拟数据')

    const mockUsers = [
      {
        id: 1,
        name: '小美',
        age: 25,
        avatar: 'https://picsum.photos/400/600?random=1',
        location: '北京',
        occupation: '设计师',
        education: '本科',
        tags: ['爱旅行', '喜欢美食', '健身达人'],
        photos: [
          'https://picsum.photos/400/600?random=11',
          'https://picsum.photos/400/600?random=12',
          'https://picsum.photos/400/600?random=13'
        ],
        bio: '热爱生活，喜欢尝试新事物，希望遇到志同道合的你',
        height: 165,
        weight: 52,
        zodiac: '天秤座',
        hometown: '北京',
        interests: ['摄影', '旅行', '美食', '健身'],
        verified: true,
        match_score: 92,
        is_online: true,
        distance: '2.3km'
      },
      {
        id: 2,
        name: '小雨',
        age: 27,
        avatar: 'https://picsum.photos/400/600?random=2',
        location: '上海',
        occupation: '程序员',
        education: '硕士',
        tags: ['技术宅', '爱读书', '咖啡控'],
        photos: [
          'https://picsum.photos/400/600?random=21',
          'https://picsum.photos/400/600?random=22',
          'https://picsum.photos/400/600?random=23'
        ],
        bio: '代码改变世界，希望找到能一起看星星的人',
        height: 170,
        weight: 55,
        zodiac: '处女座',
        hometown: '杭州',
        interests: ['编程', '阅读', '咖啡', '电影'],
        verified: true,
        match_score: 88,
        is_online: false,
        distance: '5.1km'
      },
      {
        id: 3,
        name: '小晴',
        age: 24,
        avatar: 'https://picsum.photos/400/600?random=3',
        location: '深圳',
        occupation: '教师',
        education: '本科',
        tags: ['温柔', '爱孩子', '喜欢音乐'],
        photos: [
          'https://picsum.photos/400/600?random=31',
          'https://picsum.photos/400/600?random=32',
          'https://picsum.photos/400/600?random=33'
        ],
        bio: '用心教育每一个孩子，也希望遇到那个对的人',
        height: 162,
        weight: 48,
        zodiac: '双鱼座',
        hometown: '广州',
        interests: ['音乐', '绘画', '瑜伽', '烘焙'],
        verified: true,
        match_score: 95,
        is_online: true,
        distance: '1.8km'
      },
      {
        id: 4,
        name: '小慧',
        age: 26,
        avatar: 'https://picsum.photos/400/600?random=4',
        location: '成都',
        occupation: '医生',
        education: '硕士',
        tags: ['温暖', '专业', '爱动物'],
        photos: [
          'https://picsum.photos/400/600?random=41',
          'https://picsum.photos/400/600?random=42',
          'https://picsum.photos/400/600?random=43'
        ],
        bio: '救死扶伤是我的职责，寻找真爱是我的心愿',
        height: 168,
        weight: 53,
        zodiac: '白羊座',
        hometown: '重庆',
        interests: ['医学', '宠物', '登山', '美食'],
        verified: true,
        match_score: 85,
        is_online: true,
        distance: '3.7km'
      },
      {
        id: 5,
        name: '小萌',
        age: 23,
        avatar: 'https://picsum.photos/400/600?random=5',
        location: '杭州',
        occupation: '插画师',
        education: '本科',
        tags: ['文艺', '创意', '爱画画'],
        photos: [
          'https://picsum.photos/400/600?random=51',
          'https://picsum.photos/400/600?random=52',
          'https://picsum.photos/400/600?random=53'
        ],
        bio: '用画笔记录美好，用心感受世界',
        height: 160,
        weight: 45,
        zodiac: '巨蟹座',
        hometown: '苏州',
        interests: ['绘画', '设计', '手工', '猫咪'],
        verified: true,
        match_score: 90,
        is_online: false,
        distance: '4.2km'
      }
    ]

    this.setData({
      userList: mockUsers,
      currentIndex: 0,
      loading: false,
      todayMatches: 3,
      totalLikes: 156,
      superLikesLeft: 5
    })

    wx.showToast({
      title: '演示模式',
      icon: 'none',
      duration: 2000
    })
  },

  // 智能筛选相关方法
  applyFilter(e) {
    const filter = e.currentTarget.dataset.filter
    console.log('应用筛选:', filter)

    this.setData({
      currentFilter: filter
    })

    // 根据筛选条件过滤用户列表
    this.filterUserList(filter)
  },

  filterUserList(filter) {
    // 重新加载推荐用户
    this.loadRecommendations()

    wx.showToast({
      title: `已切换到${this.getFilterName(filter)}`,
      icon: 'none',
      duration: 1500
    })
  },

  getFilterName(filter) {
    const names = {
      'all': '全部用户',
      'nearby': '附近用户',
      'online': '在线用户',
      'match': '高匹配用户',
      'vip': 'VIP用户'
    }
    return names[filter] || '全部用户'
  },

  toggleAdvancedFilter() {
    this.setData({
      showAdvancedFilter: !this.data.showAdvancedFilter
    })
  },





  onDistanceChange(e) {
    this.setData({
      distanceIndex: e.detail.value
    })
  },



  applyAdvancedFilter() {
    const { ageOptions, minAgeIndex, maxAgeIndex, distanceFilterOptions, distanceIndex } = this.data

    const minAge = parseInt(ageOptions[minAgeIndex])
    const maxAge = parseInt(ageOptions[maxAgeIndex])
    const distance = distanceFilterOptions[distanceIndex]

    console.log('应用高级筛选:', { minAge, maxAge, distance })

    this.setData({
      showAdvancedFilter: false
    })

    wx.showToast({
      title: `已应用筛选条件`,
      icon: 'none',
      duration: 2000
    })
  },

  // 专业服务相关方法
  goToParentService() {
    wx.showModal({
      title: '家长代相亲服务',
      content: '专业的家长代相亲服务，为您的子女寻找合适的对象。我们提供：\n\n• 专业红娘一对一服务\n• 严格的身份认证\n• 个性化匹配方案\n• 全程跟踪服务',
      confirmText: '了解详情',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中，敬请期待',
            icon: 'none'
          })
        }
      }
    })
  },

  goToMatchmakerService() {
    wx.showModal({
      title: '专业红娘服务',
      content: '高端定制化红娘服务，为您提供：\n\n• 资深红娘一对一服务\n• 精准匹配推荐\n• 约会指导服务\n• VIP专属通道',
      confirmText: '立即咨询',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中，敬请期待',
            icon: 'none'
          })
        }
      }
    })
  },

  // 推荐用户相关方法

  filterUsersByTab(tab) {
    // 模拟根据标签页筛选用户
    let filteredUsers = [...this.data.recommendedUsers]

    switch (tab) {
      case 'latest':
        // 最新用户
        break
      case 'activity':
        // 活动用户
        filteredUsers = filteredUsers.filter(user => user.is_online)
        break
      case 'nearby':
        // 附近用户
        filteredUsers = filteredUsers.filter(user => parseFloat(user.distance) <= 3)
        break
      case 'match':
        // 高匹配用户
        filteredUsers = filteredUsers.filter(user => user.match_score >= 90)
        break
      case 'video':
        // 跳转到视频推荐页面
        wx.navigateTo({
          url: '/pages/video-recommend/video-recommend'
        })
        return
    }

    this.setData({
      recommendedUsers: filteredUsers
    })

    wx.showToast({
      title: `已切换到${this.getTabName(tab)}`,
      icon: 'none'
    })
  },

  getTabName(tab) {
    const names = {
      'latest': '最新',
      'activity': '活动',
      'nearby': '附近',
      'match': '匹配',
      'video': '视频'
    }
    return names[tab] || '最新'
  },

  // 用户操作方法
  viewUserProfile(e) {
    const user = e.currentTarget.dataset.user
    console.log('查看用户资料:', user)

    wx.showToast({
      title: '跳转到用户详情页',
      icon: 'none'
    })
  },

  applyConnection(e) {
    e.stopPropagation()
    const user = e.currentTarget.dataset.user

    wx.showModal({
      title: '申请牵线',
      content: `确定要申请与${user.nickname}的牵线服务吗？\n\n牵线成功后，您将获得：\n• 专业红娘介绍\n• 个性化匹配建议\n• 约会指导服务`,
      confirmText: '确定申请',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '申请已提交，红娘将尽快联系您',
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
  },





  // 加载更多推荐
  loadMoreRecommendations() {
    wx.showLoading({
      title: '加载中...'
    })

    // 模拟加载更多用户数据
    setTimeout(() => {
      const newUsers = [
        {
          id: 4,
          nickname: '小美',
          age: 23,
          occupation: '设计师',
          is_online: true,
          match_score: 89,
          location: '上海市浦东新区',
          distance: '3.5km',
          marital_status: '未婚',
          education: '本科',
          height: 168,
          income: '6-8千元/月',
          interests: ['设计', '咖啡', '旅行'],
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=120&h=120&fit=crop&crop=face',
          is_verified: true,
          is_vip: false,
          is_liked: false,
          photo_index: 1,
          photo_total: 4,
          last_active: '2小时前',
          today_views: 18
        },
        {
          id: 5,
          nickname: '志强',
          age: 30,
          occupation: '医生',
          is_online: false,
          match_score: 91,
          location: '杭州市西湖区',
          distance: '8.2km',
          marital_status: '未婚',
          education: '博士',
          height: 178,
          income: '15-20千元/月',
          interests: ['医学', '跑步', '阅读'],
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
          is_verified: true,
          is_vip: true,
          is_liked: false,
          photo_index: 2,
          photo_total: 7,
          last_active: '5小时前',
          today_views: 12
        }
      ]

      const currentUsers = [...this.data.recommendedUsers]
      this.setData({
        recommendedUsers: [...currentUsers, ...newUsers]
      })

      wx.hideLoading()
      wx.showToast({
        title: `新增${newUsers.length}位推荐用户`,
        icon: 'none'
      })
    }, 1500)
  },

  // 调整筛选条件
  adjustFilters() {
    this.setData({
      showAdvancedFilter: true,
      showEmptyState: false
    })
  },

  // 模拟加载状态
  simulateLoading() {
    this.setData({
      isLoading: true,
      showEmptyState: false
    })

    setTimeout(() => {
      this.setData({
        isLoading: false,
        showEmptyState: this.data.recommendedUsers.length === 0
      })
    }, 2000)
  },

  // ==================== 3D卡片堆叠相关方法 ====================

  // 初始化3D卡片堆叠数据
  initStackCards(userList) {
    if (!userList || userList.length === 0) return

    // 取前3张卡片进行堆叠显示
    const stackCards = userList.slice(0, 3).map((user, index) => ({
      ...user,
      transform: this.getCardTransform(index)
    }))

    this.setData({
      stackCards,
      currentCardIndex: 0
    })
  },

  // 获取卡片的3D变换样式
  getCardTransform(index) {
    const transforms = [
      'translateZ(0) scale(1)', // 前景卡片
      'scale(0.95) translateY(30rpx) translateZ(-100rpx)', // 中景卡片
      'scale(0.9) translateY(60rpx) translateZ(-200rpx)' // 背景卡片
    ]
    return transforms[index] || 'scale(0.85) translateY(90rpx) translateZ(-300rpx)'
  },

  // 切换视图模式
  toggleViewMode() {
    const newMode = !this.data.useStackMode
    this.setData({
      useStackMode: newMode
    })

    if (newMode && this.data.userList.length > 0) {
      this.initStackCards(this.data.userList)
    }

    wx.showToast({
      title: newMode ? '已切换到卡片模式' : '已切换到列表模式',
      icon: 'none'
    })
  },

  // 卡片触摸开始
  onCardTouchStart(e) {
    const touch = e.touches[0]
    this.setData({
      touchStartX: touch.clientX,
      touchStartY: touch.clientY,
      touchStartTime: Date.now(),
      isDragging: true
    })
  },

  // 卡片触摸移动
  onCardTouchMove(e) {
    if (!this.data.isDragging) return

    const touch = e.touches[0]
    const deltaX = touch.clientX - this.data.touchStartX
    const deltaY = touch.clientY - this.data.touchStartY

    // 计算旋转角度和缩放比例
    const rotation = deltaX * 0.1
    const scale = 1 - Math.abs(deltaX) * 0.0005

    // 限制最小缩放比例
    const finalScale = Math.max(scale, 0.8)

    const transform = `translateX(${deltaX}rpx) translateY(${deltaY}rpx) rotate(${rotation}deg) scale(${finalScale})`

    // 更新当前卡片的变换
    const stackCards = [...this.data.stackCards]
    if (stackCards[0]) {
      stackCards[0].transform = transform
      this.setData({ stackCards })
    }
  },

  // 卡片触摸结束
  onCardTouchEnd(e) {
    if (!this.data.isDragging) return

    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - this.data.touchStartX
    const deltaTime = Date.now() - this.data.touchStartTime

    this.setData({ isDragging: false })

    // 判断滑动方向和距离
    const threshold = 100 // 滑动阈值
    const isQuickSwipe = deltaTime < 300 && Math.abs(deltaX) > 50

    if (Math.abs(deltaX) > threshold || isQuickSwipe) {
      if (deltaX > 0) {
        // 右滑 - 喜欢
        this.likeCard({ currentTarget: { dataset: { index: 0 } } })
      } else {
        // 左滑 - 跳过
        this.dislikeCard({ currentTarget: { dataset: { index: 0 } } })
      }
    } else {
      // 回弹到原位
      this.resetCardPosition()
    }
  },

  // 重置卡片位置
  resetCardPosition() {
    const stackCards = [...this.data.stackCards]
    if (stackCards[0]) {
      stackCards[0].transform = this.getCardTransform(0)
      this.setData({ stackCards })
    }
  },

  // 喜欢卡片
  likeCard(e) {
    const index = e.currentTarget.dataset.index
    const user = this.data.stackCards[index]

    if (!user) return

    // 显示喜欢动画
    this.animateCardExit('right')

    // 处理喜欢逻辑
    this.processLike(user)

    // 移到下一张卡片
    setTimeout(() => {
      this.nextCard()
    }, 300)
  },

  // 跳过卡片
  dislikeCard(e) {
    const index = e.currentTarget.dataset.index

    // 显示跳过动画
    this.animateCardExit('left')

    // 移到下一张卡片
    setTimeout(() => {
      this.nextCard()
    }, 300)
  },

  // 超级喜欢卡片
  superLikeCard(e) {
    const index = e.currentTarget.dataset.index
    const user = this.data.stackCards[index]

    if (!user) return

    // 显示超级喜欢动画
    this.animateCardExit('up')

    // 处理超级喜欢逻辑
    this.processSuperLike(user)

    // 移到下一张卡片
    setTimeout(() => {
      this.nextCard()
    }, 300)
  },

  // 卡片退出动画
  animateCardExit(direction) {
    const stackCards = [...this.data.stackCards]
    if (!stackCards[0]) return

    let transform = ''
    switch (direction) {
      case 'right':
        transform = 'translateX(200%) rotate(30deg) scale(0.8)'
        break
      case 'left':
        transform = 'translateX(-200%) rotate(-30deg) scale(0.8)'
        break
      case 'up':
        transform = 'translateY(-200%) scale(1.1)'
        break
    }

    stackCards[0].transform = transform
    this.setData({ stackCards })
  },

  // 移到下一张卡片
  nextCard() {
    const { stackCards, userList, currentCardIndex } = this.data

    // 移除第一张卡片
    const newStackCards = stackCards.slice(1)

    // 如果还有更多用户，添加新卡片到末尾
    const nextUserIndex = currentCardIndex + 3
    if (nextUserIndex < userList.length) {
      const newCard = {
        ...userList[nextUserIndex],
        transform: this.getCardTransform(2)
      }
      newStackCards.push(newCard)
    }

    // 重新计算剩余卡片的变换
    const updatedStackCards = newStackCards.map((card, index) => ({
      ...card,
      transform: this.getCardTransform(index)
    }))

    this.setData({
      stackCards: updatedStackCards,
      currentCardIndex: currentCardIndex + 1,
      showSwipeHint: false // 隐藏滑动提示
    })

    // 如果没有更多卡片了，显示空状态
    if (updatedStackCards.length === 0) {
      this.showNoMoreCards()
    }
  },

  // 显示没有更多卡片的状态
  showNoMoreCards() {
    wx.showModal({
      title: '没有更多推荐了',
      content: '已经看完所有推荐用户，要刷新获取更多推荐吗？',
      confirmText: '刷新',
      cancelText: '返回列表',
      success: (res) => {
        if (res.confirm) {
          this.refreshRecommendations()
        } else {
          this.setData({ useStackMode: false })
        }
      }
    })
  },

  // 处理喜欢逻辑
  processLike(user) {
    console.log('喜欢用户:', user.nickname)

    // 这里可以调用API
    // matchingAPI.likeUser(user.id)

    wx.showToast({
      title: `已喜欢 ${user.nickname}`,
      icon: 'success'
    })
  },

  // 处理超级喜欢逻辑
  processSuperLike(user) {
    console.log('超级喜欢用户:', user.nickname)

    // 这里可以调用API
    // matchingAPI.superLikeUser(user.id)

    wx.showToast({
      title: `已超级喜欢 ${user.nickname}`,
      icon: 'success'
    })
  },

  // ==================== 页面跳转方法 ====================

  // 跳转到礼物商城
  goToGift() {
    wx.navigateTo({
      url: '/pages/gift/gift'
    })
  },

  // 跳转到VIP页面
  goToVip() {
    wx.navigateTo({
      url: '/pages/vip/vip'
    })
  },

  // 跳转到红娘团队
  goToMatchmaker() {
    wx.navigateTo({
      url: '/pages/matchmaker/matchmaker'
    })
  },

  // 跳转到活动页面
  goToActivity() {
    wx.navigateTo({
      url: '/pages/activity/activity'
    })
  },

  // 跳转到安全须知
  goToSafety() {
    wx.navigateTo({
      url: '/pages/safety/safety'
    })
  },

  // 跳转到新闻页面
  goToNews() {
    wx.navigateTo({
      url: '/pages/news/news'
    })
  },

  // 跳转到找搭子
  goToPartner() {
    wx.navigateTo({
      url: '/pages/partner/partner'
    })
  },

  // 跳转到合作商家
  goToMerchant() {
    wx.navigateTo({
      url: '/pages/merchant/merchant'
    })
  },

  // 跳转到情感课堂
  goToClass() {
    wx.navigateTo({
      url: '/pages/class/class'
    })
  },

  // 跳转到交友相亲
  goToDating() {
    wx.navigateTo({
      url: '/pages/dating/dating'
    })
  },

  // 跳转到我的关注
  goToFollowing() {
    wx.navigateTo({
      url: '/pages/following/following'
    })
  },

  // 跳转到我的约会
  goToDate() {
    wx.navigateTo({
      url: '/pages/date/date'
    })
  },

  // 跳转到家长服务
  goToParentService() {
    wx.navigateTo({
      url: '/pages/parent/parent'
    })
  },

  // 跳转到红娘服务
  goToMatchmakerService() {
    wx.navigateTo({
      url: '/pages/matchmaker-service/matchmaker-service'
    })
  },

  // 跳转到红娘培训
  goToMatchmakerTraining() {
    wx.navigateTo({
      url: '/pages/matchmaker-training/training'
    })
  },

  // ========== VIP功能增强 ==========

  /**
   * 初始化VIP功能增强
   */
  async initVipEnhancements() {
    try {
      // 检查用户VIP状态
      const vipStatus = await this.checkVipStatus()

      if (vipStatus.isVIP) {
        console.log('🌟 初始化VIP功能增强')

        // 添加VIP推荐标识
        this.addVipRecommendationBadges()

        // 显示VIP特权状态
        this.displayVipPrivilegeStatus()

        // 创建VIP快捷入口
        this.createVipShortcuts()

        // 启用VIP专属功能
        this.enableVipExclusiveFeatures()

        console.log('✅ VIP功能增强初始化完成')
      }
    } catch (error) {
      console.error('VIP功能增强初始化失败:', error)
    }
  },

  /**
   * 检查VIP状态
   */
  async checkVipStatus() {
    try {
      // 这里应该调用实际的VIP状态API
      // 暂时使用模拟数据
      return {
        isVIP: true,
        level: 'gold',
        expireDate: '2024-12-31',
        remainingDays: 45,
        dailyLikesUsed: 12,
        dailyLikesLimit: 999,
        superLikesUsed: 2,
        superLikesLimit: 5,
        canInvisibleBrowse: true,
        invisibleBrowseActive: false
      }
    } catch (error) {
      console.error('检查VIP状态失败:', error)
      return { isVIP: false }
    }
  },

  /**
   * 添加VIP推荐标识
   */
  addVipRecommendationBadges() {
    const recommendedUsers = (this.data.recommendedUsers || []).map(user => {
      // 为VIP用户添加优先推荐标识
      const isVipRecommendation = this.checkVipRecommendation(user)
      const priorityLevel = this.calculatePriorityLevel(user)

      return {
        ...user,
        isVipRecommendation,
        priorityLevel,
        vipMatchScore: isVipRecommendation ? user.match_score + 5 : user.match_score,
        showVipBadge: isVipRecommendation
      }
    })

    // 按VIP推荐优先级排序
    recommendedUsers.sort((a, b) => {
      if (a.isVipRecommendation && !b.isVipRecommendation) return -1
      if (!a.isVipRecommendation && b.isVipRecommendation) return 1
      return b.vipMatchScore - a.vipMatchScore
    })

    this.setData({ recommendedUsers })
    console.log('✅ VIP推荐标识已添加')
  },

  /**
   * 检查是否为VIP推荐
   */
  checkVipRecommendation(user) {
    // VIP推荐逻辑：
    // 1. 用户是VIP
    // 2. 匹配度高于90%
    // 3. 最近活跃
    // 4. 已认证
    return user.is_vip ||
           user.match_score >= 90 ||
           user.is_verified ||
           (user.last_active && user.last_active.includes('分钟前')) ||
           (user.last_active && user.last_active.includes('小时前'))
  },

  /**
   * 计算优先级等级
   */
  calculatePriorityLevel(user) {
    let priority = 0

    if (user.is_vip) priority += 3
    if (user.match_score >= 95) priority += 2
    if (user.is_verified) priority += 1
    if (user.is_online) priority += 2

    if (priority >= 6) return 'high'
    if (priority >= 3) return 'medium'
    return 'normal'
  },

  /**
   * 显示VIP特权使用状态
   */
  async displayVipPrivilegeStatus() {
    try {
      const vipStatus = await this.checkVipStatus()

      const privilegeStatus = {
        dailyLikes: {
          used: vipStatus.dailyLikesUsed || 0,
          total: vipStatus.dailyLikesLimit || 999,
          percentage: this.calculateUsagePercentage(
            vipStatus.dailyLikesUsed,
            vipStatus.dailyLikesLimit
          ),
          isUnlimited: vipStatus.dailyLikesLimit === 999
        },
        superLikes: {
          used: vipStatus.superLikesUsed || 0,
          total: vipStatus.superLikesLimit || 5,
          percentage: this.calculateUsagePercentage(
            vipStatus.superLikesUsed,
            vipStatus.superLikesLimit
          ),
          remaining: Math.max(0, (vipStatus.superLikesLimit || 5) - (vipStatus.superLikesUsed || 0))
        },
        invisibleBrowse: {
          enabled: vipStatus.canInvisibleBrowse,
          active: vipStatus.invisibleBrowseActive
        }
      }

      this.setData({
        vipPrivilegeStatus: privilegeStatus,
        showVipStatus: true
      })

      console.log('✅ VIP特权状态已显示')
    } catch (error) {
      console.error('显示VIP特权状态失败:', error)
    }
  },

  /**
   * 计算使用百分比
   */
  calculateUsagePercentage(used, total) {
    if (total === 999 || total === 0) return 0 // 无限制
    return Math.round((used / total) * 100)
  },

  /**
   * 创建VIP快捷入口
   */
  createVipShortcuts() {
    const vipShortcuts = [
      {
        id: 'vip_visitors',
        title: '访客记录',
        icon: '👀',
        description: '查看谁看过我',
        action: 'navigateToVisitors',
        vipOnly: true,
        badge: this.getVisitorsBadgeCount()
      },
      {
        id: 'vip_undo',
        title: '撤销操作',
        icon: '↶',
        description: '撤销刚才的选择',
        action: 'showUndoOptions',
        vipOnly: true,
        enabled: this.hasUndoableActions()
      },
      {
        id: 'vip_filter',
        title: '高级筛选',
        icon: '🔍',
        description: '更精准的匹配',
        action: 'openAdvancedFilter',
        vipOnly: true
      },
      {
        id: 'vip_customer_service',
        title: '专属客服',
        icon: '👨‍💼',
        description: '24小时专属服务',
        action: 'openVipCustomerService',
        vipOnly: true,
        online: true
      }
    ]

    this.setData({
      vipShortcuts,
      showVipShortcuts: true
    })

    console.log('✅ VIP快捷入口已创建')
  },

  /**
   * 获取访客记录徽章数量
   */
  getVisitorsBadgeCount() {
    // 模拟返回访客数量
    return Math.floor(Math.random() * 10) + 1
  },

  /**
   * 检查是否有可撤销的操作
   */
  hasUndoableActions() {
    // 模拟检查是否有可撤销的操作
    return Math.random() > 0.5
  },

  /**
   * 启用VIP专属功能
   */
  enableVipExclusiveFeatures() {
    console.log('🌟 启用VIP专属功能')

    // 启用无限喜欢
    this.setData({
      unlimitedLikes: true,
      dailyLikesLimit: 999
    })

    // 启用隐身浏览
    this.setData({
      invisibleBrowseEnabled: true
    })

    // 启用高级筛选
    this.setData({
      advancedFilterEnabled: true
    })

    // 启用专属客服
    this.setData({
      vipCustomerServiceEnabled: true
    })

    console.log('✅ VIP专属功能已启用')
  }
})
