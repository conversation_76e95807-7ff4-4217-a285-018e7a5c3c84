<!--pages/home/<USER>
<view class="home-container">
  <!-- 简化的顶部导航 -->
  <view class="home-header">
    <view class="header-content">
      <view class="header-left">
        <text class="app-title">缘分</text>
        <view class="location-info" bindtap="selectLocation">
          <text class="location-icon">📍</text>
          <text class="location-text">{{currentLocation || '北京'}}</text>
          <text class="location-arrow">▼</text>
        </view>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="showNotifications">
          <text class="icon">🔔</text>
          <view class="badge" wx:if="{{notificationCount > 0}}">{{notificationCount}}</view>
        </view>
        <view class="action-btn" bindtap="showFilterModal">
          <text class="icon">⚙️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-container">
        <text class="search-icon">🔍</text>
        <input class="search-input"
               placeholder="搜索用户昵称、兴趣爱好..."
               value="{{searchKeyword}}"
               bindinput="onSearchInput"
               bindconfirm="onSearch"
               bindfocus="onSearchFocus"
               bindblur="onSearchBlur" />
        <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="clearSearch">
          <text class="clear-icon">✕</text>
        </view>
      </view>
      <view class="advanced-filter-btn" bindtap="showAdvancedFilter">
        <text class="filter-icon">⚙️</text>
        <text class="filter-text">筛选</text>
      </view>
    </view>
  </view>

  <!-- 轮播横幅 -->
  <view class="banner-section">
    <swiper class="banner-swiper"
            indicator-dots="true"
            indicator-color="rgba(255,255,255,0.3)"
            indicator-active-color="#ffffff"
            autoplay="true"
            interval="5000"
            duration="500"
            circular="true"
            current="{{currentBannerIndex}}"
            bindchange="onBannerChange">
      <swiper-item wx:for="{{bannerList}}" wx:key="id" class="banner-item">
        <view class="banner-content banner-{{item.type}}">
          <view class="banner-text">
            <view class="banner-title">{{item.title}}</view>
            <view class="banner-subtitle">{{item.subtitle}}</view>
          </view>
          <view class="banner-icon">
            <text class="icon-text">{{item.icon}}</text>
          </view>
          <view class="banner-decoration">
            <view class="decoration-circle decoration-1"></view>
            <view class="decoration-circle decoration-2"></view>
            <view class="decoration-circle decoration-3"></view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
      <view class="tabs-container">
        <view class="tab-item {{currentTab === 'recommend' ? 'active' : ''}}" data-tab="recommend" bindtap="switchTab">
          <text class="tab-text">推荐</text>
          <view class="tab-count" wx:if="{{tabCounts.recommend}}">{{tabCounts.recommend}}</view>
        </view>
        <view class="tab-item {{currentTab === 'nearby' ? 'active' : ''}}" data-tab="nearby" bindtap="switchTab">
          <text class="tab-text">附近</text>
          <view class="tab-count" wx:if="{{tabCounts.nearby}}">{{tabCounts.nearby}}</view>
        </view>
        <view class="tab-item {{currentTab === 'online' ? 'active' : ''}}" data-tab="online" bindtap="switchTab">
          <text class="tab-text">在线</text>
          <view class="tab-count" wx:if="{{tabCounts.online}}">{{tabCounts.online}}</view>
        </view>
        <view class="tab-item {{currentTab === 'new' ? 'active' : ''}}" data-tab="new" bindtap="switchTab">
          <text class="tab-text">新人</text>
          <view class="tab-count" wx:if="{{tabCounts.new}}">{{tabCounts.new}}</view>
        </view>
        <view class="tab-item {{currentTab === 'vip' ? 'active' : ''}}" data-tab="vip" bindtap="switchTab">
          <text class="tab-text">VIP</text>
          <view class="tab-count" wx:if="{{tabCounts.vip}}">{{tabCounts.vip}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 3D卡片堆叠区域 -->
  <view class="card-stack-container">
    <view class="card-stack" wx:if="{{userList.length > 0}}">
      <view
        wx:for="{{userList.slice(0, 3)}}"
        wx:key="id"
        class="user-card-3d card-{{index}}"
        data-index="{{index}}"
        animation="{{index === 0 ? cardAnimation : ''}}"
        bindtouchstart="onCardTouchStart"
        bindtouchmove="onCardTouchMove"
        bindtouchend="onCardTouchEnd"
      >
        <view class="card-content">
          <!-- 用户头像 -->
          <view class="card-avatar-section">
            <image
              class="card-avatar"
              src="{{item.avatar}}"
              mode="aspectFill"
              lazy-load="true"
            />
            <view wx:if="{{item.is_online}}" class="online-indicator-3d"></view>
            <view wx:if="{{item.is_vip}}" class="vip-badge-3d">
              <text class="vip-icon">👑</text>
            </view>
          </view>

          <!-- 用户信息 -->
          <view class="card-info-section">
            <view class="card-name-row">
              <text class="card-name">{{item.nickname}}</text>
              <text class="card-age">{{item.age}}岁</text>
            </view>
            <text class="card-occupation">{{item.occupation}}</text>
            <view class="card-location-row">
              <text class="location-icon">📍</text>
              <text class="card-location">{{item.location}}</text>
              <text class="card-distance">{{item.distance}}</text>
            </view>
            <view class="card-match-score">
              <text class="match-icon">💕</text>
              <text class="match-text">匹配度 {{item.match_score}}%</text>
            </view>
          </view>

          <!-- 渐变遮罩 -->
          <view class="card-gradient-overlay"></view>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions-3d" wx:if="{{index === 0}}">
          <view class="action-btn-3d dislike-btn-3d" bindtap="dislikeUser" data-user="{{item}}">
            <text class="action-icon-3d">✕</text>
          </view>
          <view class="action-btn-3d like-btn-3d" bindtap="likeUser" data-user="{{item}}">
            <text class="action-icon-3d">💖</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{userList.length === 0 && !loading}}" class="empty-stack">
      <view class="empty-icon">🔍</view>
      <text class="empty-title">暂无推荐用户</text>
      <text class="empty-subtitle">试试调整筛选条件</text>
      <button class="refresh-btn" bindtap="refreshRecommendations">刷新推荐</button>
    </view>
  </view>

  <!-- 用户推荐卡片 -->
  <view class="user-recommendations">
    <view class="recommendations-container">
      <view class="user-card" wx:for="{{userList}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <view class="card-header">
          <image class="user-avatar" src="{{item.avatar || '/images/avatar/default.svg'}}" mode="aspectFill"></image>
          <view class="online-indicator" wx:if="{{item.is_online}}"></view>
          <view class="vip-badge" wx:if="{{item.is_vip}}">
            <text class="vip-text">VIP</text>
          </view>
        </view>

        <view class="card-content">
          <view class="user-info">
            <text class="user-name">{{item.nickname}}</text>
            <text class="user-age">{{item.age}}岁</text>
          </view>
          <text class="user-location">{{item.location}}</text>
          <text class="user-occupation">{{item.occupation}}</text>

          <view class="user-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">{{tag}}</text>
          </view>
        </view>

        <view class="card-actions">
          <view class="action-btn dislike-btn" bindtap="dislikeUser" data-user-id="{{item.id}}">
            <text class="action-icon">✕</text>
          </view>
          <view class="action-btn like-btn" bindtap="likeUser" data-user-id="{{item.id}}">
            <text class="action-icon">❤️</text>
          </view>
          <view class="action-btn super-like-btn" bindtap="superLikeUser" data-user-id="{{item.id}}">
            <text class="action-icon">⭐</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <view class="load-more-btn" bindtap="loadMoreUsers">
          <text class="load-text">加载更多</text>
        </view>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!hasMore && userList.length > 0}}">
        <text class="no-more-text">没有更多用户了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{userList.length === 0}}">
        <text class="empty-icon">💔</text>
        <text class="empty-text">暂无推荐用户</text>
        <view class="refresh-btn" bindtap="refreshUsers">
          <text class="refresh-text">刷新试试</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Function Grid -->
  <view class="function-grid fade-in-scale">
    <view class="grid-row">
      <view class="function-item premium-button" bindtap="goToNews">
        <custom-icon name="notification" type="warning" size="large" class="function-icon bounce-element"></custom-icon>
        <text class="function-title">红娘喜讯</text>
      </view>
      <view class="function-item premium-button" bindtap="goToActivity">
        <custom-icon name="activity" type="success" size="large" class="function-icon floating-element"></custom-icon>
        <text class="function-title">青年活动</text>
      </view>
      <view class="function-item premium-button" bindtap="goToPartner">
        <custom-icon name="profile" type="primary" size="large" class="function-icon"></custom-icon>
        <text class="function-title">找搭子</text>
      </view>
      <view class="function-item premium-button" bindtap="goToMerchant">
        <custom-icon name="verified" type="warning" size="large" class="function-icon sparkle-element"></custom-icon>
        <text class="function-title">合作商家</text>
      </view>
      <view class="function-item premium-button" bindtap="goToClass">
        <custom-icon name="message" type="primary" size="large" class="function-icon"></custom-icon>
        <text class="function-title">情感课堂</text>
      </view>
    </view>
    <view class="grid-row">
      <view class="function-item premium-button" bindtap="goToDating">
        <custom-icon name="heart" type="danger" size="large" class="function-icon heartbeat-element"></custom-icon>
        <text class="function-title">交友相亲</text>
      </view>
      <view class="function-item premium-button" bindtap="goToFollowing">
        <custom-icon name="star" type="warning" size="large" class="function-icon sparkle-element"></custom-icon>
        <text class="function-title">我的关注</text>
      </view>
      <view class="function-item premium-button" bindtap="goToDate">
        <custom-icon name="activity" type="primary" size="large" class="function-icon floating-element"></custom-icon>
        <text class="function-title">我的约会</text>
      </view>
      <view class="function-item premium-button" bindtap="goToMatchmaker">
        <custom-icon name="matchmaker" type="gold" size="large" class="function-icon premium-glow"></custom-icon>
        <text class="function-title">红娘团队</text>
      </view>
      <view class="function-item premium-button" bindtap="goToSafety">
        <custom-icon name="security" type="success" size="large" class="function-icon bounce-element"></custom-icon>
        <text class="function-title">防骗提醒</text>
      </view>
    </view>
  </view>

  <!-- Smart Filter Bar -->
  <view class="smart-filter">
    <view class="filter-header">
      <text class="filter-title">智能筛选</text>
      <view class="filter-header-actions">
        <!-- 模式切换按钮 -->
        <view class="view-mode-toggle" bindtap="toggleViewMode">
          <view class="mode-btn {{useStackMode ? 'active' : ''}}">
            <text class="mode-icon">🎴</text>
            <text class="mode-text">卡片</text>
          </view>
          <view class="mode-btn {{!useStackMode ? 'active' : ''}}">
            <text class="mode-icon">📋</text>
            <text class="mode-text">列表</text>
          </view>
        </view>
        <view class="advanced-filter-btn" bindtap="toggleAdvancedFilter">
          <text class="advanced-text">高级筛选</text>
          <text class="filter-icon">⚙️</text>
        </view>
      </view>
    </view>
    <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
      <view class="filter-tags">
        <view class="filter-tag {{currentFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="applyFilter">
          <text class="tag-text">全部</text>
          <text class="tag-count">1.2k</text>
        </view>
        <view class="filter-tag {{currentFilter === 'nearby' ? 'active' : ''}}" data-filter="nearby" bindtap="applyFilter">
          <text class="tag-icon">📍</text>
          <text class="tag-text">附近</text>
          <text class="tag-count">86</text>
        </view>
        <view class="filter-tag {{currentFilter === 'online' ? 'active' : ''}}" data-filter="online" bindtap="applyFilter">
          <text class="tag-icon online-dot">●</text>
          <text class="tag-text">在线</text>
          <text class="tag-count">234</text>
        </view>
        <view class="filter-tag {{currentFilter === 'match' ? 'active' : ''}}" data-filter="match" bindtap="applyFilter">
          <text class="tag-icon">💖</text>
          <text class="tag-text">高匹配</text>
          <text class="tag-count">45</text>
        </view>
        <view class="filter-tag {{currentFilter === 'vip' ? 'active' : ''}}" data-filter="vip" bindtap="applyFilter">
          <text class="tag-icon">👑</text>
          <text class="tag-text">VIP</text>
          <text class="tag-count">128</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- Advanced Filter Panel -->
  <view class="advanced-filter {{showAdvancedFilter ? 'show' : ''}}" wx:if="{{showAdvancedFilter}}">
    <view class="filter-panel">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">年龄范围</text>
          <view class="age-range">
            <picker class="age-picker" range="{{ageOptions}}" value="{{minAgeIndex}}" bindchange="onMinAgeChange">
              <text class="picker-text">{{ageOptions[minAgeIndex]}}岁</text>
            </picker>
            <text class="range-separator">-</text>
            <picker class="age-picker" range="{{ageOptions}}" value="{{maxAgeIndex}}" bindchange="onMaxAgeChange">
              <text class="picker-text">{{ageOptions[maxAgeIndex]}}岁</text>
            </picker>
          </view>
        </view>
        <view class="filter-item">
          <text class="filter-label">距离</text>
          <picker class="distance-picker" range="{{distanceOptions}}" value="{{distanceIndex}}" bindchange="onDistanceChange">
            <text class="picker-text">{{distanceOptions[distanceIndex]}}</text>
          </picker>
        </view>
      </view>
      <view class="filter-actions">
        <view class="filter-btn reset-btn" bindtap="resetFilters">重置</view>
        <view class="filter-btn apply-btn" bindtap="applyAdvancedFilter">应用筛选</view>
      </view>
    </view>
  </view>

  <!-- User Cards List -->
  <view class="user-cards-section">
    <!-- 访客模式提示 -->
    <view class="guest-tip" wx:if="{{isGuestMode}}">
      <view class="tip-content">
        <text class="tip-icon">👋</text>
        <text class="tip-text">登录后查看更多精准推荐</text>
        <view class="tip-btn" bindtap="goToLogin">立即登录</view>
      </view>
    </view>

    <!-- 原型图风格用户列表 -->
    <view class="prototype-user-list" wx:if="{{userList.length > 0}}">
      <view
        class="prototype-user-card"
        wx:for="{{userList}}"
        wx:key="id"
        bindtap="viewUserDetail"
        data-user="{{item}}"
      >
        <!-- 左侧头像区域 -->
        <view class="card-left">
          <view class="avatar-container">
            <image src="{{item.avatar}}" class="user-avatar" mode="aspectFill" />
            <view class="vip-badge" wx:if="{{item.is_vip}}">
              <text class="vip-text">VIP</text>
            </view>
            <view class="online-dot" wx:if="{{item.is_online}}"></view>
          </view>
        </view>

        <!-- 右侧信息区域 -->
        <view class="card-right">
          <!-- 用户基本信息 -->
          <view class="user-info-header">
            <view class="user-name-age">
              <text class="user-name">{{item.nickname}}</text>
              <text class="user-age">{{item.age}}岁</text>
            </view>
            <view class="match-score" wx:if="{{item.match_score}}">
              <text class="match-icon">❤️</text>
              <text class="match-text">匹配度</text>
              <text class="match-percentage">{{item.match_score}}%</text>
            </view>
          </view>

          <!-- 职业信息 -->
          <text class="user-occupation">{{item.occupation}}</text>

          <!-- 位置和距离 -->
          <view class="location-info">
            <text class="location-icon">📍</text>
            <text class="location-text">{{item.location}}</text>
            <text class="distance-text">{{item.distance || '2.3km'}}</text>
          </view>

          <!-- 基本信息 -->
          <view class="basic-info">
            <text class="info-item">{{item.education || '本科'}}</text>
            <text class="info-separator">·</text>
            <text class="info-item">身高{{item.height || '175cm'}}</text>
          </view>

          <!-- 月收入 -->
          <text class="income-info">月入{{item.income || '3-5千元/月'}}</text>

          <!-- 兴趣标签 -->
          <view class="interest-tags" wx:if="{{item.interests && item.interests.length > 0}}">
            <text class="interest-tag" wx:for="{{item.interests}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>

          <!-- 操作按钮 -->
          <view class="card-actions">
            <view class="action-btn chat-btn" bindtap="startChat" data-user="{{item}}" catchtap="">
              <text class="btn-text">聊聊</text>
            </view>
            <view class="action-btn like-btn" bindtap="likeUser" data-user="{{item}}" catchtap="">
              <text class="btn-icon">❤️</text>
            </view>
            <view class="action-btn view-btn" bindtap="viewProfile" data-user="{{item}}" catchtap="">
              <text class="btn-icon">👁️</text>
            </view>
          </view>

          <!-- 底部状态信息 -->
          <view class="card-footer">
            <view class="footer-item">
              <text class="footer-icon">⏰</text>
              <text class="footer-text">{{item.last_active || '1小时前活跃'}}</text>
            </view>
            <view class="footer-item">
              <text class="footer-icon">👀</text>
              <text class="footer-text">今日已有 {{item.today_views || '12'}} 人查看</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 传统列表模式（备用） -->
    <view class="user-cards-list traditional-list" wx:if="{{userList.length > 0 && !useStackMode}}">
      <view
        class="user-card-item"
        wx:for="{{userList}}"
        wx:key="id"
        bindtap="viewUserDetail"
        data-user="{{item}}"
      >
        <view class="card-content">
          <view class="card-left">
            <view class="user-basic-info">
              <view class="user-name-row">
                <text class="user-name">{{item.nickname}}</text>
                <view class="vip-badge" wx:if="{{item.is_vip}}">
                  <text class="vip-text">VIP</text>
                </view>
                <view class="online-status" wx:if="{{item.is_online}}">
                  <text class="online-dot">●</text>
                  <text class="online-text">在线</text>
                </view>
              </view>
              <text class="user-details">{{item.age}}岁 · {{item.occupation}} · {{item.location}}</text>
              <view class="user-tags" wx:if="{{item.interests.length > 0}}">
                <text class="tag" wx:for="{{item.interests}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
              </view>
              <view class="match-score" wx:if="{{item.match_score && !item.isDemo}}">
                <text class="match-text">匹配度 {{item.match_score}}%</text>
              </view>
            </view>
          </view>
          <view class="card-right">
            <image src="{{item.avatar}}" class="user-avatar" mode="aspectFill" />
            <view class="card-actions">
              <view class="action-btn dislike-btn" bindtap="dislikeUser" data-user="{{item}}" catchtap="">
                <text class="btn-icon">✕</text>
              </view>
              <view class="action-btn like-btn" bindtap="likeUser" data-user="{{item}}" catchtap="">
                <text class="btn-icon">❤️</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{userList.length === 0 && !loading}}">
      <view class="empty-icon">💔</view>
      <text class="empty-title">暂无推荐用户</text>
      <text class="empty-desc">完善你的资料，获得更多推荐</text>
      <button class="btn btn-primary" bindtap="refreshRecommendations">
        刷新推荐
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在为你寻找心动的人...</text>
    </view>
  </view>

  <!-- 加载更多按钮 -->
  <view class="load-more-section" wx:if="{{userList.length > 0}}">
    <button class="load-more-btn" bindtap="loadMoreUsers">
      <text class="btn-text">加载更多推荐</text>
    </button>
  </view>
</view>



<!-- 匹配成功弹窗 -->
<view class="match-modal" wx:if="{{showMatchModal}}" bindtap="hideMatchModal">
  <view class="match-content" catchtap="">
    <view class="match-header">
      <text class="match-title">🎉 恭喜匹配成功！</text>
    </view>
    <view class="match-users">
      <image src="{{currentUser.avatar}}" class="match-avatar" />
      <view class="match-heart">❤️</view>
      <image src="{{matchedUser.avatar}}" class="match-avatar" />
    </view>
    <view class="match-info">
      <text class="match-desc">你和 {{matchedUser.nickname}} 互相喜欢</text>
      <text class="match-tip">现在可以开始聊天了</text>
    </view>
    <view class="match-actions">
      <button class="btn btn-secondary" bindtap="hideMatchModal">
        继续寻找
      </button>
      <button class="btn btn-primary" bindtap="goToChat">
        开始聊天
      </button>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
  <view class="filter-content" catchtap="">
    <view class="filter-header">
      <text class="filter-title">筛选条件</text>
      <text class="filter-close" bindtap="hideFilterModal">✕</text>
    </view>
    <scroll-view class="filter-body" scroll-y>
      <!-- 年龄范围 -->
      <view class="filter-section">
        <text class="filter-label">年龄范围</text>
        <view class="age-range">
          <slider 
            min="18" 
            max="60" 
            value="{{filters.minAge}}" 
            bindchange="onMinAgeChange"
            activeColor="#667eea"
          />
          <text class="age-text">{{filters.minAge}} - {{filters.maxAge}}岁</text>
          <slider 
            min="18" 
            max="60" 
            value="{{filters.maxAge}}" 
            bindchange="onMaxAgeChange"
            activeColor="#667eea"
          />
        </view>
      </view>
      
      <!-- 距离范围 -->
      <view class="filter-section">
        <text class="filter-label">距离范围</text>
        <view class="distance-options">
          <text 
            class="distance-option {{filters.distance === item ? 'active' : ''}}"
            wx:for="{{distanceOptions}}"
            wx:key="*this"
            bindtap="selectDistance"
            data-distance="{{item}}"
          >
            {{item}}
          </text>
        </view>
      </view>
      
      <!-- 学历筛选 -->
      <view class="filter-section">
        <text class="filter-label">学历要求</text>
        <picker bindchange="selectEducation"
                value="{{educationIndex}}"
                range="{{educationOptions}}">
          <view class="picker-display">
            <text class="picker-text">{{educationOptions[educationIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 收入筛选 -->
      <view class="filter-section">
        <text class="filter-label">收入范围</text>
        <picker bindchange="selectIncome"
                value="{{incomeIndex}}"
                range="{{incomeOptions}}">
          <view class="picker-display">
            <text class="picker-text">{{incomeOptions[incomeIndex]}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 其他筛选条件 -->
      <view class="filter-section">
        <text class="filter-label">其他条件</text>
        <view class="filter-options">
          <view class="filter-option">
            <text class="option-text">只看VIP用户</text>
            <switch 
              checked="{{filters.vipOnly}}" 
              bindchange="onVipOnlyChange"
              color="#667eea"
            />
          </view>
          <view class="filter-option">
            <text class="option-text">只看在线用户</text>
            <switch 
              checked="{{filters.onlineOnly}}" 
              bindchange="onOnlineOnlyChange"
              color="#667eea"
            />
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="filter-footer">
      <button class="btn btn-secondary" bindtap="resetFilters">
        重置
      </button>
      <button class="btn btn-primary" bindtap="applyFilters">
        应用筛选
      </button>
    </view>
  </view>

  <!-- 推荐用户标签页 -->
  <view class="recommendation-tabs">
    <view class="tabs-header">
      <view class="tab-item {{currentTab === 'latest' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="latest">
        <text class="tab-text">最新</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'latest'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'activity' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="activity">
        <text class="tab-text">活动</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'activity'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'nearby' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="nearby">
        <text class="tab-text">附近</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'nearby'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'match' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="match">
        <text class="tab-text">匹配</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'match'}}"></view>
      </view>
      <view class="tab-item {{currentTab === 'video' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="video">
        <text class="tab-text">视频</text>
        <view class="tab-indicator" wx:if="{{currentTab === 'video'}}"></view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-card">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在为您推荐合适的用户...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{showEmptyState}}">
    <view class="empty-card">
      <view class="empty-icon">🔍</view>
      <text class="empty-title">暂无推荐用户</text>
      <text class="empty-desc">试试调整筛选条件或稍后再来看看</text>
      <view class="empty-btn" bindtap="adjustFilters">调整筛选条件</view>
    </view>
  </view>

  <!-- 用户推荐卡片列表 -->
  <view class="user-cards-container" wx:if="{{!isLoading && !showEmptyState}}">
    <view class="user-card"
          wx:for="{{recommendedUsers}}"
          wx:key="id"
          bindtap="viewUserProfile"
          data-user="{{item}}">
      <view class="card-content">
        <view class="user-info">
          <view class="user-header">
            <view class="user-basic">
              <text class="user-name">{{item.nickname}}</text>
              <text class="user-age">{{item.age}}岁 / {{item.occupation}}</text>
              <view class="online-status" wx:if="{{item.is_online}}"></view>
            </view>
            <view class="match-score">
              <text class="match-icon">❤️</text>
              <text class="match-text">匹配度 {{item.match_score}}%</text>
            </view>
          </view>

          <view class="user-location">
            <text class="location-icon">📍</text>
            <text class="location-text">{{item.location}} · 距离您 {{item.distance}}</text>
          </view>

          <view class="user-details">
            <text class="detail-text">{{item.marital_status}} · {{item.education}} · 身高{{item.height}}cm</text>
          </view>

          <view class="user-income">
            <text class="income-text">月入{{item.income}}</text>
          </view>

          <!-- 兴趣标签 -->
          <view class="interest-tags">
            <text class="interest-tag"
                  wx:for="{{item.interests}}"
                  wx:key="*this"
                  wx:for-item="interest">
              {{interest}}
            </text>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <view class="primary-btn" bindtap="applyConnection" data-user="{{item}}">
              <text class="btn-icon">👥</text>
              <text class="btn-text">申请牵线</text>
            </view>
            <view class="action-btn like-btn" bindtap="likeUser" data-user="{{item}}">
              <text class="btn-icon">{{item.is_liked ? '❤️' : '🤍'}}</text>
            </view>
            <view class="action-btn view-btn" bindtap="viewUserDetail" data-user="{{item}}">
              <text class="btn-icon">👁️</text>
            </view>
          </view>
        </view>

        <view class="user-avatar-container">
          <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
          <view class="avatar-badge verified" wx:if="{{item.is_verified}}">已实名</view>
          <view class="avatar-badge vip" wx:if="{{item.is_vip}}">
            <text class="vip-icon">👑</text>
            <text class="vip-text">VIP</text>
          </view>
          <view class="photo-count">{{item.photo_index}}/{{item.photo_total}}</view>
        </view>
      </view>

      <!-- 最后活跃时间 -->
      <view class="last-active">
        <text class="active-icon">🕐</text>
        <text class="active-text">{{item.last_active}}活跃</text>
        <text class="view-count">👁️ 今日{{item.today_views}}次查看</text>
      </view>
    </view>
  </view>

  <!-- 加载更多按钮 -->
  <view class="load-more-section">
    <view class="load-more-btn" bindtap="loadMoreRecommendations">
      <text class="load-more-icon">🔄</text>
      <text class="load-more-text">加载更多推荐</text>
    </view>
  </view>

  <!-- 搜索建议组件 -->
  <search-suggestions
    visible="{{showSearchSuggestions}}"
    keyword="{{searchKeyword}}"
    bind:select="onSearchSelect"
    bind:hide="hideSearchSuggestions">
  </search-suggestions>

  <!-- 高级筛选组件 -->
  <advanced-filter
    visible="{{showAdvancedFilter}}"
    filters="{{advancedFilters}}"
    bind:apply="onAdvancedFilterApply"
    bind:hide="hideAdvancedFilter">
  </advanced-filter>
</view>
