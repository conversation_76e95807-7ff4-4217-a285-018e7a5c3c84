/* pages/home/<USER>/
@import "../../styles/ui-standards.wxss";

.home-container {
  min-height: 100vh;
  background: var(--gradient-primary);
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  position: relative;
}

.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60vh;
  background: var(--gradient-primary);
  z-index: -1;
}

.home-container::after {
  content: '';
  position: absolute;
  top: 60vh;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-secondary);
  z-index: -1;
}

/* 简化的顶部导航 */
.home-header {
  background: transparent;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  border-bottom: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.app-title {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.location-icon {
  font-size: 24rpx;
}

.location-text {
  font-size: 24rpx;
  color: var(--text-white);
  opacity: 0.9;
}

.location-arrow {
  font-size: 20rpx;
  color: var(--text-white);
  opacity: 0.7;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-btn .icon {
  font-size: 28rpx;
}

.badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  background: #ff4757;
  color: #ffffff;
  border-radius: 50%;
  min-width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: 600;
}

/* 搜索栏 */
.search-section {
  background: transparent;
  padding: 0 32rpx 20rpx;
}

/* 轮播横幅 */
.banner-section {
  margin: 0 32rpx 24rpx;
}

.banner-swiper {
  height: 200rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.banner-item {
  height: 100%;
}

.banner-content {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

/* 横幅类型样式 */
.banner-tips {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.banner-verify {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.banner-matchmaker {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.banner-text {
  flex: 1;
  color: #ffffff;
}

.banner-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.banner-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.banner-icon {
  margin-left: 24rpx;
}

.banner-icon .icon-text {
  font-size: 64rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0,0,0,0.1));
}

/* 装饰元素 */
.banner-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
}

.decoration-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: -60rpx;
}

.decoration-2 {
  width: 80rpx;
  height: 80rpx;
  top: 40rpx;
  right: 120rpx;
}

.decoration-3 {
  width: 60rpx;
  height: 60rpx;
  bottom: -30rpx;
  right: 40rpx;
}

/* 3D卡片堆叠样式 */
.card-stack-container {
  padding: 0 32rpx;
  margin: 32rpx 0;
}

.card-stack {
  position: relative;
  height: 600rpx;
  perspective: 1000rpx;
  perspective-origin: center center;
}

.user-card-3d {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 20rpx 40rpx rgba(0,0,0,0.15);
  background: #ffffff;
  transform-style: preserve-3d;
}

/* 卡片层叠效果 */
.user-card-3d.card-0 {
  z-index: 3;
  transform: translateZ(0rpx) scale(1);
  opacity: 1;
}

.user-card-3d.card-1 {
  z-index: 2;
  transform: scale(0.95) translateY(20rpx) translateZ(-50rpx);
  opacity: 0.8;
}

.user-card-3d.card-2 {
  z-index: 1;
  transform: scale(0.9) translateY(40rpx) translateZ(-100rpx);
  opacity: 0.6;
}

/* 卡片内容 */
.card-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-avatar-section {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.card-avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 6rpx solid #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
}

.online-indicator-3d {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 24rpx;
  height: 24rpx;
  background: #4CAF50;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  animation: pulse-3d 2s ease-in-out infinite;
}

@keyframes pulse-3d {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.vip-badge-3d {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
  animation: glow-3d 2s ease-in-out infinite alternate;
}

@keyframes glow-3d {
  from {
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
  }
  to {
    box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.6);
  }
}

.vip-icon {
  font-size: 24rpx;
  color: #ffffff;
}

.card-info-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  padding: 32rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-name-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.card-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #333333;
}

.card-age {
  font-size: 28rpx;
  color: #666666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.card-occupation {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.card-location-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.location-icon {
  font-size: 24rpx;
}

.card-location {
  font-size: 24rpx;
  color: #999999;
  flex: 1;
}

.card-distance {
  font-size: 24rpx;
  color: #999999;
}

.card-match-score {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  align-self: flex-start;
}

.match-icon {
  font-size: 24rpx;
}

.match-text {
  font-size: 24rpx;
  font-weight: 600;
}

.card-gradient-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(transparent 0%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.3) 100%);
  pointer-events: none;
}

/* 3D卡片操作按钮 */
.card-actions-3d {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 40rpx;
  z-index: 10;
}

.action-btn-3d {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.dislike-btn-3d {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #ff4757;
}

.like-btn-3d {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
}

.action-btn-3d:active {
  transform: scale(0.9);
}

.action-icon-3d {
  font-size: 32rpx;
  color: #ffffff;
}

.dislike-btn-3d .action-icon-3d {
  color: #ff4757;
}

/* 空状态 */
.empty-stack {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.advanced-filter-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.advanced-filter-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.filter-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.filter-text {
  font-size: 20rpx;
  color: var(--text-white);
  opacity: 0.9;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  font-size: 20rpx;
  color: #666;
}

/* 筛选标签 */
.filter-tabs {
  background: #ffffff;
  padding: 0 32rpx 20rpx;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  gap: 16rpx;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
}

.tab-text {
  font-size: 28rpx;
  color: var(--text-white);
  opacity: 0.9;
}

.tab-item.active .tab-text {
  color: var(--primary-color);
  opacity: 1;
  font-weight: 600;
}

.tab-count {
  background: rgba(255, 255, 255, 0.3);
  color: var(--text-white);
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

.tab-item.active .tab-count {
  background: var(--primary-color);
  color: var(--text-white);
}

/* 用户推荐卡片 */
.user-recommendations {
  padding: 0 32rpx;
}

.recommendations-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.user-card:active {
  transform: scale(0.98);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.online-indicator {
  position: absolute;
  top: 4rpx;
  left: 96rpx;
  width: 24rpx;
  height: 24rpx;
  background: #10b981;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
}

.vip-badge {
  position: absolute;
  top: -8rpx;
  left: 96rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #8b5a00;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 600;
}

.card-content {
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-age {
  font-size: 28rpx;
  color: #666;
}

.user-location, .user-occupation {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 16rpx;
}

.tag {
  padding: 6rpx 12rpx;
  background: #f0f0f0;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: #666;
}

.card-actions {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.dislike-btn {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}

.like-btn {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.3);
}

.super-like-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-icon {
  font-size: 32rpx;
  color: #666;
}

.like-btn .action-icon,
.super-like-btn .action-icon {
  color: #ffffff;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx 0;
}

.load-more-btn {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  border-radius: 32rpx;
  display: inline-block;
}

.load-text {
  font-size: 28rpx;
  color: #666;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
  display: block;
}

.refresh-btn {
  padding: 16rpx 32rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 32rpx;
  display: inline-block;
}

.refresh-text {
  font-size: 28rpx;
  color: #ffffff;
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* Tab Navigation */
.tab-navigation {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 0 32rpx;
}

.tab-scroll {
  white-space: nowrap;
}

.tab-items {
  display: flex;
  align-items: center;
  gap: 0;
}

.tab-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 32rpx;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #667eea;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3rpx;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #667eea;
  font-weight: 600;
}

/* Quick Stats */
.quick-stats {
  padding: 32rpx;
  margin-top: 32rpx;
}

.stats-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%);
  backdrop-filter: blur(40rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 48rpx;
  padding: 48rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: float 3s ease-in-out infinite;
}

.stat-item:nth-child(2) { animation-delay: 0.5s; }
.stat-item:nth-child(3) { animation-delay: 1s; }
.stat-item:nth-child(4) { animation-delay: 1.5s; }

@keyframes float {
  0%, 100% { transform: translateY(0rpx); }
  50% { transform: translateY(-20rpx); }
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1.2;
}

.stat-item:nth-child(1) .stat-number { color: #667eea; }
.stat-item:nth-child(2) .stat-number { color: #ff6b9d; }
.stat-item:nth-child(3) .stat-number { color: #4ecdc4; }
.stat-item:nth-child(4) .stat-number { color: #45b7d1; }

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 专业服务区域 */
.special-services {
  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 32rpx;
  border: 2rpx solid #fed7aa;
  box-shadow: 0 4rpx 16rpx rgba(251, 146, 60, 0.1);
}

.services-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.services-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #ea580c;
}

.services-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.service-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.service-item.parent-service {
  border: 2rpx solid #fed7aa;
}

.service-item.matchmaker-service {
  border: 2rpx solid #e0e7ff;
}

.service-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

/* 专业服务图标容器 - 适配新的图标组件 */
.service-icon-container {
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
}

.service-icon {
  /* 图标样式现在由图标组件处理 */
}

/* 移除旧的图标背景样式 */

.service-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.service-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.service-tags {
  display: flex;
  justify-content: center;
  gap: 8rpx;
}

.service-tag {
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.service-tag.professional {
  background: #fed7aa;
  color: #ea580c;
}

.service-tag.reliable {
  background: #dcfce7;
  color: #16a34a;
}

.service-tag.premium {
  background: #e0e7ff;
  color: #7c3aed;
}

.service-tag.custom {
  background: #dbeafe;
  color: #2563eb;
}

/* Banner Slider */
.banner-slider {
  margin: 32rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.banner-swiper {
  height: 240rpx;
  border-radius: 32rpx;
}

.slide {
  position: relative;
  height: 100%;
  padding: 48rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.slide-1 {
  background: linear-gradient(135deg, #fce7f3 0%, #e879f9 50%, #fce7f3 100%);
}

.slide-2 {
  background: linear-gradient(135deg, #dbeafe 0%, #3b82f6 50%, #e0e7ff 100%);
}

.slide-3 {
  background: linear-gradient(135deg, #dcfce7 0%, #10b981 50%, #d1fae5 100%);
}

.slide-content {
  position: relative;
  z-index: 2;
}

.slide-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.slide-desc {
  display: block;
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.4;
}

.slide-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-icon {
  position: absolute;
  font-size: 48rpx;
  opacity: 0.6;
}

.decoration-1 {
  top: 32rpx;
  left: 32rpx;
  transform: rotate(-12deg);
}

.decoration-2 {
  top: 24rpx;
  right: 48rpx;
}

.decoration-3 {
  top: 48rpx;
  right: 96rpx;
  font-size: 32rpx;
  opacity: 0.5;
}

.decoration-4 {
  top: 32rpx;
  right: 32rpx;
}

.decoration-5 {
  bottom: 32rpx;
  left: 48rpx;
  font-size: 36rpx;
  opacity: 0.5;
}

.decoration-6 {
  top: 32rpx;
  right: 32rpx;
}

.decoration-7 {
  bottom: 32rpx;
  left: 48rpx;
  font-size: 36rpx;
  opacity: 0.5;
}

/* Function Grid */
.function-grid {
  padding: 0 32rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}

.grid-row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.function-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 28rpx 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.function-item:hover::before,
.function-item:active::before {
  opacity: 1;
}

.function-item:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.12);
}

.function-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0,0,0,0.15);
}

/* 功能图标容器 - 适配新的图标组件 */
.function-icon {
  margin: 0 auto 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.function-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.2;
  position: relative;
  z-index: 1;
}

/* Smart Filter */
.smart-filter {
  background: white;
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.advanced-filter-btn {
  display: flex;
  align-items: center;
  color: #667eea;
  font-size: 28rpx;
  font-weight: 500;
}

.advanced-text {
  margin-right: 8rpx;
}

.filter-icon {
  font-size: 24rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  gap: 16rpx;
}

.filter-tag {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  color: #6b7280;
  padding: 16rpx 24rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tag-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.online-dot {
  color: #10b981;
}

.tag-text {
  margin-right: 8rpx;
}

.tag-count {
  background: rgba(255,255,255,0.2);
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.filter-tag:not(.active) .tag-count {
  background: #e5e7eb;
  color: #6b7280;
}

/* Advanced Filter Panel */
.advanced-filter {
  margin: 0 32rpx 32rpx;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.advanced-filter.show {
  max-height: 400rpx;
}

.filter-panel {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.filter-row {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.filter-item {
  flex: 1;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.age-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.age-picker,
.distance-picker {
  flex: 1;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #374151;
}

.range-separator {
  color: #9ca3af;
  font-size: 28rpx;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.filter-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.reset-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.apply-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* User Cards Section */
.user-cards-section {
  padding: 32rpx;
  margin-top: 32rpx;
}

.guest-tip {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.tip-icon {
  font-size: 48rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
}

.tip-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.user-cards-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.user-card-item {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.card-content {
  display: flex;
  gap: 24rpx;
}

.card-left {
  flex: 1;
}

.user-basic-info {
  margin-bottom: 16rpx;
}

.user-name-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #667eea;
}

.vip-badge {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.online-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.online-dot {
  color: #4ecdc4;
  font-size: 16rpx;
}

.online-text {
  font-size: 24rpx;
  color: #4ecdc4;
}

.user-details {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.match-score {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  display: inline-block;
}

.card-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  object-fit: cover;
}

.card-actions {
  display: flex;
  gap: 16rpx;
}

.card-actions .action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.dislike-btn {
  background: #ff6b6b;
  color: white;
}

.like-btn {
  background: #ff6b9d;
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  backdrop-filter: blur(20rpx);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  display: block;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 80rpx 32rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* Load More Section */
.load-more-section {
  padding: 32rpx;
  text-align: center;
}

.load-more-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  backdrop-filter: blur(20rpx);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* Match Modal */
.match-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.match-content {
  background: white;
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  margin: 32rpx;
  text-align: center;
  max-width: 600rpx;
}

.match-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 48rpx;
}

.match-users {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 48rpx;
}

.match-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid #667eea;
}

.match-heart {
  font-size: 48rpx;
  margin: 0 32rpx;
}

.match-info {
  margin-bottom: 48rpx;
}

.match-desc {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.match-tip {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.match-actions {
  display: flex;
  gap: 24rpx;
}

.match-actions .btn {
  flex: 1;
}

/* Filter Modal */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.filter-close {
  font-size: 32rpx;
  color: #999;
  padding: 16rpx;
}

.filter-body {
  padding: 32rpx;
  max-height: 60vh;
}

.filter-section {
  margin-bottom: 48rpx;
}

.filter-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.age-range {
  padding: 0 16rpx;
}

.age-text {
  text-align: center;
  font-size: 28rpx;
  color: #667eea;
  margin: 16rpx 0;
  display: block;
}

.distance-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.distance-option {
  padding: 16rpx 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.distance-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
}

.option-text {
  font-size: 30rpx;
  color: #333;
}

.filter-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.filter-footer .btn {
  flex: 1;
}

/* 推荐用户标签页 */
.recommendation-tabs {
  margin: 32rpx;
  margin-bottom: 0;
}

.tabs-header {
  display: flex;
  justify-content: flex-start;
  gap: 48rpx;
  padding-bottom: 16rpx;
}

.tab-item {
  position: relative;
  padding: 16rpx 0;
  cursor: pointer;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #999;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #667eea;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

/* 用户推荐卡片列表 */
.user-cards-container {
  padding: 32rpx;
  padding-top: 16rpx;
  padding-bottom: 120rpx;
}

.user-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-card:hover::before,
.user-card:active::before {
  opacity: 1;
}

.user-card:active {
  transform: translateY(-4rpx) scale(0.99);
  box-shadow: 0 16rpx 48rpx rgba(0,0,0,0.12);
}

.user-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 20rpx 56rpx rgba(0,0,0,0.15);
}

.card-content {
  display: flex;
  gap: 32rpx;
}

.user-info {
  flex: 1;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
}

.user-age {
  font-size: 24rpx;
  color: #666;
}

.online-status {
  width: 16rpx;
  height: 16rpx;
  background: #10b981;
  border-radius: 50%;
}

.match-score {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.match-icon {
  font-size: 20rpx;
}

.match-text {
  font-size: 20rpx;
  color: #666;
}

.user-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.location-icon {
  font-size: 20rpx;
}

.location-text {
  font-size: 24rpx;
  color: #666;
}

.user-details {
  margin-bottom: 8rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #666;
}

.user-income {
  margin-bottom: 24rpx;
}

.income-text {
  font-size: 24rpx;
  color: #666;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.interest-tag {
  background: #f0f9ff;
  color: #0369a1;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  border: 1rpx solid #e0f2fe;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.primary-btn {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
}

.user-avatar-container {
  position: relative;
  width: 192rpx;
  height: 192rpx;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 32rpx;
}

.avatar-badge {
  position: absolute;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.avatar-badge.verified {
  top: 16rpx;
  left: 16rpx;
  background: #10b981;
  color: white;
}

.avatar-badge.vip {
  bottom: 16rpx;
  right: 16rpx;
  background: #fbbf24;
  color: white;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.vip-icon {
  font-size: 16rpx;
}

.vip-text {
  font-size: 20rpx;
}

.photo-count {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(0,0,0,0.5);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.last-active {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.active-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.active-text {
  font-size: 20rpx;
  color: #999;
}

.view-count {
  font-size: 20rpx;
  color: #999;
}

/* 加载更多按钮 */
.load-more-section {
  text-align: center;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.load-more-btn {
  background: white;
  border: 4rpx solid #667eea;
  color: #667eea;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  display: inline-flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #f8f9ff;
  transform: scale(0.98);
}

.load-more-icon {
  font-size: 24rpx;
}

.load-more-text {
  font-size: 28rpx;
}

/* 选择器样式 */
.picker-display {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  padding: 32rpx;
  padding-top: 16rpx;
}

.loading-card {
  background: white;
  border-radius: 32rpx;
  padding: 96rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 8rpx solid #f3f4f6;
  border-top: 8rpx solid #667eea;
  border-radius: 50%;
  margin: 0 auto 48rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  padding: 32rpx;
  padding-top: 16rpx;
}

.empty-card {
  background: white;
  border-radius: 32rpx;
  padding: 96rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.empty-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 48rpx;
  border-radius: 48rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: inline-block;
}

/* ==================== 模式切换按钮 ==================== */
.filter-header-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.view-mode-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 32rpx;
  padding: 4rpx;
  margin-right: 16rpx;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 28rpx;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
}

.mode-icon {
  font-size: 24rpx;
}

.mode-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #333;
}

/* ==================== 3D卡片堆叠系统 ==================== */
.card-stack-container {
  padding: 32rpx;
  margin-top: 32rpx;
}

.card-stack {
  position: relative;
  height: 960rpx;
  perspective: 2000rpx; /* 3D透视 */
  transform-style: preserve-3d;
}

.user-card.stack-card {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(40rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 48rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 40rpx 80rpx rgba(0,0,0,0.15);
  transform-style: preserve-3d;
}

.card-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.05;
  z-index: 0;
}

/* 卡片头像区域 */
.card-avatar-section {
  position: relative;
  height: 480rpx;
  overflow: hidden;
  border-radius: 48rpx 48rpx 0 0;
}

.card-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  right: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 215, 0, 0.3);
}

.vip-icon {
  font-size: 20rpx;
}

.vip-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
}

.online-indicator {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 12rpx;
  backdrop-filter: blur(20rpx);
}

.online-dot {
  width: 16rpx;
  height: 16rpx;
  background: #00d4aa;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 卡片信息区域 */
.card-info-section {
  padding: 32rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.user-main-info {
  display: flex;
  align-items: baseline;
  gap: 16rpx;
}

.user-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.user-age {
  font-size: 32rpx;
  color: #666;
}

.user-occupation {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

.user-location {
  font-size: 24rpx;
  color: #999;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 8rpx;
}

.interest-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.match-info {
  margin-top: 16rpx;
}

.match-score-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.match-progress {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.match-text {
  font-size: 20rpx;
  color: #667eea;
  font-weight: 500;
}

/* 卡片操作区域 */
.card-actions-section {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24rpx;
}

.card-actions-section .action-btn {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(20rpx);
}

.dislike-btn {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.9) 0%, rgba(255, 107, 107, 0.7) 100%);
  border: 1rpx solid rgba(255, 107, 107, 0.3);
}

.super-like-btn {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.9) 0%, rgba(255, 193, 7, 0.7) 100%);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
}

.like-btn {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.7) 100%);
  border: 1rpx solid rgba(102, 126, 234, 0.3);
}

.card-actions-section .btn-icon {
  font-size: 32rpx;
  color: white;
}

.btn-text {
  font-size: 20rpx;
  color: white;
  font-weight: 500;
}

/* 滑动提示 */
.swipe-hint {
  position: absolute;
  bottom: 160rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
  animation: fadeInOut 3s ease-in-out infinite;
}

.hint-text {
  font-size: 20rpx;
  margin-bottom: 8rpx;
  display: block;
}

.hint-arrows {
  display: flex;
  justify-content: space-between;
  gap: 32rpx;
}

.arrow-left, .arrow-right {
  font-size: 24rpx;
  font-weight: bold;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* 卡片计数器 */
.card-counter {
  text-align: center;
  margin-top: 32rpx;
}

.counter-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.3);
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(20rpx);
}

/* 原型图风格用户列表 */
.prototype-user-list {
  padding: 0 32rpx;
  margin-top: 32rpx;
}

.prototype-user-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  display: flex;
  gap: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.prototype-user-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 左侧头像区域 */
.card-left {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  object-fit: cover;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.user-card:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
}

.vip-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 16rpx;
  padding: 6rpx 12rpx;
  font-size: 18rpx;
  color: white;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  animation: vipGlow 2s ease-in-out infinite alternate;
}

@keyframes vipGlow {
  from {
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
  }
  to {
    box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.6);
  }
}

.online-dot {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #4CAF50;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  animation: onlinePulse 2s ease-in-out infinite;
}

@keyframes onlinePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 右侧信息区域 */
.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.user-info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-name-age {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-age {
  font-size: 24rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.match-score {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(255, 107, 157, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 16rpx;
}

.match-icon {
  font-size: 20rpx;
}

.match-text {
  font-size: 20rpx;
  color: #ff6b9d;
}

.match-percentage {
  font-size: 20rpx;
  font-weight: 600;
  color: #ff6b9d;
}

.user-occupation {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 20rpx;
}

.location-text {
  font-size: 22rpx;
  color: #666;
}

.distance-text {
  font-size: 22rpx;
  color: #999;
}

.basic-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-item {
  font-size: 22rpx;
  color: #666;
}

.info-separator {
  font-size: 22rpx;
  color: #ccc;
}

.income-info {
  font-size: 22rpx;
  color: #666;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 8rpx;
}

.interest-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.2);
}

/* 操作按钮 */
.card-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.chat-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

.chat-btn:active {
  transform: scale(0.95);
}

.like-btn {
  background: rgba(255, 107, 157, 0.1);
  color: #ff6b9d;
  border: 1rpx solid rgba(255, 107, 157, 0.3);
  flex: 0 0 80rpx;
}

.view-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  flex: 0 0 80rpx;
}

.btn-icon {
  font-size: 28rpx;
}

/* 底部状态信息 */
.card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.footer-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.footer-icon {
  font-size: 18rpx;
  opacity: 0.6;
}

.footer-text {
  font-size: 20rpx;
  color: #999;
}

/* 传统列表模式样式保持不变 */
.traditional-list {
  /* 保持原有的列表样式 */
}

/* ==================== Premium动画系统 ==================== */

/* 1. 浮动动画 - 用于装饰元素 */
@keyframes floating {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-15rpx) rotate(2deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10rpx) rotate(0deg);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-20rpx) rotate(-2deg);
    opacity: 1;
  }
}

.floating-element {
  animation: floating 3s ease-in-out infinite;
}

/* 2. 脉冲环动画 - 用于重要按钮 */
@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    box-shadow: 0 0 0 20rpx rgba(102, 126, 234, 0.3);
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
    box-shadow: 0 0 0 40rpx rgba(102, 126, 234, 0);
  }
}

.pulse-ring-element {
  animation: pulse-ring 2s infinite;
}

/* 3. 闪光动画 - 用于文字和按钮 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0.8;
  }
}

.shimmer-text {
  background: linear-gradient(90deg, #667eea 0%, #ffffff 50%, #764ba2 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 3s ease-in-out infinite;
}

.shimmer-button {
  background: linear-gradient(90deg, #667eea 0%, #ffffff 50%, #764ba2 100%);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

/* 4. Premium徽章发光动画 */
@keyframes premium-glow {
  0% {
    box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8), 0 0 50rpx rgba(255, 215, 0, 0.6);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.7);
    transform: scale(1);
  }
}

.premium-glow {
  animation: premium-glow 2s ease-in-out infinite alternate;
}

/* 5. 弹跳动画 - 用于重要元素 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

.bounce-element {
  animation: bounce 2s infinite;
}

/* 6. 旋转闪烁动画 - 用于加载和特效 */
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  25% {
    opacity: 0.5;
    transform: scale(0.5) rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
  75% {
    opacity: 0.5;
    transform: scale(0.5) rotate(270deg);
  }
}

.sparkle-element {
  animation: sparkle 2s ease-in-out infinite;
}

/* 7. 渐变背景动画 */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animated {
  background: linear-gradient(-45deg, #667eea, #764ba2, #ff6b9d, #4ecdc4);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
}

/* 8. 心跳动画 - 用于喜欢按钮 */
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

.heartbeat-element {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* 9. 滑入动画 - 用于页面元素 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

/* 10. 淡入动画 - 用于内容展示 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

/* Premium动画组合类 */
.premium-card {
  animation: slideInUp 0.6s ease-out, floating 3s ease-in-out 0.6s infinite;
}

.premium-button {
  transition: all 0.3s ease;
}

.premium-button:active {
  transform: scale(0.95);
  animation: pulse-ring 0.6s ease-out;
}

.premium-text {
  animation: shimmer 3s ease-in-out infinite;
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .pulse-ring-element,
  .shimmer-text,
  .shimmer-button,
  .premium-glow,
  .bounce-element,
  .sparkle-element,
  .gradient-animated,
  .heartbeat-element {
    animation: none;
  }
}
