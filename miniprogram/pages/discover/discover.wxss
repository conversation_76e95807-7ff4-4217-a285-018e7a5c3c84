/* pages/discover/discover.wxss */
.discover-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 顶部导航 - Premium毛玻璃效果 */
.discover-header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(40rpx);
  border-bottom: 1rpx solid rgba(255,255,255,0.2);
  color: #ffffff;
  padding: 48rpx 32rpx 32rpx;
  padding-top: calc(48rpx + env(safe-area-inset-top));
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.user-count-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.count-number {
  font-weight: 600;
}

.count-label {
  opacity: 0.9;
}

.action-btn {
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 位置选择器 */
.location-selector {
  background: white;
  margin: 0 32rpx;
  margin-top: -16rpx;
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.location-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-icon {
  margin-right: 16rpx;
}

.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.location-arrow {
  font-size: 20rpx;
  color: #999;
}

/* 搜索栏 - Premium毛玻璃效果 */
.search-section {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(40rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  margin: 0 32rpx;
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  margin-bottom: 32rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(248, 249, 250, 0.8);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255,255,255,0.5);
  border-radius: 48rpx;
  padding: 16rpx 24rpx;
}

.search-icon {
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 搜索建议 */
.search-suggestions {
  border-top: 1rpx solid #f0f0f0;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.suggestion-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
}

/* 热门标签 */
.hot-tags {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.tags-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tags-refresh {
  font-size: 24rpx;
  color: #667eea;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-tag {
  background: #f8f9fa;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  border: 1rpx solid #e9ecef;
}

/* 高级筛选面板 */
.advanced-filter {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.advanced-filter.show {
  opacity: 1;
  visibility: visible;
}

.filter-panel {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.advanced-filter.show .filter-panel {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-close {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
}

.filter-content {
  margin-bottom: 32rpx;
}

.filter-group {
  margin-bottom: 32rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.age-range-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.age-picker {
  flex: 1;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.range-separator {
  color: #999;
  font-size: 28rpx;
}

.distance-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.distance-option {
  background: #f8f9fa;
  color: #666;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.distance-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.online-switch {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.verify-options {
  display: flex;
  gap: 16rpx;
}

.verify-option {
  flex: 1;
  background: #f8f9fa;
  color: #666;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  text-align: center;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.verify-option.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

/* 兴趣标签 */
.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.interest-tag {
  background: #f8f9fa;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.interest-tag.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

/* 快速筛选标签栏 */
.quick-filters {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 0;
}

.filters-scroll {
  padding: 0 32rpx;
}

.filter-tabs {
  display: flex;
  gap: 24rpx;
  white-space: nowrap;
}

.filter-tab {
  background: #f8f9fa;
  color: #666;
  padding: 16rpx 24rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.filter-icon {
  font-size: 20rpx;
}

.filter-icon.online-icon {
  color: #10b981;
  font-size: 16rpx;
}

.filter-tab.active .filter-icon.online-icon {
  color: white;
}

.filter-text {
  font-size: 24rpx;
  font-weight: 500;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.filter-tab:not(.active) .filter-count {
  background: #e9ecef;
  color: #666;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.filter-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.apply-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 视图模式切换栏 */
.view-mode-bar {
  background: white;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mode-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.mode-tab {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.mode-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tab-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  font-weight: 500;
}

.result-count {
  color: #666;
}

.count-text {
  font-size: 24rpx;
}

/* 功能入口 */
.feature-section {
  padding: 32rpx;
  margin-top: 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.feature-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.95);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
}

.feature-icon.nearby {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-icon.online {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.feature-icon.new {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.feature-icon.visitor {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
}

.icon {
  font-size: 32rpx;
  color: #ffffff;
}

.feature-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 通用区块样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.section-more {
  font-size: 28rpx;
  color: #667eea;
}

/* 用户展示区域 */
.users-display-section {
  margin: 0 32rpx 32rpx;
}

/* 列表视图 */
.list-view {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.user-list-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
  align-items: flex-start;
}

.user-list-item:last-child {
  border-bottom: none;
}

.list-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.list-info {
  flex: 1;
  margin-right: 16rpx;
}

.list-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.list-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.list-age {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.list-badges {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.badge {
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.vip-badge {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #8b5a00;
}

.verify-badge {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  color: white;
}

.online-dot {
  width: 16rpx;
  height: 16rpx;
  background: #10b981;
  border-radius: 50%;
  border: 2rpx solid white;
}

.list-location {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.list-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.list-bio {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.list-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.like-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  color: white;
}

.chat-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 网格视图 */
.grid-view {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.user-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.grid-item {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.grid-avatar-container {
  position: relative;
  margin-bottom: 16rpx;
}

.grid-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}

.grid-badges {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.grid-info {
  margin-bottom: 16rpx;
}

.grid-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.grid-age {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.grid-distance {
  display: block;
  font-size: 20rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.grid-match {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  display: inline-block;
}

.match-text {
  font-weight: 500;
}

.grid-actions {
  display: flex;
  justify-content: center;
}

.grid-action-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 地图视图 */
.map-view {
  position: relative;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.user-map {
  width: 100%;
  height: 600rpx;
}

.map-controls {
  position: absolute;
  bottom: 32rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.map-btn {
  background: white;
  border-radius: 48rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 附近的人 */
.nearby-section {
  margin-bottom: 32rpx;
}

.nearby-list {
  padding: 0 32rpx;
  white-space: nowrap;
}

.nearby-item {
  display: inline-block;
  width: 160rpx;
  margin-right: 16rpx;
  text-align: center;
  position: relative;
  vertical-align: top;
}

.nearby-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 12rpx;
}

.nearby-info {
  text-align: center;
}

.nearby-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.nearby-distance {
  font-size: 24rpx;
  color: #666666;
}

.online-dot {
  position: absolute;
  top: 8rpx;
  right: 20rpx;
  width: 24rpx;
  height: 24rpx;
  background: #10b981;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
}

/* 在线用户 */
.online-section {
  margin-bottom: 32rpx;
}

.online-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 0 32rpx;
}

.online-item {
  text-align: center;
}

.online-avatar-container {
  position: relative;
  margin-bottom: 12rpx;
}

.online-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #10b981;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
}

.online-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.online-age {
  font-size: 22rpx;
  color: #666666;
}

/* 新用户 */
.new-users-section {
  margin-bottom: 32rpx;
}

.new-users-list {
  padding: 0 32rpx;
}

.new-user-item {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.new-user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.new-user-info {
  flex: 1;
}

.new-user-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.new-user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.new-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.badge-text {
  font-size: 20rpx;
  font-weight: 500;
}

.new-user-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.new-user-time {
  font-size: 24rpx;
  color: #999999;
}

.new-user-actions {
  margin-left: 16rpx;
}

.like-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 32rpx;
  color: #ffffff;
}

/* 推荐用户 */
.recommend-section {
  margin-bottom: 32rpx;
}

.recommend-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 0 32rpx;
}

.recommend-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.recommend-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}

.recommend-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.recommend-age {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.recommend-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8rpx;
}

.tag {
  background: #f0f0f0;
  color: #666666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.recommend-match {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #667eea;
  color: #ffffff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.match-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-text {
  font-size: 28rpx;
  color: #999999;
}

/* 地图视图样式 */
.map-view {
  height: calc(100vh - 300rpx);
  position: relative;
}

.user-map {
  width: 100%;
  height: 100%;
}

/* 地图控件 */
.map-controls {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  z-index: 10;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.control-icon {
  font-size: 32rpx;
}

.control-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 地图底部信息栏 */
.map-info-bar {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  z-index: 10;
}

.user-info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: slideUpMap 0.3s ease-out;
}

@keyframes slideUpMap {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.user-avatar-small {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
}

.user-info-text {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-distance {
  font-size: 24rpx;
  color: #666666;
}

.user-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn-small {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.like-btn {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
}

.chat-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-btn-small:active {
  transform: scale(0.9);
}

.action-text {
  font-size: 28rpx;
  color: #ffffff;
}
