// pages/discover/discover.js
Page({
  data: {
    // 发现数据
    discoverList: [],
    loading: false,
    hasMore: true,
    page: 1,

    // 位置和搜索
    currentLocation: '北京朝阳区',
    totalUserCount: 1234,
    searchKeyword: '',
    showSuggestions: false,
    searchSuggestions: ['摄影爱好者', '健身达人', '美食探索', '旅行伙伴', '读书分享'],
    hotTags: ['摄影', '健身', '美食', '旅行', '音乐', '电影', '读书', '宠物'],

    // 视图模式
    viewMode: 'list', // list, grid, map
    showAdvancedFilter: false,

    // 地图相关
    mapCenter: {
      longitude: 116.397428,
      latitude: 39.90923
    },
    mapScale: 16,
    mapMarkers: [],
    selectedUser: null,

    // 筛选条件
    filters: {
      minAge: 20,
      maxAge: 35,
      distance: '不限',
      onlineOnly: false,
      verified: false
    },

    // 筛选选项
    ageOptions: ['18', '20', '22', '25', '28', '30', '32', '35', '40', '45', '50'],
    minAgeIndex: 1, // 20岁
    maxAgeIndex: 7, // 35岁
    distanceOptions: ['不限', '5km内', '10km内', '20km内', '50km内', '同城'],
    interestOptions: ['读书', '旅行', '健身', '美食', '音乐', '摄影', '电影', '游戏'],
    selectedInterests: [],

    // 快速筛选
    currentFilter: 'all',
    filterCounts: {
      all: '1.2k',
      online: '234',
      new: '89',
      match: '45',
      nearby: '67',
      vip: '128',
      verified: '456'
    },

    // 用户数据
    displayUsers: [],
    totalCount: 0,
    nearbyUsers: [],
    onlineUsers: [],
    newUsers: [],
    recommendUsers: [],

    // 地图相关
    userLocation: null,
    mapScale: 16,
    mapMarkers: [],

    // 统计数据
    nearbyCount: 86,
    onlineCount: 234,
    newUserCount: 45,
    visitorCount: 128
  },

  onLoad() {
    this.loadDiscoverList()
    this.getUserLocation()
    this.loadMockData()
    this.initMapData()
  },

  onShow() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 加载发现列表
  async loadDiscoverList(refresh = false) {
    if (this.data.loading) return
    
    try {
      this.setData({ loading: true })
      
      const page = refresh ? 1 : this.data.page
      const result = await this.getDiscoverData(page)
      
      const discoverList = refresh ? result.list : [...this.data.discoverList, ...result.list]
      
      this.setData({
        discoverList,
        page: page + 1,
        hasMore: result.hasMore,
        loading: false
      })
      
      if (refresh) {
        wx.stopPullDownRefresh()
      }
    } catch (error) {
      console.error('加载发现列表失败:', error)
      this.setData({ loading: false })
    }
  },

  // 获取发现数据
  getDiscoverData(page) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `user_${page}_${i}`,
          avatar: `/images/avatar/user${i % 6 + 1}.jpg`,
          nickname: `用户${page}${i}`,
          age: 20 + Math.floor(Math.random() * 15),
          profession: ['设计师', '程序员', '教师', '医生', '律师'][Math.floor(Math.random() * 5)],
          location: '北京市朝阳区',
          distance: Math.floor(Math.random() * 10) + 1,
          isOnline: Math.random() > 0.5,
          isVip: Math.random() > 0.7,
          matchRate: Math.floor(Math.random() * 30) + 70
        }))
        
        resolve({
          list: mockData,
          hasMore: page < 5
        })
      }, 1000)
    })
  },

  // 刷新数据
  refreshData() {
    this.loadDiscoverList(true)
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadDiscoverList()
    }
  },

  // 获取用户位置
  getUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          userLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        })
      }
    })
  },

  // 查看用户详情
  viewUserDetail(e) {
    const { userId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/user/detail?userId=${userId}`
    })
  },

  // 喜欢用户
  async likeUser(e) {
    const { userId } = e.currentTarget.dataset
    
    try {
      // 模拟喜欢操作
      await new Promise(resolve => setTimeout(resolve, 500))
      
      wx.showToast({
        title: '已发送喜欢',
        icon: 'success'
      })
    } catch (error) {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 打招呼
  sayHello(e) {
    const { userId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/chat/detail?userId=${userId}&type=hello`
    })
  },

  // 筛选设置
  openFilter() {
    wx.navigateTo({
      url: '/pages/filter/settings'
    })
  },

  // 加载模拟数据
  loadMockData() {
    const mockUsers = [
      {
        id: 1,
        nickname: '小雨',
        age: 25,
        avatar: '/images/avatar/female1.svg',
        location: '北京朝阳区',
        distance: '2.3km',
        bio: '喜欢摄影和旅行，寻找有趣的灵魂',
        interests: ['摄影', '旅行', '美食'],
        is_online: true,
        is_vip: true,
        is_verified: true,
        match_score: 92,
        join_time: '3天前'
      },
      {
        id: 2,
        nickname: '阳光男孩',
        age: 28,
        avatar: '/images/avatar/male1.svg',
        location: '北京海淀区',
        distance: '5.8km',
        bio: '健身爱好者，积极向上的生活态度',
        interests: ['健身', '音乐', '电影'],
        is_online: false,
        is_vip: false,
        is_verified: true,
        match_score: 88,
        join_time: '1周前'
      },
      {
        id: 3,
        nickname: '文艺青年',
        age: 26,
        avatar: '/images/avatar/default.svg',
        location: '北京西城区',
        distance: '8.2km',
        bio: '热爱读书和写作，寻找精神共鸣',
        interests: ['读书', '写作', '咖啡'],
        is_online: true,
        is_vip: false,
        is_verified: false,
        match_score: 85,
        join_time: '5天前'
      }
    ]

    this.setData({
      displayUsers: mockUsers,
      totalCount: mockUsers.length,
      nearbyUsers: mockUsers.slice(0, 2),
      onlineUsers: mockUsers.filter(user => user.is_online),
      newUsers: mockUsers.slice(0, 1),
      recommendUsers: mockUsers
    })
  },

  // 位置选择
  selectLocation() {
    const locations = ['北京朝阳区', '北京海淀区', '北京西城区', '北京东城区', '北京丰台区']
    wx.showActionSheet({
      itemList: locations,
      success: (res) => {
        this.setData({
          currentLocation: locations[res.tapIndex]
        })
        this.refreshUserList()
      }
    })
  },

  // 搜索相关方法
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword,
      showSuggestions: keyword.length > 0
    })
  },

  onSearchConfirm() {
    const keyword = this.data.searchKeyword.trim()
    if (keyword) {
      console.log('搜索关键词:', keyword)
      this.performSearch(keyword)
    }
  },

  clearSearch() {
    this.setData({
      searchKeyword: '',
      showSuggestions: false
    })
  },

  selectSuggestion(e) {
    const suggestion = e.currentTarget.dataset.suggestion
    this.setData({
      searchKeyword: suggestion,
      showSuggestions: false
    })
    this.performSearch(suggestion)
  },

  selectHotTag(e) {
    const tag = e.currentTarget.dataset.tag
    this.setData({
      searchKeyword: tag
    })
    this.performSearch(tag)
  },

  refreshHotTags() {
    const allTags = ['摄影', '健身', '美食', '旅行', '音乐', '电影', '读书', '宠物', '游戏', '运动', '艺术', '科技']
    const shuffled = allTags.sort(() => 0.5 - Math.random())
    this.setData({
      hotTags: shuffled.slice(0, 8)
    })
  },

  performSearch(keyword) {
    console.log('执行搜索:', keyword)
    wx.showToast({
      title: `搜索"${keyword}"`,
      icon: 'none'
    })
    // 这里可以调用真实的搜索API
  },

  // 视图模式切换
  toggleViewMode() {
    const modes = ['list', 'grid', 'map']
    const currentIndex = modes.indexOf(this.data.viewMode)
    const nextIndex = (currentIndex + 1) % modes.length
    this.switchViewMode({ currentTarget: { dataset: { mode: modes[nextIndex] } } })
  },

  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      viewMode: mode
    })

    if (mode === 'map') {
      this.initMapMarkers()
    }

    wx.showToast({
      title: `切换到${this.getViewModeName(mode)}`,
      icon: 'none'
    })
  },

  getViewModeName(mode) {
    const names = {
      'list': '列表视图',
      'grid': '网格视图',
      'map': '地图视图'
    }
    return names[mode] || '列表视图'
  },

  // 地图相关方法
  initMapMarkers() {
    const markers = this.data.displayUsers.map((user, index) => ({
      id: user.id,
      latitude: 39.9042 + (Math.random() - 0.5) * 0.01, // 模拟位置
      longitude: 116.4074 + (Math.random() - 0.5) * 0.01,
      iconPath: user.avatar,
      width: 40,
      height: 40,
      callout: {
        content: `${user.nickname}, ${user.age}岁`,
        color: '#333',
        fontSize: 12,
        borderRadius: 8,
        bgColor: '#fff',
        padding: 8,
        display: 'BYCLICK'
      }
    }))

    this.setData({
      mapMarkers: markers
    })
  },

  onMarkerTap(e) {
    const markerId = e.detail.markerId
    const user = this.data.displayUsers.find(u => u.id === markerId)
    if (user) {
      this.viewUserDetail({ currentTarget: { dataset: { user } } })
    }
  },

  centerToUser() {
    if (this.data.userLocation) {
      // 地图重新定位到用户位置
      wx.showToast({
        title: '已定位到当前位置',
        icon: 'none'
      })
    }
  },

  zoomIn() {
    this.setData({
      mapScale: Math.min(this.data.mapScale + 2, 20)
    })
  },

  // 筛选相关方法
  toggleFilter() {
    this.setData({
      showAdvancedFilter: !this.data.showAdvancedFilter
    })
  },

  onMinAgeChange(e) {
    this.setData({
      minAgeIndex: e.detail.value
    })
  },

  onMaxAgeChange(e) {
    this.setData({
      maxAgeIndex: e.detail.value
    })
  },

  selectDistance(e) {
    const distance = e.currentTarget.dataset.distance
    this.setData({
      'filters.distance': distance
    })
  },

  onOnlineToggle(e) {
    this.setData({
      'filters.onlineOnly': e.detail.value
    })
  },

  selectVerify(e) {
    const verified = e.currentTarget.dataset.verified
    this.setData({
      'filters.verified': verified === 'true'
    })
  },

  toggleInterest(e) {
    const interest = e.currentTarget.dataset.interest
    const selectedInterests = [...this.data.selectedInterests]

    const index = selectedInterests.indexOf(interest)
    if (index > -1) {
      // 如果已选中，则取消选中
      selectedInterests.splice(index, 1)
    } else {
      // 如果未选中，则添加选中
      selectedInterests.push(interest)
    }

    this.setData({
      selectedInterests: selectedInterests
    })
  },

  resetFilters() {
    this.setData({
      'filters.minAge': 20,
      'filters.maxAge': 35,
      'filters.distance': '不限',
      'filters.onlineOnly': false,
      'filters.verified': false,
      minAgeIndex: 1,
      maxAgeIndex: 7
    })

    wx.showToast({
      title: '筛选条件已重置',
      icon: 'none'
    })
  },

  applyFilters() {
    const { ageOptions, minAgeIndex, maxAgeIndex, filters } = this.data

    const minAge = parseInt(ageOptions[minAgeIndex])
    const maxAge = parseInt(ageOptions[maxAgeIndex])

    console.log('应用筛选条件:', {
      minAge,
      maxAge,
      distance: filters.distance,
      onlineOnly: filters.onlineOnly,
      verified: filters.verified
    })

    this.setData({
      showAdvancedFilter: false
    })

    // 这里可以调用真实的筛选API
    this.refreshUserList()

    wx.showToast({
      title: '筛选条件已应用',
      icon: 'none'
    })
  },

  refreshUserList() {
    // 重新加载用户列表
    this.loadMockData()

    wx.showToast({
      title: '列表已刷新',
      icon: 'none'
    })
  },

  // 用户交互方法
  viewUserDetail(e) {
    const user = e.currentTarget.dataset.user
    console.log('查看用户详情:', user)

    wx.showModal({
      title: user.nickname,
      content: `${user.age}岁，${user.location}\n${user.bio}`,
      showCancel: false
    })
  },

  likeUser(e) {
    const user = e.currentTarget.dataset.user
    console.log('喜欢用户:', user)

    wx.showToast({
      title: `已喜欢${user.nickname}`,
      icon: 'none'
    })
  },

  chatUser(e) {
    const user = e.currentTarget.dataset.user
    console.log('聊天用户:', user)

    wx.showToast({
      title: `开始与${user.nickname}聊天`,
      icon: 'none'
    })
  },

  // 快速筛选方法
  applyQuickFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      currentFilter: filter
    })

    // 根据筛选条件过滤用户
    this.filterUsersByQuickFilter(filter)
  },

  filterUsersByQuickFilter(filter) {
    let filteredUsers = [...this.data.displayUsers]

    switch (filter) {
      case 'all':
        // 显示所有用户
        break
      case 'online':
        filteredUsers = filteredUsers.filter(user => user.is_online)
        break
      case 'new':
        // 模拟新用户筛选
        filteredUsers = filteredUsers.filter(user => user.id % 3 === 0)
        break
      case 'match':
        filteredUsers = filteredUsers.filter(user => user.match_score >= 90)
        break
      case 'nearby':
        filteredUsers = filteredUsers.filter(user => {
          const distance = parseFloat(user.distance)
          return distance <= 10
        })
        break
      case 'vip':
        filteredUsers = filteredUsers.filter(user => user.is_vip)
        break
      case 'verified':
        filteredUsers = filteredUsers.filter(user => user.is_verified)
        break
    }

    this.setData({
      displayUsers: filteredUsers
    })

    wx.showToast({
      title: `已筛选${this.getFilterName(filter)}`,
      icon: 'none'
    })
  },

  getFilterName(filter) {
    const names = {
      'all': '全部用户',
      'online': '在线用户',
      'new': '新用户',
      'match': '高匹配用户',
      'nearby': '同城用户',
      'vip': 'VIP用户',
      'verified': '已认证用户'
    }
    return names[filter] || '全部用户'
  },

  // 切换到地图模式
  goToMapMode() {
    this.setData({
      viewMode: 'map'
    })
    this.initMapData()
  },

  // 初始化地图数据
  initMapData() {
    // 获取用户位置
    this.getUserLocationForMap()
    // 生成地图标记
    this.generateMapMarkers()
  },

  // 获取用户位置用于地图
  getUserLocationForMap() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          mapCenter: {
            longitude: res.longitude,
            latitude: res.latitude
          }
        })
        this.generateMapMarkers()
      },
      fail: () => {
        // 使用默认位置（北京）
        console.log('获取位置失败，使用默认位置')
      }
    })
  },

  // 生成地图标记
  generateMapMarkers() {
    const markers = this.data.displayUsers.slice(0, 20).map((user, index) => {
      // 在用户位置周围随机生成坐标
      const offsetLng = (Math.random() - 0.5) * 0.02 // 约1km范围
      const offsetLat = (Math.random() - 0.5) * 0.02

      return {
        id: user.id,
        longitude: this.data.mapCenter.longitude + offsetLng,
        latitude: this.data.mapCenter.latitude + offsetLat,
        iconPath: user.is_vip ? '/images/markers/vip-marker.png' : '/images/markers/user-marker.png',
        width: 40,
        height: 40,
        callout: {
          content: `${user.nickname} ${user.age}岁`,
          color: '#333333',
          fontSize: 12,
          borderRadius: 8,
          bgColor: '#ffffff',
          padding: 8,
          display: 'BYCLICK'
        },
        userData: user
      }
    })

    this.setData({
      mapMarkers: markers
    })
  },

  // 地图标记点击事件
  onMarkerTap(e) {
    const markerId = e.detail.markerId
    const marker = this.data.mapMarkers.find(m => m.id === markerId)

    if (marker) {
      this.setData({
        selectedUser: marker.userData
      })
    }
  },

  // 地图区域变化事件
  onMapRegionChange(e) {
    if (e.type === 'end') {
      // 更新地图中心点
      this.setData({
        mapCenter: {
          longitude: e.detail.centerLocation.longitude,
          latitude: e.detail.centerLocation.latitude
        }
      })

      // 重新生成附近用户标记
      this.generateMapMarkers()
    }
  },

  // 定位到用户位置
  centerToUser() {
    this.getUserLocationForMap()
  },

  // 放大地图
  zoomIn() {
    const newScale = Math.min(this.data.mapScale + 2, 20)
    this.setData({
      mapScale: newScale
    })
  },

  // 缩小地图
  zoomOut() {
    const newScale = Math.max(this.data.mapScale - 2, 5)
    this.setData({
      mapScale: newScale
    })
  },

  // 从地图喜欢用户
  likeUserFromMap() {
    if (this.data.selectedUser) {
      wx.showToast({
        title: `已喜欢 ${this.data.selectedUser.nickname}`,
        icon: 'success'
      })

      // 清除选中用户
      this.setData({
        selectedUser: null
      })
    }
  },

  // 与用户聊天
  chatWithUser() {
    if (this.data.selectedUser) {
      wx.navigateTo({
        url: `/pages/chat-detail/chat-detail?userId=${this.data.selectedUser.id}`
      })
    }
  }
})
