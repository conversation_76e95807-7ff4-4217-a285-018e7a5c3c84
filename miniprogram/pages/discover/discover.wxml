<!--pages/discover/discover.wxml-->
<view class="discover-container">
  <!-- 简化的顶部导航 -->
  <view class="discover-header">
    <view class="header-content">
      <text class="header-title">发现</text>
      <view class="header-actions">
        <view class="action-btn" bindtap="goToMapMode">
          <text class="icon">🗺️</text>
        </view>
        <view class="action-btn" bindtap="toggleViewMode">
          <text class="icon">{{viewMode === 'grid' ? '📋' : '⊞'}}</text>
        </view>
        <view class="action-btn" bindtap="toggleFilter">
          <text class="icon">⚙️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 位置和搜索栏 -->
  <view class="search-section">
    <!-- 位置选择 -->
    <view class="location-bar" bindtap="selectLocation">
      <text class="location-icon">📍</text>
      <text class="location-text">{{currentLocation || '北京'}}</text>
      <text class="location-arrow">▼</text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-container">
        <text class="search-icon">🔍</text>
        <input class="search-input"
               placeholder="搜索用户昵称、兴趣爱好..."
               value="{{searchKeyword}}"
               bindinput="onSearchInput"
               bindconfirm="onSearchConfirm" />
        <view wx:if="{{searchKeyword}}" bindtap="clearSearch" class="clear-btn">
          <text class="clear-icon">✕</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 简化的筛选标签 -->
  <view class="filter-tabs">
    <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false">
      <view class="tabs-container">
        <view class="tab-item {{currentFilter === 'all' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="all">
          <text class="tab-text">全部</text>
        </view>
        <view class="tab-item {{currentFilter === 'nearby' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="nearby">
          <text class="tab-text">附近</text>
        </view>
        <view class="tab-item {{currentFilter === 'online' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="online">
          <text class="tab-text">在线</text>
        </view>
        <view class="tab-item {{currentFilter === 'new' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="new">
          <text class="tab-text">新人</text>
        </view>
        <view class="tab-item {{currentFilter === 'verified' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="verified">
          <text class="tab-text">已认证</text>
        </view>
        <view class="tab-item {{currentFilter === 'vip' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="vip">
          <text class="tab-text">VIP</text>
        </view>

        <view class="filter-tab {{currentFilter === 'verified' ? 'active' : ''}}"
              bindtap="applyQuickFilter"
              data-filter="verified">
          <text class="filter-icon">🛡️</text>
          <text class="filter-text">已认证</text>
          <text class="filter-count">{{filterCounts.verified}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 高级筛选面板 -->
  <view class="advanced-filter {{showAdvancedFilter ? 'show' : ''}}" wx:if="{{showAdvancedFilter}}">
    <view class="filter-panel">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <text class="filter-close" bindtap="toggleFilter">✕</text>
      </view>

      <view class="filter-content">
        <view class="filter-group">
          <text class="filter-label">年龄范围</text>
          <view class="age-range-selector">
            <picker class="age-picker" range="{{ageOptions}}" value="{{minAgeIndex}}" bindchange="onMinAgeChange">
              <text class="picker-text">{{ageOptions[minAgeIndex]}}岁</text>
            </picker>
            <text class="range-separator">-</text>
            <picker class="age-picker" range="{{ageOptions}}" value="{{maxAgeIndex}}" bindchange="onMaxAgeChange">
              <text class="picker-text">{{ageOptions[maxAgeIndex]}}岁</text>
            </picker>
          </view>
        </view>

        <view class="filter-group">
          <text class="filter-label">距离范围</text>
          <view class="distance-options">
            <view class="distance-option {{filters.distance === item ? 'active' : ''}}"
                  wx:for="{{distanceOptions}}"
                  wx:key="*this"
                  bindtap="selectDistance"
                  data-distance="{{item}}">
              {{item}}
            </view>
          </view>
        </view>

        <view class="filter-group">
          <text class="filter-label">在线状态</text>
          <view class="online-switch">
            <switch checked="{{filters.onlineOnly}}" bindchange="onOnlineToggle" color="#667eea" />
            <text class="switch-label">只看在线用户</text>
          </view>
        </view>

        <view class="filter-group">
          <text class="filter-label">认证状态</text>
          <view class="verify-options">
            <view class="verify-option {{filters.verified === true ? 'active' : ''}}"
                  bindtap="selectVerify"
                  data-verified="{{true}}">
              已认证
            </view>
            <view class="verify-option {{filters.verified === false ? 'active' : ''}}"
                  bindtap="selectVerify"
                  data-verified="{{false}}">
              全部
            </view>
          </view>
        </view>

        <view class="filter-group">
          <text class="filter-label">兴趣标签</text>
          <view class="interest-tags">
            <view class="interest-tag {{selectedInterests.indexOf(item) !== -1 ? 'active' : ''}}"
                  wx:for="{{interestOptions}}"
                  wx:key="*this"
                  bindtap="toggleInterest"
                  data-interest="{{item}}">
              {{item}}
            </view>
          </view>
        </view>
      </view>

      <view class="filter-actions">
        <view class="filter-btn reset-btn" bindtap="resetFilters">重置</view>
        <view class="filter-btn apply-btn" bindtap="applyFilters">应用筛选</view>
      </view>
    </view>
  </view>

  <!-- 视图模式切换栏 -->
  <view class="view-mode-bar">
    <view class="mode-tabs">
      <view class="mode-tab {{viewMode === 'list' ? 'active' : ''}}"
            bindtap="switchViewMode"
            data-mode="list">
        <text class="tab-icon">📋</text>
        <text class="tab-text">列表</text>
      </view>
      <view class="mode-tab {{viewMode === 'grid' ? 'active' : ''}}"
            bindtap="switchViewMode"
            data-mode="grid">
        <text class="tab-icon">⊞</text>
        <text class="tab-text">网格</text>
      </view>
      <view class="mode-tab {{viewMode === 'map' ? 'active' : ''}}"
            bindtap="switchViewMode"
            data-mode="map">
        <text class="tab-icon">🗺️</text>
        <text class="tab-text">地图</text>
      </view>
    </view>
    <view class="result-count">
      <text class="count-text">找到 {{totalCount}} 个用户</text>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="feature-section">
    <view class="feature-grid">
      <view class="feature-item" bindtap="goToNearby">
        <view class="feature-icon nearby">
          <text class="icon">📍</text>
        </view>
        <text class="feature-title">附近的人</text>
        <text class="feature-desc">{{nearbyCount}}人在线</text>
      </view>

      <view class="feature-item" bindtap="goToOnline">
        <view class="feature-icon online">
          <text class="icon">🟢</text>
        </view>
        <text class="feature-title">在线用户</text>
        <text class="feature-desc">{{onlineCount}}人在线</text>
      </view>

      <view class="feature-item" bindtap="goToNewUsers">
        <view class="feature-icon new">
          <text class="icon">✨</text>
        </view>
        <text class="feature-title">新用户</text>
        <text class="feature-desc">{{newUserCount}}位新朋友</text>
      </view>

      <view class="feature-item" bindtap="goToVisitors">
        <view class="feature-icon visitor">
          <text class="icon">👁️</text>
        </view>
        <text class="feature-title">访客记录</text>
        <text class="feature-desc">{{visitorCount}}次访问</text>
      </view>
    </view>
  </view>

  <!-- 用户展示区域 -->
  <view class="users-display-section">
    <!-- 地图视图 -->
    <view class="map-view" wx:if="{{viewMode === 'map'}}">
      <map
        id="userMap"
        class="user-map"
        longitude="{{mapCenter.longitude}}"
        latitude="{{mapCenter.latitude}}"
        scale="{{mapScale}}"
        markers="{{mapMarkers}}"
        bindmarkertap="onMarkerTap"
        bindregionchange="onMapRegionChange"
        show-location="true"
        enable-3D="true"
        enable-overlooking="true"
        enable-zoom="true"
        enable-scroll="true"
        enable-rotate="true"
      >
        <!-- 地图控件 -->
        <cover-view class="map-controls">
          <cover-view class="control-btn" bindtap="centerToUser">
            <cover-view class="control-icon">📍</cover-view>
          </cover-view>
          <cover-view class="control-btn" bindtap="zoomIn">
            <cover-view class="control-text">+</cover-view>
          </cover-view>
          <cover-view class="control-btn" bindtap="zoomOut">
            <cover-view class="control-text">-</cover-view>
          </cover-view>
        </cover-view>

        <!-- 地图底部信息栏 -->
        <cover-view class="map-info-bar" wx:if="{{selectedUser}}">
          <cover-view class="user-info-card">
            <cover-image class="user-avatar-small" src="{{selectedUser.avatar}}"></cover-image>
            <cover-view class="user-info-text">
              <cover-view class="user-name">{{selectedUser.nickname}}</cover-view>
              <cover-view class="user-distance">距离 {{selectedUser.distance}}</cover-view>
            </cover-view>
            <cover-view class="user-actions">
              <cover-view class="action-btn-small like-btn" bindtap="likeUserFromMap">
                <cover-view class="action-text">💖</cover-view>
              </cover-view>
              <cover-view class="action-btn-small chat-btn" bindtap="chatWithUser">
                <cover-view class="action-text">💬</cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
    </view>

    <!-- 列表视图 -->
    <view class="list-view" wx:if="{{viewMode === 'list'}}">
      <view class="user-list-item" wx:for="{{displayUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <image src="{{item.avatar}}" class="list-avatar" />
        <view class="list-info">
          <view class="list-header">
            <text class="list-name">{{item.nickname}}</text>
            <text class="list-age">{{item.age}}岁</text>
            <view class="list-badges">
              <text class="badge vip-badge" wx:if="{{item.is_vip}}">VIP</text>
              <text class="badge verify-badge" wx:if="{{item.is_verified}}">✓</text>
              <view class="online-dot" wx:if="{{item.is_online}}"></view>
            </view>
          </view>
          <text class="list-location">📍 {{item.location}} · {{item.distance}}</text>
          <view class="list-tags">
            <text class="tag" wx:for="{{item.interests}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
          <text class="list-bio">{{item.bio}}</text>
        </view>
        <view class="list-actions">
          <view class="action-btn like-btn" bindtap="likeUser" data-user="{{item}}" catchtap="">
            <text class="btn-icon">❤️</text>
          </view>
          <view class="action-btn chat-btn" bindtap="chatUser" data-user="{{item}}" catchtap="">
            <text class="btn-icon">💬</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 网格视图 -->
    <view class="grid-view" wx:if="{{viewMode === 'grid'}}">
      <view class="user-grid">
        <view class="grid-item" wx:for="{{displayUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
          <view class="grid-avatar-container">
            <image src="{{item.avatar}}" class="grid-avatar" />
            <view class="grid-badges">
              <text class="badge vip-badge" wx:if="{{item.is_vip}}">VIP</text>
              <view class="online-dot" wx:if="{{item.is_online}}"></view>
            </view>
          </view>
          <view class="grid-info">
            <text class="grid-name">{{item.nickname}}</text>
            <text class="grid-age">{{item.age}}岁</text>
            <text class="grid-distance">{{item.distance}}</text>
            <view class="grid-match" wx:if="{{item.match_score}}">
              <text class="match-text">{{item.match_score}}%匹配</text>
            </view>
          </view>
          <view class="grid-actions">
            <view class="grid-action-btn like-btn" bindtap="likeUser" data-user="{{item}}" catchtap="">
              <text class="btn-icon">❤️</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 地图视图 -->
    <view class="map-view" wx:if="{{viewMode === 'map'}}">
      <map class="user-map"
           latitude="{{userLocation.latitude}}"
           longitude="{{userLocation.longitude}}"
           scale="{{mapScale}}"
           markers="{{mapMarkers}}"
           bindmarkertap="onMarkerTap"
           show-location="{{true}}">
      </map>
      <view class="map-controls">
        <view class="map-btn" bindtap="centerToUser">
          <text class="btn-icon">📍</text>
          <text class="btn-text">定位</text>
        </view>
        <view class="map-btn" bindtap="zoomIn">
          <text class="btn-icon">🔍</text>
          <text class="btn-text">放大</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 附近的人 -->
  <view class="nearby-section" wx:if="{{viewMode !== 'list' && viewMode !== 'grid' && viewMode !== 'map'}}">
    <view class="section-header">
      <text class="section-title">附近的人</text>
      <text class="section-more" bindtap="goToNearby">查看全部</text>
    </view>
    
    <scroll-view class="nearby-list" scroll-x>
      <view class="nearby-item" wx:for="{{nearbyUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <image src="{{item.avatar}}" class="nearby-avatar" />
        <view class="nearby-info">
          <text class="nearby-name">{{item.nickname}}</text>
          <text class="nearby-distance">{{item.distance}}</text>
        </view>
        <view class="online-dot" wx:if="{{item.is_online}}"></view>
      </view>
    </scroll-view>
  </view>

  <!-- 在线用户 -->
  <view class="online-section">
    <view class="section-header">
      <text class="section-title">在线用户</text>
      <text class="section-more" bindtap="goToOnline">查看全部</text>
    </view>
    
    <view class="online-grid">
      <view class="online-item" wx:for="{{onlineUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <view class="online-avatar-container">
          <image src="{{item.avatar}}" class="online-avatar" />
          <view class="online-indicator"></view>
        </view>
        <text class="online-name">{{item.nickname}}</text>
        <text class="online-age">{{item.age}}岁</text>
      </view>
    </view>
  </view>

  <!-- 新用户 -->
  <view class="new-users-section">
    <view class="section-header">
      <text class="section-title">新用户</text>
      <text class="section-more" bindtap="goToNewUsers">查看全部</text>
    </view>
    
    <view class="new-users-list">
      <view class="new-user-item" wx:for="{{newUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <image src="{{item.avatar}}" class="new-user-avatar" />
        <view class="new-user-info">
          <view class="new-user-header">
            <text class="new-user-name">{{item.nickname}}</text>
            <view class="new-badge">
              <text class="badge-text">新人</text>
            </view>
          </view>
          <text class="new-user-desc">{{item.age}}岁 · {{item.location}}</text>
          <text class="new-user-time">{{item.join_time}}加入</text>
        </view>
        <view class="new-user-actions">
          <view class="action-btn like-btn" bindtap="likeUser" data-user="{{item}}" catchtap="">
            <text class="btn-icon">❤️</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐用户 -->
  <view class="recommend-section">
    <view class="section-header">
      <text class="section-title">为你推荐</text>
      <text class="section-more" bindtap="refreshRecommend">刷新</text>
    </view>
    
    <view class="recommend-grid">
      <view class="recommend-item" wx:for="{{recommendUsers}}" wx:key="id" bindtap="viewUserDetail" data-user="{{item}}">
        <image src="{{item.avatar}}" class="recommend-avatar" />
        <view class="recommend-info">
          <text class="recommend-name">{{item.nickname}}</text>
          <text class="recommend-age">{{item.age}}岁</text>
          <view class="recommend-tags">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
        <view class="recommend-match" wx:if="{{item.match_score}}">
          <text class="match-text">{{item.match_score}}%匹配</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view class="loading-spinner" wx:if="{{loading}}"></view>
    <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
  </view>
</view>
