// pages/community/feature.js
Page({
  data: {
    features: [
      { id: 1, title: '话题讨论', desc: '参与热门话题讨论', icon: '💬', path: '/pages/community/topics' },
      { id: 2, title: '活动报名', desc: '参加线下交友活动', icon: '🎉', path: '/pages/community/events' },
      { id: 3, title: '兴趣小组', desc: '加入兴趣爱好小组', icon: '👥', path: '/pages/community/groups' },
      { id: 4, title: '经验分享', desc: '分享交友经验心得', icon: '📝', path: '/pages/community/share' }
    ]
  },

  onLoad() {
    // 页面初始化
  },

  navigateToFeature(e) {
    const { path } = e.currentTarget.dataset
    wx.navigateTo({ url: path })
  }
})
