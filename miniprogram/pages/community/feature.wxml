<!--pages/community/feature.wxml-->
<view class="community-container">
  <!-- 顶部导航 -->
  <view class="community-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">社区</text>
      <view class="header-actions">
        <text class="action-btn" bindtap="goToSearch">🔍</text>
        <text class="action-btn" bindtap="goToNotifications">🔔</text>
      </view>
    </view>

    <!-- 社区统计 -->
    <view class="community-stats">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{stats.total_topics}}</text>
          <text class="stat-label">话题数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{stats.active_users}}</text>
          <text class="stat-label">活跃用户</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{stats.my_posts}}</text>
          <text class="stat-label">我的发帖</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="feature-section">
    <view class="feature-grid">
      <view class="feature-item" bindtap="goToTopics">
        <view class="feature-icon topics">
          <text class="icon">💬</text>
        </view>
        <text class="feature-title">话题讨论</text>
        <text class="feature-desc">{{topicsCount}}个热门话题</text>
      </view>

      <view class="feature-item" bindtap="goToGroups">
        <view class="feature-icon groups">
          <text class="icon">👥</text>
        </view>
        <text class="feature-title">兴趣小组</text>
        <text class="feature-desc">{{groupsCount}}个小组</text>
      </view>

      <view class="feature-item" bindtap="goToEvents">
        <view class="feature-icon events">
          <text class="icon">🎉</text>
        </view>
        <text class="feature-title">社区活动</text>
        <text class="feature-desc">{{eventsCount}}个活动</text>
      </view>

      <view class="feature-item" bindtap="goToRanking">
        <view class="feature-icon ranking">
          <text class="icon">🏆</text>
        </view>
        <text class="feature-title">排行榜</text>
        <text class="feature-desc">活跃度排名</text>
      </view>
    </view>
  </view>

  <!-- 热门话题 -->
  <view class="hot-topics-section">
    <view class="section-header">
      <text class="section-title">热门话题</text>
      <text class="section-more" bindtap="goToTopics">查看全部</text>
    </view>
    
    <scroll-view class="hot-topics-list" scroll-x>
      <view class="topic-item" wx:for="{{hotTopics}}" wx:key="id" bindtap="viewTopic" data-topic="{{item}}">
        <view class="topic-content">
          <text class="topic-title">#{{item.title}}</text>
          <text class="topic-desc">{{item.description}}</text>
          <view class="topic-stats">
            <text class="stat-item">💬 {{item.posts_count}}</text>
            <text class="stat-item">👁️ {{item.views_count}}</text>
          </view>
        </view>
        <view class="topic-badge" wx:if="{{item.is_hot}}">
          <text class="badge-text">热</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐小组 -->
  <view class="recommended-groups-section">
    <view class="section-header">
      <text class="section-title">推荐小组</text>
      <text class="section-more" bindtap="goToGroups">查看全部</text>
    </view>
    
    <view class="groups-grid">
      <view class="group-item" wx:for="{{recommendedGroups}}" wx:key="id" bindtap="viewGroup" data-group="{{item}}">
        <image src="{{item.avatar}}" class="group-avatar" />
        <view class="group-info">
          <text class="group-name">{{item.name}}</text>
          <text class="group-desc">{{item.description}}</text>
          <view class="group-stats">
            <text class="stat-item">👥 {{item.members_count}}人</text>
            <text class="stat-item">📝 {{item.posts_count}}帖</text>
          </view>
        </view>
        <button 
          class="join-group-btn {{item.is_joined ? 'joined' : ''}}"
          bindtap="toggleJoinGroup"
          data-group="{{item}}"
          catchtap=""
        >
          {{item.is_joined ? '已加入' : '加入'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 最新动态 -->
  <scroll-view class="latest-posts-section" scroll-y bindscrolltolower="loadMore">
    <view class="section-header">
      <text class="section-title">最新动态</text>
      <view class="filter-tabs">
        <text 
          class="filter-tab {{activeFilter === 'all' ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="all"
        >
          全部
        </text>
        <text 
          class="filter-tab {{activeFilter === 'following' ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="following"
        >
          关注
        </text>
        <text 
          class="filter-tab {{activeFilter === 'groups' ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="groups"
        >
          小组
        </text>
      </view>
    </view>
    
    <view class="posts-list">
      <view class="post-item" wx:for="{{latestPosts}}" wx:key="id">
        <view class="post-content" bindtap="viewPost" data-post="{{item}}">
          <view class="post-header">
            <image src="{{item.author.avatar}}" class="author-avatar" />
            <view class="author-info">
              <text class="author-name">{{item.author.nickname}}</text>
              <view class="post-meta">
                <text class="post-time">{{item.created_time}}</text>
                <text class="post-topic" wx:if="{{item.topic}}">#{{item.topic.title}}</text>
                <text class="post-group" wx:if="{{item.group}}">{{item.group.name}}</text>
              </view>
            </view>
            <view class="post-badges">
              <view class="vip-badge" wx:if="{{item.author.is_vip}}">
                <text class="badge-text">VIP</text>
              </view>
              <view class="hot-badge" wx:if="{{item.is_hot}}">
                <text class="badge-text">热</text>
              </view>
            </view>
          </view>
          
          <text class="post-title" wx:if="{{item.title}}">{{item.title}}</text>
          <text class="post-text">{{item.content}}</text>
          
          <!-- 图片 -->
          <view class="post-images" wx:if="{{item.images && item.images.length > 0}}">
            <image 
              src="{{image}}" 
              class="post-image"
              wx:for="{{item.images}}" 
              wx:key="*this"
              wx:for-item="image"
              bindtap="previewImage"
              data-urls="{{item.images}}"
              data-current="{{image}}"
            />
          </view>
          
          <view class="post-stats">
            <view class="stat-item">
              <text class="stat-icon">👁️</text>
              <text class="stat-text">{{item.views_count}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-icon">👍</text>
              <text class="stat-text">{{item.likes_count}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-icon">💬</text>
              <text class="stat-text">{{item.comments_count}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="post-actions">
          <button 
            class="action-btn like-btn {{item.is_liked ? 'liked' : ''}}"
            bindtap="toggleLike"
            data-post="{{item}}"
          >
            <text class="btn-icon">{{item.is_liked ? '❤️' : '🤍'}}</text>
            <text class="btn-text">{{item.likes_count}}</text>
          </button>
          
          <button 
            class="action-btn comment-btn"
            bindtap="viewPost"
            data-post="{{item}}"
          >
            <text class="btn-icon">💬</text>
            <text class="btn-text">{{item.comments_count}}</text>
          </button>
          
          <button 
            class="action-btn share-btn"
            bindtap="sharePost"
            data-post="{{item}}"
          >
            <text class="btn-icon">📤</text>
          </button>
          
          <button 
            class="action-btn more-btn"
            bindtap="showMoreActions"
            data-post="{{item}}"
          >
            <text class="btn-icon">⋯</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{latestPosts.length === 0 && !loading}}">
      <text class="empty-icon">💬</text>
      <text class="empty-title">暂无动态</text>
      <text class="empty-desc">快来发布第一条动态吧</text>
      <button class="empty-btn" bindtap="createPost">
        发布动态
      </button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <view class="loading-spinner" wx:if="{{loading}}"></view>
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>
  </scroll-view>

  <!-- 发布按钮 -->
  <view class="fab-container">
    <button class="fab-btn" bindtap="createPost">
      <text class="fab-icon">✏️</text>
    </button>
  </view>
</view>

<!-- 更多操作菜单 -->
<view class="more-actions-modal" wx:if="{{showMoreModal}}" bindtap="hideMoreActions">
  <view class="actions-content" catchtap="">
    <view class="action-item" bindtap="reportPost">
      <text class="action-icon">⚠️</text>
      <text class="action-text">举报内容</text>
    </view>
    <view class="action-item" bindtap="blockUser">
      <text class="action-icon">🚫</text>
      <text class="action-text">屏蔽用户</text>
    </view>
    <view class="action-item" bindtap="hidePost">
      <text class="action-icon">👁️‍🗨️</text>
      <text class="action-text">不感兴趣</text>
    </view>
    <view class="action-item cancel" bindtap="hideMoreActions">
      <text class="action-text">取消</text>
    </view>
  </view>
</view>
