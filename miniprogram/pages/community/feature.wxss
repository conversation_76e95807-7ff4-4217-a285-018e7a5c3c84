/* pages/community/feature.wxss */
.community-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20rpx;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.feature-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}
