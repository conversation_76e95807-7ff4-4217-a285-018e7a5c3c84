// pages/chat-conversation/conversation.js
const app = getApp()
const paymentManager = require('../../utils/payment')

Page({
  data: {
    // 聊天用户信息
    chatUser: null,
    userInfo: null,
    
    // 消息数据
    messages: [],
    hasMoreMessages: true,
    loadingMessages: false,
    scrollTop: 0,
    scrollIntoView: '',
    
    // 输入相关
    inputText: '',
    showMediaPanel: false,
    
    // 录音相关
    isRecording: false,
    recordingTime: '00:00',
    recordingHint: '正在录音...',
    recordingStartTime: 0,
    recordingTimer: null,
    recorderManager: null,
    
    // 多媒体相关
    showImagePreview: false,
    previewImageUrl: '',
    showVideoPlayer: false,
    currentVideoUrl: '',
    
    // 礼物相关
    showGiftModal: false,
    selectedGift: null,
    giftOptions: []
  },

  onLoad(options) {
    // 获取聊天用户信息
    const userId = options.userId
    const userName = options.userName || '用户'
    
    this.setData({
      chatUser: {
        id: userId,
        name: userName,
        avatar: '/images/avatar-default.jpg',
        isOnline: true,
        lastSeen: '5分钟前'
      },
      userInfo: app.globalData.userInfo || {
        avatar: '/images/avatar-self.jpg',
        nickname: '我'
      }
    })
    
    this.initRecorderManager()
    this.loadMessages()
    this.loadGiftOptions()
  },

  onUnload() {
    // 清理录音定时器
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer)
    }
  },

  // 初始化录音管理器
  initRecorderManager() {
    const recorderManager = wx.getRecorderManager()
    
    recorderManager.onStart(() => {
      console.log('录音开始')
    })
    
    recorderManager.onStop((res) => {
      console.log('录音结束', res)
      this.handleRecordingResult(res)
    })
    
    recorderManager.onError((err) => {
      console.error('录音错误', err)
      wx.showToast({
        title: '录音失败',
        icon: 'none'
      })
    })
    
    this.setData({ recorderManager })
  },

  // 加载消息
  loadMessages() {
    this.setData({ loadingMessages: true })
    
    // 模拟消息数据
    const mockMessages = this.generateMockMessages()
    
    setTimeout(() => {
      this.setData({
        messages: mockMessages,
        loadingMessages: false
      })
      
      // 滚动到底部
      this.scrollToBottom()
    }, 1000)
  },

  // 生成模拟消息数据
  generateMockMessages() {
    const messages = [
      {
        id: 'msg_1',
        type: 'text',
        content: '你好！很高兴认识你',
        isSelf: false,
        timestamp: Date.now() - 3600000,
        timeText: '1小时前',
        showTime: true,
        status: 'read'
      },
      {
        id: 'msg_2',
        type: 'text',
        content: '你好！我也很高兴认识你',
        isSelf: true,
        timestamp: Date.now() - 3500000,
        status: 'read'
      },
      {
        id: 'msg_3',
        type: 'image',
        imageUrl: '/images/chat-image-1.jpg',
        previewUrls: ['/images/chat-image-1.jpg'],
        isSelf: false,
        timestamp: Date.now() - 3000000,
        status: 'read'
      },
      {
        id: 'msg_4',
        type: 'voice',
        duration: 5,
        voiceUrl: '/audio/voice-1.mp3',
        isSelf: true,
        timestamp: Date.now() - 2500000,
        status: 'read',
        isPlaying: false
      },
      {
        id: 'msg_5',
        type: 'video',
        videoUrl: '/video/video-1.mp4',
        videoThumb: '/images/video-thumb-1.jpg',
        videoDuration: '00:15',
        isSelf: false,
        timestamp: Date.now() - 2000000,
        status: 'read'
      },
      {
        id: 'msg_6',
        type: 'location',
        locationName: '天安门广场',
        locationAddress: '北京市东城区天安门广场',
        locationThumb: '/images/location-thumb.jpg',
        latitude: 39.9042,
        longitude: 116.4074,
        isSelf: true,
        timestamp: Date.now() - 1500000,
        status: 'read'
      },
      {
        id: 'msg_7',
        type: 'gift',
        giftName: '玫瑰花',
        giftDesc: '送你一朵玫瑰花',
        giftIcon: '🌹',
        isSelf: false,
        timestamp: Date.now() - 1000000,
        status: 'read'
      },
      {
        id: 'msg_8',
        type: 'text',
        content: '谢谢你的礼物！',
        isSelf: true,
        timestamp: Date.now() - 500000,
        timeText: '刚刚',
        showTime: true,
        status: 'read'
      }
    ]
    
    return messages
  },

  // 加载礼物选项
  loadGiftOptions() {
    const gifts = [
      {
        id: 'gift_rose',
        name: '玫瑰花',
        price: 99,
        iconName: 'heart',
        iconType: 'danger',
        isPopular: true
      },
      {
        id: 'gift_chocolate',
        name: '巧克力',
        price: 199,
        iconName: 'gift',
        iconType: 'warning',
        isPopular: false
      },
      {
        id: 'gift_diamond',
        name: '钻石',
        price: 999,
        iconName: 'star',
        iconType: 'gold',
        isPopular: true
      }
    ]
    
    this.setData({ giftOptions: gifts })
  },

  // 加载更多消息
  loadMoreMessages() {
    if (this.data.loadingMessages || !this.data.hasMoreMessages) return
    
    console.log('加载更多消息')
    // 这里可以调用API加载历史消息
  },

  // 滚动到底部
  scrollToBottom() {
    const messages = this.data.messages
    if (messages.length > 0) {
      const lastMessageId = `msg-${messages[messages.length - 1].id}`
      this.setData({ scrollIntoView: lastMessageId })
    }
  },

  // 文本输入
  onTextInput(e) {
    this.setData({ inputText: e.detail.value })
  },

  // 发送文本消息
  sendTextMessage() {
    const text = this.data.inputText.trim()
    if (!text) return
    
    const message = {
      id: `msg_${Date.now()}`,
      type: 'text',
      content: text,
      isSelf: true,
      timestamp: Date.now(),
      status: 'sending'
    }
    
    // 添加到消息列表
    const messages = [...this.data.messages, message]
    this.setData({ 
      messages,
      inputText: ''
    })
    
    // 滚动到底部
    this.scrollToBottom()
    
    // 模拟发送过程
    setTimeout(() => {
      this.updateMessageStatus(message.id, 'sent')
      setTimeout(() => {
        this.updateMessageStatus(message.id, 'read')
      }, 1000)
    }, 500)
  },

  // 更新消息状态
  updateMessageStatus(messageId, status) {
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, status }
      }
      return msg
    })
    
    this.setData({ messages })
  },

  // 切换多媒体面板
  toggleMediaPanel() {
    this.setData({ showMediaPanel: !this.data.showMediaPanel })
  },

  // 开始录音
  startRecording() {
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        this.setData({ 
          isRecording: true,
          recordingStartTime: Date.now(),
          recordingTime: '00:00',
          recordingHint: '正在录音...'
        })
        
        // 开始录音
        this.data.recorderManager.start({
          duration: 60000, // 最长60秒
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 96000,
          format: 'mp3'
        })
        
        // 开始计时
        this.startRecordingTimer()
      },
      fail: () => {
        wx.showModal({
          title: '需要录音权限',
          content: '请在设置中开启录音权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  // 停止录音
  stopRecording() {
    if (!this.data.isRecording) return
    
    this.setData({ isRecording: false })
    this.data.recorderManager.stop()
    this.stopRecordingTimer()
  },

  // 取消录音
  cancelRecording() {
    if (!this.data.isRecording) return
    
    this.setData({ isRecording: false })
    this.data.recorderManager.stop()
    this.stopRecordingTimer()
    
    wx.showToast({
      title: '录音已取消',
      icon: 'none'
    })
  },

  // 录音移动事件
  onRecordingMove(e) {
    if (!this.data.isRecording) return
    
    // 检查是否上滑取消
    const startY = e.touches[0].clientY
    if (startY < 100) { // 上滑超过100px取消录音
      this.setData({ recordingHint: '松开取消录音' })
    } else {
      this.setData({ recordingHint: '正在录音...' })
    }
  },

  // 开始录音计时
  startRecordingTimer() {
    this.data.recordingTimer = setInterval(() => {
      const elapsed = Date.now() - this.data.recordingStartTime
      const seconds = Math.floor(elapsed / 1000)
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      
      const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
      this.setData({ recordingTime: timeString })
      
      // 超过60秒自动停止
      if (seconds >= 60) {
        this.stopRecording()
      }
    }, 1000)
  },

  // 停止录音计时
  stopRecordingTimer() {
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer)
      this.setData({ recordingTimer: null })
    }
  },

  // 处理录音结果
  handleRecordingResult(result) {
    const duration = Math.floor(result.duration / 1000)
    
    if (duration < 1) {
      wx.showToast({
        title: '录音时间太短',
        icon: 'none'
      })
      return
    }
    
    // 创建语音消息
    const message = {
      id: `msg_${Date.now()}`,
      type: 'voice',
      duration: duration,
      voiceUrl: result.tempFilePath,
      isSelf: true,
      timestamp: Date.now(),
      status: 'sending',
      isPlaying: false
    }
    
    // 添加到消息列表
    const messages = [...this.data.messages, message]
    this.setData({ messages })
    
    // 滚动到底部
    this.scrollToBottom()
    
    // 模拟上传和发送过程
    setTimeout(() => {
      this.updateMessageStatus(message.id, 'sent')
      setTimeout(() => {
        this.updateMessageStatus(message.id, 'read')
      }, 1000)
    }, 1000)
  },

  // 播放语音
  playVoice(e) {
    const message = e.currentTarget.dataset.message
    
    // 停止其他正在播放的语音
    const messages = this.data.messages.map(msg => ({
      ...msg,
      isPlaying: msg.id === message.id ? !msg.isPlaying : false
    }))
    
    this.setData({ messages })
    
    if (message.isPlaying) {
      // 播放语音
      wx.playVoice({
        filePath: message.voiceUrl,
        success: () => {
          console.log('语音播放完成')
          this.stopVoicePlaying(message.id)
        },
        fail: (err) => {
          console.error('语音播放失败', err)
          this.stopVoicePlaying(message.id)
        }
      })
    } else {
      // 停止播放
      wx.stopVoice()
    }
  },

  // 停止语音播放
  stopVoicePlaying(messageId) {
    const messages = this.data.messages.map(msg => ({
      ...msg,
      isPlaying: msg.id === messageId ? false : msg.isPlaying
    }))
    
    this.setData({ messages })
  },

  // 选择图片
  selectImage() {
    wx.chooseImage({
      count: 9,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.handleImageSelection(res.tempFilePaths)
      }
    })
  },

  // 拍照
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.handleImageSelection(res.tempFilePaths)
      }
    })
  },

  // 处理图片选择
  handleImageSelection(imagePaths) {
    imagePaths.forEach((imagePath, index) => {
      const message = {
        id: `msg_${Date.now()}_${index}`,
        type: 'image',
        imageUrl: imagePath,
        previewUrls: imagePaths,
        isSelf: true,
        timestamp: Date.now(),
        status: 'sending',
        uploading: true
      }
      
      // 添加到消息列表
      const messages = [...this.data.messages, message]
      this.setData({ messages })
      
      // 模拟上传过程
      setTimeout(() => {
        this.updateMessageStatus(message.id, 'sent')
        this.updateMessageUploading(message.id, false)
        setTimeout(() => {
          this.updateMessageStatus(message.id, 'read')
        }, 1000)
      }, 2000)
    })
    
    this.setData({ showMediaPanel: false })
    this.scrollToBottom()
  },

  // 更新消息上传状态
  updateMessageUploading(messageId, uploading) {
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId) {
        return { ...msg, uploading }
      }
      return msg
    })
    
    this.setData({ messages })
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url
    const urls = e.currentTarget.dataset.urls || [url]
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 选择视频
  selectVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        this.handleVideoSelection(res)
      }
    })
  },

  // 处理视频选择
  handleVideoSelection(videoResult) {
    const message = {
      id: `msg_${Date.now()}`,
      type: 'video',
      videoUrl: videoResult.tempFilePath,
      videoThumb: videoResult.thumbTempFilePath,
      videoDuration: this.formatDuration(videoResult.duration),
      isSelf: true,
      timestamp: Date.now(),
      status: 'sending',
      uploading: true
    }
    
    // 添加到消息列表
    const messages = [...this.data.messages, message]
    this.setData({ messages, showMediaPanel: false })
    
    // 滚动到底部
    this.scrollToBottom()
    
    // 模拟上传过程
    setTimeout(() => {
      this.updateMessageStatus(message.id, 'sent')
      this.updateMessageUploading(message.id, false)
      setTimeout(() => {
        this.updateMessageStatus(message.id, 'read')
      }, 1000)
    }, 3000)
  },

  // 格式化时长
  formatDuration(seconds) {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  },

  // 播放视频
  playVideo(e) {
    const message = e.currentTarget.dataset.message
    this.setData({
      showVideoPlayer: true,
      currentVideoUrl: message.videoUrl
    })
  },

  // 关闭视频播放器
  closeVideoPlayer() {
    this.setData({
      showVideoPlayer: false,
      currentVideoUrl: ''
    })
  },

  // 视频播放结束
  onVideoEnded() {
    console.log('视频播放结束')
  },

  // 选择位置
  selectLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.handleLocationSelection(res)
      },
      fail: (err) => {
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要位置权限',
            content: '请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
        }
      }
    })
  },

  // 处理位置选择
  handleLocationSelection(location) {
    const message = {
      id: `msg_${Date.now()}`,
      type: 'location',
      locationName: location.name,
      locationAddress: location.address,
      locationThumb: '/images/location-thumb.jpg', // 可以调用地图API生成缩略图
      latitude: location.latitude,
      longitude: location.longitude,
      isSelf: true,
      timestamp: Date.now(),
      status: 'sending'
    }
    
    // 添加到消息列表
    const messages = [...this.data.messages, message]
    this.setData({ messages, showMediaPanel: false })
    
    // 滚动到底部
    this.scrollToBottom()
    
    // 模拟发送过程
    setTimeout(() => {
      this.updateMessageStatus(message.id, 'sent')
      setTimeout(() => {
        this.updateMessageStatus(message.id, 'read')
      }, 1000)
    }, 500)
  },

  // 查看位置
  viewLocation(e) {
    const message = e.currentTarget.dataset.message
    
    wx.openLocation({
      latitude: message.latitude,
      longitude: message.longitude,
      name: message.locationName,
      address: message.locationAddress
    })
  },

  // 发送礼物
  sendGift() {
    this.setData({ showGiftModal: true })
  },

  // 关闭礼物弹窗
  closeGiftModal() {
    this.setData({ 
      showGiftModal: false,
      selectedGift: null
    })
  },

  // 选择礼物
  selectGift(e) {
    const gift = e.currentTarget.dataset.gift
    this.setData({ selectedGift: gift.id })
  },

  // 确认发送礼物
  async confirmSendGift() {
    const selectedGiftId = this.data.selectedGift
    const gift = this.data.giftOptions.find(g => g.id === selectedGiftId)
    
    if (!gift) return
    
    try {
      // 使用支付管理器处理礼物购买
      const orderData = {
        productId: gift.id,
        productName: gift.name,
        amount: gift.price * 100, // 转换为分
        description: `礼物 - ${gift.name}`
      }

      const result = await paymentManager.processPayment(orderData)
      
      if (result.success) {
        // 支付成功，发送礼物消息
        const message = {
          id: `msg_${Date.now()}`,
          type: 'gift',
          giftName: gift.name,
          giftDesc: `送你一个${gift.name}`,
          giftIcon: gift.iconName,
          isSelf: true,
          timestamp: Date.now(),
          status: 'sending'
        }
        
        // 添加到消息列表
        const messages = [...this.data.messages, message]
        this.setData({ 
          messages,
          showGiftModal: false,
          selectedGift: null
        })
        
        // 滚动到底部
        this.scrollToBottom()
        
        // 模拟发送过程
        setTimeout(() => {
          this.updateMessageStatus(message.id, 'sent')
          setTimeout(() => {
            this.updateMessageStatus(message.id, 'read')
          }, 1000)
        }, 500)
        
        wx.showToast({
          title: '礼物发送成功！',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('发送礼物失败:', error)
      
      let errorMessage = '发送失败，请重试'
      if (error.error) {
        errorMessage = error.error
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  },

  // 语音通话
  makeVoiceCall() {
    wx.showToast({
      title: '语音通话功能开发中',
      icon: 'none'
    })
  },

  // 视频通话
  makeVideoCall() {
    wx.showToast({
      title: '视频通话功能开发中',
      icon: 'none'
    })
  },

  // 显示聊天菜单
  showChatMenu() {
    wx.showActionSheet({
      itemList: ['查看资料', '清空聊天记录', '举报用户'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.viewUserProfile()
            break
          case 1:
            this.clearChatHistory()
            break
          case 2:
            this.reportUser()
            break
        }
      }
    })
  },

  // 查看用户资料
  viewUserProfile() {
    wx.navigateTo({
      url: `/pages/profile/profile?userId=${this.data.chatUser.id}`
    })
  },

  // 清空聊天记录
  clearChatHistory() {
    wx.showModal({
      title: '清空聊天记录',
      content: '确定要清空所有聊天记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({ messages: [] })
          wx.showToast({
            title: '聊天记录已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  // 举报用户
  reportUser() {
    wx.navigateTo({
      url: `/pages/report/report?userId=${this.data.chatUser.id}&userName=${this.data.chatUser.name}`
    })
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: `与${this.data.chatUser.name}的聊天`,
      path: `/pages/chat-conversation/conversation?userId=${this.data.chatUser.id}&userName=${this.data.chatUser.name}`
    }
  }
})
