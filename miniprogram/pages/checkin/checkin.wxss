/* 每日签到页面样式 */

.checkin-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 页面头部 */
.checkin-header {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-image {
  width: 100%;
  height: 100%;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.8), rgba(255, 142, 83, 0.8));
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.checkin-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-icon.checked {
  background: rgba(82, 196, 26, 0.2);
  border: 2px solid #52c41a;
}

.status-icon.unchecked {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.icon-image {
  width: 30px;
  height: 30px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.streak-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.streak-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.streak-value {
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.streak-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.streak-divider {
  width: 1px;
  height: 30px;
  background: rgba(255, 255, 255, 0.3);
}

/* 签到按钮 */
.checkin-action {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.checkin-btn {
  position: relative;
  width: 200px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  border: none;
  border-radius: 30px;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
}

.checkin-btn:not(.disabled):active {
  transform: scale(0.95);
}

.checkin-btn.disabled {
  background: #d9d9d9;
  box-shadow: none;
}

.checkin-btn.loading {
  background: #faad14;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
}

.btn-icon,
.btn-loading {
  width: 24px;
  height: 24px;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.checkin-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.6s ease;
}

.checkin-animation.show {
  opacity: 1;
  animation: checkinSuccess 2s ease-out;
}

.animation-circle {
  width: 80px;
  height: 80px;
  border: 3px solid #52c41a;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.animation-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: 600;
  color: #52c41a;
}

.next-reward {
  text-align: center;
}

.reward-text {
  font-size: 14px;
  color: #666;
}

.reward-desc {
  display: block;
  font-size: 12px;
  color: #ff6b6b;
  margin-top: 4px;
}

/* 奖励进度条 */
.reward-progress {
  margin: 20px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rules-btn {
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  padding: 0;
}

.rules-icon {
  width: 20px;
  height: 20px;
}

.progress-scroll {
  width: 100%;
}

.progress-list {
  display: flex;
  gap: 12px;
  padding: 0 4px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.progress-item.completed {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.progress-item.current {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.progress-item.pending {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
}

.item-day {
  font-size: 12px;
  color: #666;
}

.item-reward {
  display: flex;
  align-items: center;
  gap: 4px;
}

.reward-icon {
  width: 16px;
  height: 16px;
}

.reward-points {
  font-size: 14px;
  font-weight: 600;
  color: #ff6b6b;
}

.item-status {
  width: 20px;
  height: 20px;
}

.status-icon {
  width: 100%;
  height: 100%;
}

.item-special {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ff6b6b;
  color: white;
  font-size: 8px;
  padding: 2px 4px;
  border-radius: 6px;
}

/* 签到日历 */
.checkin-calendar {
  margin: 20px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.toggle-btn {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  width: 16px;
  height: 16px;
}

.calendar-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.calendar-content.show {
  max-height: 400px;
  padding: 16px;
}

.calendar-content.hide {
  max-height: 0;
  padding: 0 16px;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.week-day {
  text-align: center;
  padding: 8px 4px;
}

.week-text {
  font-size: 12px;
  color: #999;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.calendar-day.current-month {
  background: #fafafa;
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.calendar-day.checked-in {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.calendar-day.can-makeup {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.day-number {
  font-size: 14px;
  color: #333;
}

.day-status {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
}

.makeup-hint {
  position: absolute;
  bottom: 2px;
  right: 2px;
  font-size: 8px;
  color: #faad14;
  background: white;
  border-radius: 2px;
  padding: 1px 2px;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  gap: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #f5f5f5;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.action-text {
  font-size: 12px;
  color: #666;
}

/* 弹窗样式 */
.reward-modal,
.makeup-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.reward-modal.show,
.makeup-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 80%;
  max-width: 320px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.reward-modal.show .modal-content,
.makeup-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  text-align: center;
  margin-bottom: 20px;
}

.success-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
}

.success-title,
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.reward-list {
  margin-bottom: 20px;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.reward-info {
  flex: 1;
}

.reward-value {
  font-size: 16px;
  font-weight: 600;
  color: #ff6b6b;
}

.reward-desc {
  font-size: 12px;
  color: #999;
}

.modal-btn {
  width: 100%;
  height: 44px;
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
}

.makeup-info {
  margin-bottom: 20px;
  text-align: center;
}

.makeup-date,
.makeup-cost,
.makeup-note {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.makeup-cost {
  color: #ff6b6b;
  font-weight: 500;
}

.modal-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-size: 14px;
  font-weight: 500;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: none;
}

.confirm-btn {
  background: #ff6b6b;
  color: white;
  border: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  width: 40px;
  height: 40px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 动画效果 */
@keyframes checkinSuccess {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .checkin-header {
    height: 160px;
  }
  
  .streak-info {
    gap: 16px;
  }
  
  .streak-value {
    font-size: 20px;
  }
  
  .checkin-btn {
    width: 180px;
    height: 50px;
  }
  
  .progress-list {
    gap: 8px;
  }
  
  .progress-item {
    min-width: 70px;
    padding: 8px 4px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .checkin-container {
    background: #1a1a1a;
  }
  
  .reward-progress,
  .checkin-calendar,
  .action-btn,
  .modal-content {
    background: #2d2d2d;
    color: white;
  }
  
  .calendar-header {
    background: #404040;
  }
  
  .calendar-day.current-month {
    background: #404040;
  }
}
