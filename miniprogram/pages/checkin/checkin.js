/**
 * 每日签到页面
 * Task 3.2: 每日签到系统 - 签到界面与日历展示
 */

const checkinManager = require('../../utils/checkin-manager')

Page({
  data: {
    // 签到数据
    checkinData: {
      currentStreak: 0,
      longestStreak: 0,
      totalCheckins: 0,
      totalPoints: 0,
      hasCheckedInToday: false,
      nextReward: null,
      monthlyProgress: null
    },
    
    // 签到历史
    checkinHistory: [],
    
    // 签到日历
    calendar: {
      currentMonth: '',
      days: [],
      weekDays: ['日', '一', '二', '三', '四', '五', '六']
    },
    
    // 奖励配置
    rewardConfig: [
      { day: 1, points: 10, description: '签到积分' },
      { day: 2, points: 15, description: '连续签到奖励' },
      { day: 3, points: 20, description: '连续签到奖励' },
      { day: 4, points: 25, description: '连续签到奖励' },
      { day: 5, points: 30, description: '连续签到奖励' },
      { day: 6, points: 35, description: '连续签到奖励' },
      { day: 7, points: 50, description: '一周签到大奖', isSpecial: true }
    ],
    
    // 页面状态
    pageState: {
      isLoading: true,
      isChecking: false,
      showRewardModal: false,
      showMakeupModal: false,
      showCalendar: true
    },
    
    // 签到结果
    checkinResult: {
      success: false,
      rewards: [],
      message: ''
    },
    
    // 补签相关
    makeup: {
      selectedDate: '',
      cost: 50,
      canMakeup: false
    }
  },

  onLoad() {
    this.loadCheckinData()
    this.initCalendar()
  },

  onShow() {
    this.refreshCheckinData()
  },

  /**
   * 加载签到数据
   */
  async loadCheckinData() {
    try {
      this.setData({
        'pageState.isLoading': true
      })
      
      const userId = this.getCurrentUserId()
      const checkinData = checkinManager.getUserCheckinData(userId)
      
      this.setData({
        checkinData: checkinData,
        checkinHistory: checkinData.recentHistory,
        'pageState.isLoading': false
      })
      
      console.log('📊 签到数据加载完成')
    } catch (error) {
      console.error('加载签到数据失败:', error)
      this.showError('加载失败，请重试')
    }
  },

  /**
   * 刷新签到数据
   */
  async refreshCheckinData() {
    try {
      const userId = this.getCurrentUserId()
      const checkinData = checkinManager.getUserCheckinData(userId)
      
      this.setData({
        checkinData: checkinData,
        checkinHistory: checkinData.recentHistory
      })
      
      // 更新日历
      this.updateCalendar()
    } catch (error) {
      console.error('刷新签到数据失败:', error)
    }
  },

  /**
   * 执行签到
   */
  async onCheckin() {
    if (this.data.pageState.isChecking) {
      return
    }
    
    if (this.data.checkinData.hasCheckedInToday) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({
        'pageState.isChecking': true
      })
      
      const userId = this.getCurrentUserId()
      const result = await checkinManager.performCheckin(userId)
      
      if (result.success) {
        // 签到成功
        this.setData({
          checkinResult: {
            success: true,
            rewards: result.data.rewards,
            message: '签到成功！'
          },
          'pageState.showRewardModal': true
        })
        
        // 刷新数据
        await this.refreshCheckinData()
        
        // 显示成功动画
        this.showCheckinAnimation()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('签到失败:', error)
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'pageState.isChecking': false
      })
    }
  },

  /**
   * 显示签到动画
   */
  showCheckinAnimation() {
    // 触发签到成功动画
    this.setData({
      'pageState.showCheckinAnimation': true
    })
    
    setTimeout(() => {
      this.setData({
        'pageState.showCheckinAnimation': false
      })
    }, 2000)
  },

  /**
   * 关闭奖励弹窗
   */
  onCloseRewardModal() {
    this.setData({
      'pageState.showRewardModal': false
    })
  },

  /**
   * 初始化日历
   */
  initCalendar() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth()
    
    this.generateCalendar(year, month)
  },

  /**
   * 生成日历
   * @param {number} year 年份
   * @param {number} month 月份
   */
  generateCalendar(year, month) {
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const firstDayOfWeek = firstDay.getDay()
    const daysInMonth = lastDay.getDate()
    
    const days = []
    
    // 添加上个月的日期（填充）
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month, -i)
      days.push({
        date: date.getDate(),
        dateString: this.getDateString(date),
        isCurrentMonth: false,
        hasCheckedIn: false,
        isMakeup: false,
        isToday: false
      })
    }
    
    // 添加当月的日期
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i)
      const dateString = this.getDateString(date)
      const isToday = this.isToday(date)
      
      days.push({
        date: i,
        dateString: dateString,
        isCurrentMonth: true,
        hasCheckedIn: this.hasCheckedIn(dateString),
        isMakeup: this.isMakeupCheckin(dateString),
        isToday: isToday,
        canMakeup: this.canMakeup(dateString)
      })
    }
    
    // 添加下个月的日期（填充）
    const remainingDays = 42 - days.length // 6行 * 7天
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i)
      days.push({
        date: i,
        dateString: this.getDateString(date),
        isCurrentMonth: false,
        hasCheckedIn: false,
        isMakeup: false,
        isToday: false
      })
    }
    
    this.setData({
      'calendar.currentMonth': `${year}年${month + 1}月`,
      'calendar.days': days
    })
  },

  /**
   * 更新日历
   */
  updateCalendar() {
    const days = this.data.calendar.days.map(day => {
      if (day.isCurrentMonth) {
        return {
          ...day,
          hasCheckedIn: this.hasCheckedIn(day.dateString),
          isMakeup: this.isMakeupCheckin(day.dateString),
          canMakeup: this.canMakeup(day.dateString)
        }
      }
      return day
    })
    
    this.setData({
      'calendar.days': days
    })
  },

  /**
   * 检查是否已签到
   * @param {string} dateString 日期字符串
   */
  hasCheckedIn(dateString) {
    return this.data.checkinHistory.some(item => 
      item.date === dateString && item.hasCheckedIn
    )
  },

  /**
   * 检查是否为补签
   * @param {string} dateString 日期字符串
   */
  isMakeupCheckin(dateString) {
    return this.data.checkinHistory.some(item => 
      item.date === dateString && item.isMakeup
    )
  },

  /**
   * 检查是否可以补签
   * @param {string} dateString 日期字符串
   */
  canMakeup(dateString) {
    const date = new Date(dateString)
    const today = new Date()
    const daysDiff = Math.floor((today - date) / (24 * 60 * 60 * 1000))
    
    return daysDiff > 0 && daysDiff <= 3 && !this.hasCheckedIn(dateString)
  },

  /**
   * 日历日期点击
   */
  onCalendarDayTap(event) {
    const index = event.currentTarget.dataset.index
    const day = this.data.calendar.days[index]
    
    if (!day.isCurrentMonth) {
      return
    }
    
    if (day.isToday && !day.hasCheckedIn) {
      // 今日签到
      this.onCheckin()
    } else if (day.canMakeup) {
      // 补签
      this.showMakeupModal(day)
    } else if (day.hasCheckedIn) {
      // 显示签到详情
      this.showCheckinDetail(day)
    }
  },

  /**
   * 显示补签弹窗
   * @param {Object} day 日期对象
   */
  showMakeupModal(day) {
    this.setData({
      'makeup.selectedDate': day.dateString,
      'makeup.canMakeup': true,
      'pageState.showMakeupModal': true
    })
  },

  /**
   * 关闭补签弹窗
   */
  onCloseMakeupModal() {
    this.setData({
      'pageState.showMakeupModal': false
    })
  },

  /**
   * 执行补签
   */
  async onMakeupCheckin() {
    try {
      const userId = this.getCurrentUserId()
      const selectedDate = this.data.makeup.selectedDate
      
      const result = await checkinManager.makeupCheckin(userId, selectedDate)
      
      if (result.success) {
        wx.showToast({
          title: '补签成功',
          icon: 'success'
        })
        
        // 刷新数据
        await this.refreshCheckinData()
        
        // 关闭弹窗
        this.onCloseMakeupModal()
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('补签失败:', error)
      wx.showToast({
        title: '补签失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 显示签到详情
   * @param {Object} day 日期对象
   */
  showCheckinDetail(day) {
    const historyItem = this.data.checkinHistory.find(item => item.date === day.dateString)
    
    if (historyItem) {
      const content = `签到日期：${day.dateString}\n获得积分：${historyItem.points}\n${historyItem.isMakeup ? '补签' : '正常签到'}`
      
      wx.showModal({
        title: '签到详情',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  /**
   * 切换日历显示
   */
  onToggleCalendar() {
    this.setData({
      'pageState.showCalendar': !this.data.pageState.showCalendar
    })
  },

  /**
   * 查看签到规则
   */
  onViewRules() {
    const rules = `
签到规则：
1. 每日可签到一次，获得积分奖励
2. 连续签到可获得额外奖励
3. 第7天签到可获得特殊奖励
4. 可使用积分补签最近3天
5. VIP用户补签享受8折优惠

奖励说明：
第1天：10积分
第2天：15积分
第3天：20积分
第4天：25积分
第5天：30积分
第6天：35积分
第7天：50积分 + 特殊徽章
    `.trim()
    
    wx.showModal({
      title: '签到规则',
      content: rules,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 查看签到统计
   */
  onViewStats() {
    wx.navigateTo({
      url: '/pages/checkin-stats/checkin-stats'
    })
  },

  /**
   * 分享签到
   */
  onShareCheckin() {
    const { currentStreak, totalCheckins } = this.data.checkinData
    
    return {
      title: `我已连续签到${currentStreak}天，累计签到${totalCheckins}次！`,
      path: '/pages/checkin/checkin',
      imageUrl: '/images/checkin-share.jpg'
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshCheckinData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    return wx.getStorageSync('user_id') || 'anonymous'
  },

  /**
   * 获取日期字符串
   * @param {Date} date 日期对象
   */
  getDateString(date) {
    return date.toISOString().split('T')[0]
  },

  /**
   * 检查是否为今天
   * @param {Date} date 日期对象
   */
  isToday(date) {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
})
