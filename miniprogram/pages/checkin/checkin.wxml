<!--每日签到页面模板-->
<view class="checkin-container">
  <!-- 页面头部 -->
  <view class="checkin-header">
    <view class="header-bg">
      <image class="bg-image" src="/images/checkin-bg.jpg" mode="aspectFill"></image>
      <view class="bg-overlay"></view>
    </view>
    
    <view class="header-content">
      <view class="checkin-status">
        <view class="status-icon {{checkinData.hasCheckedInToday ? 'checked' : 'unchecked'}}">
          <image class="icon-image" src="{{checkinData.hasCheckedInToday ? '/images/checked.png' : '/images/unchecked.png'}}"></image>
        </view>
        <text class="status-text">{{checkinData.hasCheckedInToday ? '今日已签到' : '今日未签到'}}</text>
      </view>
      
      <view class="streak-info">
        <view class="streak-item">
          <text class="streak-value">{{checkinData.currentStreak}}</text>
          <text class="streak-label">连续签到</text>
        </view>
        <view class="streak-divider"></view>
        <view class="streak-item">
          <text class="streak-value">{{checkinData.totalCheckins}}</text>
          <text class="streak-label">累计签到</text>
        </view>
        <view class="streak-divider"></view>
        <view class="streak-item">
          <text class="streak-value">{{checkinData.totalPoints}}</text>
          <text class="streak-label">总积分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到按钮 -->
  <view class="checkin-action">
    <button 
      class="checkin-btn {{checkinData.hasCheckedInToday ? 'disabled' : ''}} {{pageState.isChecking ? 'loading' : ''}}"
      bindtap="onCheckin"
      disabled="{{checkinData.hasCheckedInToday || pageState.isChecking}}"
    >
      <view class="btn-content">
        <image class="btn-icon" src="/images/checkin-icon.png" wx:if="{{!pageState.isChecking}}"></image>
        <image class="btn-loading" src="/images/loading.gif" wx:if="{{pageState.isChecking}}"></image>
        <text class="btn-text">
          {{pageState.isChecking ? '签到中...' : (checkinData.hasCheckedInToday ? '已签到' : '立即签到')}}
        </text>
      </view>
      
      <!-- 签到动画 -->
      <view class="checkin-animation {{pageState.showCheckinAnimation ? 'show' : ''}}" wx:if="{{pageState.showCheckinAnimation}}">
        <view class="animation-circle"></view>
        <view class="animation-text">+{{checkinData.nextReward.points}}</view>
      </view>
    </button>
    
    <!-- 下一个奖励预览 -->
    <view class="next-reward" wx:if="{{!checkinData.hasCheckedInToday && checkinData.nextReward}}">
      <text class="reward-text">明日签到可获得 {{checkinData.nextReward.points}} 积分</text>
      <text class="reward-desc" wx:if="{{checkinData.nextReward.isSpecial}}">🎁 特殊奖励</text>
    </view>
  </view>

  <!-- 奖励进度条 -->
  <view class="reward-progress">
    <view class="progress-title">
      <text class="title-text">签到奖励进度</text>
      <button class="rules-btn" bindtap="onViewRules">
        <image class="rules-icon" src="/images/question.png"></image>
      </button>
    </view>
    
    <scroll-view class="progress-scroll" scroll-x="true">
      <view class="progress-list">
        <view 
          class="progress-item {{index < checkinData.currentStreak ? 'completed' : (index === checkinData.currentStreak ? 'current' : 'pending')}}"
          wx:for="{{rewardConfig}}"
          wx:key="day"
        >
          <view class="item-day">第{{item.day}}天</view>
          <view class="item-reward">
            <image class="reward-icon" src="/images/points.png"></image>
            <text class="reward-points">{{item.points}}</text>
          </view>
          <view class="item-status">
            <image class="status-icon" src="{{index < checkinData.currentStreak ? '/images/completed.png' : '/images/pending.png'}}"></image>
          </view>
          <view class="item-special" wx:if="{{item.isSpecial}}">
            <text class="special-text">特殊奖励</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 签到日历 -->
  <view class="checkin-calendar">
    <view class="calendar-header">
      <text class="calendar-title">{{calendar.currentMonth}}</text>
      <button class="toggle-btn" bindtap="onToggleCalendar">
        <image class="toggle-icon" src="{{pageState.showCalendar ? '/images/collapse.png' : '/images/expand.png'}}"></image>
      </button>
    </view>
    
    <view class="calendar-content {{pageState.showCalendar ? 'show' : 'hide'}}">
      <!-- 星期标题 -->
      <view class="week-header">
        <view class="week-day" wx:for="{{calendar.weekDays}}" wx:key="*this">
          <text class="week-text">{{item}}</text>
        </view>
      </view>
      
      <!-- 日历网格 -->
      <view class="calendar-grid">
        <view 
          class="calendar-day {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasCheckedIn ? 'checked-in' : ''}} {{item.canMakeup ? 'can-makeup' : ''}}"
          wx:for="{{calendar.days}}"
          wx:key="dateString"
          data-index="{{index}}"
          bindtap="onCalendarDayTap"
        >
          <text class="day-number">{{item.date}}</text>
          <view class="day-status">
            <image class="status-icon" src="/images/checked-small.png" wx:if="{{item.hasCheckedIn && !item.isMakeup}}"></image>
            <image class="status-icon makeup" src="/images/makeup.png" wx:if="{{item.isMakeup}}"></image>
            <view class="makeup-hint" wx:if="{{item.canMakeup}}">补</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="action-btn" bindtap="onViewStats">
      <image class="action-icon" src="/images/stats.png"></image>
      <text class="action-text">签到统计</text>
    </button>
    
    <button class="action-btn" open-type="share" bindtap="onShareCheckin">
      <image class="action-icon" src="/images/share.png"></image>
      <text class="action-text">分享签到</text>
    </button>
    
    <button class="action-btn" bindtap="onViewRules">
      <image class="action-icon" src="/images/rules.png"></image>
      <text class="action-text">签到规则</text>
    </button>
  </view>

  <!-- 奖励弹窗 -->
  <view class="reward-modal {{pageState.showRewardModal ? 'show' : ''}}" bindtap="onCloseRewardModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <image class="success-icon" src="/images/success-big.png"></image>
        <text class="success-title">签到成功！</text>
      </view>
      
      <view class="reward-list">
        <view class="reward-item" wx:for="{{checkinResult.rewards}}" wx:key="type">
          <image class="reward-icon" src="/images/reward-{{item.type}}.png"></image>
          <view class="reward-info">
            <text class="reward-value">+{{item.value}}</text>
            <text class="reward-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
      
      <button class="modal-btn" bindtap="onCloseRewardModal">太棒了</button>
    </view>
  </view>

  <!-- 补签弹窗 -->
  <view class="makeup-modal {{pageState.showMakeupModal ? 'show' : ''}}" bindtap="onCloseMakeupModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">补签确认</text>
        <button class="close-btn" bindtap="onCloseMakeupModal">
          <image class="close-icon" src="/images/close.png"></image>
        </button>
      </view>
      
      <view class="makeup-info">
        <text class="makeup-date">补签日期：{{makeup.selectedDate}}</text>
        <text class="makeup-cost">消耗积分：{{makeup.cost}}</text>
        <text class="makeup-note">补签后可获得10积分</text>
      </view>
      
      <view class="modal-actions">
        <button class="cancel-btn" bindtap="onCloseMakeupModal">取消</button>
        <button class="confirm-btn" bindtap="onMakeupCheckin">确认补签</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{pageState.isLoading}}">
    <view class="loading-content">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">正在加载签到数据...</text>
    </view>
  </view>
</view>
