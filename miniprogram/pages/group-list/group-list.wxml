<!--兴趣小组列表页面模板-->
<view class="group-list-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">兴趣小组</text>
      <view class="header-actions">
        <button class="action-btn search-btn" bindtap="onShowSearch">
          <image class="action-icon" src="/images/search.png"></image>
        </button>
        <button class="action-btn filter-btn" bindtap="onShowFilter">
          <image class="action-icon" src="/images/filter.png"></image>
        </button>
        <button class="action-btn create-btn" bindtap="onCreateGroup">
          <image class="action-icon" src="/images/add.png"></image>
        </button>
      </view>
    </view>
    
    <!-- 搜索框 -->
    <view class="search-box {{pageState.showSearch ? 'show' : ''}}">
      <view class="search-input-box">
        <image class="search-icon" src="/images/search.png"></image>
        <input 
          class="search-input"
          placeholder="搜索小组名称、描述或标签"
          value="{{filters.keyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <button class="cancel-btn" bindtap="onHideSearch">取消</button>
      </view>
    </view>
  </view>

  <!-- 分类导航 -->
  <scroll-view class="category-nav" scroll-x="true">
    <view class="category-list">
      <view 
        class="category-item {{filters.category === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        data-category="{{item.id}}"
        bindtap="onSelectCategory"
        style="--category-color: {{item.color}}"
      >
        <image class="category-icon" src="{{item.icon}}"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="quick-btn my-groups-btn" bindtap="onMyGroups">
      <image class="quick-icon" src="/images/my-groups.png"></image>
      <text class="quick-text">我的小组</text>
    </button>
    <button class="quick-btn create-group-btn" bindtap="onCreateGroup">
      <image class="quick-icon" src="/images/create-group.png"></image>
      <text class="quick-text">创建小组</text>
    </button>
  </view>

  <!-- 小组列表 -->
  <scroll-view 
    class="group-scroll"
    scroll-y="true"
    bindscrolltolower="onLoadMore"
    refresher-enabled="true"
    refresher-triggered="{{pageState.refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{pageState.isLoading}}">
      <view class="loading-content">
        <image class="loading-icon" src="/images/loading.gif"></image>
        <text class="loading-text">正在加载小组...</text>
      </view>
    </view>

    <!-- 小组列表 -->
    <view class="groups-list" wx:if="{{!pageState.isLoading}}">
      <view 
        class="group-item"
        wx:for="{{groups}}"
        wx:key="id"
        data-group-id="{{item.id}}"
        bindtap="onViewGroup"
      >
        <!-- 小组头像 -->
        <view class="group-avatar-container">
          <image class="group-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="group-category-badge" style="background: {{item.categoryColor}}">
            <text class="category-badge-text">{{getCategoryName(item.category)}}</text>
          </view>
        </view>

        <!-- 小组信息 -->
        <view class="group-info">
          <view class="group-header">
            <text class="group-name">{{item.name}}</text>
            <view class="group-privacy">
              <image 
                class="privacy-icon" 
                src="{{item.privacy === 'public' ? '/images/public.png' : '/images/private.png'}}"
              ></image>
            </view>
          </view>
          
          <text class="group-description">{{item.description}}</text>
          
          <!-- 小组标签 -->
          <view class="group-tags" wx:if="{{item.tags.length > 0}}">
            <text 
              class="group-tag"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >#{{tag}}</text>
          </view>
          
          <!-- 小组统计 -->
          <view class="group-stats">
            <view class="stat-item">
              <image class="stat-icon" src="/images/members.png"></image>
              <text class="stat-text">{{formatMemberCount(item.memberCount)}}人</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="/images/activities.png"></image>
              <text class="stat-text">{{item.stats.totalActivities}}活动</text>
            </view>
            <view class="stat-item">
              <image class="stat-icon" src="/images/time.png"></image>
              <text class="stat-text">{{formatTime(item.createdAt)}}</text>
            </view>
          </view>
        </view>

        <!-- 加入按钮 -->
        <view class="group-action">
          <button class="join-btn">
            <text class="join-text">查看</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!pageState.isLoading && groups.length === 0}}">
      <image class="empty-icon" src="/images/empty-groups.png"></image>
      <text class="empty-text">暂无相关小组</text>
      <text class="empty-tip">试试其他分类或创建一个新小组吧</text>
      <button class="empty-action-btn" bindtap="onCreateGroup">创建小组</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{pagination.hasMore && !pageState.isLoading}}">
      <view class="load-more-content" wx:if="{{pagination.loading}}">
        <image class="load-more-icon" src="/images/loading-small.gif"></image>
        <text class="load-more-text">正在加载更多...</text>
      </view>
      <view class="load-more-content" wx:else>
        <text class="load-more-text">上拉加载更多</text>
      </view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!pagination.hasMore && groups.length > 0}}">
      <text class="no-more-text">没有更多小组了</text>
    </view>
  </scroll-view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal {{pageState.showFilter ? 'show' : ''}}" bindtap="onHideFilter">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">排序方式</text>
        <button class="close-btn" bindtap="onHideFilter">
          <image class="close-icon" src="/images/close.png"></image>
        </button>
      </view>
      
      <view class="sort-options">
        <view 
          class="sort-option {{filters.sortBy === item.key ? 'selected' : ''}}"
          wx:for="{{sortOptions}}"
          wx:key="key"
          data-sort="{{item.key}}"
          bindtap="onSelectSort"
        >
          <image class="option-icon" src="{{item.icon}}"></image>
          <text class="option-name">{{item.name}}</text>
          <view class="option-check">
            <image class="check-icon" src="/images/check.png" wx:if="{{filters.sortBy === item.key}}"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部导航占位 -->
  <view class="bottom-placeholder"></view>
</view>
