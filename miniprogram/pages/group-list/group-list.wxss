/* 兴趣小组列表页面样式 */

.group-list-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  background: #f8f9fa;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  width: 20px;
  height: 20px;
}

.create-btn {
  background: #722ed1;
}

.create-btn .action-icon {
  filter: brightness(0) invert(1);
}

/* 搜索框 */
.search-box {
  padding: 0 16px 12px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-box.show {
  max-height: 60px;
}

.search-input-box {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 20px;
}

.search-icon {
  width: 16px;
  height: 16px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.cancel-btn {
  padding: 4px 8px;
  background: transparent;
  border: none;
  font-size: 14px;
  color: #722ed1;
}

/* 分类导航 */
.category-nav {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 12px 16px;
  gap: 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-width: 60px;
}

.category-item:active {
  background: #f0f0f0;
}

.category-item.active {
  background: var(--category-color, #722ed1);
  color: white;
}

.category-icon {
  width: 24px;
  height: 24px;
}

.category-item.active .category-icon {
  filter: brightness(0) invert(1);
}

.category-name {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.category-item.active .category-name {
  color: white;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.quick-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: #f8f9fa;
  border: none;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.quick-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.quick-icon {
  width: 32px;
  height: 32px;
}

.quick-text {
  font-size: 14px;
  color: #666;
}

/* 小组列表 */
.group-scroll {
  flex: 1;
  padding: 0 16px;
}

.groups-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 20px;
}

.group-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.group-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

/* 小组头像 */
.group-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.group-avatar {
  width: 60px;
  height: 60px;
  border-radius: 12px;
}

.group-category-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  padding: 2px 6px;
  border-radius: 8px;
  background: #722ed1;
}

.category-badge-text {
  font-size: 10px;
  color: white;
}

/* 小组信息 */
.group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.group-privacy {
  display: flex;
  align-items: center;
}

.privacy-icon {
  width: 16px;
  height: 16px;
}

.group-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 小组标签 */
.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.group-tag {
  font-size: 12px;
  color: #722ed1;
  background: #f9f0ff;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 小组统计 */
.group-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-icon {
  width: 14px;
  height: 14px;
}

.stat-text {
  font-size: 12px;
  color: #999;
}

/* 加入按钮 */
.group-action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.join-btn {
  padding: 8px 16px;
  background: #722ed1;
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.join-btn:active {
  background: #5a1ea6;
  transform: scale(0.95);
}

.join-text {
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
}

.empty-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.empty-action-btn {
  padding: 10px 24px;
  background: #722ed1;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载更多 */
.load-more {
  padding: 20px;
  text-align: center;
}

.load-more-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.load-more-icon {
  width: 20px;
  height: 20px;
}

.load-more-text {
  font-size: 14px;
  color: #999;
}

.no-more {
  padding: 20px;
  text-align: center;
}

.no-more-text {
  font-size: 14px;
  color: #999;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.filter-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.sort-options {
  padding: 16px;
}

.sort-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.sort-option:active {
  background: #f0f0f0;
}

.sort-option.selected {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}

.option-icon {
  width: 24px;
  height: 24px;
}

.option-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.option-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 16px;
  height: 16px;
}

/* 加载状态 */
.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  width: 40px;
  height: 40px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 底部占位 */
.bottom-placeholder {
  height: 80px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .group-item {
    padding: 12px;
  }
  
  .group-avatar {
    width: 50px;
    height: 50px;
  }
  
  .quick-actions {
    padding: 12px;
  }
  
  .quick-btn {
    padding: 12px 8px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .group-list-container {
    background: #1a1a1a;
  }
  
  .page-header,
  .category-nav,
  .quick-actions,
  .group-item,
  .modal-content {
    background: #2d2d2d;
    color: white;
  }
  
  .page-title,
  .group-name,
  .modal-title,
  .option-name {
    color: white;
  }
  
  .search-input-box,
  .quick-btn,
  .action-btn {
    background: #404040;
  }
  
  .group-description,
  .stat-text,
  .load-more-text,
  .no-more-text,
  .loading-text {
    color: #ccc;
  }
}
