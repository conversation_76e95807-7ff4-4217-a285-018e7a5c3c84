/**
 * 兴趣小组列表页面
 * Task 3.6: 兴趣小组功能 - 小组发现与浏览
 */

const groupManager = require('../../utils/group-manager')

Page({
  data: {
    // 小组列表
    groups: [],
    
    // 分类列表
    categories: [
      { id: 'all', name: '全部', icon: '/images/group/all.png', color: '#666' },
      { id: 'hobby', name: '兴趣爱好', icon: '/images/group/hobby.png', color: '#722ed1' },
      { id: 'sports', name: '运动健身', icon: '/images/group/sports.png', color: '#52c41a' },
      { id: 'travel', name: '旅行摄影', icon: '/images/group/travel.png', color: '#1890ff' },
      { id: 'food', name: '美食烹饪', icon: '/images/group/food.png', color: '#fa541c' },
      { id: 'music', name: '音乐舞蹈', icon: '/images/group/music.png', color: '#eb2f96' },
      { id: 'reading', name: '读书学习', icon: '/images/group/reading.png', color: '#13c2c2' },
      { id: 'game', name: '游戏娱乐', icon: '/images/group/game.png', color: '#faad14' },
      { id: 'tech', name: '科技数码', icon: '/images/group/tech.png', color: '#2f54eb' }
    ],
    
    // 筛选条件
    filters: {
      category: 'all',
      sortBy: 'memberCount',
      keyword: ''
    },
    
    // 分页信息
    pagination: {
      page: 1,
      limit: 20,
      hasMore: true,
      loading: false
    },
    
    // 页面状态
    pageState: {
      isLoading: true,
      showSearch: false,
      showFilter: false,
      refreshing: false
    },
    
    // 排序选项
    sortOptions: [
      { key: 'memberCount', name: '成员数量', icon: '/images/sort/members.png' },
      { key: 'createdAt', name: '创建时间', icon: '/images/sort/time.png' },
      { key: 'activity', name: '活跃度', icon: '/images/sort/activity.png' }
    ]
  },

  onLoad() {
    this.loadGroupList()
  },

  onShow() {
    // 刷新数据
    this.refreshGroupList()
  },

  /**
   * 加载小组列表
   */
  async loadGroupList(refresh = false) {
    if (this.data.pagination.loading) return
    
    try {
      if (refresh) {
        this.setData({
          'pagination.page': 1,
          'pagination.hasMore': true,
          'pageState.refreshing': true
        })
      } else {
        this.setData({
          'pagination.loading': true
        })
      }
      
      const { category, sortBy, keyword } = this.data.filters
      const { page, limit } = this.data.pagination
      
      const options = {
        category: category === 'all' ? null : category,
        keyword: keyword,
        sortBy: sortBy,
        page: page,
        limit: limit
      }
      
      const result = groupManager.getGroupList(options)
      
      let groups = []
      if (refresh || page === 1) {
        groups = result.groups
      } else {
        groups = [...this.data.groups, ...result.groups]
      }
      
      this.setData({
        groups: groups,
        'pagination.hasMore': result.hasMore,
        'pagination.page': refresh ? 2 : page + 1,
        'pageState.isLoading': false
      })
      
      console.log(`📋 加载小组列表: ${result.groups.length}个`)
    } catch (error) {
      console.error('加载小组列表失败:', error)
      this.showError('加载失败，请重试')
    } finally {
      this.setData({
        'pagination.loading': false,
        'pageState.refreshing': false
      })
    }
  },

  /**
   * 刷新小组列表
   */
  refreshGroupList() {
    this.loadGroupList(true)
  },

  /**
   * 选择分类
   */
  onSelectCategory(event) {
    const category = event.currentTarget.dataset.category
    
    if (category === this.data.filters.category) return
    
    this.setData({
      'filters.category': category
    })
    
    this.refreshGroupList()
  },

  /**
   * 显示搜索
   */
  onShowSearch() {
    this.setData({
      'pageState.showSearch': true
    })
  },

  /**
   * 隐藏搜索
   */
  onHideSearch() {
    this.setData({
      'pageState.showSearch': false,
      'filters.keyword': ''
    })
    
    this.refreshGroupList()
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    this.setData({
      'filters.keyword': event.detail.value
    })
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.refreshGroupList()
  },

  /**
   * 显示筛选
   */
  onShowFilter() {
    this.setData({
      'pageState.showFilter': true
    })
  },

  /**
   * 隐藏筛选
   */
  onHideFilter() {
    this.setData({
      'pageState.showFilter': false
    })
  },

  /**
   * 选择排序方式
   */
  onSelectSort(event) {
    const sortBy = event.currentTarget.dataset.sort
    
    this.setData({
      'filters.sortBy': sortBy,
      'pageState.showFilter': false
    })
    
    this.refreshGroupList()
  },

  /**
   * 查看小组详情
   */
  onViewGroup(event) {
    const groupId = event.currentTarget.dataset.groupId
    
    wx.navigateTo({
      url: `/pages/group-detail/group-detail?groupId=${groupId}`
    })
  },

  /**
   * 创建小组
   */
  onCreateGroup() {
    wx.navigateTo({
      url: '/pages/create-group/create-group'
    })
  },

  /**
   * 我的小组
   */
  onMyGroups() {
    wx.navigateTo({
      url: '/pages/my-groups/my-groups'
    })
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.pagination.hasMore && !this.data.pagination.loading) {
      this.loadGroupList()
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshGroupList()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.onLoadMore()
  },

  /**
   * 获取分类名称
   */
  getCategoryName(categoryId) {
    const category = this.data.categories.find(cat => cat.id === categoryId)
    return category ? category.name : '未知分类'
  },

  /**
   * 格式化成员数量
   */
  formatMemberCount(count) {
    if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'k'
    }
    return count.toString()
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const month = 30 * day
    
    if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < month) {
      return Math.floor(diff / day) + '天前'
    } else {
      return new Date(timestamp).toLocaleDateString()
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
})
