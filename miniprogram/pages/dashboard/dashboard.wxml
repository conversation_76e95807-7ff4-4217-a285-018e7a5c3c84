<!--实时数据看板页面模板-->
<view class="dashboard-container">
  <!-- 页面头部 -->
  <view class="dashboard-header">
    <view class="header-title">
      <text class="title-text">实时数据看板</text>
      <view class="connection-status {{pageState.isConnected ? 'connected' : 'disconnected'}}">
        <text class="status-dot"></text>
        <text class="status-text">{{pageState.isConnected ? '已连接' : '未连接'}}</text>
      </view>
    </view>
    
    <view class="header-actions">
      <button class="action-btn" bindtap="onManualRefresh">
        <image class="btn-icon" src="/images/refresh.png"></image>
      </button>
      <button class="action-btn" bindtap="toggleAutoRefresh">
        <image class="btn-icon" src="/images/{{pageState.autoRefresh ? 'pause' : 'play'}}.png"></image>
      </button>
      <button class="action-btn" bindtap="onExportData">
        <image class="btn-icon" src="/images/export.png"></image>
      </button>
    </view>
  </view>

  <!-- 筛选条件 -->
  <view class="dashboard-filters">
    <view class="filter-group">
      <text class="filter-label">时间范围:</text>
      <picker mode="selector" range="{{['1小时', '24小时', '7天', '30天']}}" value="0" bindchange="onTimeRangeChange">
        <view class="filter-picker">
          <text>{{filters.timeRange === '1h' ? '1小时' : filters.timeRange === '24h' ? '24小时' : filters.timeRange === '7d' ? '7天' : '30天'}}</text>
          <image class="picker-arrow" src="/images/arrow-down.png"></image>
        </view>
      </picker>
    </view>
  </view>

  <!-- 异常警报 -->
  <view class="alert-section" wx:if="{{alerts.length > 0}}">
    <view class="section-title">
      <image class="title-icon" src="/images/warning.png"></image>
      <text>异常警报 ({{alerts.length}})</text>
    </view>
    <view class="alert-list">
      <view 
        class="alert-item {{item.severity}}" 
        wx:for="{{alerts}}" 
        wx:key="timestamp"
        data-index="{{index}}"
        bindtap="onAlertTap"
      >
        <view class="alert-content">
          <text class="alert-message">{{item.message}}</text>
          <text class="alert-time">{{item.timestamp}}</text>
        </view>
        <image class="alert-arrow" src="/images/arrow-right.png"></image>
      </view>
    </view>
  </view>

  <!-- 实时指标卡片 -->
  <view class="metrics-section">
    <view class="section-title">
      <image class="title-icon" src="/images/dashboard.png"></image>
      <text>实时指标</text>
    </view>
    
    <view class="metrics-grid">
      <!-- 活跃用户 -->
      <view 
        class="metric-card" 
        data-metric="activeUsers" 
        data-value="{{realTimeMetrics.activeUsers}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/users.png"></image>
          <text class="metric-name">活跃用户</text>
        </view>
        <view class="metric-value">{{formatNumber(realTimeMetrics.activeUsers)}}</view>
        <view class="metric-trend {{getTrendDirection(trendData.userGrowth)}}">
          <image class="trend-icon" src="/images/trend-{{getTrendDirection(trendData.userGrowth)}}.png"></image>
        </view>
      </view>

      <!-- 页面浏览量 -->
      <view 
        class="metric-card" 
        data-metric="pageViews" 
        data-value="{{realTimeMetrics.pageViews}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/views.png"></image>
          <text class="metric-name">页面浏览</text>
        </view>
        <view class="metric-value">{{formatNumber(realTimeMetrics.pageViews)}}</view>
        <view class="metric-status normal">
          <text class="status-text">正常</text>
        </view>
      </view>

      <!-- 新注册用户 -->
      <view 
        class="metric-card" 
        data-metric="newRegistrations" 
        data-value="{{realTimeMetrics.newRegistrations}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/new-user.png"></image>
          <text class="metric-name">新注册</text>
        </view>
        <view class="metric-value">{{formatNumber(realTimeMetrics.newRegistrations)}}</view>
        <view class="metric-status success">
          <text class="status-text">良好</text>
        </view>
      </view>

      <!-- 支付交易 -->
      <view 
        class="metric-card" 
        data-metric="paymentTransactions" 
        data-value="{{realTimeMetrics.paymentTransactions}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/payment.png"></image>
          <text class="metric-name">支付交易</text>
        </view>
        <view class="metric-value">{{formatNumber(realTimeMetrics.paymentTransactions)}}</view>
        <view class="metric-status success">
          <text class="status-text">活跃</text>
        </view>
      </view>

      <!-- 转化率 -->
      <view 
        class="metric-card {{getMetricStatus('conversionRate', realTimeMetrics.conversionRate)}}" 
        data-metric="conversionRate" 
        data-value="{{realTimeMetrics.conversionRate}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/conversion.png"></image>
          <text class="metric-name">转化率</text>
        </view>
        <view class="metric-value">{{realTimeMetrics.conversionRate.toFixed(1)}}%</view>
        <view class="metric-trend {{getTrendDirection(trendData.conversionTrend)}}">
          <image class="trend-icon" src="/images/trend-{{getTrendDirection(trendData.conversionTrend)}}.png"></image>
        </view>
      </view>

      <!-- 错误率 -->
      <view 
        class="metric-card {{getMetricStatus('errorRate', realTimeMetrics.errorRate)}}" 
        data-metric="errorRate" 
        data-value="{{realTimeMetrics.errorRate}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/error.png"></image>
          <text class="metric-name">错误率</text>
        </view>
        <view class="metric-value">{{realTimeMetrics.errorRate.toFixed(2)}}%</view>
        <view class="metric-trend {{getTrendDirection(trendData.errorTrend)}}">
          <image class="trend-icon" src="/images/trend-{{getTrendDirection(trendData.errorTrend)}}.png"></image>
        </view>
      </view>

      <!-- 平均会话时长 -->
      <view 
        class="metric-card" 
        data-metric="avgSessionDuration" 
        data-value="{{realTimeMetrics.avgSessionDuration}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/time.png"></image>
          <text class="metric-name">会话时长</text>
        </view>
        <view class="metric-value">{{realTimeMetrics.avgSessionDuration.toFixed(1)}}分</view>
        <view class="metric-status normal">
          <text class="status-text">正常</text>
        </view>
      </view>

      <!-- 网络质量 -->
      <view 
        class="metric-card {{getMetricStatus('networkQuality', realTimeMetrics.networkQuality)}}" 
        data-metric="networkQuality" 
        data-value="{{realTimeMetrics.networkQuality}}"
        bindtap="onMetricTap"
      >
        <view class="metric-header">
          <image class="metric-icon" src="/images/network.png"></image>
          <text class="metric-name">网络质量</text>
        </view>
        <view class="metric-value">{{realTimeMetrics.networkQuality.toFixed(0)}}%</view>
        <view class="metric-status {{getMetricStatus('networkQuality', realTimeMetrics.networkQuality)}}">
          <text class="status-text">{{realTimeMetrics.networkQuality > 80 ? '优秀' : realTimeMetrics.networkQuality > 60 ? '良好' : '较差'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势图表 -->
  <view class="charts-section">
    <view class="section-title">
      <image class="title-icon" src="/images/chart.png"></image>
      <text>趋势分析</text>
    </view>
    
    <!-- 用户增长趋势 -->
    <view class="chart-container">
      <view class="chart-header">
        <text class="chart-title">用户增长趋势</text>
        <text class="chart-subtitle">过去24小时</text>
      </view>
      <view class="chart-content">
        <canvas 
          canvas-id="userGrowthChart" 
          class="chart-canvas"
          style="width: {{chartConfig.width}}px; height: {{chartConfig.height}}px;"
        ></canvas>
      </view>
    </view>

    <!-- 收入趋势 -->
    <view class="chart-container">
      <view class="chart-header">
        <text class="chart-title">收入趋势</text>
        <text class="chart-subtitle">过去24小时</text>
      </view>
      <view class="chart-content">
        <canvas 
          canvas-id="revenueTrendChart" 
          class="chart-canvas"
          style="width: {{chartConfig.width}}px; height: {{chartConfig.height}}px;"
        ></canvas>
      </view>
    </view>
  </view>

  <!-- 页面底部信息 -->
  <view class="dashboard-footer">
    <text class="update-time">最后更新: {{pageState.lastUpdateTime ? new Date(pageState.lastUpdateTime).toLocaleTimeString() : '未更新'}}</text>
    <text class="auto-refresh">自动刷新: {{pageState.autoRefresh ? '开启' : '关闭'}}</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{pageState.isLoading}}">
    <view class="loading-content">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">正在加载数据...</text>
    </view>
  </view>
</view>
