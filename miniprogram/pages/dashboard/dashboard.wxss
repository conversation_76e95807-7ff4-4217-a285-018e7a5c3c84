/* 实时数据看板页面样式 */

.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 页面头部 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.connection-status.connected {
  color: #52c41a;
}

.connection-status.disconnected {
  color: #ff4d4f;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e6f7ff;
  transform: scale(1.05);
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

/* 筛选条件 */
.dashboard-filters {
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.filter-picker {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
}

.picker-arrow {
  width: 12px;
  height: 12px;
}

/* 异常警报 */
.alert-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.title-icon {
  width: 20px;
  height: 20px;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.alert-item.high {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.alert-item.medium {
  border-left-color: #faad14;
  background: #fffbe6;
}

.alert-item.low {
  border-left-color: #52c41a;
  background: #f6ffed;
}

.alert-item:active {
  transform: scale(0.98);
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.alert-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.alert-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

/* 实时指标 */
.metrics-section {
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.metric-card {
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:active {
  transform: scale(0.98);
}

.metric-card.success {
  border-left: 4px solid #52c41a;
}

.metric-card.warning {
  border-left: 4px solid #faad14;
}

.metric-card.error {
  border-left: 4px solid #ff4d4f;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.metric-icon {
  width: 20px;
  height: 20px;
}

.metric-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.metric-trend {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 20px;
  height: 20px;
}

.metric-trend.up {
  color: #52c41a;
}

.metric-trend.down {
  color: #ff4d4f;
}

.metric-trend.stable {
  color: #999;
}

.trend-icon {
  width: 100%;
  height: 100%;
}

.metric-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.metric-status.success {
  background: #f6ffed;
  color: #52c41a;
}

.metric-status.warning {
  background: #fffbe6;
  color: #faad14;
}

.metric-status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.metric-status.normal {
  background: #f0f0f0;
  color: #666;
}

/* 趋势图表 */
.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  margin-bottom: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chart-subtitle {
  font-size: 12px;
  color: #999;
}

.chart-content {
  position: relative;
}

.chart-canvas {
  width: 100%;
  border-radius: 8px;
}

/* 页面底部 */
.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #666;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  width: 40px;
  height: 40px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-value {
    font-size: 20px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .dashboard-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.metric-card {
  animation: fadeInUp 0.3s ease-out;
}

.connection-status.connected .status-dot {
  animation: pulse 2s infinite;
}

.alert-item {
  animation: fadeInUp 0.3s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .dashboard-header,
  .dashboard-filters,
  .metric-card,
  .chart-container,
  .dashboard-footer {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .title-text,
  .metric-value,
  .chart-title {
    color: white;
  }
  
  .filter-picker {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
}
