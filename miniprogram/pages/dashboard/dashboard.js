/**
 * 实时数据看板页面
 * Task 2.10: 实时数据看板开发 - 管理员数据监控界面
 */

const dashboardManager = require('../../utils/dashboard-manager')

Page({
  data: {
    // 实时指标数据
    realTimeMetrics: {
      activeUsers: 0,
      pageViews: 0,
      newRegistrations: 0,
      paymentTransactions: 0,
      errorRate: 0,
      conversionRate: 0,
      avgSessionDuration: 0,
      networkQuality: 0
    },
    
    // 趋势数据
    trendData: {
      userGrowth: [],
      revenueTrend: [],
      conversionTrend: [],
      errorTrend: []
    },
    
    // 异常警报
    alerts: [],
    
    // 筛选条件
    filters: {
      timeRange: '24h',
      metrics: 'all'
    },
    
    // 页面状态
    pageState: {
      isLoading: true,
      isConnected: false,
      lastUpdateTime: 0,
      autoRefresh: true
    },
    
    // 图表配置
    chartConfig: {
      width: 0,
      height: 200,
      colors: {
        primary: '#1890FF',
        success: '#52C41A',
        warning: '#FAAD14',
        error: '#FF4D4F'
      }
    }
  },

  onLoad(options) {
    console.log('📊 数据看板页面加载')
    this.initializePage()
  },

  onShow() {
    this.connectToDashboard()
  },

  onHide() {
    this.disconnectFromDashboard()
  },

  onUnload() {
    this.cleanup()
  },

  /**
   * 初始化页面
   */
  initializePage() {
    // 获取容器尺寸
    this.getContainerSize()
    
    // 加载初始数据
    this.loadInitialData()
    
    // 设置自动刷新
    this.setupAutoRefresh()
  },

  /**
   * 获取容器尺寸
   */
  getContainerSize() {
    const query = this.createSelectorQuery()
    
    query.select('.dashboard-container')
      .boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            'chartConfig.width': rect.width - 40 // 减去padding
          })
        }
      })
      .exec()
  },

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    try {
      this.setData({
        'pageState.isLoading': true
      })
      
      const dashboardData = dashboardManager.getDashboardData(this.data.filters)
      
      this.updatePageData(dashboardData)
      
      this.setData({
        'pageState.isLoading': false
      })
      
      console.log('📊 初始数据加载完成')
    } catch (error) {
      console.error('加载初始数据失败:', error)
      this.showError('数据加载失败，请重试')
    }
  },

  /**
   * 连接到数据看板
   */
  connectToDashboard() {
    try {
      // 创建客户端对象
      this.dashboardClient = {
        onUpdate: (data) => {
          this.handleDataUpdate(data)
        }
      }
      
      // 注册到看板管理器
      dashboardManager.registerClient(this.dashboardClient)
      
      this.setData({
        'pageState.isConnected': true
      })
      
      console.log('📊 已连接到数据看板')
    } catch (error) {
      console.error('连接数据看板失败:', error)
    }
  },

  /**
   * 断开数据看板连接
   */
  disconnectFromDashboard() {
    if (this.dashboardClient) {
      dashboardManager.unregisterClient(this.dashboardClient)
      this.dashboardClient = null
      
      this.setData({
        'pageState.isConnected': false
      })
      
      console.log('📊 已断开数据看板连接')
    }
  },

  /**
   * 处理数据更新
   * @param {Object} data 更新数据
   */
  handleDataUpdate(data) {
    this.updatePageData(data)
    
    this.setData({
      'pageState.lastUpdateTime': data.timestamp
    })
    
    console.log('📊 数据已更新')
  },

  /**
   * 更新页面数据
   * @param {Object} data 数据
   */
  updatePageData(data) {
    if (data.realTime) {
      // 更新实时指标
      this.setData({
        realTimeMetrics: {
          activeUsers: data.realTime.metrics.active_users || 0,
          pageViews: data.realTime.metrics.page_views || 0,
          newRegistrations: data.realTime.metrics.new_registrations || 0,
          paymentTransactions: data.realTime.metrics.payment_transactions || 0,
          errorRate: data.realTime.metrics.error_rate || 0,
          conversionRate: data.realTime.metrics.conversion_rate || 0,
          avgSessionDuration: data.realTime.metrics.avg_session_duration || 0,
          networkQuality: data.realTime.metrics.network_quality || 0
        }
      })
      
      // 更新趋势数据
      if (data.realTime.trends) {
        this.setData({
          trendData: {
            userGrowth: data.realTime.trends.user_growth || [],
            revenueTrend: data.realTime.trends.revenue_trend || [],
            conversionTrend: data.realTime.trends.conversion_trend || [],
            errorTrend: data.realTime.trends.error_trend || []
          }
        })
      }
      
      // 更新异常警报
      this.setData({
        alerts: data.realTime.alerts || []
      })
    }
  },

  /**
   * 设置自动刷新
   */
  setupAutoRefresh() {
    if (this.data.pageState.autoRefresh) {
      this.refreshTimer = setInterval(() => {
        this.refreshData()
      }, 30000) // 30秒刷新一次
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    if (!this.data.pageState.isConnected) {
      await this.loadInitialData()
    }
  },

  /**
   * 处理时间范围变化
   */
  onTimeRangeChange(event) {
    const timeRange = event.detail.value
    
    this.setData({
      'filters.timeRange': timeRange
    })
    
    this.loadInitialData()
  },

  /**
   * 处理指标筛选变化
   */
  onMetricsFilterChange(event) {
    const metrics = event.detail.value
    
    this.setData({
      'filters.metrics': metrics
    })
    
    this.loadInitialData()
  },

  /**
   * 切换自动刷新
   */
  toggleAutoRefresh() {
    const autoRefresh = !this.data.pageState.autoRefresh
    
    this.setData({
      'pageState.autoRefresh': autoRefresh
    })
    
    if (autoRefresh) {
      this.setupAutoRefresh()
    } else {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    }
  },

  /**
   * 手动刷新
   */
  onManualRefresh() {
    this.refreshData()
    
    // 显示刷新提示
    wx.showToast({
      title: '数据已刷新',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 查看异常详情
   */
  onAlertTap(event) {
    const alertIndex = event.currentTarget.dataset.index
    const alert = this.data.alerts[alertIndex]
    
    if (alert) {
      wx.showModal({
        title: '异常详情',
        content: `${alert.message}\n\n当前值: ${alert.value}\n阈值: ${alert.threshold}\n时间: ${new Date(alert.timestamp).toLocaleString()}`,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },

  /**
   * 查看指标详情
   */
  onMetricTap(event) {
    const metric = event.currentTarget.dataset.metric
    const value = event.currentTarget.dataset.value
    
    wx.showModal({
      title: '指标详情',
      content: `${this.getMetricName(metric)}: ${this.formatMetricValue(metric, value)}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 导出数据
   */
  onExportData() {
    try {
      const exportData = {
        timestamp: Date.now(),
        metrics: this.data.realTimeMetrics,
        trends: this.data.trendData,
        alerts: this.data.alerts
      }
      
      // 模拟导出功能
      wx.showToast({
        title: '数据导出成功',
        icon: 'success'
      })
      
      console.log('导出数据:', exportData)
    } catch (error) {
      console.error('导出数据失败:', error)
      this.showError('导出失败，请重试')
    }
  },

  /**
   * 获取指标名称
   * @param {string} metric 指标键
   */
  getMetricName(metric) {
    const names = {
      activeUsers: '活跃用户',
      pageViews: '页面浏览量',
      newRegistrations: '新注册用户',
      paymentTransactions: '支付交易',
      errorRate: '错误率',
      conversionRate: '转化率',
      avgSessionDuration: '平均会话时长',
      networkQuality: '网络质量'
    }
    
    return names[metric] || metric
  },

  /**
   * 格式化指标值
   * @param {string} metric 指标键
   * @param {number} value 指标值
   */
  formatMetricValue(metric, value) {
    switch (metric) {
      case 'errorRate':
      case 'conversionRate':
      case 'networkQuality':
        return `${value.toFixed(1)}%`
      case 'avgSessionDuration':
        return `${value.toFixed(1)}分钟`
      case 'activeUsers':
      case 'pageViews':
      case 'newRegistrations':
      case 'paymentTransactions':
        return this.formatNumber(value)
      default:
        return value.toString()
    }
  },

  /**
   * 格式化数字
   * @param {number} num 数字
   */
  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    } else {
      return num.toString()
    }
  },

  /**
   * 获取指标状态
   * @param {string} metric 指标键
   * @param {number} value 指标值
   */
  getMetricStatus(metric, value) {
    switch (metric) {
      case 'errorRate':
        if (value > 5) return 'error'
        if (value > 2) return 'warning'
        return 'success'
      case 'conversionRate':
        if (value < 30) return 'warning'
        if (value > 50) return 'success'
        return 'normal'
      case 'networkQuality':
        if (value > 80) return 'success'
        if (value > 60) return 'warning'
        return 'error'
      default:
        return 'normal'
    }
  },

  /**
   * 获取趋势方向
   * @param {Array} trendData 趋势数据
   */
  getTrendDirection(trendData) {
    if (!trendData || trendData.length < 2) {
      return 'stable'
    }
    
    const latest = trendData[trendData.length - 1].value
    const previous = trendData[trendData.length - 2].value
    
    if (latest > previous * 1.05) return 'up'
    if (latest < previous * 0.95) return 'down'
    return 'stable'
  },

  /**
   * 显示错误信息
   * @param {string} message 错误消息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  },

  /**
   * 清理资源
   */
  cleanup() {
    this.disconnectFromDashboard()
    
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }
})
