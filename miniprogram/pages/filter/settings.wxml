<!--pages/filter/settings.wxml-->
<view class="filter-settings-container">
  <!-- 顶部导航 -->
  <view class="filter-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">筛选设置</text>
      <text class="reset-btn" bindtap="resetFilters">重置</text>
    </view>
  </view>

  <!-- 筛选设置表单 -->
  <scroll-view class="filter-form" scroll-y>
    <!-- 基本条件 -->
    <view class="filter-section">
      <view class="section-header">
        <text class="section-title">基本条件</text>
        <text class="section-desc">设置你的理想对象条件</text>
      </view>

      <!-- 年龄范围 -->
      <view class="filter-item">
        <text class="filter-label">年龄范围</text>
        <view class="age-range-container">
          <view class="age-range-slider">
            <slider 
              min="18" 
              max="60" 
              value="{{filterData.min_age}}"
              bindchange="onMinAgeChange"
              activeColor="#667eea"
              backgroundColor="#f0f0f0"
              block-size="20"
            />
            <view class="age-labels">
              <text class="age-label">{{filterData.min_age}}岁</text>
              <text class="age-separator">-</text>
              <text class="age-label">{{filterData.max_age}}岁</text>
            </view>
          </view>
          <view class="age-range-slider">
            <slider 
              min="18" 
              max="60" 
              value="{{filterData.max_age}}"
              bindchange="onMaxAgeChange"
              activeColor="#667eea"
              backgroundColor="#f0f0f0"
              block-size="20"
            />
          </view>
        </view>
      </view>

      <!-- 距离范围 -->
      <view class="filter-item">
        <text class="filter-label">距离范围</text>
        <view class="distance-container">
          <slider 
            min="1" 
            max="100" 
            value="{{filterData.max_distance}}"
            bindchange="onDistanceChange"
            activeColor="#667eea"
            backgroundColor="#f0f0f0"
            block-size="20"
          />
          <text class="distance-value">
            {{filterData.max_distance === 100 ? '不限' : filterData.max_distance + 'km'}}
          </text>
        </view>
      </view>

      <!-- 身高范围 -->
      <view class="filter-item">
        <text class="filter-label">身高要求</text>
        <view class="height-options">
          <view 
            class="height-option {{filterData.height_range === item.value ? 'selected' : ''}}"
            wx:for="{{heightOptions}}" 
            wx:key="value"
            bindtap="selectHeightRange"
            data-value="{{item.value}}"
          >
            <text class="option-text">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 教育背景 -->
    <view class="filter-section">
      <view class="section-header">
        <text class="section-title">教育背景</text>
      </view>

      <!-- 学历要求 -->
      <view class="filter-item">
        <text class="filter-label">学历要求</text>
        <view class="education-options">
          <view 
            class="education-option {{filterData.education_levels.includes(item.value) ? 'selected' : ''}}"
            wx:for="{{educationOptions}}" 
            wx:key="value"
            bindtap="toggleEducation"
            data-value="{{item.value}}"
          >
            <text class="option-text">{{item.label}}</text>
            <text class="option-check" wx:if="{{filterData.education_levels.includes(item.value)}}">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 职业信息 -->
    <view class="filter-section">
      <view class="section-header">
        <text class="section-title">职业信息</text>
      </view>

      <!-- 职业类型 -->
      <view class="filter-item">
        <text class="filter-label">职业类型</text>
        <view class="occupation-grid">
          <view 
            class="occupation-tag {{filterData.occupation_types.includes(item.value) ? 'selected' : ''}}"
            wx:for="{{occupationTypes}}" 
            wx:key="value"
            bindtap="toggleOccupation"
            data-value="{{item.value}}"
          >
            <text class="tag-text">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 兴趣爱好 -->
    <view class="filter-section">
      <view class="section-header">
        <text class="section-title">兴趣爱好</text>
        <text class="section-desc">选择共同兴趣，提高匹配度</text>
      </view>

      <!-- 兴趣标签 -->
      <view class="filter-item">
        <text class="filter-label">兴趣标签</text>
        <view class="interests-grid">
          <view 
            class="interest-tag {{filterData.interests.includes(item) ? 'selected' : ''}}"
            wx:for="{{interestOptions}}" 
            wx:key="*this"
            bindtap="toggleInterest"
            data-interest="{{item}}"
          >
            <text class="tag-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 高级筛选 -->
    <view class="filter-section">
      <view class="section-header">
        <text class="section-title">高级筛选</text>
        <view class="vip-badge" wx:if="{{!userInfo.is_vip}}">
          <text class="badge-text">VIP专享</text>
        </view>
      </view>

      <!-- VIP用户 -->
      <view class="filter-item">
        <text class="filter-label">VIP用户</text>
        <view class="filter-switch">
          <text class="switch-desc">仅显示VIP用户</text>
          <switch 
            checked="{{filterData.vip_only}}" 
            bindchange="onVipOnlyChange"
            color="#667eea"
            disabled="{{!userInfo.is_vip}}"
          />
        </view>
      </view>

      <!-- 认证用户 -->
      <view class="filter-item">
        <text class="filter-label">认证用户</text>
        <view class="filter-switch">
          <text class="switch-desc">仅显示已认证用户</text>
          <switch 
            checked="{{filterData.verified_only}}" 
            bindchange="onVerifiedOnlyChange"
            color="#667eea"
            disabled="{{!userInfo.is_vip}}"
          />
        </view>
      </view>

      <!-- 在线状态 -->
      <view class="filter-item">
        <text class="filter-label">在线状态</text>
        <view class="filter-switch">
          <text class="switch-desc">仅显示在线用户</text>
          <switch 
            checked="{{filterData.online_only}}" 
            bindchange="onOnlineOnlyChange"
            color="#667eea"
            disabled="{{!userInfo.is_vip}}"
          />
        </view>
      </view>

      <!-- 有照片 -->
      <view class="filter-item">
        <text class="filter-label">照片要求</text>
        <view class="filter-switch">
          <text class="switch-desc">仅显示有照片的用户</text>
          <switch 
            checked="{{filterData.has_photos}}" 
            bindchange="onHasPhotosChange"
            color="#667eea"
            disabled="{{!userInfo.is_vip}}"
          />
        </view>
      </view>
    </view>

    <!-- VIP升级提示 -->
    <view class="vip-upgrade-section" wx:if="{{!userInfo.is_vip}}">
      <view class="upgrade-card">
        <view class="upgrade-content">
          <text class="upgrade-icon">👑</text>
          <view class="upgrade-info">
            <text class="upgrade-title">升级VIP解锁高级筛选</text>
            <text class="upgrade-desc">更精准的匹配，更高效的寻找</text>
          </view>
        </view>
        <button class="upgrade-btn" bindtap="goToVip">
          立即升级
        </button>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <button 
        class="save-btn {{hasChanges ? 'active' : ''}}"
        bindtap="saveFilters"
        disabled="{{!hasChanges || saving}}"
      >
        {{saving ? '保存中...' : '保存设置'}}
      </button>
    </view>
  </scroll-view>
</view>

<!-- 重置确认弹窗 -->
<view class="reset-modal" wx:if="{{showResetModal}}" bindtap="hideResetModal">
  <view class="reset-content" catchtap="">
    <view class="reset-header">
      <text class="reset-title">重置筛选条件</text>
      <text class="reset-close" bindtap="hideResetModal">×</text>
    </view>
    
    <view class="reset-body">
      <text class="reset-desc">确定要重置所有筛选条件吗？重置后将恢复到默认设置。</text>
    </view>
    
    <view class="reset-footer">
      <button class="reset-cancel" bindtap="hideResetModal">取消</button>
      <button class="reset-confirm" bindtap="confirmReset">确认重置</button>
    </view>
  </view>
</view>
