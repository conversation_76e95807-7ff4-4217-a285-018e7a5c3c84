// pages/filter/settings.js
Page({
  data: {
    // 筛选条件
    filters: {
      ageRange: [20, 35],
      heightRange: [160, 180],
      distance: 10,
      education: '',
      income: '',
      profession: '',
      interests: [],
      onlyVerified: false,
      onlyVip: false,
      onlyOnline: false,
      onlyWithPhoto: true
    },
    
    // 选择器数据
    educationOptions: [
      '不限', '高中及以下', '大专', '本科', '硕士', '博士'
    ],
    educationIndex: 0,
    
    incomeOptions: [
      '不限', '3000以下', '3000-5000', '5000-8000', 
      '8000-12000', '12000-20000', '20000-30000', '30000以上'
    ],
    incomeIndex: 0,
    
    professionOptions: [
      '不限', '学生', '教师', '医生', '律师', '工程师', 
      '设计师', '销售', '金融', '创业', '自由职业', '其他'
    ],
    professionIndex: 0,
    
    // 兴趣标签
    interestCategories: [
      {
        name: '运动健身',
        tags: ['跑步', '健身', '游泳', '瑜伽', '篮球', '足球', '羽毛球', '乒乓球']
      },
      {
        name: '文艺爱好',
        tags: ['阅读', '写作', '绘画', '摄影', '音乐', '舞蹈', '书法', '手工']
      },
      {
        name: '休闲娱乐',
        tags: ['电影', '游戏', '旅行', '美食', '购物', 'KTV', '桌游', '密室逃脱']
      },
      {
        name: '学习成长',
        tags: ['学习', '投资', '创业', '技术', '语言', '考证', '培训', '读书会']
      }
    ],
    
    // 距离预设
    distancePresets: [1, 3, 5, 10, 20, 50, 100],
    
    // 保存状态
    saving: false
  },

  onLoad() {
    this.loadFilterSettings()
  },

  // 加载筛选设置
  loadFilterSettings() {
    try {
      const savedFilters = wx.getStorageSync('filter_settings')
      if (savedFilters) {
        // 设置选择器索引
        const educationIndex = savedFilters.education ? 
          this.data.educationOptions.indexOf(savedFilters.education) : 0
        const incomeIndex = savedFilters.income ? 
          this.data.incomeOptions.indexOf(savedFilters.income) : 0
        const professionIndex = savedFilters.profession ? 
          this.data.professionOptions.indexOf(savedFilters.profession) : 0
        
        this.setData({
          filters: { ...this.data.filters, ...savedFilters },
          educationIndex,
          incomeIndex,
          professionIndex
        })
      }
    } catch (error) {
      console.error('加载筛选设置失败:', error)
    }
  },

  // 年龄范围变化
  onAgeRangeChange(e) {
    const { type } = e.currentTarget.dataset
    const value = parseInt(e.detail.value)
    
    if (type === 'min') {
      this.setData({
        'filters.ageRange[0]': value
      })
    } else {
      this.setData({
        'filters.ageRange[1]': value
      })
    }
  },

  // 身高范围变化
  onHeightRangeChange(e) {
    const { type } = e.currentTarget.dataset
    const value = parseInt(e.detail.value)
    
    if (type === 'min') {
      this.setData({
        'filters.heightRange[0]': value
      })
    } else {
      this.setData({
        'filters.heightRange[1]': value
      })
    }
  },

  // 距离变化
  onDistanceChange(e) {
    this.setData({
      'filters.distance': parseInt(e.detail.value)
    })
  },

  // 距离预设选择
  selectDistancePreset(e) {
    const { distance } = e.currentTarget.dataset
    this.setData({
      'filters.distance': distance
    })
  },

  // 学历选择
  onEducationChange(e) {
    const index = e.detail.value
    this.setData({
      educationIndex: index,
      'filters.education': this.data.educationOptions[index]
    })
  },

  // 收入选择
  onIncomeChange(e) {
    const index = e.detail.value
    this.setData({
      incomeIndex: index,
      'filters.income': this.data.incomeOptions[index]
    })
  },

  // 职业选择
  onProfessionChange(e) {
    const index = e.detail.value
    this.setData({
      professionIndex: index,
      'filters.profession': this.data.professionOptions[index]
    })
  },

  // 开关切换
  toggleSwitch(e) {
    const { field } = e.currentTarget.dataset
    const value = e.detail.value
    
    this.setData({
      [`filters.${field}`]: value
    })
  },

  // 兴趣标签选择
  toggleInterest(e) {
    const { tag } = e.currentTarget.dataset
    const interests = [...this.data.filters.interests]
    const index = interests.indexOf(tag)
    
    if (index > -1) {
      interests.splice(index, 1)
    } else {
      if (interests.length < 10) {
        interests.push(tag)
      } else {
        wx.showToast({
          title: '最多选择10个兴趣',
          icon: 'none'
        })
        return
      }
    }
    
    this.setData({
      'filters.interests': interests
    })
  },

  // 重置筛选条件
  resetFilters() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有筛选条件吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            filters: {
              ageRange: [18, 50],
              heightRange: [150, 190],
              distance: 50,
              education: '',
              income: '',
              profession: '',
              interests: [],
              onlyVerified: false,
              onlyVip: false,
              onlyOnline: false,
              onlyWithPhoto: false
            },
            educationIndex: 0,
            incomeIndex: 0,
            professionIndex: 0
          })
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 保存筛选设置
  async saveFilters() {
    try {
      this.setData({ saving: true })
      
      // 保存到本地存储
      wx.setStorageSync('filter_settings', this.data.filters)
      
      // 模拟保存到服务器
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      this.setData({ saving: false })
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      
    } catch (error) {
      console.error('保存失败:', error)
      this.setData({ saving: false })
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 预览筛选结果
  previewResults() {
    wx.navigateTo({
      url: '/pages/discover/discover?preview=true'
    })
  }
})
