/* pages/filter/settings.wxss */
.filter-container {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 120rpx;
}

/* 筛选部分 */
.filter-section {
  background: white;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx 40rpx 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  border-bottom: 1rpx solid #f3f4f6;
}

.filter-item {
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.filter-item:last-child {
  border-bottom: none;
}

.filter-label {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 20rpx;
  display: block;
}

/* 范围滑块 */
.range-container {
  margin-bottom: 20rpx;
}

.range-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.range-value {
  font-size: 26rpx;
  color: #8b5cf6;
  font-weight: bold;
}

.range-sliders {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.range-slider-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.slider-label {
  width: 80rpx;
  font-size: 24rpx;
  color: #6b7280;
  flex-shrink: 0;
}

.range-slider {
  flex: 1;
}

.slider-value {
  width: 80rpx;
  text-align: right;
  font-size: 24rpx;
  color: #1f2937;
  flex-shrink: 0;
}

/* 距离设置 */
.distance-container {
  margin-bottom: 20rpx;
}

.distance-display {
  text-align: center;
  margin-bottom: 20rpx;
}

.distance-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #8b5cf6;
}

.distance-unit {
  font-size: 24rpx;
  color: #6b7280;
  margin-left: 8rpx;
}

.distance-slider {
  margin-bottom: 20rpx;
}

.distance-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.distance-preset {
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: 2rpx solid transparent;
}

.distance-preset.selected {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

/* 选择器 */
.picker-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #1f2937;
}

.picker-placeholder {
  color: #9ca3af;
}

.picker-arrow {
  color: #9ca3af;
  font-size: 24rpx;
}

/* 开关设置 */
.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
}

.switch-label {
  font-size: 28rpx;
  color: #374151;
}

.switch-desc {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 4rpx;
}

/* 兴趣标签 */
.interests-section {
  background: white;
  padding: 30rpx 40rpx;
  margin-bottom: 20rpx;
}

.interests-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 30rpx;
}

.interest-category {
  margin-bottom: 30rpx;
}

.category-name {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 20rpx;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.interest-tag {
  padding: 12rpx 24rpx;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.interest-tag.selected {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

.selected-count {
  text-align: center;
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 20rpx;
}

/* 操作按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.reset-btn {
  flex: 1;
  height: 80rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.preview-btn {
  flex: 1;
  height: 80rpx;
  background: #e5e7eb;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.save-btn {
  flex: 2;
  height: 80rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.save-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.save-btn.loading {
  background: #d1d5db;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .filter-container {
    padding-bottom: 100rpx;
  }
  
  .filter-item {
    padding: 24rpx 30rpx;
  }
}
