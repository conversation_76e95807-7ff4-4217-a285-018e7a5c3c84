/* 反馈设置页面样式 */

.feedback-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
}

.page-title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 通用区域样式 */
.settings-section,
.test-section,
.stats-section,
.actions-section {
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.title-icon {
  width: 20px;
  height: 20px;
}

/* 设置项样式 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.setting-status {
  margin-top: 4px;
}

.status-text {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.status-text.unsupported {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 音量控制 */
.volume-control {
  width: 200px;
  margin-top: 8px;
}

/* 测试按钮网格 */
.test-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.test-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  color: #495057;
  transition: all 0.2s ease;
}

.test-btn:not([disabled]):hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.test-btn:not([disabled]):active {
  transform: translateY(0);
}

.test-btn[disabled] {
  opacity: 0.5;
  background: #f5f5f5;
  color: #ccc;
}

.test-btn.testing {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.test-btn.primary {
  background: #722ed1;
  border-color: #722ed1;
  color: white;
}

.test-btn.primary:not([disabled]):hover {
  background: #531dab;
}

.test-icon {
  width: 24px;
  height: 24px;
}

.test-text {
  font-size: 12px;
  text-align: center;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #722ed1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 热门场景 */
.top-scenarios {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.scenarios-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.scenarios-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scenario-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.scenario-name {
  font-size: 12px;
  color: #333;
}

.scenario-count {
  font-size: 12px;
  color: #722ed1;
  font-weight: 500;
}

/* 操作按钮 */
.actions-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.secondary {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
}

.action-btn.secondary:hover {
  background: #e9ecef;
}

.action-btn.danger {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
}

.action-btn.danger:hover {
  background: #ffccc7;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 底部说明 */
.footer-note {
  margin-top: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}

.note-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .feedback-settings-container {
    padding: 12px;
  }
  
  .page-header {
    padding: 16px;
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .settings-section,
  .test-section,
  .stats-section,
  .actions-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .volume-control {
    width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.settings-section,
.test-section,
.stats-section,
.actions-section {
  animation: fadeInUp 0.3s ease-out;
}

.test-btn.testing {
  animation: pulse 1s infinite;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .feedback-settings-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .settings-section,
  .test-section,
  .stats-section,
  .actions-section,
  .footer-note {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .section-title,
  .setting-name,
  .scenarios-title {
    color: white;
  }
  
  .setting-desc,
  .note-text {
    color: rgba(255, 255, 255, 0.7);
  }
  
  .test-btn {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
  
  .stat-item {
    background: rgba(255, 255, 255, 0.1);
  }
}
