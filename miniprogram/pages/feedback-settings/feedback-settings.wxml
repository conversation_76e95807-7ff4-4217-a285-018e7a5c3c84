<!--反馈设置页面模板-->
<view class="feedback-settings-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">交互反馈设置</text>
    <text class="page-subtitle">个性化您的操作反馈体验</text>
  </view>

  <!-- 反馈设置区域 -->
  <view class="settings-section">
    <view class="section-title">
      <image class="title-icon" src="/images/settings.png"></image>
      <text>反馈设置</text>
    </view>

    <!-- 触觉反馈设置 -->
    <view class="setting-item">
      <view class="setting-info">
        <view class="setting-name">触觉反馈</view>
        <view class="setting-desc">通过震动提供操作确认</view>
        <view class="setting-status" wx:if="{{!capabilities.hapticSupported}}">
          <text class="status-text unsupported">设备不支持</text>
        </view>
      </view>
      <switch 
        checked="{{settings.hapticEnabled}}" 
        disabled="{{!capabilities.hapticSupported}}"
        bindchange="onHapticToggle"
        color="#722ed1"
      />
    </view>

    <!-- 音效反馈设置 -->
    <view class="setting-item">
      <view class="setting-info">
        <view class="setting-name">音效反馈</view>
        <view class="setting-desc">通过声音提供操作提示</view>
      </view>
      <switch 
        checked="{{settings.audioEnabled}}" 
        bindchange="onAudioToggle"
        color="#722ed1"
      />
    </view>

    <!-- 音量调节 -->
    <view class="setting-item" wx:if="{{settings.audioEnabled}}">
      <view class="setting-info">
        <view class="setting-name">音效音量</view>
        <view class="setting-desc">调节音效反馈的音量大小</view>
      </view>
      <view class="volume-control">
        <slider 
          value="{{settings.audioVolume * 100}}" 
          min="0" 
          max="100" 
          step="10"
          show-value
          bindchange="onVolumeChange"
          activeColor="#722ed1"
          backgroundColor="#f0f0f0"
        />
      </view>
    </view>
  </view>

  <!-- 反馈测试区域 -->
  <view class="test-section">
    <view class="section-title">
      <image class="title-icon" src="/images/test.png"></image>
      <text>反馈测试</text>
    </view>

    <view class="test-grid">
      <!-- 触觉反馈测试 -->
      <button 
        class="test-btn {{testStatus.hapticTesting ? 'testing' : ''}}" 
        bindtap="onTestHaptic"
        disabled="{{!capabilities.hapticSupported || !settings.hapticEnabled}}"
      >
        <image class="test-icon" src="/images/haptic.png"></image>
        <text class="test-text">{{testStatus.hapticTesting ? '测试中...' : '触觉反馈'}}</text>
      </button>

      <!-- 音效反馈测试 -->
      <button 
        class="test-btn {{testStatus.audioTesting ? 'testing' : ''}}" 
        bindtap="onTestAudio"
        disabled="{{!settings.audioEnabled}}"
      >
        <image class="test-icon" src="/images/audio.png"></image>
        <text class="test-text">{{testStatus.audioTesting ? '测试中...' : '音效反馈'}}</text>
      </button>

      <!-- 视觉反馈测试 -->
      <button 
        class="test-btn {{testStatus.visualTesting ? 'testing' : ''}}" 
        bindtap="onTestVisual"
      >
        <image class="test-icon" src="/images/visual.png"></image>
        <text class="test-text">{{testStatus.visualTesting ? '测试中...' : '视觉反馈'}}</text>
      </button>

      <!-- 全部测试 -->
      <button class="test-btn primary" bindtap="onTestAll">
        <image class="test-icon" src="/images/test-all.png"></image>
        <text class="test-text">全部测试</text>
      </button>
    </view>
  </view>

  <!-- 统计信息区域 -->
  <view class="stats-section">
    <view class="section-title">
      <image class="title-icon" src="/images/stats.png"></image>
      <text>使用统计</text>
    </view>

    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{stats.totalFeedbacks}}</text>
        <text class="stat-label">总反馈次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.coverageRate.toFixed(1)}}%</text>
        <text class="stat-label">功能覆盖率</text>
      </view>
    </view>

    <!-- 热门场景 -->
    <view class="top-scenarios" wx:if="{{stats.topScenarios.length > 0}}">
      <text class="scenarios-title">热门反馈场景</text>
      <view class="scenarios-list">
        <view 
          class="scenario-item" 
          wx:for="{{stats.topScenarios}}" 
          wx:key="index"
        >
          <text class="scenario-name">{{item[0]}}</text>
          <text class="scenario-count">{{item[1]}}次</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="actions-section">
    <button class="action-btn secondary" bindtap="onViewStats">
      <image class="btn-icon" src="/images/chart.png"></image>
      <text>详细统计</text>
    </button>
    
    <button class="action-btn secondary" bindtap="onFeedbackHelp">
      <image class="btn-icon" src="/images/help.png"></image>
      <text>使用说明</text>
    </button>
    
    <button class="action-btn danger" bindtap="onResetSettings">
      <image class="btn-icon" src="/images/reset.png"></image>
      <text>重置设置</text>
    </button>
  </view>

  <!-- 说明文字 -->
  <view class="footer-note">
    <text class="note-text">
      交互反馈可以提升操作体验，您可以根据个人喜好和使用环境进行调整。
      建议在安静环境中关闭音效反馈，在需要专注时可关闭触觉反馈。
    </text>
  </view>
</view>
