/**
 * 反馈设置页面
 * Task 2.12: 交互反馈系统完善 - 用户反馈偏好设置
 */

const feedbackManager = require('../../utils/feedback-manager')

Page({
  data: {
    // 反馈设置
    settings: {
      hapticEnabled: true,
      audioEnabled: false,
      audioVolume: 0.5
    },
    
    // 设备能力
    capabilities: {
      hapticSupported: false
    },
    
    // 测试状态
    testStatus: {
      hapticTesting: false,
      audioTesting: false,
      visualTesting: false
    },
    
    // 统计信息
    stats: {
      totalFeedbacks: 0,
      coverageRate: 0,
      topScenarios: []
    }
  },

  onLoad() {
    this.loadSettings()
    this.loadStats()
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const preferences = feedbackManager.getUserPreferences()
      
      this.setData({
        settings: {
          hapticEnabled: preferences.hapticEnabled,
          audioEnabled: preferences.audioEnabled,
          audioVolume: preferences.audioVolume
        },
        capabilities: {
          hapticSupported: preferences.hapticSupported
        }
      })
    } catch (error) {
      console.error('加载反馈设置失败:', error)
    }
  },

  /**
   * 加载统计信息
   */
  loadStats() {
    try {
      const stats = feedbackManager.getFeedbackStats()
      
      this.setData({
        stats: {
          totalFeedbacks: stats.totalFeedbacks,
          coverageRate: stats.coverage.coverageRate,
          topScenarios: stats.scenarioUsageArray.slice(0, 5)
        }
      })
    } catch (error) {
      console.error('加载反馈统计失败:', error)
    }
  },

  /**
   * 触觉反馈开关
   */
  onHapticToggle(event) {
    const enabled = event.detail.value
    
    this.setData({
      'settings.hapticEnabled': enabled
    })
    
    this.saveSettings()
    
    // 测试反馈
    if (enabled) {
      feedbackManager.trigger('toggle_switch', {
        visual: null,
        audio: null
      })
    }
  },

  /**
   * 音效反馈开关
   */
  onAudioToggle(event) {
    const enabled = event.detail.value
    
    this.setData({
      'settings.audioEnabled': enabled
    })
    
    this.saveSettings()
    
    // 测试反馈
    if (enabled) {
      feedbackManager.trigger('toggle_switch', {
        haptic: null,
        visual: null
      })
    }
  },

  /**
   * 音量调节
   */
  onVolumeChange(event) {
    const volume = event.detail.value / 100
    
    this.setData({
      'settings.audioVolume': volume
    })
    
    this.saveSettings()
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      feedbackManager.setUserPreferences(this.data.settings)
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('保存反馈设置失败:', error)
      
      wx.showToast({
        title: '保存失败',
        icon: 'error',
        duration: 2000
      })
    }
  },

  /**
   * 测试触觉反馈
   */
  onTestHaptic() {
    if (!this.data.capabilities.hapticSupported) {
      wx.showToast({
        title: '设备不支持触觉反馈',
        icon: 'none',
        duration: 2000
      })
      return
    }
    
    this.setData({
      'testStatus.hapticTesting': true
    })
    
    // 测试不同强度的触觉反馈
    setTimeout(() => {
      feedbackManager.testFeedback('haptic')
    }, 100)
    
    setTimeout(() => {
      this.setData({
        'testStatus.hapticTesting': false
      })
    }, 1000)
  },

  /**
   * 测试音效反馈
   */
  onTestAudio() {
    if (!this.data.settings.audioEnabled) {
      wx.showToast({
        title: '请先开启音效反馈',
        icon: 'none',
        duration: 2000
      })
      return
    }
    
    this.setData({
      'testStatus.audioTesting': true
    })
    
    setTimeout(() => {
      feedbackManager.testFeedback('audio')
    }, 100)
    
    setTimeout(() => {
      this.setData({
        'testStatus.audioTesting': false
      })
    }, 1000)
  },

  /**
   * 测试视觉反馈
   */
  onTestVisual() {
    this.setData({
      'testStatus.visualTesting': true
    })
    
    setTimeout(() => {
      feedbackManager.testFeedback('visual')
    }, 100)
    
    setTimeout(() => {
      this.setData({
        'testStatus.visualTesting': false
      })
    }, 1000)
  },

  /**
   * 测试全部反馈
   */
  onTestAll() {
    feedbackManager.testFeedback('all')
    
    wx.showToast({
      title: '全部反馈测试完成',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 重置设置
   */
  onResetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要重置所有反馈设置吗？',
      success: (res) => {
        if (res.confirm) {
          const defaultSettings = {
            hapticEnabled: true,
            audioEnabled: false,
            audioVolume: 0.5
          }
          
          this.setData({
            settings: defaultSettings
          })
          
          feedbackManager.setUserPreferences(defaultSettings)
          
          wx.showToast({
            title: '设置已重置',
            icon: 'success',
            duration: 1500
          })
        }
      }
    })
  },

  /**
   * 查看反馈统计
   */
  onViewStats() {
    const stats = feedbackManager.getFeedbackStats()
    
    const content = `
总反馈次数: ${stats.totalFeedbacks}
覆盖率: ${stats.coverage.coverageRate.toFixed(1)}%
触觉反馈: ${stats.hapticFeedbacks}次
视觉反馈: ${stats.visualFeedbacks}次
音效反馈: ${stats.audioFeedbacks}次
    `.trim()
    
    wx.showModal({
      title: '反馈统计',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 反馈帮助
   */
  onFeedbackHelp() {
    const helpContent = `
触觉反馈：通过设备震动提供操作确认
视觉反馈：通过动画效果增强交互体验
音效反馈：通过声音提供操作提示

建议：
• 在安静环境中可关闭音效反馈
• 触觉反馈有助于提升操作确认感
• 视觉反馈让界面更加生动
    `.trim()
    
    wx.showModal({
      title: '反馈说明',
      content: helpContent,
      showCancel: false,
      confirmText: '知道了'
    })
  }
})
