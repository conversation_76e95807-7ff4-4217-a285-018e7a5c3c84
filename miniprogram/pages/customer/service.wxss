/* pages/customer/service.wxss */
.service-container {
  min-height: 100vh;
  background: #f8fafc;
}

/* 客服状态 */
.service-status {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-info {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.offline {
  background: #ef4444;
}

.status-text {
  font-size: 28rpx;
  color: #1f2937;
  margin-right: 16rpx;
}

.status-desc {
  font-size: 24rpx;
  color: #6b7280;
}

.chat-btn {
  padding: 16rpx 32rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.chat-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
}

/* 快速入口 */
.quick-actions {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.actions-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-item {
  padding: 30rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid transparent;
}

.action-item:active {
  border-color: #8b5cf6;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}

.action-text {
  font-size: 26rpx;
  color: #374151;
}

/* 常见问题 */
.faq-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.faq-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.faq-item {
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  border-left: 4rpx solid #8b5cf6;
}

.faq-question {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 联系方式 */
.contact-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.contact-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  background: #8b5cf6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: white;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.contact-action {
  padding: 12rpx 24rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 聊天界面 */
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 30rpx;
  background: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-title {
  font-size: 32rpx;
  font-weight: bold;
}

.chat-close {
  font-size: 32rpx;
}

.chat-messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 20rpx;
  display: flex;
}

.message-item.user {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.4;
}

.message-content.service {
  background: #f3f4f6;
  color: #1f2937;
}

.message-content.user {
  background: #8b5cf6;
  color: white;
}

.message-time {
  font-size: 20rpx;
  color: #9ca3af;
  text-align: center;
  margin-top: 8rpx;
}

.chat-input {
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.input-field {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 35rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 70rpx;
  height: 70rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 28rpx;
}

/* 反馈弹窗 */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedback-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.feedback-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 30rpx;
  text-align: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 12rpx;
  display: block;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #1f2937;
}

.picker-placeholder {
  color: #9ca3af;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.feedback-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.submit-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
}

/* VIP客服样式 */
.vip-status {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border: 2rpx solid #ffd700;
  position: relative;
  overflow: hidden;
}

.vip-status::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: vip-shine 3s infinite;
}

@keyframes vip-shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.vip-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.8);
  color: #ffd700;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.vip-icon {
  font-size: 24rpx;
}

.vip-text {
  font-weight: 600;
  letter-spacing: 1rpx;
}

.vip-queue {
  background: rgba(255, 255, 255, 0.9);
  color: #333333;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  margin-top: 8rpx;
}

.queue-text {
  font-weight: 500;
}

/* VIP服务项样式 */
.vip-service {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border: 2rpx solid #ffd700;
  position: relative;
  overflow: hidden;
}

.vip-service::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: vip-shine 3s infinite;
}

.vip-service .service-title {
  color: #333333;
  font-weight: 600;
}

.vip-service .service-desc {
  color: #666666;
  font-weight: 500;
}

.service-icon.vip-chat {
  background: linear-gradient(135deg, #333333 0%, #555555 100%);
  position: relative;
}

.vip-badge-small {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #ffffff;
  font-size: 16rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 600;
  line-height: 1;
}

/* VIP聊天消息样式 */
.chat-message.vip-message {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-left: 4rpx solid #333333;
}

.chat-message.vip-message .message-content {
  color: #333333;
  font-weight: 500;
}

.chat-message.vip-message .message-time {
  color: #666666;
}

/* VIP客服头像 */
.vip-avatar {
  position: relative;
}

.vip-avatar::after {
  content: '👑';
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 24rpx;
  background: #333333;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .vip-badge {
    padding: 6rpx 12rpx;
    font-size: 18rpx;
  }

  .vip-badge-small {
    font-size: 14rpx;
    padding: 2rpx 6rpx;
  }
}
