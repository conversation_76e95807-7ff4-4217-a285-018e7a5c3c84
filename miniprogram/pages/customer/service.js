// pages/customer/service.js
const app = getApp()

Page({
  data: {
    // 用户信息
    userInfo: {},
    isVip: false,

    // 客服信息
    customerService: {
      online: true,
      responseTime: '平均5分钟内回复',
      workTime: '9:00-21:00'
    },

    // VIP客服信息
    vipCustomerService: {
      online: true,
      responseTime: '平均1分钟内回复',
      workTime: '24小时在线',
      queuePosition: 0
    },
    
    // 常见问题
    faqList: [
      {
        id: 1,
        question: '如何提升匹配成功率？',
        answer: '完善个人资料，上传清晰照片，积极互动',
        category: 'match'
      },
      {
        id: 2,
        question: 'VIP会员有什么特权？',
        answer: '无限喜欢、查看谁喜欢我、高级筛选等',
        category: 'vip'
      },
      {
        id: 3,
        question: '如何举报不当行为？',
        answer: '在用户详情页点击举报按钮，选择举报原因',
        category: 'safety'
      },
      {
        id: 4,
        question: '忘记密码怎么办？',
        answer: '在登录页点击忘记密码，通过手机验证码重置',
        category: 'account'
      }
    ],
    
    // 联系方式
    contactInfo: {
      phone: '************',
      email: '<EMAIL>',
      wechat: 'xiangqin_service',
      qq: '*********'
    },
    
    // 反馈表单
    feedbackForm: {
      type: '',
      content: '',
      contact: ''
    },
    
    feedbackTypes: [
      { value: 'bug', name: '功能异常' },
      { value: 'suggestion', name: '功能建议' },
      { value: 'complaint', name: '投诉举报' },
      { value: 'other', name: '其他问题' }
    ],
    
    typeIndex: -1,
    showFeedbackModal: false,
    submitting: false,
    
    // 在线客服状态
    chatConnected: false,
    chatMessages: [],
    inputMessage: ''
  },

  onLoad() {
    this.loadUserInfo()
    this.checkServiceStatus()
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const userInfo = app.globalData.userInfo || wx.getStorageSync('user_info')
      const isVip = userInfo?.vip_status === 'active' || userInfo?.is_vip || false

      this.setData({
        userInfo,
        isVip
      })

      if (isVip) {
        this.checkVipServiceStatus()
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  },

  // 检查VIP客服状态
  async checkVipServiceStatus() {
    try {
      // 模拟VIP客服状态检查
      const vipServiceStatus = {
        online: true,
        responseTime: '平均30秒内回复',
        workTime: '24小时专属服务',
        queuePosition: Math.floor(Math.random() * 3) // 0-2的队列位置
      }

      this.setData({
        vipCustomerService: vipServiceStatus
      })
    } catch (error) {
      console.error('检查VIP客服状态失败:', error)
    }
  },

  // 检查客服状态
  async checkServiceStatus() {
    try {
      // 模拟检查客服在线状态
      const isOnline = new Date().getHours() >= 9 && new Date().getHours() <= 21
      this.setData({
        'customerService.online': isOnline
      })
    } catch (error) {
      console.error('检查客服状态失败:', error)
    }
  },

  // 开始在线客服
  startOnlineChat() {
    if (!this.data.customerService.online) {
      wx.showModal({
        title: '客服不在线',
        content: '当前时间客服不在线，您可以留言或查看常见问题',
        confirmText: '留言反馈',
        success: (res) => {
          if (res.confirm) {
            this.showFeedbackForm()
          }
        }
      })
      return
    }

    // 连接在线客服
    this.connectToService('normal')
  },

  // 开始VIP专属客服
  startVipChat() {
    if (!this.data.isVip) {
      wx.showModal({
        title: 'VIP专属服务',
        content: '该服务仅限VIP会员使用，是否前往开通VIP？',
        confirmText: '开通VIP',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/vip-privileges/vip-privileges'
            })
          }
        }
      })
      return
    }

    // 连接VIP专属客服
    this.connectToService('vip')
  },

  // 连接客服
  async connectToService(serviceType = 'normal') {
    try {
      const isVip = serviceType === 'vip'
      wx.showLoading({
        title: isVip ? 'VIP专属客服连接中...' : '连接中...'
      })

      // 模拟连接时间（VIP更快）
      const connectTime = isVip ? 500 : 2000
      await new Promise(resolve => setTimeout(resolve, connectTime))

      const welcomeMessage = isVip
        ? '您好！我是您的VIP专属客服，很高兴为您服务！有什么可以帮助您的吗？'
        : '您好！我是客服小助手，有什么可以帮助您的吗？'

      this.setData({
        chatConnected: true,
        serviceType: serviceType,
        chatMessages: [
          {
            id: 1,
            type: 'service',
            content: welcomeMessage,
            time: new Date().toLocaleTimeString(),
            isVip: isVip
          }
        ]
      })
      
      wx.hideLoading()
      wx.showToast({ title: '连接成功', icon: 'success' })
      
    } catch (error) {
      wx.hideLoading()
      wx.showToast({ title: '连接失败', icon: 'none' })
    }
  },

  // 发送消息
  sendMessage() {
    const { inputMessage, chatMessages } = this.data
    
    if (!inputMessage.trim()) {
      return
    }
    
    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      time: new Date().toLocaleTimeString()
    }
    
    this.setData({
      chatMessages: [...chatMessages, newMessage],
      inputMessage: ''
    })
    
    // 模拟客服回复
    setTimeout(() => {
      this.simulateServiceReply(inputMessage)
    }, 1000)
  },

  // 模拟客服回复
  simulateServiceReply(userMessage) {
    const replies = [
      '我已经收到您的问题，正在为您查询...',
      '感谢您的反馈，我们会认真处理',
      '这个问题我来帮您解决',
      '请稍等，我为您转接专业客服'
    ]
    
    const reply = {
      id: Date.now(),
      type: 'service',
      content: replies[Math.floor(Math.random() * replies.length)],
      time: new Date().toLocaleTimeString()
    }
    
    this.setData({
      chatMessages: [...this.data.chatMessages, reply]
    })
  },

  // 输入消息
  onMessageInput(e) {
    this.setData({
      inputMessage: e.detail.value
    })
  },

  // 查看FAQ详情
  viewFaqDetail(e) {
    const { faq } = e.currentTarget.dataset
    wx.showModal({
      title: faq.question,
      content: faq.answer,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 显示反馈表单
  showFeedbackForm() {
    this.setData({ showFeedbackModal: true })
  },

  // 隐藏反馈表单
  hideFeedbackForm() {
    this.setData({ 
      showFeedbackModal: false,
      feedbackForm: {
        type: '',
        content: '',
        contact: ''
      },
      typeIndex: -1
    })
  },

  // 反馈类型选择
  onFeedbackTypeChange(e) {
    const index = e.detail.value
    this.setData({
      typeIndex: index,
      'feedbackForm.type': this.data.feedbackTypes[index].value
    })
  },

  // 反馈内容输入
  onFeedbackContentInput(e) {
    this.setData({
      'feedbackForm.content': e.detail.value
    })
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({
      'feedbackForm.contact': e.detail.value
    })
  },

  // 提交反馈
  async submitFeedback() {
    const { feedbackForm } = this.data
    
    if (!feedbackForm.type) {
      wx.showToast({ title: '请选择问题类型', icon: 'none' })
      return
    }
    
    if (!feedbackForm.content.trim()) {
      wx.showToast({ title: '请输入问题描述', icon: 'none' })
      return
    }
    
    try {
      this.setData({ submitting: true })
      
      // 模拟提交
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      this.setData({ submitting: false })
      this.hideFeedbackForm()
      
      wx.showToast({ title: '提交成功', icon: 'success' })
      
    } catch (error) {
      this.setData({ submitting: false })
      wx.showToast({ title: '提交失败', icon: 'none' })
    }
  },

  // 复制联系方式
  copyContact(e) {
    const { text } = e.currentTarget.dataset
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({ title: '已复制', icon: 'success' })
      }
    })
  },

  // 拨打电话
  makeCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone
    })
  },

  // 查看帮助文档
  viewHelpDocs() {
    wx.navigateTo({
      url: '/pages/help/documentation'
    })
  },

  // 查看用户反馈
  viewUserFeedback() {
    wx.navigateTo({
      url: '/pages/user/feedback'
    })
  }
})
