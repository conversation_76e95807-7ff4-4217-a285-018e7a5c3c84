<!--pages/customer/service.wxml-->
<view class="customer-service-container">
  <!-- 顶部导航 -->
  <view class="service-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">客服中心</text>
      <view class="header-actions">
        <text class="action-btn" bindtap="goToHistory">📋</text>
      </view>
    </view>

    <!-- 服务状态 -->
    <view class="service-status">
      <!-- VIP专属客服状态 -->
      <view class="status-card vip-status" wx:if="{{isVip}}">
        <view class="vip-badge">
          <text class="vip-icon">👑</text>
          <text class="vip-text">VIP专属</text>
        </view>
        <view class="status-info">
          <text class="status-icon">🟢</text>
          <view class="status-details">
            <text class="status-title">专属客服在线</text>
            <text class="status-desc">{{vipCustomerService.responseTime}}</text>
          </view>
        </view>
        <view class="vip-queue" wx:if="{{vipCustomerService.queuePosition > 0}}">
          <text class="queue-text">排队：{{vipCustomerService.queuePosition}}人</text>
        </view>
        <text class="service-time">{{vipCustomerService.workTime}}</text>
      </view>

      <!-- 普通客服状态 -->
      <view class="status-card">
        <view class="status-info">
          <text class="status-icon">{{customerService.online ? '🟢' : '🔴'}}</text>
          <view class="status-details">
            <text class="status-title">{{customerService.online ? '客服在线' : '客服离线'}}</text>
            <text class="status-desc">{{customerService.responseTime}}</text>
          </view>
        </view>
        <text class="service-time">{{customerService.workTime}}</text>
      </view>
    </view>
  </view>

  <!-- 快捷服务 -->
  <view class="quick-service-section">
    <view class="section-header">
      <text class="section-title">快捷服务</text>
    </view>
    
    <view class="quick-service-grid">
      <!-- VIP专属客服 -->
      <view class="service-item vip-service" wx:if="{{isVip}}" bindtap="startVipChat">
        <view class="service-icon vip-chat">
          <text class="icon">👑</text>
          <view class="vip-badge-small">VIP</view>
        </view>
        <text class="service-title">VIP专属客服</text>
        <text class="service-desc">优先响应·专业服务</text>
      </view>

      <view class="service-item" bindtap="startOnlineChat">
        <view class="service-icon chat">
          <text class="icon">💬</text>
        </view>
        <text class="service-title">在线客服</text>
        <text class="service-desc">实时人工客服</text>
      </view>

      <view class="service-item" bindtap="callService">
        <view class="service-icon call">
          <text class="icon">📞</text>
        </view>
        <text class="service-title">电话客服</text>
        <text class="service-desc">400-123-4567</text>
      </view>

      <view class="service-item" bindtap="goToFeedback">
        <view class="service-icon feedback">
          <text class="icon">📝</text>
        </view>
        <text class="service-title">意见反馈</text>
        <text class="service-desc">提交问题建议</text>
      </view>

      <view class="service-item" bindtap="goToFAQ">
        <view class="service-icon faq">
          <text class="icon">❓</text>
        </view>
        <text class="service-title">常见问题</text>
        <text class="service-desc">快速找到答案</text>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="faq-section">
    <view class="section-header">
      <text class="section-title">常见问题</text>
      <text class="section-more" bindtap="goToFAQ">查看全部</text>
    </view>
    
    <view class="faq-list">
      <view class="faq-item" wx:for="{{frequentQuestions}}" wx:key="id" bindtap="toggleFAQ" data-index="{{index}}">
        <view class="faq-question">
          <text class="question-text">{{item.question}}</text>
          <text class="question-arrow {{item.expanded ? 'expanded' : ''}}">></text>
        </view>
        <view class="faq-answer {{item.expanded ? 'expanded' : ''}}" wx:if="{{item.expanded}}">
          <text class="answer-text">{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 问题分类 -->
  <view class="categories-section">
    <view class="section-header">
      <text class="section-title">问题分类</text>
    </view>
    
    <view class="categories-grid">
      <view class="category-item" wx:for="{{problemCategories}}" wx:key="id" bindtap="viewCategory" data-category="{{item}}">
        <view class="category-icon">{{item.icon}}</view>
        <text class="category-title">{{item.title}}</text>
        <text class="category-count">{{item.questions_count}}个问题</text>
      </view>
    </view>
  </view>

  <!-- 联系方式 -->
  <view class="contact-section">
    <view class="section-header">
      <text class="section-title">联系我们</text>
    </view>
    
    <view class="contact-list">
      <view class="contact-item" bindtap="copyText" data-text="{{contactInfo.phone}}">
        <view class="contact-icon phone">
          <text class="icon">📞</text>
        </view>
        <view class="contact-info">
          <text class="contact-title">客服电话</text>
          <text class="contact-value">{{contactInfo.phone}}</text>
        </view>
        <text class="contact-action">复制</text>
      </view>

      <view class="contact-item" bindtap="copyText" data-text="{{contactInfo.email}}">
        <view class="contact-icon email">
          <text class="icon">📧</text>
        </view>
        <view class="contact-info">
          <text class="contact-title">客服邮箱</text>
          <text class="contact-value">{{contactInfo.email}}</text>
        </view>
        <text class="contact-action">复制</text>
      </view>

      <view class="contact-item" bindtap="copyText" data-text="{{contactInfo.wechat}}">
        <view class="contact-icon wechat">
          <text class="icon">💚</text>
        </view>
        <view class="contact-info">
          <text class="contact-title">微信客服</text>
          <text class="contact-value">{{contactInfo.wechat}}</text>
        </view>
        <text class="contact-action">复制</text>
      </view>

      <view class="contact-item" bindtap="viewAddress">
        <view class="contact-icon address">
          <text class="icon">📍</text>
        </view>
        <view class="contact-info">
          <text class="contact-title">公司地址</text>
          <text class="contact-value">{{contactInfo.address}}</text>
        </view>
        <text class="contact-action">查看</text>
      </view>
    </view>
  </view>

  <!-- 服务评价 -->
  <view class="rating-section">
    <view class="section-header">
      <text class="section-title">服务评价</text>
    </view>
    
    <view class="rating-card">
      <text class="rating-title">您对我们的服务满意吗？</text>
      <view class="rating-stars">
        <text 
          class="star {{index < serviceRating ? 'active' : ''}}"
          wx:for="{{5}}" 
          wx:key="*this"
          bindtap="rateService"
          data-rating="{{index + 1}}"
        >
          ⭐
        </text>
      </view>
      <text class="rating-desc">{{ratingTexts[serviceRating - 1] || '请选择评分'}}</text>
      
      <view class="rating-feedback" wx:if="{{serviceRating > 0}}">
        <textarea 
          class="feedback-input" 
          placeholder="请留下您的宝贵意见..."
          value="{{feedbackText}}"
          bindinput="onFeedbackInput"
          maxlength="200"
        />
        <button class="submit-feedback-btn" bindtap="submitFeedback">
          提交评价
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 在线客服聊天弹窗 -->
<view class="chat-modal" wx:if="{{showChatModal}}" bindtap="hideChatModal">
  <view class="chat-content" catchtap="">
    <view class="chat-header">
      <view class="agent-info">
        <image src="{{currentAgent.avatar}}" class="agent-avatar" />
        <view class="agent-details">
          <text class="agent-name">{{currentAgent.name}}</text>
          <text class="agent-status">{{currentAgent.status}}</text>
        </view>
      </view>
      <text class="chat-close" bindtap="hideChatModal">×</text>
    </view>
    
    <scroll-view class="chat-messages" scroll-y scroll-into-view="{{scrollToView}}">
      <view class="message-item {{message.type}}" wx:for="{{chatMessages}}" wx:key="id" id="msg-{{index}}">
        <image src="{{message.avatar}}" class="message-avatar" wx:if="{{message.type === 'agent'}}" />
        <view class="message-content">
          <text class="message-text">{{message.content}}</text>
          <text class="message-time">{{message.time}}</text>
        </view>
        <image src="{{message.avatar}}" class="message-avatar" wx:if="{{message.type === 'user'}}" />
      </view>
    </scroll-view>
    
    <view class="chat-input-section">
      <view class="input-container">
        <input 
          class="chat-input" 
          placeholder="请输入您的问题..."
          value="{{inputMessage}}"
          bindinput="onMessageInput"
          bindconfirm="sendMessage"
        />
        <button class="send-btn" bindtap="sendMessage">发送</button>
      </view>
      
      <view class="quick-replies">
        <text 
          class="quick-reply"
          wx:for="{{quickReplies}}" 
          wx:key="*this"
          bindtap="selectQuickReply"
          data-reply="{{item}}"
        >
          {{item}}
        </text>
      </view>
    </view>
  </view>
</view>

<!-- 反馈提交成功提示 -->
<view class="success-modal" wx:if="{{showSuccessModal}}">
  <view class="success-content">
    <text class="success-icon">✅</text>
    <text class="success-title">提交成功</text>
    <text class="success-desc">感谢您的反馈，我们会认真处理</text>
    <button class="success-btn" bindtap="hideSuccessModal">
      知道了
    </button>
  </view>
</view>
