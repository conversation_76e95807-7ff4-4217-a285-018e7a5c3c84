// pages/help/documentation.js
Page({
  data: {
    // 搜索
    searchKeyword: '',
    searchResults: [],
    showSearchResults: false,
    
    // 帮助分类
    helpCategories: [
      {
        id: 'getting-started',
        title: '快速入门',
        icon: '🚀',
        items: [
          { id: 1, title: '如何注册账号', content: '点击注册按钮，输入手机号获取验证码...' },
          { id: 2, title: '完善个人资料', content: '上传头像，填写基本信息，添加兴趣爱好...' },
          { id: 3, title: '开始匹配', content: '设置筛选条件，浏览推荐用户，发送喜欢...' }
        ]
      },
      {
        id: 'matching',
        title: '匹配交友',
        icon: '💕',
        items: [
          { id: 4, title: '如何提高匹配率', content: '完善资料，上传清晰照片，积极互动...' },
          { id: 5, title: '匹配算法说明', content: '基于兴趣爱好、地理位置、年龄等因素...' },
          { id: 6, title: '超级喜欢功能', content: '让对方优先看到你的喜欢，提高匹配概率...' }
        ]
      },
      {
        id: 'chat',
        title: '聊天消息',
        icon: '💬',
        items: [
          { id: 7, title: '发送消息', content: '匹配成功后可以开始聊天，支持文字、表情、图片...' },
          { id: 8, title: '聊天礼仪', content: '保持礼貌，真诚交流，避免发送不当内容...' },
          { id: 9, title: '消息撤回', content: '发送后2分钟内可以撤回消息...' }
        ]
      },
      {
        id: 'vip',
        title: 'VIP会员',
        icon: '👑',
        items: [
          { id: 10, title: 'VIP特权介绍', content: '无限喜欢、查看谁喜欢我、高级筛选等特权...' },
          { id: 11, title: '如何开通VIP', content: '在VIP页面选择套餐，支持微信支付、支付宝...' },
          { id: 12, title: 'VIP续费说明', content: '到期前会提醒续费，支持自动续费...' }
        ]
      },
      {
        id: 'safety',
        title: '安全隐私',
        icon: '🛡️',
        items: [
          { id: 13, title: '账号安全', content: '设置强密码，开启登录验证，定期检查登录记录...' },
          { id: 14, title: '隐私保护', content: '控制个人信息可见性，设置访客权限...' },
          { id: 15, title: '举报投诉', content: '遇到不当行为可以举报，我们会及时处理...' }
        ]
      },
      {
        id: 'payment',
        title: '充值支付',
        icon: '💰',
        items: [
          { id: 16, title: '充值方式', content: '支持微信支付、支付宝等多种支付方式...' },
          { id: 17, title: '退款政策', content: '特殊情况下可申请退款，详见退款条款...' },
          { id: 18, title: '发票申请', content: '可在个人中心申请电子发票...' }
        ]
      }
    ],
    
    // 当前查看的帮助项
    currentHelpItem: null,
    showHelpDetail: false,
    
    // 反馈
    showFeedbackModal: false,
    feedbackContent: '',
    feedbackContact: ''
  },

  onLoad() {
    // 页面初始化
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    
    if (keyword.trim()) {
      this.performSearch(keyword)
    } else {
      this.setData({ 
        showSearchResults: false,
        searchResults: []
      })
    }
  },

  // 执行搜索
  performSearch(keyword) {
    const results = []
    
    this.data.helpCategories.forEach(category => {
      category.items.forEach(item => {
        if (item.title.includes(keyword) || item.content.includes(keyword)) {
          results.push({
            ...item,
            category: category.title,
            categoryIcon: category.icon
          })
        }
      })
    })
    
    this.setData({
      searchResults: results,
      showSearchResults: true
    })
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showSearchResults: false
    })
  },

  // 查看帮助详情
  viewHelpDetail(e) {
    const { item } = e.currentTarget.dataset
    this.setData({
      currentHelpItem: item,
      showHelpDetail: true
    })
  },

  // 关闭帮助详情
  closeHelpDetail() {
    this.setData({
      showHelpDetail: false,
      currentHelpItem: null
    })
  },

  // 帮助是否有用
  markHelpful(e) {
    const { helpful } = e.currentTarget.dataset
    
    if (helpful) {
      wx.showToast({
        title: '感谢您的反馈',
        icon: 'success'
      })
    } else {
      // 显示反馈表单
      this.setData({ showFeedbackModal: true })
    }
  },

  // 显示反馈表单
  showFeedback() {
    this.setData({ showFeedbackModal: true })
  },

  // 隐藏反馈表单
  hideFeedback() {
    this.setData({
      showFeedbackModal: false,
      feedbackContent: '',
      feedbackContact: ''
    })
  },

  // 反馈内容输入
  onFeedbackInput(e) {
    this.setData({
      feedbackContent: e.detail.value
    })
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({
      feedbackContact: e.detail.value
    })
  },

  // 提交反馈
  async submitFeedback() {
    const { feedbackContent, feedbackContact } = this.data
    
    if (!feedbackContent.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }
    
    try {
      // 模拟提交反馈
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      this.hideFeedback()
      wx.showToast({
        title: '反馈提交成功',
        icon: 'success'
      })
      
    } catch (error) {
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      })
    }
  },

  // 联系客服
  contactService() {
    wx.navigateTo({
      url: '/pages/customer/service'
    })
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.navigateTo({
      url: '/pages/legal/user-agreement'
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/legal/privacy-policy'
    })
  },

  // 分享帮助
  shareHelp() {
    return {
      title: '相亲交友小程序使用帮助',
      path: '/pages/help/documentation'
    }
  },

  // 分享给好友
  onShareAppMessage() {
    return this.shareHelp()
  }
})
