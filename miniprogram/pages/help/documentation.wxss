/* pages/help/documentation.wxss */
.help-container {
  min-height: 100vh;
  background: #f8fafc;
}

/* 搜索栏 */
.search-section {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e7eb;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
  height: 70rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 35rpx;
  padding: 0 50rpx 0 70rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
}

.search-clear {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
}

/* 搜索结果 */
.search-results {
  background: white;
  margin-bottom: 20rpx;
}

.results-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 28rpx;
  color: #6b7280;
}

.result-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f3f4f6;
  display: flex;
  align-items: center;
}

.result-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.result-content {
  flex: 1;
}

.result-title {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.result-category {
  font-size: 22rpx;
  color: #8b5cf6;
}

/* 帮助分类 */
.help-categories {
  padding: 20rpx;
}

.category-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.category-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  display: flex;
  align-items: center;
}

.category-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
}

.category-items {
  padding: 0;
}

.help-item {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.help-item:last-child {
  border-bottom: none;
}

.help-item-title {
  font-size: 28rpx;
  color: #1f2937;
  flex: 1;
}

.help-item-arrow {
  font-size: 24rpx;
  color: #d1d5db;
}

/* 帮助详情弹窗 */
.help-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.help-detail-content {
  background: white;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  flex: 1;
}

.detail-close {
  font-size: 32rpx;
  color: #9ca3af;
}

.detail-body {
  padding: 30rpx;
  flex: 1;
  overflow-y: auto;
}

.detail-content {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.detail-actions {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.helpful-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.helpful-text {
  font-size: 26rpx;
  color: #6b7280;
}

.helpful-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: 2rpx solid #e5e7eb;
  background: white;
  color: #6b7280;
}

.helpful-btn.yes {
  border-color: #10b981;
  color: #10b981;
}

.helpful-btn.no {
  border-color: #ef4444;
  color: #ef4444;
}

.contact-service-btn {
  padding: 12rpx 24rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 反馈弹窗 */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.feedback-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.feedback-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 30rpx;
  text-align: center;
}

.feedback-form {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 12rpx;
  display: block;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  box-sizing: border-box;
}

.feedback-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.feedback-btn {
  flex: 1;
  height: 70rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

.service-btn {
  flex: 1;
  height: 70rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

/* 空状态 */
.empty-results {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  line-height: 1.5;
}
