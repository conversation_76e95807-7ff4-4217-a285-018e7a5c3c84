<!--pages/help/documentation.wxml-->
<view class="help-container">
  <!-- 顶部导航 -->
  <view class="help-header">
    <view class="header-content">
      <text class="back-btn" bindtap="goBack">‹</text>
      <text class="header-title">帮助中心</text>
      <view class="header-actions">
        <text class="action-btn" bindtap="searchHelp">🔍</text>
        <text class="action-btn" bindtap="contactSupport">💬</text>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-container">
        <text class="search-icon">🔍</text>
        <input 
          class="search-input" 
          placeholder="搜索帮助内容..."
          value="{{searchQuery}}"
          bindinput="onSearchInput"
          bindconfirm="performSearch"
        />
        <button class="search-btn" bindtap="performSearch" wx:if="{{searchQuery}}">
          搜索
        </button>
      </view>
    </view>
  </view>

  <!-- 帮助分类 -->
  <scroll-view class="help-content" scroll-y>
    <!-- 快速入门 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">🚀</text>
        <text class="section-title">快速入门</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{quickStartItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 账号管理 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">👤</text>
        <text class="section-title">账号管理</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{accountItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 匹配交友 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">💕</text>
        <text class="section-title">匹配交友</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{matchingItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 聊天功能 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">💬</text>
        <text class="section-title">聊天功能</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{chatItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- VIP会员 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">👑</text>
        <text class="section-title">VIP会员</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{vipItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 安全隐私 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">🛡️</text>
        <text class="section-title">安全隐私</text>
      </view>
      
      <view class="help-items">
        <view class="help-item" wx:for="{{securityItems}}" wx:key="id" bindtap="viewHelpItem" data-item="{{item}}">
          <view class="item-content">
            <text class="item-icon">{{item.icon}}</text>
            <view class="item-info">
              <text class="item-title">{{item.title}}</text>
              <text class="item-desc">{{item.description}}</text>
            </view>
          </view>
          <text class="item-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 常见问题 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">❓</text>
        <text class="section-title">常见问题</text>
      </view>
      
      <view class="faq-items">
        <view class="faq-item" wx:for="{{faqItems}}" wx:key="id" bindtap="toggleFAQ" data-index="{{index}}">
          <view class="faq-question">
            <text class="question-text">{{item.question}}</text>
            <text class="question-arrow {{item.expanded ? 'expanded' : ''}}">></text>
          </view>
          <view class="faq-answer {{item.expanded ? 'expanded' : ''}}" wx:if="{{item.expanded}}">
            <text class="answer-text">{{item.answer}}</text>
            <view class="answer-actions" wx:if="{{item.helpful !== undefined}}">
              <text class="helpful-text">这个回答对你有帮助吗？</text>
              <view class="helpful-buttons">
                <button 
                  class="helpful-btn {{item.helpful === true ? 'active' : ''}}"
                  bindtap="markHelpful"
                  data-index="{{index}}"
                  data-helpful="true"
                  catchtap=""
                >
                  👍 有帮助
                </button>
                <button 
                  class="helpful-btn {{item.helpful === false ? 'active' : ''}}"
                  bindtap="markHelpful"
                  data-index="{{index}}"
                  data-helpful="false"
                  catchtap=""
                >
                  👎 没帮助
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">📞</text>
        <text class="section-title">联系我们</text>
      </view>
      
      <view class="contact-items">
        <view class="contact-item" bindtap="contactSupport">
          <view class="contact-content">
            <text class="contact-icon">💬</text>
            <view class="contact-info">
              <text class="contact-title">在线客服</text>
              <text class="contact-desc">7×24小时在线服务</text>
            </view>
          </view>
          <view class="contact-status online">
            <text class="status-text">在线</text>
          </view>
        </view>

        <view class="contact-item" bindtap="callSupport">
          <view class="contact-content">
            <text class="contact-icon">📞</text>
            <view class="contact-info">
              <text class="contact-title">客服电话</text>
              <text class="contact-desc">400-123-4567</text>
            </view>
          </view>
          <text class="contact-arrow">></text>
        </view>

        <view class="contact-item" bindtap="emailSupport">
          <view class="contact-content">
            <text class="contact-icon">📧</text>
            <view class="contact-info">
              <text class="contact-title">邮箱反馈</text>
              <text class="contact-desc"><EMAIL></text>
            </view>
          </view>
          <text class="contact-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 意见反馈 -->
    <view class="help-section">
      <view class="section-header">
        <text class="section-icon">📝</text>
        <text class="section-title">意见反馈</text>
      </view>
      
      <view class="feedback-form">
        <textarea 
          class="feedback-input" 
          placeholder="请描述您遇到的问题或建议..."
          value="{{feedbackText}}"
          bindinput="onFeedbackInput"
          maxlength="500"
        />
        <text class="char-count">{{feedbackText.length}}/500</text>
        
        <view class="feedback-type">
          <text class="type-label">反馈类型</text>
          <view class="type-options">
            <view 
              class="type-option {{selectedFeedbackType === item.value ? 'selected' : ''}}"
              wx:for="{{feedbackTypes}}" 
              wx:key="value"
              bindtap="selectFeedbackType"
              data-type="{{item.value}}"
            >
              <text class="option-text">{{item.name}}</text>
            </view>
          </view>
        </view>
        
        <button 
          class="submit-feedback-btn"
          bindtap="submitFeedback"
          disabled="{{!feedbackText || submitting}}"
        >
          {{submitting ? '提交中...' : '提交反馈'}}
        </button>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 帮助详情弹窗 -->
<view class="help-detail-modal" wx:if="{{showDetailModal}}" bindtap="hideDetailModal">
  <view class="detail-content" catchtap="">
    <view class="detail-header">
      <text class="detail-title">{{selectedItem.title}}</text>
      <text class="detail-close" bindtap="hideDetailModal">×</text>
    </view>
    
    <scroll-view class="detail-body" scroll-y>
      <view class="detail-steps" wx:if="{{selectedItem.steps}}">
        <view class="step-item" wx:for="{{selectedItem.steps}}" wx:key="*this" wx:for-index="stepIndex">
          <view class="step-number">{{stepIndex + 1}}</view>
          <view class="step-content">
            <text class="step-text">{{item}}</text>
          </view>
        </view>
      </view>
      
      <view class="detail-text" wx:if="{{selectedItem.content}}">
        <text class="content-text">{{selectedItem.content}}</text>
      </view>
      
      <view class="detail-images" wx:if="{{selectedItem.images}}">
        <image 
          src="{{image}}" 
          class="detail-image"
          wx:for="{{selectedItem.images}}" 
          wx:key="*this"
          wx:for-item="image"
          bindtap="previewImage"
          data-urls="{{selectedItem.images}}"
          data-current="{{image}}"
        />
      </view>
      
      <view class="detail-tips" wx:if="{{selectedItem.tips}}">
        <text class="tips-title">💡 小贴士</text>
        <view class="tips-list">
          <text class="tip-item" wx:for="{{selectedItem.tips}}" wx:key="*this">
            • {{item}}
          </text>
        </view>
      </view>
    </scroll-view>
    
    <view class="detail-footer">
      <view class="helpful-section">
        <text class="helpful-text">这个帮助对你有用吗？</text>
        <view class="helpful-buttons">
          <button class="helpful-btn" bindtap="rateHelpful" data-helpful="true">
            👍 有用
          </button>
          <button class="helpful-btn" bindtap="rateHelpful" data-helpful="false">
            👎 没用
          </button>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 搜索结果弹窗 -->
<view class="search-modal" wx:if="{{showSearchModal}}" bindtap="hideSearchModal">
  <view class="search-content" catchtap="">
    <view class="search-header">
      <text class="search-title">搜索结果</text>
      <text class="search-close" bindtap="hideSearchModal">×</text>
    </view>
    
    <view class="search-results">
      <view class="result-item" wx:for="{{searchResults}}" wx:key="id" bindtap="viewSearchResult" data-result="{{item}}">
        <text class="result-title">{{item.title}}</text>
        <text class="result-desc">{{item.description}}</text>
        <text class="result-category">{{item.category}}</text>
      </view>
      
      <view class="no-results" wx:if="{{searchResults.length === 0}}">
        <text class="no-results-text">没有找到相关内容</text>
        <button class="contact-btn" bindtap="contactSupport">
          联系客服
        </button>
      </view>
    </view>
  </view>
</view>
