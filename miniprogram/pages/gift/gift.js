// pages/gift/gift.js
const app = getApp()
const paymentManager = require('../../utils/payment')

Page({
  data: {
    userBalance: null,
    giftCategories: [],
    selectedCategory: 'all',
    gifts: [],
    filteredGifts: [],
    selectedGift: null,
    giftQuantity: 1,
    showHistory: false,
    purchaseHistory: [],
    showRecharge: false,
    rechargePackages: [],
    selectedRecharge: null,
    purchasing: false
  },

  onLoad() {
    this.loadUserBalance()
    this.loadGiftCategories()
    this.loadGifts()
    this.loadPurchaseHistory()
    this.loadRechargePackages()
  },

  // 加载用户余额
  async loadUserBalance() {
    try {
      // 模拟用户余额数据
      const userBalance = {
        coins: 1280,
        lastUpdate: new Date().toISOString()
      }
      
      this.setData({ userBalance })
    } catch (error) {
      console.error('加载用户余额失败:', error)
    }
  },

  // 加载礼物分类
  loadGiftCategories() {
    const categories = [
      {
        id: 'all',
        name: '全部',
        iconName: 'gift',
        iconType: 'primary'
      },
      {
        id: 'flower',
        name: '鲜花',
        iconName: 'heart',
        iconType: 'danger'
      },
      {
        id: 'jewelry',
        name: '首饰',
        iconName: 'star',
        iconType: 'gold'
      },
      {
        id: 'food',
        name: '美食',
        iconName: 'gift',
        iconType: 'warning'
      },
      {
        id: 'luxury',
        name: '奢侈品',
        iconName: 'vip',
        iconType: 'gold'
      }
    ]
    
    this.setData({ giftCategories: categories })
  },

  // 加载礼物列表
  loadGifts() {
    const gifts = [
      {
        id: 'gift_rose',
        name: '玫瑰花',
        description: '浪漫的红玫瑰',
        price: 99,
        originalPrice: 120,
        discount: 8.3,
        category: 'flower',
        iconName: 'heart',
        iconType: 'danger',
        isPopular: true
      },
      {
        id: 'gift_chocolate',
        name: '巧克力礼盒',
        description: '甜蜜的巧克力',
        price: 199,
        category: 'food',
        iconName: 'gift',
        iconType: 'warning',
        isPopular: false
      },
      {
        id: 'gift_necklace',
        name: '钻石项链',
        description: '闪耀的钻石项链',
        price: 999,
        originalPrice: 1299,
        discount: 7.7,
        category: 'jewelry',
        iconName: 'star',
        iconType: 'gold',
        isPopular: true
      },
      {
        id: 'gift_perfume',
        name: '香水',
        description: '迷人的香水',
        price: 599,
        category: 'luxury',
        iconName: 'vip',
        iconType: 'secondary',
        isPopular: false
      },
      {
        id: 'gift_watch',
        name: '手表',
        description: '精致的手表',
        price: 1999,
        originalPrice: 2499,
        discount: 8.0,
        category: 'luxury',
        iconName: 'verified',
        iconType: 'gold',
        isPopular: true
      },
      {
        id: 'gift_cake',
        name: '生日蛋糕',
        description: '美味的生日蛋糕',
        price: 299,
        category: 'food',
        iconName: 'activity',
        iconType: 'warning',
        isPopular: false
      }
    ]
    
    this.setData({ 
      gifts,
      filteredGifts: gifts
    })
  },

  // 加载购买记录
  loadPurchaseHistory() {
    const history = [
      {
        id: 'history_1',
        giftName: '玫瑰花',
        giftIconName: 'heart',
        giftIconType: 'danger',
        price: 99,
        purchaseTime: '2025-01-06 14:30',
        status: 'completed',
        statusText: '已完成'
      },
      {
        id: 'history_2',
        giftName: '巧克力礼盒',
        giftIconName: 'gift',
        giftIconType: 'warning',
        price: 199,
        purchaseTime: '2025-01-05 16:20',
        status: 'completed',
        statusText: '已完成'
      }
    ]
    
    this.setData({ purchaseHistory: history })
  },

  // 加载充值套餐
  loadRechargePackages() {
    const packages = [
      {
        id: 'recharge_100',
        coins: 100,
        price: 10,
        bonus: 0
      },
      {
        id: 'recharge_500',
        coins: 500,
        price: 50,
        bonus: 50
      },
      {
        id: 'recharge_1000',
        coins: 1000,
        price: 100,
        bonus: 200
      },
      {
        id: 'recharge_2000',
        coins: 2000,
        price: 200,
        bonus: 500
      }
    ]
    
    this.setData({ rechargePackages: packages })
  },

  // 选择礼物分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ selectedCategory: category })
    this.filterGifts(category)
  },

  // 筛选礼物
  filterGifts(category) {
    const { gifts } = this.data
    let filteredGifts = gifts
    
    if (category !== 'all') {
      filteredGifts = gifts.filter(gift => gift.category === category)
    }
    
    this.setData({ filteredGifts })
  },

  // 选择礼物
  selectGift(e) {
    const gift = e.currentTarget.dataset.gift
    this.setData({ 
      selectedGift: gift,
      giftQuantity: 1
    })
  },

  // 关闭礼物详情弹窗
  closeGiftModal() {
    this.setData({ selectedGift: null })
  },

  // 增加数量
  increaseQuantity() {
    const quantity = this.data.giftQuantity + 1
    if (quantity <= 99) {
      this.setData({ giftQuantity: quantity })
    }
  },

  // 减少数量
  decreaseQuantity() {
    const quantity = this.data.giftQuantity - 1
    if (quantity >= 1) {
      this.setData({ giftQuantity: quantity })
    }
  },

  // 购买礼物
  buyGift(e) {
    const gift = e.currentTarget.dataset.gift
    this.setData({ 
      selectedGift: gift,
      giftQuantity: 1
    })
  },

  // 确认购买礼物
  async confirmBuyGift() {
    const { selectedGift, giftQuantity, userBalance } = this.data
    
    if (!selectedGift) return
    
    const totalPrice = selectedGift.price * giftQuantity
    
    // 检查余额
    if (userBalance.coins < totalPrice) {
      wx.showModal({
        title: '余额不足',
        content: '您的金币余额不足，是否前往充值？',
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            this.goToRecharge()
          }
        }
      })
      return
    }

    this.setData({ purchasing: true })

    try {
      // 模拟购买API调用
      await this.simulatePurchase(selectedGift, giftQuantity)
      
      // 更新余额
      const newBalance = {
        ...userBalance,
        coins: userBalance.coins - totalPrice
      }
      
      this.setData({ 
        userBalance: newBalance,
        selectedGift: null,
        purchasing: false
      })
      
      wx.showToast({
        title: '购买成功！',
        icon: 'success'
      })
      
      // 刷新购买记录
      this.loadPurchaseHistory()
      
    } catch (error) {
      console.error('购买礼物失败:', error)
      wx.showToast({
        title: '购买失败，请重试',
        icon: 'none'
      })
      this.setData({ purchasing: false })
    }
  },

  // 模拟购买过程
  simulatePurchase(gift, quantity) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true })
      }, 1500)
    })
  },

  // 前往充值
  goToRecharge() {
    this.setData({ showRecharge: true })
  },

  // 关闭充值弹窗
  closeRechargeModal() {
    this.setData({ 
      showRecharge: false,
      selectedRecharge: null
    })
  },

  // 选择充值套餐
  selectRechargePackage(e) {
    const packageData = e.currentTarget.dataset.package
    this.setData({ selectedRecharge: packageData.id })
  },

  // 确认充值
  async confirmRecharge() {
    const { selectedRecharge, rechargePackages } = this.data
    
    if (!selectedRecharge) return
    
    const packageData = rechargePackages.find(pkg => pkg.id === selectedRecharge)
    if (!packageData) return

    try {
      // 使用支付管理器处理充值
      const orderData = {
        productId: packageData.id,
        productName: `${packageData.coins}金币充值`,
        amount: packageData.price * 100, // 转换为分
        description: `金币充值 - ${packageData.coins}金币`
      }

      const result = await paymentManager.processPayment(orderData)
      
      if (result.success) {
        // 充值成功，更新余额
        const newCoins = packageData.coins + (packageData.bonus || 0)
        const newBalance = {
          ...this.data.userBalance,
          coins: this.data.userBalance.coins + newCoins
        }
        
        this.setData({ 
          userBalance: newBalance,
          showRecharge: false,
          selectedRecharge: null
        })
        
        wx.showToast({
          title: '充值成功！',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('充值失败:', error)
      
      let errorMessage = '充值失败，请重试'
      if (error.error) {
        errorMessage = error.error
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  },

  // 切换购买记录显示
  toggleHistory() {
    this.setData({ showHistory: !this.data.showHistory })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '礼物商城 - 用心意表达爱意',
      path: '/pages/gift/gift'
    }
  }
})
