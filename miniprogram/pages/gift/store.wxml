<!--pages/gift/store.wxml-->
<view class="gift-store-container">
  <!-- 简化的顶部导航 -->
  <view class="gift-header">
    <view class="header-content">
      <view class="header-left">
        <view class="back-btn" bindtap="goBack">
          <text class="back-icon">‹</text>
        </view>
        <text class="header-title">🎁 礼物商城</text>
      </view>
      <view class="header-right">
        <view class="action-btn" bindtap="goToHistory">
          <text class="action-icon">📋</text>
          <view class="cart-badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 余额卡片 -->
  <view class="balance-section">
    <view class="balance-card">
      <view class="balance-info">
        <text class="balance-label">💖 我的爱心币</text>
        <text class="balance-amount">{{userBalance}}</text>
      </view>
      <view class="recharge-btn" bindtap="goToRecharge">
        <text class="recharge-text">充值</text>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-tabs">
        <view
          class="category-tab {{activeCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="switchCategory"
          data-category="{{item.id}}"
        >
          <text class="tab-icon">{{item.icon}}</text>
          <text class="tab-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 礼物网格 -->
  <view class="gifts-section">
    <view class="gifts-grid">
      <view class="gift-card" wx:for="{{giftsList}}" wx:key="id" bindtap="selectGift" data-gift="{{item}}">
        <!-- 礼物图标 -->
        <view class="gift-icon-area">
          <text class="gift-emoji">{{item.icon}}</text>
          <view class="gift-badge" wx:if="{{item.is_hot}}">热门</view>
          <view class="gift-badge limited" wx:if="{{item.is_limited}}">限量</view>
        </view>

        <!-- 礼物信息 -->
        <view class="gift-info">
          <text class="gift-name">{{item.name}}</text>
          <view class="gift-price">
            <text class="price-icon">💖</text>
            <text class="price-text">{{item.price}}</text>
          </view>
        </view>

        <!-- 购买按钮 -->
        <view class="buy-btn" bindtap="buyGift" data-gift="{{item}}" catchtap="">
          <text class="buy-text">购买</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <view class="loading-spinner" wx:if="{{loading}}"></view>
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>
  </view>
</view>

<!-- 简化的礼物详情弹窗 -->
<view class="gift-modal" wx:if="{{showGiftModal}}" bindtap="hideGiftModal">
  <view class="modal-content" catchtap="">
    <!-- 弹窗头部 -->
    <view class="modal-header">
      <text class="modal-title">礼物详情</text>
      <view class="close-btn" bindtap="hideGiftModal">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 礼物信息 -->
    <view class="modal-body">
      <view class="gift-display">
        <text class="gift-large-icon">{{selectedGift.icon}}</text>
        <text class="gift-title">{{selectedGift.name}}</text>
        <text class="gift-description">{{selectedGift.description}}</text>
      </view>

      <!-- 数量和价格 -->
      <view class="purchase-section">
        <view class="quantity-row">
          <text class="quantity-label">数量</text>
          <view class="quantity-controls">
            <view class="quantity-btn" bindtap="decreaseQuantity">-</view>
            <text class="quantity-value">{{giftQuantity}}</text>
            <view class="quantity-btn" bindtap="increaseQuantity">+</view>
          </view>
        </view>

        <view class="total-row">
          <text class="total-label">总计</text>
          <view class="total-amount">
            <text class="amount-icon">💖</text>
            <text class="amount-text">{{totalPrice}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 购买按钮 -->
    <view class="modal-footer">
      <view class="purchase-btn" bindtap="buyNow">
        <text class="purchase-text">立即购买</text>
      </view>
    </view>
  </view>
</view>

<!-- 购买成功提示 -->
<view class="success-modal" wx:if="{{showSuccessModal}}">
  <view class="success-content">
    <text class="success-icon">🎉</text>
    <text class="success-title">购买成功</text>
    <text class="success-desc">礼物已添加到你的礼物库</text>
    <view class="success-btn" bindtap="hideSuccessModal">
      <text class="success-btn-text">知道了</text>
    </view>
  </view>
</view>
