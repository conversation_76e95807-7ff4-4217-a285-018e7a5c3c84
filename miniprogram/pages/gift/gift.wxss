/* pages/gift/gift.wxss */

/* 礼物商城容器 */
.gift-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 礼物商城头部 */
.gift-header {
  padding: 48rpx 32rpx 32rpx;
  padding-top: calc(48rpx + env(safe-area-inset-top));
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.95) 0%, rgba(245, 87, 108, 0.9) 100%);
  backdrop-filter: blur(40rpx);
  border-bottom: 1rpx solid rgba(255,255,255,0.2);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.gift-title-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16rpx;
}

.gift-icon {
  margin-bottom: 8rpx;
}

.gift-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.gift-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 余额卡片 */
.balance-section {
  flex-shrink: 0;
}

.balance-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 32rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  min-width: 200rpx;
}

.balance-icon {
  margin-bottom: 8rpx;
}

.balance-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.recharge-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  font-weight: 500;
}

/* 礼物分类 */
.gift-categories {
  background: white;
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.categories-header {
  margin-bottom: 24rpx;
}

.categories-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.categories-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 24rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  border-radius: 24rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.category-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.category-item.active .category-name {
  color: white;
}

.category-icon {
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 礼物列表 */
.gift-list-section {
  margin: 32rpx;
}

.gift-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.gift-item {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.gift-item:active {
  transform: scale(0.98);
}

/* 礼物图标容器 */
.gift-icon-container {
  position: relative;
  margin-bottom: 16rpx;
}

.gift-icon {
  margin-bottom: 8rpx;
}

.popular-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
  font-weight: bold;
}

/* 礼物信息 */
.gift-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.gift-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.gift-desc {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 价格信息 */
.gift-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  margin-top: 16rpx;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.price-icon {
  margin-right: 4rpx;
}

.current-price {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff6b9d;
}

.original-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-badge {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: bold;
}

/* 购买按钮 */
.gift-buy-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-top: 16rpx;
  transition: all 0.3s ease;
}

.gift-buy-btn:active {
  transform: scale(0.95);
}

/* 购买记录 */
.purchase-history {
  background: white;
  margin: 32rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.history-toggle {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #667eea;
  font-size: 24rpx;
}

.history-list {
  padding: 0 32rpx 32rpx;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-gift {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.history-icon {
  margin-right: 8rpx;
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.history-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.history-time {
  font-size: 20rpx;
  color: #999;
}

.history-price {
  margin: 0 24rpx;
}

.price-text {
  font-size: 24rpx;
  color: #ff6b9d;
  font-weight: bold;
}

.history-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.history-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

/* 弹窗样式 */
.gift-modal,
.recharge-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.gift-modal-content,
.recharge-modal-content {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  backdrop-filter: blur(40rpx);
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 32rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

/* 礼物展示 */
.gift-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.display-icon {
  margin-bottom: 16rpx;
}

.display-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.display-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 模态框价格 */
.modal-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 24rpx;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.price-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b9d;
}

.price-note {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 数量控制 */
.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.quantity-btn {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.quantity-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  min-width: 48rpx;
  text-align: center;
}

/* 总价 */
.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 107, 157, 0.1);
  border-radius: 24rpx;
  margin-bottom: 32rpx;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.total-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b9d;
}

/* 模态框底部 */
.modal-footer {
  padding: 32rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
}

.confirm-buy-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

/* 充值套餐 */
.recharge-packages {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.recharge-item {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.recharge-item.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.package-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.package-coins {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.recharge-item.selected .package-coins,
.recharge-item.selected .package-price {
  color: white;
}

.package-price {
  font-size: 24rpx;
  color: #666;
}

.package-bonus {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: bold;
}

.recharge-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.recharge-btn:disabled {
  background: #ccc;
  color: #999;
}
