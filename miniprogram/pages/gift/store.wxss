/* pages/gift/store.wxss */
.gift-store-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
}

/* 简化的头部 */
.gift-header {
  background: #ffffff;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  border-bottom: 1rpx solid #f0f0f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-icon {
  font-size: 28rpx;
}

.cart-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff6b9d;
  color: white;
  border-radius: 50%;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
}

/* 余额卡片 */
.balance-section {
  padding: 24rpx 32rpx;
}

.balance-card {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8e9e 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.balance-info {
  flex: 1;
}

.balance-label {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  display: block;
}

.balance-amount {
  font-size: 48rpx;
  font-weight: 700;
  display: block;
}

.recharge-btn {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  border: none;
}

.recharge-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

/* 分类标签 */
.category-section {
  padding: 0 32rpx 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-tabs {
  display: flex;
  gap: 16rpx;
}

.category-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  background: #ffffff;
  white-space: nowrap;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.category-tab.active {
  background: #ff6b9d;
  color: white;
}

.tab-icon {
  font-size: 32rpx;
}

.tab-text {
  font-size: 24rpx;
  font-weight: 500;
}

.category-tab.active .tab-text {
  color: white;
}

/* 礼物网格 */
.gifts-section {
  padding: 0 32rpx;
}

.gifts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.gift-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.gift-card:active {
  transform: scale(0.98);
}

.gift-icon-area {
  position: relative;
  margin-bottom: 16rpx;
}

.gift-emoji {
  font-size: 80rpx;
  display: block;
}

.gift-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff6b9d;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
}

.gift-badge.limited {
  background: #ffd700;
  color: #8b5a00;
}

.gift-info {
  flex: 1;
  margin-bottom: 16rpx;
}

.gift-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.gift-price {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.price-icon {
  font-size: 24rpx;
}

.price-text {
  font-size: 28rpx;
  font-weight: 700;
  color: #ff6b9d;
}

.buy-btn {
  width: 100%;
  background: #ff6b9d;
  color: white;
  border-radius: 32rpx;
  padding: 16rpx 0;
  border: none;
  transition: all 0.3s ease;
}

.buy-btn:active {
  background: #ff5a8a;
}

.buy-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

/* 礼物详情弹窗 */
.gift-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #666;
}

.modal-body {
  padding: 32rpx;
}

.gift-display {
  text-align: center;
  margin-bottom: 32rpx;
}

.gift-large-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.gift-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.gift-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.purchase-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.quantity-row, .total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-label, .total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.quantity-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  min-width: 48rpx;
  text-align: center;
}

.total-amount {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.amount-icon {
  font-size: 24rpx;
}

.amount-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #ff6b9d;
}

.modal-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.purchase-btn {
  width: 100%;
  background: #ff6b9d;
  color: white;
  border-radius: 32rpx;
  padding: 20rpx 0;
  text-align: center;
}

.purchase-text {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
}

/* 购买成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-content {
  width: 70%;
  max-width: 500rpx;
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
}

.success-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  display: block;
}

.success-btn {
  background: #ff6b9d;
  color: white;
  border-radius: 32rpx;
  padding: 16rpx 48rpx;
  border: none;
}

.success-btn-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
  background: #f9fafb;
  border: 2rpx solid transparent;
}

.category-item.active {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
}

/* 礼物列表 */
.gift-list {
  padding: 20rpx;
}

.gift-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.gift-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.gift-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  display: block;
}

.gift-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.gift-desc {
  font-size: 22rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.gift-price {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.price-icon {
  font-size: 24rpx;
  color: #f59e0b;
  margin-right: 6rpx;
}

.price-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #f59e0b;
}

.gift-btn {
  width: 100%;
  height: 60rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #9ca3af;
}

.loading-icon {
  margin-right: 12rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 购买弹窗 */
.purchase-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.purchase-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.purchase-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.purchase-gift-icon {
  font-size: 100rpx;
  margin-bottom: 16rpx;
  display: block;
}

.purchase-gift-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.purchase-gift-price {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.purchase-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.confirm-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
}

/* 赠送弹窗 */
.gift-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.gift-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}

.gift-target {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 12rpx;
}

.target-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.target-info {
  flex: 1;
}

.target-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1f2937;
}

.gift-message-section {
  margin-bottom: 30rpx;
}

.message-label {
  font-size: 26rpx;
  color: #374151;
  margin-bottom: 12rpx;
}

.message-input {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #1f2937;
  box-sizing: border-box;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.history-btn {
  flex: 1;
  height: 70rpx;
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

.cart-btn {
  flex: 1;
  height: 70rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}
