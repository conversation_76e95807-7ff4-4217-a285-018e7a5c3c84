// pages/gift/store.js
const { giftAPI, paymentAPI } = require('../../utils/api')

Page({
  data: {
    categories: [],
    activeCategory: 1,
    giftsList: [],
    userBalance: 0,
    cartCount: 0,
    selectedGift: null,
    showGiftModal: false,
    showSuccessModal: false,
    giftQuantity: 1,
    totalPrice: 0,
    hasMore: true,
    loading: false,
    targetUser: null
  },

  onLoad(options) {
    // 检查是否有目标用户（赠送礼物）
    if (options.userId) {
      this.setData({
        targetUser: {
          id: options.userId,
          nickname: options.nickname || '用户',
          avatar: options.avatar || '/images/avatar/default.svg'
        }
      })
    }

    this.loadUserBalance()
    this.loadCategories()
    this.loadGifts()
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 加载用户余额
  loadUserBalance() {
    // 模拟用户余额
    this.setData({
      userBalance: 1580
    })
  },

  // 加载礼物分类
  loadCategories() {
    const mockCategories = [
      { id: 1, name: '热门', icon: '🔥' },
      { id: 2, name: '鲜花', icon: '🌹' },
      { id: 3, name: '礼品', icon: '🎁' },
      { id: 4, name: '美食', icon: '🍰' },
      { id: 5, name: '奢侈品', icon: '💎' }
    ]

    this.setData({
      categories: mockCategories
    })
  },

  // 加载礼物列表
  loadGifts() {
    const mockGifts = [
      {
        id: 1,
        name: '玫瑰花',
        icon: '🌹',
        price: 10,
        description: '代表爱情的经典礼物',
        is_hot: true,
        is_limited: false,
        category_id: 2
      },
      {
        id: 2,
        name: '巧克力',
        icon: '🍫',
        price: 20,
        description: '甜蜜的心意',
        is_hot: false,
        is_limited: false,
        category_id: 4
      },
      {
        id: 3,
        name: '钻石戒指',
        icon: '💍',
        price: 999,
        description: '永恒的承诺',
        is_hot: true,
        is_limited: true,
        category_id: 5
      },
      {
        id: 4,
        name: '泰迪熊',
        icon: '🧸',
        price: 50,
        description: '可爱的陪伴',
        is_hot: false,
        is_limited: false,
        category_id: 3
      },
      {
        id: 5,
        name: '香水',
        icon: '🌸',
        price: 188,
        description: '迷人的香气',
        is_hot: true,
        is_limited: false,
        category_id: 5
      },
      {
        id: 6,
        name: '生日蛋糕',
        icon: '🎂',
        price: 88,
        description: '甜蜜的祝福',
        is_hot: false,
        is_limited: false,
        category_id: 4
      }
    ]

    this.setData({
      giftsList: mockGifts
    })
  },

  // 切换分类
  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.category
    this.setData({
      activeCategory: categoryId
    })
    // 这里可以根据分类筛选礼物
    this.loadGifts()
  },

  // 选择礼物
  selectGift(e) {
    const gift = e.currentTarget.dataset.gift
    this.setData({
      selectedGift: gift,
      giftQuantity: 1,
      totalPrice: gift.price,
      showGiftModal: true
    })
  },

  // 隐藏礼物详情弹窗
  hideGiftModal() {
    this.setData({
      showGiftModal: false,
      selectedGift: null,
      giftQuantity: 1,
      totalPrice: 0
    })
  },

  // 增加数量
  increaseQuantity() {
    const quantity = this.data.giftQuantity + 1
    this.setData({
      giftQuantity: quantity,
      totalPrice: this.data.selectedGift.price * quantity
    })
  },

  // 减少数量
  decreaseQuantity() {
    if (this.data.giftQuantity > 1) {
      const quantity = this.data.giftQuantity - 1
      this.setData({
        giftQuantity: quantity,
        totalPrice: this.data.selectedGift.price * quantity
      })
    }
  },

  // 直接购买礼物
  buyGift(e) {
    const gift = e.currentTarget.dataset.gift

    if (this.data.userBalance < gift.price) {
      wx.showModal({
        title: '余额不足',
        content: '您的爱心币余额不足，是否前往充值？',
        success: (res) => {
          if (res.confirm) {
            this.goToRecharge()
          }
        }
      })
      return
    }

    this.purchaseGift(gift, 1)
  },

  // 立即购买
  buyNow() {
    if (this.data.userBalance < this.data.totalPrice) {
      wx.showModal({
        title: '余额不足',
        content: '您的爱心币余额不足，是否前往充值？',
        success: (res) => {
          if (res.confirm) {
            this.goToRecharge()
          }
        }
      })
      return
    }

    this.purchaseGift(this.data.selectedGift, this.data.giftQuantity)
  },

  // 购买礼物
  purchaseGift(gift, quantity) {
    const totalCost = gift.price * quantity

    wx.showLoading({
      title: '购买中...'
    })

    setTimeout(() => {
      wx.hideLoading()

      // 更新余额
      const newBalance = this.data.userBalance - totalCost
      this.setData({
        userBalance: newBalance,
        showGiftModal: false,
        showSuccessModal: true
      })

      // 如果是赠送给其他用户
      if (this.data.targetUser) {
        wx.showToast({
          title: `已赠送给${this.data.targetUser.nickname}`,
          icon: 'success'
        })
      }
    }, 1500)
  },

  // 隐藏成功弹窗
  hideSuccessModal() {
    this.setData({
      showSuccessModal: false
    })
  },

  // 去充值
  goToRecharge() {
    wx.navigateTo({
      url: '/pages/recharge/recharge'
    })
  },

  // 查看历史记录
  goToHistory() {
    wx.navigateTo({
      url: '/pages/gift/history'
    })
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) return

    wx.showToast({
      title: '没有更多礼物了',
      icon: 'none'
    })
  }

  // 加载用户爱心币
  async loadUserCoins() {
    try {
      const response = await paymentAPI.getCoinBalance()
      this.setData({ userCoins: response.data.balance || 0 })
    } catch (error) {
      console.error('加载爱心币失败:', error)
      wx.showToast({
        title: '加载余额失败',
        icon: 'none'
      })
      this.setData({ userCoins: 0 })
    }
  },

  // 加载礼物分类
  async loadGiftCategories() {
    try {
      const response = await giftAPI.getGiftCategories()
      const categories = response.data || []

      // 添加"全部"分类
      const allCategories = [
        { id: 0, name: '全部', icon: '🎁' },
        ...categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          icon: cat.icon || '🎁'
        }))
      ]

      this.setData({ giftCategories: allCategories })
    } catch (error) {
      console.error('加载分类失败:', error)
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      })
      // 设置默认的全部分类
      this.setData({
        giftCategories: [{ id: 0, name: '全部', icon: '🎁' }]
      })
    }
  },

  // 加载礼物列表
  async loadGiftList() {
    try {
      this.setData({ loading: true })

      // 构建查询参数
      const params = {}
      if (this.data.currentCategory > 0) {
        params.category_id = this.data.currentCategory
      }

      const response = await giftAPI.getGifts(params)
      const gifts = response.data || []

      // 处理礼品数据
      const processedGifts = gifts.map(gift => ({
        id: gift.id,
        name: gift.name,
        price: gift.price,
        icon: gift.icon || '🎁',
        animation_url: gift.animation_url,
        category: gift.category?.id || 0,
        description: gift.description || '',
        original_price: gift.original_price,
        is_rare: gift.is_rare,
        is_limited: gift.is_limited,
        limited_quantity: gift.limited_quantity,
        sold_quantity: gift.sold_quantity,
        effects: gift.effects,
        is_popular: gift.is_popular,
        send_count: gift.send_count,
        is_available: gift.is_available,
        discount_rate: gift.discount_rate
      }))

      this.setData({
        giftList: processedGifts,
        loading: false
      })
    } catch (error) {
      console.error('加载礼物失败:', error)
      wx.showToast({
        title: '加载礼物失败',
        icon: 'none'
      })
      this.setData({
        giftList: [],
        loading: false
      })
    }
  },

  // 切换分类
  switchCategory(e) {
    const { category } = e.currentTarget.dataset
    this.setData({ currentCategory: category })
    this.loadGiftList()
  },

  // 选择礼物
  selectGift(e) {
    const { gift } = e.currentTarget.dataset
    
    if (this.data.targetUser) {
      // 赠送模式
      this.setData({
        selectedGift: gift,
        showGiftModal: true
      })
    } else {
      // 购买模式
      this.setData({
        selectedGift: gift,
        showPurchaseModal: true
      })
    }
  },

  // 购买礼物
  async purchaseGift() {
    const { selectedGift, userCoins } = this.data
    
    if (userCoins < selectedGift.price) {
      wx.showModal({
        title: '爱心币不足',
        content: '您的爱心币不足，是否前往充值？',
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/recharge/center'
            })
          }
        }
      })
      return
    }
    
    try {
      this.setData({ purchasing: true })

      // 购买礼物（添加到自己的礼物盒）
      const purchaseData = {
        gift_id: selectedGift.id,
        receiver_id: app.globalData.userInfo.id, // 购买给自己
        quantity: 1,
        message: '购买礼物',
        is_anonymous: false
      }

      await giftAPI.sendGift(purchaseData)

      // 刷新金币余额
      await this.loadUserCoins()

      this.setData({
        purchasing: false,
        showPurchaseModal: false
      })

      wx.showToast({
        title: '购买成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('购买失败:', error)
      this.setData({ purchasing: false })

      const errorMsg = error.message || '购买失败'
      wx.showToast({
        title: errorMsg,
        icon: 'none'
      })
    }
  },

  // 赠送礼物
  async sendGift() {
    const { selectedGift, targetUser, giftMessage, userCoins } = this.data

    if (!targetUser) {
      wx.showToast({ title: '请选择赠送对象', icon: 'none' })
      return
    }

    if (userCoins < selectedGift.price) {
      wx.showModal({
        title: '爱心币不足',
        content: '您的爱心币不足，是否前往充值？',
        confirmText: '去充值',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/recharge/center'
            })
          }
        }
      })
      return
    }

    try {
      this.setData({ purchasing: true })

      // 发送礼品
      const giftData = {
        gift_id: selectedGift.id,
        receiver_id: targetUser.id,
        quantity: 1,
        message: giftMessage || '',
        is_anonymous: false
      }

      await giftAPI.sendGift(giftData)

      // 刷新金币余额
      await this.loadUserCoins()

      this.setData({
        purchasing: false,
        showGiftModal: false,
        giftMessage: ''
      })

      wx.showToast({
        title: '赠送成功',
        icon: 'success'
      })
      
      // 返回聊天页面
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      
    } catch (error) {
      console.error('赠送失败:', error)
      this.setData({ purchasing: false })
      wx.showToast({
        title: '赠送失败',
        icon: 'none'
      })
    }
  },

  // 输入赠送消息
  onGiftMessageInput(e) {
    this.setData({
      giftMessage: e.detail.value
    })
  },

  // 关闭弹窗
  hidePurchaseModal() {
    this.setData({ showPurchaseModal: false })
  },

  hideGiftModal() {
    this.setData({ 
      showGiftModal: false,
      giftMessage: ''
    })
  },

  // 查看礼物记录
  viewGiftHistory() {
    wx.navigateTo({
      url: '/pages/gift/history'
    })
  },

  // 前往充值
  goToRecharge() {
    wx.navigateTo({
      url: '/pages/recharge/center'
    })
  }
})
