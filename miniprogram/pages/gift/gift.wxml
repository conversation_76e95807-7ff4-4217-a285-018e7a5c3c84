<!--pages/gift/gift.wxml-->
<view class="gift-container">
  <!-- 礼物商城头部 -->
  <view class="gift-header gradient-animated">
    <view class="header-content">
      <view class="gift-title-section">
        <custom-icon name="gift" type="secondary" size="xlarge" class="gift-icon premium-glow"></custom-icon>
        <text class="gift-title shimmer-text">礼物商城</text>
        <text class="gift-subtitle">用心意表达爱意</text>
      </view>
      
      <!-- 用户金币余额 -->
      <view class="balance-section" wx:if="{{userBalance}}">
        <view class="balance-card">
          <custom-icon name="star" type="gold" size="medium" class="balance-icon sparkle-element"></custom-icon>
          <text class="balance-text">余额: {{userBalance.coins}}金币</text>
          <view class="recharge-btn" bindtap="goToRecharge">
            <text class="recharge-text">充值</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 礼物分类 -->
  <view class="gift-categories slide-in-up">
    <view class="categories-header">
      <text class="categories-title">礼物分类</text>
    </view>
    
    <scroll-view class="categories-scroll" scroll-x>
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === category.id ? 'active' : ''}} premium-button"
          wx:for="{{giftCategories}}" 
          wx:key="id"
          wx:for-item="category"
          bindtap="selectCategory"
          data-category="{{category.id}}"
        >
          <custom-icon name="{{category.iconName}}" type="{{category.iconType}}" size="medium" class="category-icon floating-element"></custom-icon>
          <text class="category-name">{{category.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 礼物列表 -->
  <view class="gift-list-section">
    <view class="gift-grid">
      <view 
        class="gift-item premium-card"
        wx:for="{{filteredGifts}}" 
        wx:key="id"
        wx:for-item="gift"
        bindtap="selectGift"
        data-gift="{{gift}}"
      >
        <!-- 礼物图标 -->
        <view class="gift-icon-container">
          <custom-icon name="{{gift.iconName}}" type="{{gift.iconType}}" size="xlarge" class="gift-icon {{gift.isPopular ? 'premium-glow' : 'floating-element'}}"></custom-icon>
          <view class="popular-badge sparkle-element" wx:if="{{gift.isPopular}}">
            <text class="badge-text">热门</text>
          </view>
        </view>
        
        <!-- 礼物信息 -->
        <view class="gift-info">
          <text class="gift-name">{{gift.name}}</text>
          <text class="gift-desc">{{gift.description}}</text>
          
          <!-- 价格信息 -->
          <view class="gift-price">
            <view class="price-section">
              <custom-icon name="star" type="gold" size="small" class="price-icon"></custom-icon>
              <text class="current-price">{{gift.price}}金币</text>
              <text class="original-price" wx:if="{{gift.originalPrice}}">{{gift.originalPrice}}金币</text>
            </view>
            <view class="discount-badge" wx:if="{{gift.discount}}">
              <text class="discount-text">{{gift.discount}}折</text>
            </view>
          </view>
        </view>
        
        <!-- 购买按钮 -->
        <view class="gift-buy-btn premium-button heartbeat-element" bindtap="buyGift" data-gift="{{gift}}" catchtap="">
          <text class="buy-text">购买</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 购买记录 -->
  <view class="purchase-history" wx:if="{{showHistory}}">
    <view class="history-header">
      <text class="history-title">购买记录</text>
      <view class="history-toggle" bindtap="toggleHistory">
        <text class="toggle-text">{{showHistory ? '收起' : '展开'}}</text>
        <text class="toggle-icon">{{showHistory ? '▲' : '▼'}}</text>
      </view>
    </view>
    
    <view class="history-list">
      <view class="history-item" wx:for="{{purchaseHistory}}" wx:key="id">
        <view class="history-gift">
          <custom-icon name="{{item.giftIconName}}" type="{{item.giftIconType}}" size="medium" class="history-icon"></custom-icon>
          <view class="history-info">
            <text class="history-name">{{item.giftName}}</text>
            <text class="history-time">{{item.purchaseTime}}</text>
          </view>
        </view>
        <view class="history-price">
          <text class="price-text">{{item.price}}金币</text>
        </view>
        <view class="history-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 礼物详情弹窗 -->
<view class="gift-modal" wx:if="{{selectedGift}}" bindtap="closeGiftModal">
  <view class="gift-modal-content premium-card" catchtap="">
    <view class="modal-header">
      <text class="modal-title">礼物详情</text>
      <view class="modal-close" bindtap="closeGiftModal">
        <custom-icon name="close" type="transparent" size="medium"></custom-icon>
      </view>
    </view>
    
    <view class="modal-body">
      <!-- 礼物展示 -->
      <view class="gift-display">
        <custom-icon name="{{selectedGift.iconName}}" type="{{selectedGift.iconType}}" size="xlarge" class="display-icon premium-glow"></custom-icon>
        <text class="display-name">{{selectedGift.name}}</text>
        <text class="display-desc">{{selectedGift.description}}</text>
      </view>
      
      <!-- 价格信息 -->
      <view class="modal-price">
        <view class="price-info">
          <custom-icon name="star" type="gold" size="medium" class="price-icon sparkle-element"></custom-icon>
          <text class="price-amount">{{selectedGift.price}}金币</text>
        </view>
        <text class="price-note">购买后可立即赠送给心仪的TA</text>
      </view>
      
      <!-- 购买数量 -->
      <view class="quantity-section">
        <text class="quantity-label">购买数量</text>
        <view class="quantity-controls">
          <view class="quantity-btn" bindtap="decreaseQuantity">
            <text class="btn-text">-</text>
          </view>
          <text class="quantity-value">{{giftQuantity}}</text>
          <view class="quantity-btn" bindtap="increaseQuantity">
            <text class="btn-text">+</text>
          </view>
        </view>
      </view>
      
      <!-- 总价 -->
      <view class="total-price">
        <text class="total-label">总计:</text>
        <text class="total-amount">{{selectedGift.price * giftQuantity}}金币</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="confirm-buy-btn premium-button pulse-ring-element" bindtap="confirmBuyGift">
        <text class="shimmer-text">确认购买</text>
      </button>
    </view>
  </view>
</view>

<!-- 充值弹窗 -->
<view class="recharge-modal" wx:if="{{showRecharge}}" bindtap="closeRechargeModal">
  <view class="recharge-modal-content premium-card" catchtap="">
    <view class="modal-header">
      <text class="modal-title">金币充值</text>
      <view class="modal-close" bindtap="closeRechargeModal">
        <custom-icon name="close" type="transparent" size="medium"></custom-icon>
      </view>
    </view>
    
    <view class="recharge-packages">
      <view 
        class="recharge-item {{selectedRecharge === package.id ? 'selected' : ''}} premium-button"
        wx:for="{{rechargePackages}}" 
        wx:key="id"
        wx:for-item="package"
        bindtap="selectRechargePackage"
        data-package="{{package}}"
      >
        <view class="package-info">
          <text class="package-coins">{{package.coins}}金币</text>
          <text class="package-price">¥{{package.price}}</text>
        </view>
        <view class="package-bonus" wx:if="{{package.bonus}}">
          <text class="bonus-text">送{{package.bonus}}金币</text>
        </view>
      </view>
    </view>
    
    <button class="recharge-btn premium-button" bindtap="confirmRecharge" disabled="{{!selectedRecharge}}">
      <text class="{{selectedRecharge ? 'shimmer-text' : ''}}">立即充值</text>
    </button>
  </view>
</view>
