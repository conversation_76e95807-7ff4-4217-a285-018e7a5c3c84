/**
 * 身份认证页面
 * Task 3.7: 身份认证系统 - 认证流程与界面
 */

const identityManager = require('../../utils/identity-manager')

Page({
  data: {
    // 用户认证状态
    userStatus: {
      level: 'none',
      status: 'not_started',
      verification: null,
      progress: null
    },
    
    // 认证等级列表
    verificationLevels: [],
    
    // 当前选择的认证等级
    selectedLevel: 'standard',
    
    // 认证进度
    verificationProgress: null,
    
    // 页面状态
    pageState: {
      isLoading: true,
      showLevelSelector: false,
      showProgress: false,
      currentStep: 0
    },
    
    // 证件上传状态
    documentUpload: {
      isUploading: false,
      uploadProgress: 0,
      selectedImages: []
    },
    
    // 人脸识别状态
    faceVerification: {
      isProcessing: false,
      attempts: 0,
      maxAttempts: 3,
      showCamera: false
    }
  },

  onLoad() {
    this.loadUserVerificationStatus()
    this.loadVerificationLevels()
  },

  onShow() {
    // 刷新认证状态
    this.refreshVerificationStatus()
  },

  /**
   * 加载用户认证状态
   */
  async loadUserVerificationStatus() {
    try {
      this.setData({
        'pageState.isLoading': true
      })
      
      const userId = this.getCurrentUserId()
      const userStatus = identityManager.getUserVerificationStatus(userId)
      
      this.setData({
        userStatus: userStatus,
        'pageState.isLoading': false
      })
      
      // 如果有进行中的认证，加载进度
      if (userStatus.verification && userStatus.verification.status === 'in_progress') {
        this.loadVerificationProgress(userStatus.verification.id)
      }
      
      console.log('📋 用户认证状态加载完成')
    } catch (error) {
      console.error('加载用户认证状态失败:', error)
      this.showError('加载失败，请重试')
    }
  },

  /**
   * 加载认证等级列表
   */
  loadVerificationLevels() {
    try {
      const levels = identityManager.getAllVerificationLevels()
      
      this.setData({
        verificationLevels: levels
      })
      
      console.log('📋 认证等级列表加载完成')
    } catch (error) {
      console.error('加载认证等级失败:', error)
    }
  },

  /**
   * 加载认证进度
   */
  loadVerificationProgress(verificationId) {
    try {
      const progress = identityManager.getVerificationProgress(verificationId)
      
      this.setData({
        verificationProgress: progress,
        'pageState.showProgress': true
      })
      
      console.log('📊 认证进度加载完成')
    } catch (error) {
      console.error('加载认证进度失败:', error)
    }
  },

  /**
   * 刷新认证状态
   */
  refreshVerificationStatus() {
    this.loadUserVerificationStatus()
  },

  /**
   * 显示等级选择器
   */
  onShowLevelSelector() {
    this.setData({
      'pageState.showLevelSelector': true
    })
  },

  /**
   * 隐藏等级选择器
   */
  onHideLevelSelector() {
    this.setData({
      'pageState.showLevelSelector': false
    })
  },

  /**
   * 选择认证等级
   */
  onSelectLevel(event) {
    const level = event.currentTarget.dataset.level
    
    this.setData({
      selectedLevel: level,
      'pageState.showLevelSelector': false
    })
  },

  /**
   * 开始认证
   */
  async onStartVerification() {
    try {
      const userId = this.getCurrentUserId()
      const level = this.data.selectedLevel
      
      wx.showLoading({
        title: '正在创建认证...'
      })
      
      const result = await identityManager.startVerification(userId, level)
      
      wx.hideLoading()
      
      if (result.success) {
        wx.showToast({
          title: '认证创建成功',
          icon: 'success'
        })
        
        // 刷新状态并显示进度
        this.refreshVerificationStatus()
        this.loadVerificationProgress(result.verification.id)
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('开始认证失败:', error)
      this.showError('创建认证失败，请重试')
    }
  },

  /**
   * 上传证件照片
   */
  async onUploadDocument(event) {
    const documentType = event.currentTarget.dataset.type
    
    try {
      // 选择图片
      const chooseResult = await this.chooseImage()
      if (!chooseResult.success) {
        return
      }
      
      this.setData({
        'documentUpload.isUploading': true,
        'documentUpload.uploadProgress': 0
      })
      
      const userId = this.getCurrentUserId()
      const verificationId = this.data.userStatus.verification.id
      
      // 模拟上传进度
      this.simulateUploadProgress()
      
      const result = await identityManager.uploadDocument(
        userId, 
        verificationId, 
        documentType, 
        chooseResult.imageData
      )
      
      this.setData({
        'documentUpload.isUploading': false
      })
      
      if (result.success) {
        wx.showToast({
          title: '证件上传成功',
          icon: 'success'
        })
        
        // 刷新进度
        this.loadVerificationProgress(verificationId)
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      this.setData({
        'documentUpload.isUploading': false
      })
      console.error('上传证件失败:', error)
      this.showError('上传失败，请重试')
    }
  },

  /**
   * 选择图片
   */
  chooseImage() {
    return new Promise((resolve) => {
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            resolve({
              success: true,
              imageData: {
                tempFilePath: res.tempFilePaths[0],
                size: res.tempFiles[0].size
              }
            })
          } else {
            resolve({ success: false })
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          resolve({ success: false })
        }
      })
    })
  },

  /**
   * 模拟上传进度
   */
  simulateUploadProgress() {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 20
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
      }
      
      this.setData({
        'documentUpload.uploadProgress': Math.floor(progress)
      })
    }, 200)
  },

  /**
   * 开始人脸识别
   */
  async onStartFaceVerification() {
    try {
      this.setData({
        'faceVerification.showCamera': true
      })
      
      // 这里应该调用人脸识别组件
      // 暂时模拟人脸识别流程
      setTimeout(() => {
        this.performFaceVerification()
      }, 1000)
    } catch (error) {
      console.error('启动人脸识别失败:', error)
      this.showError('启动人脸识别失败')
    }
  },

  /**
   * 执行人脸识别
   */
  async performFaceVerification() {
    try {
      this.setData({
        'faceVerification.isProcessing': true
      })
      
      const userId = this.getCurrentUserId()
      const verificationId = this.data.userStatus.verification.id
      
      // 模拟人脸数据
      const faceData = {
        image: 'base64_encoded_image',
        landmarks: [/* 人脸特征点 */]
      }
      
      const result = await identityManager.performFaceVerification(
        userId, 
        verificationId, 
        faceData
      )
      
      this.setData({
        'faceVerification.isProcessing': false,
        'faceVerification.showCamera': false,
        'faceVerification.attempts': this.data.faceVerification.attempts + 1
      })
      
      if (result.success) {
        wx.showToast({
          title: '人脸识别成功',
          icon: 'success'
        })
        
        // 刷新进度
        this.loadVerificationProgress(verificationId)
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        })
        
        // 检查是否还有尝试机会
        if (result.attemptsLeft > 0) {
          wx.showModal({
            title: '识别失败',
            content: `人脸识别失败，您还有${result.attemptsLeft}次机会，是否重试？`,
            success: (res) => {
              if (res.confirm) {
                this.onStartFaceVerification()
              }
            }
          })
        }
      }
    } catch (error) {
      this.setData({
        'faceVerification.isProcessing': false,
        'faceVerification.showCamera': false
      })
      console.error('人脸识别失败:', error)
      this.showError('人脸识别失败，请重试')
    }
  },

  /**
   * 取消人脸识别
   */
  onCancelFaceVerification() {
    this.setData({
      'faceVerification.showCamera': false,
      'faceVerification.isProcessing': false
    })
  },

  /**
   * 重新提交认证
   */
  async onResubmitVerification() {
    try {
      const userId = this.getCurrentUserId()
      const verificationId = this.data.userStatus.verification.id
      
      wx.showModal({
        title: '重新提交认证',
        content: '重新提交将清空当前进度，确定要继续吗？',
        success: async (res) => {
          if (res.confirm) {
            const result = await identityManager.resubmitVerification(userId, verificationId)
            
            if (result.success) {
              wx.showToast({
                title: '重新提交成功',
                icon: 'success'
              })
              
              this.refreshVerificationStatus()
            } else {
              wx.showToast({
                title: result.message,
                icon: 'none'
              })
            }
          }
        }
      })
    } catch (error) {
      console.error('重新提交认证失败:', error)
      this.showError('重新提交失败，请重试')
    }
  },

  /**
   * 取消认证
   */
  async onCancelVerification() {
    try {
      const userId = this.getCurrentUserId()
      const verificationId = this.data.userStatus.verification.id
      
      wx.showModal({
        title: '取消认证',
        content: '确定要取消当前认证吗？',
        success: async (res) => {
          if (res.confirm) {
            const result = await identityManager.cancelVerification(userId, verificationId)
            
            if (result.success) {
              wx.showToast({
                title: '认证已取消',
                icon: 'success'
              })
              
              this.refreshVerificationStatus()
            } else {
              wx.showToast({
                title: result.message,
                icon: 'none'
              })
            }
          }
        }
      })
    } catch (error) {
      console.error('取消认证失败:', error)
      this.showError('取消认证失败，请重试')
    }
  },

  /**
   * 查看认证历史
   */
  onViewHistory() {
    wx.navigateTo({
      url: '/pages/verification-history/verification-history'
    })
  },

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    return wx.getStorageSync('user_id') || 'anonymous'
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshVerificationStatus()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})
