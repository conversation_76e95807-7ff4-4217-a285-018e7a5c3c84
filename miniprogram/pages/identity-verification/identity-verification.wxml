<!--身份认证页面模板-->
<view class="verification-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">身份认证</text>
    <button class="history-btn" bindtap="onViewHistory">
      <image class="history-icon" src="/images/history.png"></image>
      <text class="history-text">历史</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{pageState.isLoading}}">
    <view class="loading-content">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">正在加载认证信息...</text>
    </view>
  </view>

  <!-- 认证内容 -->
  <scroll-view class="content-scroll" scroll-y="true" wx:if="{{!pageState.isLoading}}">
    <!-- 认证状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <image class="status-icon" src="/images/verify/{{userStatus.level}}.png"></image>
        <view class="status-info">
          <text class="status-level">{{userStatus.level === 'none' ? '未认证' : userStatus.level + '认证'}}</text>
          <text class="status-description">
            {{userStatus.status === 'not_started' ? '提升账户安全性，解锁更多功能' : 
              userStatus.status === 'pending' ? '认证申请已提交，等待处理' :
              userStatus.status === 'in_progress' ? '认证进行中，请完成剩余步骤' :
              userStatus.status === 'completed' ? '认证已完成，享受专属权益' :
              userStatus.status === 'rejected' ? '认证被拒绝，请重新提交' : '认证状态异常'}}
          </text>
        </view>
      </view>
      
      <!-- 认证进度 -->
      <view class="progress-section" wx:if="{{userStatus.progress}}">
        <view class="progress-header">
          <text class="progress-title">认证进度</text>
          <text class="progress-percentage">{{userStatus.progress.percentage.toFixed(0)}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{userStatus.progress.percentage}}%"></view>
        </view>
        <text class="progress-text">{{userStatus.progress.completed}}/{{userStatus.progress.total}} 步骤已完成</text>
      </view>
    </view>

    <!-- 认证等级选择 -->
    <view class="level-section" wx:if="{{userStatus.status === 'not_started'}}">
      <view class="section-header">
        <text class="section-title">选择认证等级</text>
        <text class="section-subtitle">不同等级享受不同权益</text>
      </view>
      
      <view class="level-list">
        <view 
          class="level-item {{selectedLevel === item.level ? 'selected' : ''}}"
          wx:for="{{verificationLevels}}"
          wx:key="level"
          data-level="{{item.level}}"
          bindtap="onSelectLevel"
        >
          <image class="level-icon" src="{{item.icon}}"></image>
          <view class="level-info">
            <text class="level-name">{{item.name}}</text>
            <view class="level-benefits">
              <text 
                class="benefit-item"
                wx:for="{{item.benefits}}"
                wx:for-item="benefit"
                wx:key="*this"
              >{{benefit}}</text>
            </view>
          </view>
          <view class="level-check">
            <image class="check-icon" src="/images/check.png" wx:if="{{selectedLevel === item.level}}"></image>
          </view>
        </view>
      </view>
      
      <button class="start-btn" bindtap="onStartVerification">
        开始{{selectedLevel}}认证
      </button>
    </view>

    <!-- 认证进度详情 -->
    <view class="progress-detail" wx:if="{{pageState.showProgress && verificationProgress}}">
      <view class="section-header">
        <text class="section-title">认证步骤</text>
        <text class="section-subtitle">请按顺序完成以下步骤</text>
      </view>
      
      <view class="steps-list">
        <view 
          class="step-item {{step.status}}"
          wx:for="{{verificationProgress.steps}}"
          wx:key="requirement"
        >
          <view class="step-indicator">
            <image class="step-icon" src="/images/step/{{step.status}}.png"></image>
          </view>
          
          <view class="step-content">
            <text class="step-title">{{getStepTitle(step.requirement)}}</text>
            <text class="step-description">{{getStepDescription(step.requirement)}}</text>
            
            <!-- 证件上传步骤 -->
            <view class="step-action" wx:if="{{step.requirement === 'id_card' && step.status === 'pending'}}">
              <button 
                class="action-btn upload-btn"
                data-type="id_card"
                bindtap="onUploadDocument"
                disabled="{{documentUpload.isUploading}}"
              >
                {{documentUpload.isUploading ? '上传中...' : '上传身份证'}}
              </button>
              
              <!-- 上传进度 -->
              <view class="upload-progress" wx:if="{{documentUpload.isUploading}}">
                <view class="progress-bar">
                  <view class="progress-fill" style="width: {{documentUpload.uploadProgress}}%"></view>
                </view>
                <text class="progress-text">{{documentUpload.uploadProgress}}%</text>
              </view>
            </view>
            
            <!-- 人脸识别步骤 -->
            <view class="step-action" wx:if="{{step.requirement === 'face_recognition' && step.status === 'pending'}}">
              <button 
                class="action-btn face-btn"
                bindtap="onStartFaceVerification"
                disabled="{{faceVerification.isProcessing}}"
              >
                {{faceVerification.isProcessing ? '识别中...' : '开始人脸识别'}}
              </button>
              
              <!-- 识别尝试次数 -->
              <text class="attempts-text" wx:if="{{faceVerification.attempts > 0}}">
                已尝试 {{faceVerification.attempts}}/{{faceVerification.maxAttempts}} 次
              </text>
            </view>
            
            <!-- 步骤详情 -->
            <view class="step-details" wx:if="{{step.details}}">
              <text class="detail-text" wx:if="{{step.details.uploadedAt}}">
                上传时间: {{formatTime(step.details.uploadedAt)}}
              </text>
              <text class="detail-text" wx:if="{{step.details.confidence}}">
                识别置信度: {{(step.details.confidence * 100).toFixed(1)}}%
              </text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons" wx:if="{{userStatus.status === 'rejected'}}">
        <button class="action-btn secondary-btn" bindtap="onCancelVerification">
          取消认证
        </button>
        <button class="action-btn primary-btn" bindtap="onResubmitVerification">
          重新提交
        </button>
      </view>
    </view>

    <!-- 认证完成 -->
    <view class="completion-section" wx:if="{{userStatus.status === 'completed'}}">
      <view class="completion-content">
        <image class="completion-icon" src="/images/success-large.png"></image>
        <text class="completion-title">认证完成</text>
        <text class="completion-subtitle">恭喜您完成{{userStatus.level}}认证</text>
        
        <view class="benefits-list">
          <text class="benefits-title">您现在可以享受以下权益：</text>
          <view 
            class="benefit-item"
            wx:for="{{getCurrentLevelBenefits()}}"
            wx:key="*this"
          >
            <image class="benefit-icon" src="/images/check-small.png"></image>
            <text class="benefit-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 认证说明 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">认证说明</text>
      </view>
      
      <view class="info-list">
        <view class="info-item">
          <image class="info-icon" src="/images/info/security.png"></image>
          <view class="info-content">
            <text class="info-title">安全保障</text>
            <text class="info-description">我们采用银行级加密技术保护您的个人信息</text>
          </view>
        </view>
        
        <view class="info-item">
          <image class="info-icon" src="/images/info/privacy.png"></image>
          <view class="info-content">
            <text class="info-title">隐私保护</text>
            <text class="info-description">认证信息仅用于身份验证，不会泄露给第三方</text>
          </view>
        </view>
        
        <view class="info-item">
          <image class="info-icon" src="/images/info/support.png"></image>
          <view class="info-content">
            <text class="info-title">客服支持</text>
            <text class="info-description">认证过程中遇到问题可联系客服获得帮助</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 人脸识别摄像头 -->
  <view class="camera-modal {{faceVerification.showCamera ? 'show' : ''}}" bindtap="onCancelFaceVerification">
    <view class="camera-content" catchtap="">
      <view class="camera-header">
        <text class="camera-title">人脸识别</text>
        <button class="close-btn" bindtap="onCancelFaceVerification">
          <image class="close-icon" src="/images/close.png"></image>
        </button>
      </view>
      
      <view class="camera-container">
        <camera class="camera-view" mode="normal" flash="off"></camera>
        <view class="camera-overlay">
          <view class="face-frame"></view>
          <text class="camera-tip">请将面部对准框内</text>
        </view>
      </view>
      
      <view class="camera-actions">
        <button class="camera-btn cancel-btn" bindtap="onCancelFaceVerification">
          取消
        </button>
        <button 
          class="camera-btn capture-btn"
          bindtap="performFaceVerification"
          disabled="{{faceVerification.isProcessing}}"
        >
          {{faceVerification.isProcessing ? '识别中...' : '开始识别'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 等级选择弹窗 -->
  <view class="level-modal {{pageState.showLevelSelector ? 'show' : ''}}" bindtap="onHideLevelSelector">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择认证等级</text>
        <button class="close-btn" bindtap="onHideLevelSelector">
          <image class="close-icon" src="/images/close.png"></image>
        </button>
      </view>
      
      <view class="level-options">
        <view 
          class="level-option {{selectedLevel === item.level ? 'selected' : ''}}"
          wx:for="{{verificationLevels}}"
          wx:key="level"
          data-level="{{item.level}}"
          bindtap="onSelectLevel"
        >
          <image class="option-icon" src="{{item.icon}}"></image>
          <view class="option-info">
            <text class="option-name">{{item.name}}</text>
            <text class="option-requirements">需要: {{item.requirements.join(', ')}}</text>
          </view>
          <view class="option-check">
            <image class="check-icon" src="/images/check.png" wx:if="{{selectedLevel === item.level}}"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
