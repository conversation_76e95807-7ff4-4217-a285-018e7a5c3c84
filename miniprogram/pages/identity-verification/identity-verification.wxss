/* 身份认证页面样式 */

.verification-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.history-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: none;
  border-radius: 16px;
  font-size: 14px;
}

.history-icon {
  width: 16px;
  height: 16px;
}

.history-text {
  color: #666;
}

/* 内容滚动区 */
.content-scroll {
  flex: 1;
  padding: 16px;
}

/* 认证状态卡片 */
.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
}

.status-info {
  flex: 1;
}

.status-level {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.status-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 认证进度 */
.progress-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-title {
  font-size: 14px;
  color: #333;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #722ed1;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #722ed1, #9254de);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #999;
}

/* 认证等级选择 */
.level-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 14px;
  color: #666;
}

.level-list {
  margin-bottom: 20px;
}

.level-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.level-item:active {
  background: #f8f9fa;
}

.level-item.selected {
  border-color: #722ed1;
  background: #f9f0ff;
}

.level-icon {
  width: 32px;
  height: 32px;
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.level-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.benefit-item {
  font-size: 12px;
  color: #722ed1;
  background: #f9f0ff;
  padding: 2px 6px;
  border-radius: 8px;
}

.level-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 20px;
  height: 20px;
}

.start-btn {
  width: 100%;
  padding: 14px;
  background: #722ed1;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}

/* 认证步骤 */
.progress-detail {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.steps-list {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.step-item:last-child {
  border-bottom: none;
}

.step-indicator {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.step-icon {
  width: 24px;
  height: 24px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.step-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.step-action {
  margin-bottom: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.upload-btn {
  background: #722ed1;
  color: white;
}

.face-btn {
  background: #52c41a;
  color: white;
}

.action-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.upload-progress {
  margin-top: 8px;
}

.attempts-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.step-details {
  margin-top: 8px;
}

.detail-text {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 2px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
}

.secondary-btn {
  flex: 1;
  background: #f0f0f0;
  color: #666;
}

.primary-btn {
  flex: 1;
  background: #722ed1;
  color: white;
}

/* 认证完成 */
.completion-section {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  margin-bottom: 16px;
  text-align: center;
}

.completion-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.completion-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.completion-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.benefits-list {
  text-align: left;
}

.benefits-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.benefit-icon {
  width: 16px;
  height: 16px;
}

.benefit-text {
  font-size: 14px;
  color: #666;
}

/* 认证说明 */
.info-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.info-list {
  margin-top: 16px;
}

.info-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.info-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 人脸识别摄像头 */
.camera-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.camera-modal.show {
  opacity: 1;
  visibility: visible;
}

.camera-content {
  width: 90%;
  max-width: 400px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.camera-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: #f0f0f0;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.camera-container {
  position: relative;
  aspect-ratio: 3/4;
  background: #000;
}

.camera-view {
  width: 100%;
  height: 100%;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.face-frame {
  width: 200px;
  height: 250px;
  border: 2px solid #722ed1;
  border-radius: 50%;
  margin-bottom: 20px;
}

.camera-tip {
  color: white;
  font-size: 14px;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 16px;
}

.camera-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
}

.camera-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.capture-btn {
  background: #722ed1;
  color: white;
}

.capture-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

/* 等级选择弹窗 */
.level-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.level-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.level-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.level-options {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.level-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.level-option:active {
  background: #f8f9fa;
}

.level-option.selected {
  border-color: #722ed1;
  background: #f9f0ff;
}

.option-icon {
  width: 32px;
  height: 32px;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.option-requirements {
  font-size: 12px;
  color: #666;
}

.option-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载状态 */
.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  width: 40px;
  height: 40px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .status-card,
  .level-section,
  .progress-detail,
  .completion-section,
  .info-section {
    margin: 0 0 12px 0;
    border-radius: 8px;
  }
  
  .content-scroll {
    padding: 12px;
  }
  
  .camera-content {
    width: 95%;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .verification-container {
    background: #1a1a1a;
  }
  
  .page-header,
  .status-card,
  .level-section,
  .progress-detail,
  .completion-section,
  .info-section,
  .camera-content,
  .modal-content {
    background: #2d2d2d;
    color: white;
  }
  
  .page-title,
  .status-level,
  .section-title,
  .level-name,
  .step-title,
  .completion-title,
  .benefits-title,
  .info-title,
  .camera-title,
  .modal-title,
  .option-name {
    color: white;
  }
  
  .status-description,
  .section-subtitle,
  .step-description,
  .completion-subtitle,
  .benefit-text,
  .info-description,
  .option-requirements {
    color: #ccc;
  }
  
  .progress-bar,
  .level-item,
  .step-item,
  .info-item,
  .level-option {
    border-color: #404040;
  }
  
  .history-btn,
  .secondary-btn {
    background: #404040;
    color: #ccc;
  }
}
