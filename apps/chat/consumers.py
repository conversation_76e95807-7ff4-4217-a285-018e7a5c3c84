import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from .models import ChatSession, ChatMessage, OnlineStatus
from apps.users.models import User


class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.session_id = self.scope['url_route']['kwargs']['session_id']
        self.room_group_name = f'chat_{self.session_id}'
        
        # 验证用户权限
        user = self.scope["user"]
        if isinstance(user, AnonymousUser):
            await self.close()
            return
            
        # 验证用户是否有权限访问此聊天会话
        has_permission = await self.check_session_permission(user, self.session_id)
        if not has_permission:
            await self.close()
            return
        
        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 设置用户在线状态
        await self.set_user_online(user)
    
    async def disconnect(self, close_code):
        # 离开房间组
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        # 更新用户在线状态
        user = self.scope["user"]
        if not isinstance(user, AnonymousUser):
            await self.set_user_offline(user)
    
    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'chat_message')
            
            if message_type == 'chat_message':
                await self.handle_chat_message(text_data_json)
            elif message_type == 'typing':
                await self.handle_typing(text_data_json)
            elif message_type == 'read_message':
                await self.handle_read_message(text_data_json)
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'error': 'Invalid JSON format'
            }))
    
    async def handle_chat_message(self, data):
        user = self.scope["user"]
        content = data.get('content', '')
        message_type = data.get('message_type', 'text')
        media_url = data.get('media_url', '')
        
        if not content and not media_url:
            return
        
        # 保存消息到数据库
        message = await self.save_message(
            session_id=self.session_id,
            sender=user,
            content=content,
            message_type=message_type,
            media_url=media_url
        )
        
        if message:
            # 发送消息到房间组
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': {
                        'id': message.id,
                        'sender_id': message.sender.id,
                        'sender_nickname': message.sender.nickname,
                        'sender_avatar': message.sender.avatar,
                        'content': message.content,
                        'message_type': message.message_type,
                        'media_url': message.media_url,
                        'created_at': message.created_at.isoformat(),
                    }
                }
            )
    
    async def handle_typing(self, data):
        user = self.scope["user"]
        is_typing = data.get('is_typing', False)
        
        # 发送打字状态到房间组
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': user.id,
                'nickname': user.nickname,
                'is_typing': is_typing
            }
        )
    
    async def handle_read_message(self, data):
        user = self.scope["user"]
        message_ids = data.get('message_ids', [])
        
        # 标记消息为已读
        await self.mark_messages_as_read(message_ids, user)
        
        # 通知发送者消息已读
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'message_read',
                'reader_id': user.id,
                'message_ids': message_ids
            }
        )
    
    # WebSocket消息处理器
    async def chat_message(self, event):
        message = event['message']
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': message
        }))
    
    async def typing_status(self, event):
        await self.send(text_data=json.dumps({
            'type': 'typing_status',
            'user_id': event['user_id'],
            'nickname': event['nickname'],
            'is_typing': event['is_typing']
        }))
    
    async def message_read(self, event):
        await self.send(text_data=json.dumps({
            'type': 'message_read',
            'reader_id': event['reader_id'],
            'message_ids': event['message_ids']
        }))
    
    # 数据库操作方法
    @database_sync_to_async
    def check_session_permission(self, user, session_id):
        try:
            session = ChatSession.objects.get(id=session_id)
            return user in [session.user1, session.user2]
        except ChatSession.DoesNotExist:
            return False
    
    @database_sync_to_async
    def save_message(self, session_id, sender, content, message_type, media_url):
        try:
            session = ChatSession.objects.get(id=session_id)
            receiver = session.get_other_user(sender)
            
            message = ChatMessage.objects.create(
                session=session,
                sender=sender,
                receiver=receiver,
                content=content,
                message_type=message_type,
                media_url=media_url
            )
            
            # 更新会话信息
            session.last_message = content[:100]
            session.last_message_time = message.created_at
            session.last_message_sender = sender
            session.increment_unread_count(receiver)
            
            return message
        except ChatSession.DoesNotExist:
            return None
    
    @database_sync_to_async
    def mark_messages_as_read(self, message_ids, reader):
        ChatMessage.objects.filter(
            id__in=message_ids,
            receiver=reader,
            is_read=False
        ).update(is_read=True)
    
    @database_sync_to_async
    def set_user_online(self, user):
        online_status, created = OnlineStatus.objects.get_or_create(user=user)
        online_status.set_online()
    
    @database_sync_to_async
    def set_user_offline(self, user):
        try:
            online_status = OnlineStatus.objects.get(user=user)
            online_status.set_offline()
        except OnlineStatus.DoesNotExist:
            pass


class OnlineStatusConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        user = self.scope["user"]
        if isinstance(user, AnonymousUser):
            await self.close()
            return
        
        self.user_id = user.id
        self.online_group_name = 'online_users'
        
        # 加入在线用户组
        await self.channel_layer.group_add(
            self.online_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 设置用户在线状态
        await self.set_user_online(user)
        
        # 广播用户上线
        await self.channel_layer.group_send(
            self.online_group_name,
            {
                'type': 'user_online',
                'user_id': user.id,
                'nickname': user.nickname
            }
        )
    
    async def disconnect(self, close_code):
        user = self.scope["user"]
        if not isinstance(user, AnonymousUser):
            # 设置用户离线状态
            await self.set_user_offline(user)
            
            # 广播用户下线
            await self.channel_layer.group_send(
                self.online_group_name,
                {
                    'type': 'user_offline',
                    'user_id': user.id,
                    'nickname': user.nickname
                }
            )
        
        # 离开在线用户组
        await self.channel_layer.group_discard(
            self.online_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'heartbeat':
                await self.send(text_data=json.dumps({
                    'type': 'heartbeat_response',
                    'timestamp': data.get('timestamp')
                }))
                
        except json.JSONDecodeError:
            pass
    
    # 消息处理器
    async def user_online(self, event):
        await self.send(text_data=json.dumps({
            'type': 'user_online',
            'user_id': event['user_id'],
            'nickname': event['nickname']
        }))
    
    async def user_offline(self, event):
        await self.send(text_data=json.dumps({
            'type': 'user_offline',
            'user_id': event['user_id'],
            'nickname': event['nickname']
        }))
    
    @database_sync_to_async
    def set_user_online(self, user):
        online_status, created = OnlineStatus.objects.get_or_create(user=user)
        online_status.set_online()
    
    @database_sync_to_async
    def set_user_offline(self, user):
        try:
            online_status = OnlineStatus.objects.get(user=user)
            online_status.set_offline()
        except OnlineStatus.DoesNotExist:
            pass
