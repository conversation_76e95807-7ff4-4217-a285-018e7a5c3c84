from django.contrib import admin
from django.utils.html import format_html
from .models import (ChatSession, ChatMessage, MessageTemplate, 
                    ChatStatistics, OnlineStatus)


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ['id', 'user1', 'user2', 'status_display', 'last_message_time',
                   'unread_count_user1', 'unread_count_user2']
    list_filter = ['status', 'created_at']
    search_fields = ['user1__nickname', 'user2__nickname']
    readonly_fields = ['created_at', 'updated_at']
    list_per_page = 25  # 分页优化
    list_max_show_all = 100

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user1', 'user2')
    
    def status_display(self, obj):
        colors = {'active': 'green', 'blocked': 'red', 'deleted': 'gray'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ['id', 'sender', 'receiver', 'message_type_display',
                   'content_preview', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['sender__nickname', 'receiver__nickname', 'content']
    readonly_fields = ['created_at']
    list_per_page = 30  # 分页优化
    list_max_show_all = 150

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('sender', 'receiver', 'session')
    
    def message_type_display(self, obj):
        colors = {
            'text': 'blue',
            'image': 'green',
            'voice': 'orange',
            'video': 'purple',
            'gift': 'red',
            'system': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.message_type, 'black'),
            obj.get_message_type_display()
        )
    message_type_display.short_description = '消息类型'
    
    def content_preview(self, obj):
        if obj.is_recalled:
            return '[消息已撤回]'
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'


@admin.register(MessageTemplate)
class MessageTemplateAdmin(admin.ModelAdmin):
    list_display = ['title', 'category_display', 'usage_count', 'success_rate', 'is_active']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['title', 'content']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']
    
    def category_display(self, obj):
        return obj.get_category_display()
    category_display.short_description = '分类'


@admin.register(ChatStatistics)
class ChatStatisticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_messages_sent', 'total_messages_received', 
                   'active_sessions', 'response_rate']
    search_fields = ['user__nickname']
    readonly_fields = ['updated_at']


@admin.register(OnlineStatus)
class OnlineStatusAdmin(admin.ModelAdmin):
    list_display = ['user', 'status_display', 'last_seen', 'status_message']
    list_filter = ['status', 'last_seen']
    search_fields = ['user__nickname', 'status_message']
    readonly_fields = ['last_seen']
    
    def status_display(self, obj):
        colors = {'online': 'green', 'away': 'orange', 'busy': 'red', 'offline': 'gray'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
