from django.db import models
from apps.users.models import User
from apps.matching.models import Match


class ChatSession(models.Model):
    """聊天会话"""
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('blocked', '已屏蔽'),
        ('deleted', '已删除'),
    ]
    
    match = models.OneToOneField(Match, on_delete=models.CASCADE, related_name='chat_session', 
                                verbose_name='匹配记录')
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions_as_user1', 
                             verbose_name='用户1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions_as_user2', 
                             verbose_name='用户2')
    
    # 最后消息信息
    last_message = models.TextField(blank=True, verbose_name='最后消息')
    last_message_time = models.DateTimeField(null=True, blank=True, verbose_name='最后消息时间')
    last_message_sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                          related_name='last_messages', verbose_name='最后消息发送者')
    
    # 未读消息数
    unread_count_user1 = models.IntegerField(default=0, verbose_name='用户1未读数')
    unread_count_user2 = models.IntegerField(default=0, verbose_name='用户2未读数')
    
    # 会话状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    
    # 屏蔽信息
    blocked_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='blocked_sessions', verbose_name='屏蔽者')
    blocked_at = models.DateTimeField(null=True, blank=True, verbose_name='屏蔽时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'chat_sessions'
        verbose_name = '聊天会话'
        verbose_name_plural = '聊天会话'
        ordering = ['-last_message_time']
    
    def __str__(self):
        return f'{self.user1.nickname} & {self.user2.nickname} 的聊天'
    
    def get_other_user(self, user):
        """获取对话的另一个用户"""
        return self.user2 if self.user1 == user else self.user1
    
    def get_unread_count(self, user):
        """获取用户的未读消息数"""
        return self.unread_count_user1 if self.user1 == user else self.unread_count_user2
    
    def mark_as_read(self, user):
        """标记消息为已读"""
        if self.user1 == user:
            self.unread_count_user1 = 0
        else:
            self.unread_count_user2 = 0
        self.save()
    
    def increment_unread_count(self, receiver):
        """增加未读消息数"""
        if self.user1 == receiver:
            self.unread_count_user1 += 1
        else:
            self.unread_count_user2 += 1
        self.save()


class ChatMessage(models.Model):
    """聊天消息"""
    MESSAGE_TYPES = [
        ('text', '文本'),
        ('image', '图片'),
        ('voice', '语音'),
        ('video', '视频'),
        ('gift', '礼物'),
        ('location', '位置'),
        ('system', '系统消息'),
    ]
    
    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages', 
                               verbose_name='会话')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages', 
                              verbose_name='发送者')
    receiver = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_messages', 
                                verbose_name='接收者')
    
    # 消息内容
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='text', 
                                   verbose_name='消息类型')
    content = models.TextField(verbose_name='消息内容')
    media_url = models.URLField(blank=True, verbose_name='媒体URL')
    
    # 消息状态
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    # 消息扩展信息
    extra_data = models.JSONField(default=dict, verbose_name='扩展数据')
    
    # 消息撤回
    is_recalled = models.BooleanField(default=False, verbose_name='是否撤回')
    recalled_at = models.DateTimeField(null=True, blank=True, verbose_name='撤回时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'chat_messages'
        verbose_name = '聊天消息'
        verbose_name_plural = '聊天消息'
        ordering = ['created_at']
    
    def __str__(self):
        return f'{self.sender.nickname} -> {self.receiver.nickname}: {self.content[:50]}'
    
    def mark_as_read(self):
        """标记为已读"""
        if not self.is_read:
            self.is_read = True
            from django.utils import timezone
            self.read_at = timezone.now()
            self.save()
    
    def recall(self):
        """撤回消息"""
        if not self.is_recalled:
            self.is_recalled = True
            from django.utils import timezone
            self.recalled_at = timezone.now()
            self.content = '[消息已撤回]'
            self.save()


class MessageTemplate(models.Model):
    """消息模板"""
    CATEGORY_CHOICES = [
        ('greeting', '问候语'),
        ('icebreaker', '破冰话题'),
        ('compliment', '赞美'),
        ('question', '提问'),
        ('invitation', '邀请'),
        ('goodbye', '告别'),
    ]
    
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name='分类')
    title = models.CharField(max_length=100, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    
    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    success_rate = models.FloatField(default=0.0, verbose_name='成功率')
    
    # 适用条件
    gender_filter = models.IntegerField(choices=User.GENDER_CHOICES, null=True, blank=True, 
                                       verbose_name='性别筛选')
    age_min = models.IntegerField(null=True, blank=True, verbose_name='最小年龄')
    age_max = models.IntegerField(null=True, blank=True, verbose_name='最大年龄')
    
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'message_templates'
        verbose_name = '消息模板'
        verbose_name_plural = '消息模板'
        ordering = ['-usage_count']
    
    def __str__(self):
        return self.title


class ChatStatistics(models.Model):
    """聊天统计"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='chat_stats', 
                               verbose_name='用户')
    
    # 消息统计
    total_messages_sent = models.IntegerField(default=0, verbose_name='发送消息总数')
    total_messages_received = models.IntegerField(default=0, verbose_name='接收消息总数')
    total_sessions = models.IntegerField(default=0, verbose_name='会话总数')
    active_sessions = models.IntegerField(default=0, verbose_name='活跃会话数')
    
    # 今日统计
    today_messages_sent = models.IntegerField(default=0, verbose_name='今日发送消息')
    today_messages_received = models.IntegerField(default=0, verbose_name='今日接收消息')
    today_new_sessions = models.IntegerField(default=0, verbose_name='今日新会话')
    
    # 响应统计
    avg_response_time = models.FloatField(default=0.0, verbose_name='平均响应时间(分钟)')
    response_rate = models.FloatField(default=0.0, verbose_name='回复率')
    
    # 时间统计
    last_message_time = models.DateTimeField(null=True, blank=True, verbose_name='最后消息时间')
    last_online_time = models.DateTimeField(null=True, blank=True, verbose_name='最后在线时间')
    
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'chat_statistics'
        verbose_name = '聊天统计'
        verbose_name_plural = '聊天统计'
    
    def __str__(self):
        return f'{self.user.nickname} 聊天统计'
    
    def reset_daily_stats(self):
        """重置每日统计"""
        self.today_messages_sent = 0
        self.today_messages_received = 0
        self.today_new_sessions = 0
        self.save()


class OnlineStatus(models.Model):
    """在线状态"""
    STATUS_CHOICES = [
        ('online', '在线'),
        ('away', '离开'),
        ('busy', '忙碌'),
        ('offline', '离线'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='online_status', 
                               verbose_name='用户')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='offline', 
                             verbose_name='状态')
    
    # 位置信息
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')
    
    # 时间信息
    last_seen = models.DateTimeField(auto_now=True, verbose_name='最后在线时间')
    status_message = models.CharField(max_length=100, blank=True, verbose_name='状态消息')
    
    class Meta:
        db_table = 'online_status'
        verbose_name = '在线状态'
        verbose_name_plural = '在线状态'
    
    def __str__(self):
        return f'{self.user.nickname} - {self.get_status_display()}'
    
    def is_online(self):
        """是否在线"""
        return self.status == 'online'
    
    def set_online(self):
        """设置为在线"""
        self.status = 'online'
        self.save()
    
    def set_offline(self):
        """设置为离线"""
        self.status = 'offline'
        self.save()


class MessageStatus(models.Model):
    """消息状态模型"""
    STATUS_CHOICES = [
        ('sent', '已发送'),
        ('delivered', '已送达'),
        ('read', '已读'),
    ]

    message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE, related_name='status_records', verbose_name='消息')

    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='sent', verbose_name='消息状态')
    sent_at = models.DateTimeField(auto_now_add=True, verbose_name='发送时间')
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='送达时间')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='已读时间')

    # 接收者信息
    receiver = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_message_status', verbose_name='接收者')

    class Meta:
        db_table = 'message_status'
        verbose_name = '消息状态'
        verbose_name_plural = '消息状态'
        unique_together = ['message', 'receiver']
        ordering = ['-sent_at']

    def __str__(self):
        return f"{self.message.sender.nickname} -> {self.receiver.nickname}: {self.get_status_display()}"

    def mark_delivered(self):
        """标记为已送达"""
        if self.status == 'sent':
            from django.utils import timezone
            self.status = 'delivered'
            self.delivered_at = timezone.now()
            self.save()

    def mark_read(self):
        """标记为已读"""
        if self.status in ['sent', 'delivered']:
            from django.utils import timezone
            self.status = 'read'
            if not self.delivered_at:
                self.delivered_at = timezone.now()
            self.read_at = timezone.now()
            self.save()
