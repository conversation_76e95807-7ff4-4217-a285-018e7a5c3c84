from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'chat/sessions', views.ChatSessionViewSet, basename='chat-session')
router.register(r'chat/messages', views.ChatMessageViewSet, basename='chat-message')
router.register(r'chat/templates', views.MessageTemplateViewSet, basename='message-template')

urlpatterns = [
    path('', include(router.urls)),
    path('chat/stats/', views.chat_stats, name='chat-stats'),
    path('chat/online-users/', views.online_users, name='online-users'),
    path('chat/update-status/', views.update_status, name='update-status'),

    # 新增消息状态功能API
    path('chat/messages/<int:message_id>/status/', views.MessageStatusView.as_view(), name='message_status'),
    path('chat/messages/mark-read/', views.MarkMessagesReadView.as_view(), name='mark_messages_read'),
    path('chat/sessions-with-status/', views.ChatMessagesWithStatusView.as_view(), name='chat_sessions_with_status'),
]
