from rest_framework import serializers
from .models import (
    ChatSession, ChatMessage, MessageTemplate, 
    ChatStatistics, OnlineStatus
)
from apps.users.serializers import UserProfileSerializer


class ChatSessionSerializer(serializers.ModelSerializer):
    """聊天会话序列化器"""
    other_user = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    last_message_preview = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatSession
        fields = [
            'id', 'other_user', 'last_message', 'last_message_preview',
            'last_message_time', 'unread_count', 'status', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_other_user(self, obj):
        """获取对话的另一个用户"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_user(request.user)
            return UserProfileSerializer(other_user, context=self.context).data
        return None
    
    def get_unread_count(self, obj):
        """获取当前用户的未读消息数"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_unread_count(request.user)
        return 0
    
    def get_last_message_preview(self, obj):
        """获取最后消息预览"""
        if not obj.last_message:
            return ""
        
        # 根据消息类型返回不同的预览
        if obj.last_message.startswith('['):
            return obj.last_message  # 系统消息
        
        # 普通文本消息，限制长度
        return obj.last_message[:50] + '...' if len(obj.last_message) > 50 else obj.last_message


class ChatMessageSerializer(serializers.ModelSerializer):
    """聊天消息序列化器"""
    sender = UserProfileSerializer(read_only=True)
    receiver = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = ChatMessage
        fields = [
            'id', 'sender', 'receiver', 'message_type', 'content',
            'media_url', 'is_read', 'read_at', 'is_recalled',
            'recalled_at', 'extra_data', 'created_at'
        ]
        read_only_fields = ['id', 'sender', 'receiver', 'is_read', 'read_at', 'created_at']


class SendMessageSerializer(serializers.Serializer):
    """发送消息序列化器"""
    MESSAGE_TYPES = [
        ('text', '文本'),
        ('image', '图片'),
        ('voice', '语音'),
        ('video', '视频'),
        ('location', '位置'),
    ]
    
    session_id = serializers.IntegerField()
    message_type = serializers.ChoiceField(choices=MESSAGE_TYPES, default='text')
    content = serializers.CharField(max_length=1000)
    media_url = serializers.URLField(required=False, allow_blank=True)
    extra_data = serializers.JSONField(required=False, default=dict)
    
    def validate_session_id(self, value):
        """验证会话ID"""
        try:
            session = ChatSession.objects.get(id=value)
            return value
        except ChatSession.DoesNotExist:
            raise serializers.ValidationError("聊天会话不存在")
    
    def validate(self, data):
        """验证消息内容"""
        message_type = data.get('message_type', 'text')
        content = data.get('content', '')
        media_url = data.get('media_url', '')
        
        if message_type == 'text' and not content:
            raise serializers.ValidationError("文本消息内容不能为空")
        
        if message_type in ['image', 'voice', 'video'] and not media_url:
            raise serializers.ValidationError(f"{message_type}消息必须包含媒体URL")
        
        return data


class MessageTemplateSerializer(serializers.ModelSerializer):
    """消息模板序列化器"""
    
    class Meta:
        model = MessageTemplate
        fields = [
            'id', 'category', 'title', 'content', 'usage_count',
            'success_rate', 'is_active'
        ]
        read_only_fields = ['id', 'usage_count', 'success_rate']


class ChatStatisticsSerializer(serializers.ModelSerializer):
    """聊天统计序列化器"""
    
    class Meta:
        model = ChatStatistics
        fields = [
            'total_messages_sent', 'total_messages_received', 'total_sessions',
            'active_sessions', 'today_messages_sent', 'today_messages_received',
            'today_new_sessions', 'avg_response_time', 'response_rate'
        ]


class OnlineStatusSerializer(serializers.ModelSerializer):
    """在线状态序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = OnlineStatus
        fields = ['user', 'status', 'last_seen', 'status_message']
        read_only_fields = ['user', 'last_seen']


class MarkAsReadSerializer(serializers.Serializer):
    """标记已读序列化器"""
    message_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    
    def validate_message_ids(self, value):
        """验证消息ID列表"""
        if not value:
            raise serializers.ValidationError("消息ID列表不能为空")
        
        # 验证消息是否存在
        existing_count = ChatMessage.objects.filter(id__in=value).count()
        if existing_count != len(value):
            raise serializers.ValidationError("部分消息不存在")
        
        return value


class RecallMessageSerializer(serializers.Serializer):
    """撤回消息序列化器"""
    message_id = serializers.IntegerField()
    
    def validate_message_id(self, value):
        """验证消息ID"""
        try:
            message = ChatMessage.objects.get(id=value)
            
            # 检查是否是发送者
            request = self.context.get('request')
            if request and message.sender != request.user:
                raise serializers.ValidationError("只能撤回自己发送的消息")
            
            # 检查时间限制（2分钟内可撤回）
            from django.utils import timezone
            from datetime import timedelta
            
            if timezone.now() - message.created_at > timedelta(minutes=2):
                raise serializers.ValidationError("消息发送超过2分钟，无法撤回")
            
            if message.is_recalled:
                raise serializers.ValidationError("消息已经撤回")
            
            return value
        except ChatMessage.DoesNotExist:
            raise serializers.ValidationError("消息不存在")


class ChatSessionCreateSerializer(serializers.Serializer):
    """创建聊天会话序列化器"""
    match_id = serializers.IntegerField()
    
    def validate_match_id(self, value):
        """验证匹配ID"""
        from apps.matching.models import Match
        
        try:
            match = Match.objects.get(id=value, status='matched')
            
            # 检查当前用户是否是匹配的一方
            request = self.context.get('request')
            if request and request.user not in [match.user1, match.user2]:
                raise serializers.ValidationError("您不是此匹配的参与者")
            
            return value
        except Match.DoesNotExist:
            raise serializers.ValidationError("匹配记录不存在或未匹配成功")
