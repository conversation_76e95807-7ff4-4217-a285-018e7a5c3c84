from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.db.models import Q
from django.utils import timezone

from .models import (
    ChatSession, ChatMessage, MessageTemplate, 
    ChatStatistics, OnlineStatus
)
from .serializers import (
    ChatSessionSerializer, ChatMessageSerializer, SendMessageSerializer,
    MessageTemplateSerializer, ChatStatisticsSerializer, OnlineStatusSerializer,
    MarkAsReadSerializer, RecallMessageSerializer, ChatSessionCreateSerializer
)
from apps.matching.models import Match


class ChatSessionViewSet(ModelViewSet):
    """聊天会话视图集"""
    serializer_class = ChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的聊天会话"""
        return ChatSession.objects.filter(
            Q(user1=self.request.user) | Q(user2=self.request.user),
            status='active'
        ).order_by('-last_message_time')
    
    @drf_action(detail=False, methods=['post'])
    def create_session(self, request):
        """创建聊天会话"""
        serializer = ChatSessionCreateSerializer(data=request.data, context={'request': request})
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        match_id = serializer.validated_data['match_id']

        try:
            match = Match.objects.get(id=match_id, status='matched')
        except Match.DoesNotExist:
            return Response({
                'code': 404,
                'message': '匹配记录不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查是否已存在会话
        existing_session = ChatSession.objects.filter(match=match).first()
        if existing_session:
            session_serializer = ChatSessionSerializer(existing_session, context={'request': request})
            return Response({
                'code': 200,
                'message': '会话已存在',
                'data': session_serializer.data
            })

        # 创建新会话
        session = ChatSession.objects.create(
            match=match,
            user1=match.user1,
            user2=match.user2
        )

        session_serializer = ChatSessionSerializer(session, context={'request': request})

        return Response({
            'code': 200,
            'message': '会话创建成功',
            'data': session_serializer.data
        })

    @drf_action(detail=True, methods=['post'])
    def block(self, request, pk=None):
        """屏蔽会话"""
        session = self.get_object()

        session.status = 'blocked'
        session.blocked_by = request.user
        session.blocked_at = timezone.now()
        session.save()

        return Response({
            'code': 200,
            'message': '会话已屏蔽'
        })

    @drf_action(detail=True, methods=['post'])
    def unblock(self, request, pk=None):
        """取消屏蔽会话"""
        session = self.get_object()
        
        if session.blocked_by != request.user:
            return Response({
                'code': 403,
                'message': '只能取消自己的屏蔽'
            }, status=status.HTTP_403_FORBIDDEN)
        
        session.status = 'active'
        session.blocked_by = None
        session.blocked_at = None
        session.save()
        
        return Response({
            'code': 200,
            'message': '已取消屏蔽'
        })


class ChatMessageViewSet(ModelViewSet):
    """聊天消息视图集"""
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的聊天消息"""
        session_id = self.request.query_params.get('session_id')
        if session_id:
            # 验证用户是否有权限访问此会话
            try:
                session = ChatSession.objects.get(
                    id=session_id,
                    status='active'
                )
                if self.request.user in [session.user1, session.user2]:
                    return ChatMessage.objects.filter(
                        session=session
                    ).order_by('-created_at')
            except ChatSession.DoesNotExist:
                pass
        
        return ChatMessage.objects.none()
    
    @drf_action(detail=False, methods=['post'])
    def send(self, request):
        """发送消息"""
        serializer = SendMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        session_id = serializer.validated_data['session_id']

        try:
            session = ChatSession.objects.get(id=session_id, status='active')
        except ChatSession.DoesNotExist:
            return Response({
                'code': 404,
                'message': '聊天会话不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 验证用户权限
        if request.user not in [session.user1, session.user2]:
            return Response({
                'code': 403,
                'message': '无权限访问此会话'
            }, status=status.HTTP_403_FORBIDDEN)

        # 获取接收者
        receiver = session.get_other_user(request.user)

        # 创建消息
        message = ChatMessage.objects.create(
            session=session,
            sender=request.user,
            receiver=receiver,
            message_type=serializer.validated_data['message_type'],
            content=serializer.validated_data['content'],
            media_url=serializer.validated_data.get('media_url', ''),
            extra_data=serializer.validated_data.get('extra_data', {})
        )

        # 更新会话信息
        session.last_message = message.content[:100]
        session.last_message_time = message.created_at
        session.last_message_sender = request.user
        session.increment_unread_count(receiver)

        # 序列化返回
        message_serializer = ChatMessageSerializer(message, context={'request': request})

        return Response({
            'code': 200,
            'message': '发送成功',
            'data': message_serializer.data
        })

    @drf_action(detail=False, methods=['post'])
    def mark_read(self, request):
        """标记消息已读"""
        serializer = MarkAsReadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        message_ids = serializer.validated_data['message_ids']

        # 只能标记发给自己的消息为已读
        messages = ChatMessage.objects.filter(
            id__in=message_ids,
            receiver=request.user,
            is_read=False
        )

        # 批量更新
        updated_count = messages.update(
            is_read=True,
            read_at=timezone.now()
        )

        # 更新会话未读数
        for message in messages:
            message.session.mark_as_read(request.user)

        return Response({
            'code': 200,
            'message': f'已标记{updated_count}条消息为已读'
        })

    @drf_action(detail=False, methods=['post'])
    def recall(self, request):
        """撤回消息"""
        serializer = RecallMessageSerializer(data=request.data, context={'request': request})
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        message_id = serializer.validated_data['message_id']
        
        try:
            message = ChatMessage.objects.get(id=message_id)
            message.recall()
            
            return Response({
                'code': 200,
                'message': '消息已撤回'
            })
        except ChatMessage.DoesNotExist:
            return Response({
                'code': 404,
                'message': '消息不存在'
            }, status=status.HTTP_404_NOT_FOUND)


class MessageTemplateViewSet(ReadOnlyModelViewSet):
    """消息模板视图集"""
    queryset = MessageTemplate.objects.filter(is_active=True)
    serializer_class = MessageTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """根据用户性别和年龄筛选模板"""
        queryset = super().get_queryset()
        user = self.request.user
        
        # 根据性别筛选
        if user.gender:
            queryset = queryset.filter(
                Q(gender_filter__isnull=True) | Q(gender_filter=user.gender)
            )
        
        # 根据年龄筛选
        if user.age:
            queryset = queryset.filter(
                Q(age_min__isnull=True) | Q(age_min__lte=user.age),
                Q(age_max__isnull=True) | Q(age_max__gte=user.age)
            )
        
        return queryset.order_by('category', '-usage_count')
    
    @drf_action(detail=False, methods=['get'])
    def by_category(self, request):
        """按分类获取模板"""
        category = request.query_params.get('category')
        if not category:
            return Response({
                'code': 400,
                'message': '缺少category参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        templates = self.get_queryset().filter(category=category)
        serializer = self.get_serializer(templates, many=True)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def chat_stats(request):
    """获取聊天统计"""
    stats, created = ChatStatistics.objects.get_or_create(user=request.user)
    serializer = ChatStatisticsSerializer(stats)
    
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def online_users(request):
    """获取在线用户列表"""
    online_statuses = OnlineStatus.objects.filter(
        status='online'
    ).select_related('user')[:50]
    
    serializer = OnlineStatusSerializer(online_statuses, many=True, context={'request': request})
    
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_status(request):
    """更新在线状态"""
    status_value = request.data.get('status', 'online')
    status_message = request.data.get('status_message', '')
    
    if status_value not in ['online', 'away', 'busy', 'offline']:
        return Response({
            'code': 400,
            'message': '无效的状态值'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    online_status, created = OnlineStatus.objects.get_or_create(user=request.user)
    online_status.status = status_value
    online_status.status_message = status_message
    online_status.save()
    
    return Response({
        'code': 200,
        'message': '状态更新成功'
    })


# ==================== 新增消息状态功能API ====================

from rest_framework.views import APIView

class MessageStatusView(APIView):
    """消息状态API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, message_id):
        """获取消息状态"""
        user = request.user

        try:
            # 这里应该从MessageStatus表获取真实数据
            # 模拟消息状态数据
            message_status = {
                'message_id': message_id,
                'status': 'read',
                'sent_at': '2024-01-15T10:30:00Z',
                'delivered_at': '2024-01-15T10:30:05Z',
                'read_at': '2024-01-15T10:35:00Z'
            }

            return Response({
                'code': 200,
                'data': message_status
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取消息状态失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MarkMessagesReadView(APIView):
    """批量标记消息已读API"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """批量标记消息已读"""
        user = request.user

        try:
            session_id = request.data.get('session_id')
            message_ids = request.data.get('message_ids', [])

            if not session_id:
                return Response({
                    'code': 400,
                    'message': '会话ID不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 这里应该更新MessageStatus表
            # 模拟标记已读操作
            marked_count = len(message_ids) if message_ids else 0

            return Response({
                'code': 200,
                'message': f'成功标记{marked_count}条消息为已读',
                'data': {
                    'marked_count': marked_count,
                    'session_id': session_id
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'标记已读失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChatMessagesWithStatusView(APIView):
    """带状态的聊天消息API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取带状态的聊天消息列表"""
        user = request.user

        try:
            # 模拟带消息状态的聊天列表
            chat_sessions = [
                {
                    'id': 1,
                    'user': {
                        'id': 2,
                        'nickname': '小红',
                        'avatar': 'https://example.com/avatar1.jpg'
                    },
                    'last_message': '你好，很高兴认识你',
                    'last_message_time': '14:30',
                    'unread_count': 2,
                    'is_last_message_mine': True,
                    'last_message_status': 'read',  # sent, delivered, read
                    'distance': '2.3km'
                },
                {
                    'id': 2,
                    'user': {
                        'id': 3,
                        'nickname': '小明',
                        'avatar': 'https://example.com/avatar2.jpg'
                    },
                    'last_message': '今天天气不错',
                    'last_message_time': '13:45',
                    'unread_count': 0,
                    'is_last_message_mine': False,
                    'last_message_status': 'delivered',
                    'distance': '5.1km'
                }
            ]

            return Response({
                'code': 200,
                'data': {
                    'sessions': chat_sessions,
                    'total_unread': sum(session['unread_count'] for session in chat_sessions)
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取聊天列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
