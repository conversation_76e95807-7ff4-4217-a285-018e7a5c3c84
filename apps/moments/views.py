from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.db.models import Q, Count, F
from django.utils import timezone
from datetime import timed<PERSON><PERSON>, date
import random

from .models import (
    Moment, MomentLike, MomentComment, MomentCommentLike,
    MomentView, MomentReport, Topic, MomentTopic
)
from .serializers import (
    MomentSerializer, MomentCreateSerializer, MomentCommentSerializer,
    MomentCommentCreateSerializer, MomentLikeSerializer, MomentReportSerializer,
    TopicSerializer, MomentStatisticsSerializer, HotTopicSerializer,
    RecommendMomentSerializer, MomentFeedSerializer, TopicFollowSerializer
)
from apps.users.models import UserAction


class MomentViewSet(ModelViewSet):
    """动态视图集"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return MomentCreateSerializer
        return MomentSerializer
    
    def get_queryset(self):
        """获取动态列表"""
        queryset = Moment.objects.filter(
            status='published'
        ).select_related('user').prefetch_related('topics')
        
        # 根据可见性筛选
        user = self.request.user
        if not user.is_authenticated:
            queryset = queryset.filter(visibility='public')
        else:
            # 获取关注的用户
            following_users = UserAction.objects.filter(
                user=user,
                action_type='follow'
            ).values_list('target_user_id', flat=True)
            
            queryset = queryset.filter(
                Q(visibility='public') |
                Q(visibility='followers', user__in=following_users) |
                Q(user=user)
            )
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        """创建动态"""
        serializer.save(user=self.request.user)
    
    @drf_action(detail=False, methods=['get'])
    def feed(self, request):
        """获取动态流"""
        page_size = min(int(request.GET.get('page_size', 20)), 50)
        cursor = request.GET.get('cursor')
        
        queryset = self.get_queryset()
        
        # 基于游标的分页
        if cursor:
            try:
                cursor_moment = Moment.objects.get(id=cursor)
                queryset = queryset.filter(created_at__lt=cursor_moment.created_at)
            except Moment.DoesNotExist:
                pass
        
        moments = list(queryset[:page_size + 1])
        has_more = len(moments) > page_size
        
        if has_more:
            moments = moments[:page_size]
            next_cursor = str(moments[-1].id)
        else:
            next_cursor = None
        
        # 记录浏览
        for moment in moments:
            MomentView.objects.get_or_create(
                user=request.user,
                moment=moment,
                defaults={'view_time': timezone.now()}
            )
        
        serializer = MomentSerializer(moments, many=True, context={'request': request})
        
        feed_data = {
            'moments': serializer.data,
            'has_more': has_more,
            'next_cursor': next_cursor
        }
        
        feed_serializer = MomentFeedSerializer(feed_data)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': feed_serializer.data
        })
    
    @drf_action(detail=False, methods=['get'])
    def recommend(self, request):
        """获取推荐动态"""
        user = request.user
        page_size = min(int(request.GET.get('page_size', 10)), 20)
        
        # 获取用户兴趣标签（基于点赞、评论、关注等行为）
        user_interests = self.get_user_interests(user)
        
        # 获取推荐动态
        recommended_moments = self.get_recommended_moments(user, user_interests, page_size)
        
        serializer = RecommendMomentSerializer(recommended_moments, many=True)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    @drf_action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """点赞动态"""
        moment = self.get_object()
        
        like, created = MomentLike.objects.get_or_create(
            user=request.user,
            moment=moment
        )
        
        if created:
            # 更新点赞数
            moment.like_count = F('like_count') + 1
            moment.save()
            
            message = '点赞成功'
        else:
            message = '已经点过赞了'
        
        return Response({
            'code': 200,
            'message': message
        })
    
    @drf_action(detail=True, methods=['post'])
    def unlike(self, request, pk=None):
        """取消点赞"""
        moment = self.get_object()
        
        try:
            like = MomentLike.objects.get(user=request.user, moment=moment)
            like.delete()
            
            # 更新点赞数
            moment.like_count = F('like_count') - 1
            moment.save()
            
            return Response({
                'code': 200,
                'message': '取消点赞成功'
            })
        except MomentLike.DoesNotExist:
            return Response({
                'code': 400,
                'message': '您还没有点赞'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @drf_action(detail=True, methods=['get'])
    def comments(self, request, pk=None):
        """获取动态评论"""
        moment = self.get_object()
        
        comments = MomentComment.objects.filter(
            moment=moment
        ).select_related('user', 'reply_to_user').order_by('-created_at')
        
        serializer = MomentCommentSerializer(comments, many=True, context={'request': request})
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    @drf_action(detail=True, methods=['post'])
    def comment(self, request, pk=None):
        """评论动态"""
        moment = self.get_object()
        
        data = request.data.copy()
        data['moment'] = moment.id
        
        serializer = MomentCommentCreateSerializer(data=data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        comment = serializer.save(user=request.user)
        
        comment_serializer = MomentCommentSerializer(comment, context={'request': request})
        
        return Response({
            'code': 200,
            'message': '评论成功',
            'data': comment_serializer.data
        })
    
    @drf_action(detail=True, methods=['post'])
    def report(self, request, pk=None):
        """举报动态"""
        moment = self.get_object()
        
        # 检查是否已经举报过
        existing_report = MomentReport.objects.filter(
            user=request.user,
            moment=moment
        ).first()
        
        if existing_report:
            return Response({
                'code': 400,
                'message': '您已经举报过这条动态'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        data = request.data.copy()
        data['moment'] = moment.id
        
        serializer = MomentReportSerializer(data=data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        serializer.save(user=request.user)
        
        return Response({
            'code': 200,
            'message': '举报成功，我们会尽快处理'
        })
    
    @drf_action(detail=False, methods=['get'])
    def my_moments(self, request):
        """获取我的动态"""
        moments = Moment.objects.filter(
            user=request.user
        ).order_by('-created_at')
        
        serializer = MomentSerializer(moments, many=True, context={'request': request})
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    def get_user_interests(self, user):
        """获取用户兴趣标签"""
        # 基于用户行为分析兴趣
        interests = []
        
        # 分析点赞的动态话题
        liked_topics = Topic.objects.filter(
            momenttopic__moment__momentlike__user=user
        ).annotate(
            like_count=Count('momenttopic__moment__momentlike')
        ).order_by('-like_count')[:10]
        
        interests.extend([topic.name for topic in liked_topics])
        
        # 分析评论的动态话题
        commented_topics = Topic.objects.filter(
            momenttopic__moment__momentcomment__user=user
        ).annotate(
            comment_count=Count('momenttopic__moment__momentcomment')
        ).order_by('-comment_count')[:10]
        
        interests.extend([topic.name for topic in commented_topics])
        
        return list(set(interests))
    
    def get_recommended_moments(self, user, interests, limit):
        """获取推荐动态"""
        # 基于兴趣推荐
        recommended = []
        
        if interests:
            # 基于话题推荐
            topic_moments = Moment.objects.filter(
                topics__name__in=interests,
                status='published',
                visibility='public'
            ).exclude(user=user).annotate(
                score=Count('momentlike') + Count('momentcomment') * 2
            ).order_by('-score')[:limit * 2]
            
            for moment in topic_moments:
                recommended.append({
                    'moment': moment,
                    'recommend_score': 0.8,
                    'recommend_reason': '基于您的兴趣推荐'
                })
        
        # 热门动态推荐
        hot_moments = Moment.objects.filter(
            status='published',
            visibility='public',
            created_at__gte=timezone.now() - timedelta(days=7)
        ).exclude(user=user).annotate(
            score=Count('momentlike') + Count('momentcomment') * 2 + Count('momentview')
        ).order_by('-score')[:limit]
        
        for moment in hot_moments:
            if not any(r['moment'].id == moment.id for r in recommended):
                recommended.append({
                    'moment': moment,
                    'recommend_score': 0.6,
                    'recommend_reason': '热门动态'
                })
        
        # 随机推荐
        if len(recommended) < limit:
            random_moments = Moment.objects.filter(
                status='published',
                visibility='public'
            ).exclude(user=user).order_by('?')[:limit - len(recommended)]
            
            for moment in random_moments:
                if not any(r['moment'].id == moment.id for r in recommended):
                    recommended.append({
                        'moment': moment,
                        'recommend_score': 0.3,
                        'recommend_reason': '为您推荐'
                    })
        
        return recommended[:limit]


class TopicViewSet(ReadOnlyModelViewSet):
    """话题视图集"""
    queryset = Topic.objects.filter(is_active=True)
    serializer_class = TopicSerializer
    permission_classes = [permissions.IsAuthenticated]

    @drf_action(detail=False, methods=['get'])
    def hot(self, request):
        """获取热门话题"""
        # 计算最近7天的话题热度
        hot_topics = Topic.objects.filter(
            is_active=True
        ).annotate(
            recent_moments=Count(
                'momenttopic__moment',
                filter=Q(momenttopic__moment__created_at__gte=timezone.now() - timedelta(days=7))
            )
        ).order_by('-recent_moments', '-follow_count')[:20]

        hot_topic_data = []
        for topic in hot_topics:
            # 计算增长率
            prev_week_count = MomentTopic.objects.filter(
                topic=topic,
                moment__created_at__range=[
                    timezone.now() - timedelta(days=14),
                    timezone.now() - timedelta(days=7)
                ]
            ).count()

            current_week_count = topic.recent_moments

            if prev_week_count > 0:
                growth_rate = (current_week_count - prev_week_count) / prev_week_count * 100
            else:
                growth_rate = 100 if current_week_count > 0 else 0

            hot_topic_data.append({
                'topic': topic,
                'recent_moments_count': current_week_count,
                'growth_rate': round(growth_rate, 2)
            })

        serializer = HotTopicSerializer(hot_topic_data, many=True)

        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    @drf_action(detail=True, methods=['get'])
    def moments(self, request, pk=None):
        """获取话题下的动态"""
        topic = self.get_object()

        moments = Moment.objects.filter(
            topics=topic,
            status='published',
            visibility='public'
        ).order_by('-created_at')

        # 分页
        page_size = min(int(request.GET.get('page_size', 20)), 50)
        page = int(request.GET.get('page', 1))
        start = (page - 1) * page_size
        end = start + page_size

        paginated_moments = moments[start:end]

        serializer = MomentSerializer(paginated_moments, many=True, context={'request': request})

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'moments': serializer.data,
                'has_more': end < moments.count()
            }
        })

    @drf_action(detail=True, methods=['post'])
    def follow(self, request, pk=None):
        """关注话题"""
        topic = self.get_object()

        # 记录关注行为
        follow_action, created = UserAction.objects.get_or_create(
            user=request.user,
            target_user=None,  # 话题关注没有目标用户
            action_type='follow_topic',
            defaults={'extra_data': {'topic_id': topic.id}}
        )

        if created:
            # 更新话题关注数
            topic.follow_count = F('follow_count') + 1
            topic.save()

            message = '关注成功'
        else:
            message = '已经关注过了'

        return Response({
            'code': 200,
            'message': message
        })

    @drf_action(detail=True, methods=['post'])
    def unfollow(self, request, pk=None):
        """取消关注话题"""
        topic = self.get_object()

        try:
            follow_action = UserAction.objects.get(
                user=request.user,
                action_type='follow_topic',
                extra_data__topic_id=topic.id
            )
            follow_action.delete()

            # 更新话题关注数
            topic.follow_count = F('follow_count') - 1
            topic.save()

            return Response({
                'code': 200,
                'message': '取消关注成功'
            })
        except UserAction.DoesNotExist:
            return Response({
                'code': 400,
                'message': '您还没有关注这个话题'
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def moment_statistics(request):
    """获取动态统计"""
    user = request.user
    today = date.today()

    # 总统计
    total_moments = Moment.objects.filter(user=user).count()
    total_likes = MomentLike.objects.filter(moment__user=user).count()
    total_comments = MomentComment.objects.filter(moment__user=user).count()
    total_views = MomentView.objects.filter(moment__user=user).count()

    # 今日统计
    today_moments = Moment.objects.filter(
        user=user,
        created_at__date=today
    ).count()

    today_likes = MomentLike.objects.filter(
        moment__user=user,
        created_at__date=today
    ).count()

    today_comments = MomentComment.objects.filter(
        moment__user=user,
        created_at__date=today
    ).count()

    today_views = MomentView.objects.filter(
        moment__user=user,
        view_time__date=today
    ).count()

    stats_data = {
        'total_moments': total_moments,
        'total_likes': total_likes,
        'total_comments': total_comments,
        'total_views': total_views,
        'today_moments': today_moments,
        'today_likes': today_likes,
        'today_comments': today_comments,
        'today_views': today_views
    }

    serializer = MomentStatisticsSerializer(stats_data)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def like_comment(request):
    """点赞评论"""
    comment_id = request.data.get('comment_id')

    if not comment_id:
        return Response({
            'code': 400,
            'message': '缺少comment_id参数'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        comment = MomentComment.objects.get(id=comment_id)
    except MomentComment.DoesNotExist:
        return Response({
            'code': 404,
            'message': '评论不存在'
        }, status=status.HTTP_404_NOT_FOUND)

    like, created = MomentCommentLike.objects.get_or_create(
        user=request.user,
        comment=comment
    )

    if created:
        # 更新评论点赞数
        comment.like_count = F('like_count') + 1
        comment.save()

        message = '点赞成功'
    else:
        message = '已经点过赞了'

    return Response({
        'code': 200,
        'message': message
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def unlike_comment(request):
    """取消点赞评论"""
    comment_id = request.data.get('comment_id')

    if not comment_id:
        return Response({
            'code': 400,
            'message': '缺少comment_id参数'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        comment = MomentComment.objects.get(id=comment_id)
        like = MomentCommentLike.objects.get(user=request.user, comment=comment)
        like.delete()

        # 更新评论点赞数
        comment.like_count = F('like_count') - 1
        comment.save()

        return Response({
            'code': 200,
            'message': '取消点赞成功'
        })
    except MomentComment.DoesNotExist:
        return Response({
            'code': 404,
            'message': '评论不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except MomentCommentLike.DoesNotExist:
        return Response({
            'code': 400,
            'message': '您还没有点赞这条评论'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_moments(request):
    """搜索动态"""
    keyword = request.GET.get('keyword', '').strip()

    if not keyword:
        return Response({
            'code': 400,
            'message': '请输入搜索关键词'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 搜索动态内容
    moments = Moment.objects.filter(
        Q(content__icontains=keyword) | Q(topics__name__icontains=keyword),
        status='published',
        visibility='public'
    ).distinct().order_by('-created_at')

    # 分页
    page_size = min(int(request.GET.get('page_size', 20)), 50)
    page = int(request.GET.get('page', 1))
    start = (page - 1) * page_size
    end = start + page_size

    paginated_moments = moments[start:end]

    serializer = MomentSerializer(paginated_moments, many=True, context={'request': request})

    return Response({
        'code': 200,
        'message': 'success',
        'data': {
            'moments': serializer.data,
            'total': moments.count(),
            'has_more': end < moments.count()
        }
    })


# ==================== 新增Stories功能API ====================

from rest_framework.views import APIView

class StoriesView(APIView):
    """Stories功能API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取Stories列表"""
        user = request.user
        story_type = request.GET.get('type', 'all')

        try:
            # 模拟Stories数据
            my_stories = [
                {
                    'id': 1,
                    'content_type': 'image',
                    'content_url': 'https://example.com/story1.jpg',
                    'thumbnail_url': 'https://example.com/thumb1.jpg',
                    'view_count': 23,
                    'created_at': '2024-01-15T08:00:00Z',
                    'expires_at': '2024-01-16T08:00:00Z'
                }
            ]

            friend_stories = [
                {
                    'user': {
                        'id': 2,
                        'nickname': '小明',
                        'avatar': 'https://example.com/avatar2.jpg'
                    },
                    'stories': [
                        {
                            'id': 2,
                            'content_type': 'video',
                            'content_url': 'https://example.com/story2.mp4',
                            'thumbnail_url': 'https://example.com/thumb2.jpg',
                            'is_viewed': False,
                            'created_at': '2024-01-15T09:00:00Z'
                        }
                    ],
                    'unviewed_count': 1
                }
            ]

            return Response({
                'code': 200,
                'data': {
                    'my_stories': my_stories,
                    'friend_stories': friend_stories
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取Stories失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """发布Story"""
        user = request.user

        try:
            content_type = request.data.get('content_type', 'image')
            content_url = request.data.get('content_url', '')
            text_content = request.data.get('text_content', '')
            background_color = request.data.get('background_color', '#FF6B6B')
            duration = request.data.get('duration', 15)

            # 这里应该创建UserStory记录
            story_data = {
                'id': random.randint(1000, 9999),
                'content_type': content_type,
                'content_url': content_url,
                'text_content': text_content,
                'background_color': background_color,
                'duration': duration,
                'view_count': 0,
                'created_at': timezone.now().isoformat(),
                'expires_at': (timezone.now() + timedelta(hours=24)).isoformat()
            }

            return Response({
                'code': 200,
                'message': 'Story发布成功',
                'data': story_data
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'发布Story失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StoryViewView(APIView):
    """Story查看API"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, story_id):
        """查看Story"""
        user = request.user

        try:
            # 这里应该记录到StoryView表
            return Response({
                'code': 200,
                'message': '查看记录成功'
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'记录查看失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StoryViewersView(APIView):
    """Story查看者列表API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, story_id):
        """获取Story查看者列表"""
        user = request.user

        try:
            # 模拟查看者数据
            viewers = [
                {
                    'id': 1,
                    'nickname': '小红',
                    'avatar': 'https://example.com/avatar1.jpg',
                    'viewed_at': '2024-01-15T10:30:00Z'
                },
                {
                    'id': 2,
                    'nickname': '小明',
                    'avatar': 'https://example.com/avatar2.jpg',
                    'viewed_at': '2024-01-15T11:00:00Z'
                }
            ]

            return Response({
                'code': 200,
                'data': {
                    'viewers': viewers,
                    'total_count': len(viewers)
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取查看者失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class HotTopicsView(APIView):
    """热门话题API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取热门话题"""
        try:
            # 模拟热门话题数据
            hot_topics = [
                {
                    'id': 1,
                    'name': '今日穿搭',
                    'post_count': 1234,
                    'participant_count': 567,
                    'hot_score': 95.5,
                    'cover_image': 'https://example.com/topic1.jpg'
                },
                {
                    'id': 2,
                    'name': '美食分享',
                    'post_count': 856,
                    'participant_count': 423,
                    'hot_score': 87.2,
                    'cover_image': 'https://example.com/topic2.jpg'
                }
            ]

            return Response({
                'code': 200,
                'data': hot_topics
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取热门话题失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
