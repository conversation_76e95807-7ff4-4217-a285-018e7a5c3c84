from django.contrib import admin
from django.utils.html import format_html
from .models import (Moment, MomentLike, MomentComment, MomentCommentLike, 
                    MomentView, MomentReport, Topic, MomentTopic)


@admin.register(Moment)
class MomentAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'content_preview', 'status_display', 'like_count',
                   'comment_count', 'view_count', 'hot_score', 'is_reviewed', 'created_at']
    list_filter = ['status', 'is_reviewed', 'created_at']
    search_fields = ['user__nickname', 'content', 'location']
    readonly_fields = ['like_count', 'comment_count', 'view_count', 'share_count',
                      'hot_score', 'quality_score', 'created_at', 'updated_at']
    list_per_page = 20  # 分页优化
    list_max_show_all = 100

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user').prefetch_related('topics')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'content', 'images')
        }),
        ('位置信息', {
            'fields': ('location', 'latitude', 'longitude')
        }),
        ('统计信息', {
            'fields': ('like_count', 'comment_count', 'view_count', 'share_count')
        }),
        ('状态信息', {
            'fields': ('status', 'is_reviewed', 'reviewed_at', 'review_result')
        }),
        ('推荐信息', {
            'fields': ('hot_score', 'quality_score')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'
    
    def status_display(self, obj):
        colors = {
            'published': 'green',
            'draft': 'orange', 
            'deleted': 'red',
            'blocked': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(MomentLike)
class MomentLikeAdmin(admin.ModelAdmin):
    list_display = ['moment', 'user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['moment__content', 'user__nickname']
    readonly_fields = ['created_at']


@admin.register(MomentComment)
class MomentCommentAdmin(admin.ModelAdmin):
    list_display = ['id', 'moment', 'user', 'content_preview', 'reply_to', 
                   'like_count', 'reply_count', 'is_deleted', 'created_at']
    list_filter = ['is_deleted', 'created_at']
    search_fields = ['content', 'user__nickname', 'moment__content']
    readonly_fields = ['like_count', 'reply_count', 'created_at']
    
    def content_preview(self, obj):
        return obj.content[:30] + '...' if len(obj.content) > 30 else obj.content
    content_preview.short_description = '评论内容'


@admin.register(MomentCommentLike)
class MomentCommentLikeAdmin(admin.ModelAdmin):
    list_display = ['comment', 'user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['comment__content', 'user__nickname']
    readonly_fields = ['created_at']


@admin.register(MomentView)
class MomentViewAdmin(admin.ModelAdmin):
    list_display = ['moment', 'user', 'view_duration', 'created_at']
    list_filter = ['created_at']
    search_fields = ['moment__content', 'user__nickname']
    readonly_fields = ['created_at']


@admin.register(MomentReport)
class MomentReportAdmin(admin.ModelAdmin):
    list_display = ['id', 'moment', 'reporter', 'report_type_display', 
                   'status_display', 'created_at']
    list_filter = ['report_type', 'status', 'created_at']
    search_fields = ['moment__content', 'reporter__nickname', 'reason']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('举报信息', {
            'fields': ('moment', 'reporter', 'report_type', 'reason', 'evidence')
        }),
        ('处理信息', {
            'fields': ('status', 'admin_note')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def report_type_display(self, obj):
        return obj.get_report_type_display()
    report_type_display.short_description = '举报类型'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'processing': 'blue',
            'resolved': 'green',
            'rejected': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '处理状态'


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    list_display = ['name', 'moment_count', 'participant_count', 'hot_score', 
                   'is_trending', 'is_official', 'is_active', 'created_at']
    list_filter = ['is_trending', 'is_official', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['moment_count', 'participant_count', 'hot_score', 
                      'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'cover_image')
        }),
        ('统计信息', {
            'fields': ('moment_count', 'participant_count', 'hot_score')
        }),
        ('管理信息', {
            'fields': ('is_trending', 'is_official', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(MomentTopic)
class MomentTopicAdmin(admin.ModelAdmin):
    list_display = ['moment', 'topic', 'created_at']
    list_filter = ['created_at']
    search_fields = ['moment__content', 'topic__name']
    readonly_fields = ['created_at']
