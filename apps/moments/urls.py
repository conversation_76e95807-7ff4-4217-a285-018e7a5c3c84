from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'moments', views.MomentViewSet, basename='moment')
router.register(r'topics', views.TopicViewSet, basename='topic')

urlpatterns = [
    path('', include(router.urls)),
    
    # 动态统计
    path('moments/statistics/', views.moment_statistics, name='moment-statistics'),
    
    # 评论点赞
    path('moments/like-comment/', views.like_comment, name='like-comment'),
    path('moments/unlike-comment/', views.unlike_comment, name='unlike-comment'),
    
    # 搜索
    path('moments/search/', views.search_moments, name='search-moments'),

    # 新增Stories功能API
    path('stories/', views.StoriesView.as_view(), name='stories'),
    path('stories/<int:story_id>/view/', views.StoryViewView.as_view(), name='story_view'),
    path('stories/<int:story_id>/viewers/', views.StoryViewersView.as_view(), name='story_viewers'),

    # 话题相关
    path('topics/hot/', views.HotTopicsView.as_view(), name='hot_topics'),
]
