from rest_framework import serializers
from .models import (
    Moment, <PERSON>Like, <PERSON>Comment, MomentCommentLike,
    MomentView, MomentReport, Topic, MomentTopic
)
from apps.users.serializers import UserProfileSerializer


class TopicSerializer(serializers.ModelSerializer):
    """话题序列化器"""
    
    class Meta:
        model = Topic
        fields = [
            'id', 'name', 'description', 'cover_image', 'moment_count',
            'follow_count', 'is_hot', 'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'moment_count', 'follow_count', 'created_at']


class MomentSerializer(serializers.ModelSerializer):
    """动态序列化器"""
    user = UserProfileSerializer(read_only=True)
    topics = TopicSerializer(many=True, read_only=True)
    like_count = serializers.IntegerField(read_only=True)
    comment_count = serializers.IntegerField(read_only=True)
    view_count = serializers.IntegerField(read_only=True)
    is_liked = serializers.SerializerMethodField()
    is_following_user = serializers.SerializerMethodField()
    
    class Meta:
        model = Moment
        fields = [
            'id', 'user', 'content', 'images', 'video_url', 'location',
            'topics', 'visibility', 'like_count', 'comment_count', 'view_count',
            'is_liked', 'is_following_user', 'is_top', 'status', 'created_at'
        ]
        read_only_fields = [
            'id', 'user', 'like_count', 'comment_count', 'view_count',
            'is_top', 'status', 'created_at'
        ]
    
    def get_is_liked(self, obj):
        """获取当前用户是否点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return MomentLike.objects.filter(
                moment=obj,
                user=request.user
            ).exists()
        return False
    
    def get_is_following_user(self, obj):
        """获取是否关注了动态发布者"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            from apps.users.models import UserAction
            return UserAction.objects.filter(
                user=request.user,
                target_user=obj.user,
                action_type='follow'
            ).exists()
        return False


class MomentCreateSerializer(serializers.ModelSerializer):
    """创建动态序列化器"""
    topic_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True
    )
    
    class Meta:
        model = Moment
        fields = [
            'content', 'images', 'video_url', 'location',
            'visibility', 'topic_ids'
        ]
    
    def validate_content(self, value):
        """验证内容"""
        if not value or len(value.strip()) < 5:
            raise serializers.ValidationError("动态内容至少需要5个字符")
        if len(value) > 1000:
            raise serializers.ValidationError("动态内容不能超过1000个字符")
        return value
    
    def validate_images(self, value):
        """验证图片"""
        if value and len(value) > 9:
            raise serializers.ValidationError("最多只能上传9张图片")
        return value
    
    def validate_topic_ids(self, value):
        """验证话题ID"""
        if value:
            if len(value) > 5:
                raise serializers.ValidationError("最多只能选择5个话题")
            
            # 验证话题是否存在
            existing_topics = Topic.objects.filter(
                id__in=value,
                is_active=True
            ).count()
            
            if existing_topics != len(value):
                raise serializers.ValidationError("部分话题不存在或已禁用")
        
        return value
    
    def create(self, validated_data):
        """创建动态"""
        topic_ids = validated_data.pop('topic_ids', [])
        
        moment = Moment.objects.create(**validated_data)
        
        # 关联话题
        if topic_ids:
            topics = Topic.objects.filter(id__in=topic_ids, is_active=True)
            for topic in topics:
                MomentTopic.objects.create(moment=moment, topic=topic)
                # 更新话题动态数量
                topic.moment_count += 1
                topic.save()
        
        return moment


class MomentCommentSerializer(serializers.ModelSerializer):
    """动态评论序列化器"""
    user = UserProfileSerializer(read_only=True)
    reply_to_user = UserProfileSerializer(read_only=True)
    like_count = serializers.IntegerField(read_only=True)
    is_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = MomentComment
        fields = [
            'id', 'user', 'moment', 'content', 'reply_to_user',
            'like_count', 'is_liked', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'like_count', 'created_at']
    
    def get_is_liked(self, obj):
        """获取当前用户是否点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return MomentCommentLike.objects.filter(
                comment=obj,
                user=request.user
            ).exists()
        return False


class MomentCommentCreateSerializer(serializers.ModelSerializer):
    """创建评论序列化器"""
    reply_to_user_id = serializers.IntegerField(required=False)
    
    class Meta:
        model = MomentComment
        fields = ['moment', 'content', 'reply_to_user_id']
    
    def validate_content(self, value):
        """验证评论内容"""
        if not value or len(value.strip()) < 1:
            raise serializers.ValidationError("评论内容不能为空")
        if len(value) > 500:
            raise serializers.ValidationError("评论内容不能超过500个字符")
        return value
    
    def validate_reply_to_user_id(self, value):
        """验证回复用户ID"""
        if value:
            from apps.users.models import User
            try:
                User.objects.get(id=value, status=1)
            except User.DoesNotExist:
                raise serializers.ValidationError("回复的用户不存在")
        return value
    
    def create(self, validated_data):
        """创建评论"""
        reply_to_user_id = validated_data.pop('reply_to_user_id', None)
        
        if reply_to_user_id:
            from apps.users.models import User
            validated_data['reply_to_user'] = User.objects.get(id=reply_to_user_id)
        
        comment = MomentComment.objects.create(**validated_data)
        
        # 更新动态评论数
        moment = comment.moment
        moment.comment_count += 1
        moment.save()
        
        return comment


class MomentLikeSerializer(serializers.ModelSerializer):
    """动态点赞序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = MomentLike
        fields = ['id', 'user', 'moment', 'created_at']
        read_only_fields = ['id', 'user', 'created_at']


class MomentReportSerializer(serializers.ModelSerializer):
    """动态举报序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = MomentReport
        fields = [
            'id', 'user', 'moment', 'reason', 'description',
            'status', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'status', 'created_at']
    
    def validate_reason(self, value):
        """验证举报原因"""
        valid_reasons = [
            'spam', 'inappropriate', 'harassment', 'fake',
            'violence', 'copyright', 'other'
        ]
        if value not in valid_reasons:
            raise serializers.ValidationError("无效的举报原因")
        return value


class MomentViewSerializer(serializers.ModelSerializer):
    """动态浏览记录序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = MomentView
        fields = ['id', 'user', 'moment', 'view_time', 'duration']
        read_only_fields = ['id', 'user', 'view_time']


class MomentStatisticsSerializer(serializers.Serializer):
    """动态统计序列化器"""
    total_moments = serializers.IntegerField()
    total_likes = serializers.IntegerField()
    total_comments = serializers.IntegerField()
    total_views = serializers.IntegerField()
    today_moments = serializers.IntegerField()
    today_likes = serializers.IntegerField()
    today_comments = serializers.IntegerField()
    today_views = serializers.IntegerField()


class HotTopicSerializer(serializers.Serializer):
    """热门话题序列化器"""
    topic = TopicSerializer()
    recent_moments_count = serializers.IntegerField()
    growth_rate = serializers.FloatField()


class RecommendMomentSerializer(serializers.Serializer):
    """推荐动态序列化器"""
    moment = MomentSerializer()
    recommend_score = serializers.FloatField()
    recommend_reason = serializers.CharField()


class MomentFeedSerializer(serializers.Serializer):
    """动态流序列化器"""
    moments = MomentSerializer(many=True)
    has_more = serializers.BooleanField()
    next_cursor = serializers.CharField(allow_null=True)


class TopicFollowSerializer(serializers.Serializer):
    """话题关注序列化器"""
    topic_id = serializers.IntegerField()
    
    def validate_topic_id(self, value):
        """验证话题ID"""
        try:
            Topic.objects.get(id=value, is_active=True)
            return value
        except Topic.DoesNotExist:
            raise serializers.ValidationError("话题不存在或已禁用")
