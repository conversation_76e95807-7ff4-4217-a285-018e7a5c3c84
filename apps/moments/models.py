from django.db import models
from apps.users.models import User


class Moment(models.Model):
    """动态"""
    STATUS_CHOICES = [
        ('published', '已发布'),
        ('draft', '草稿'),
        ('deleted', '已删除'),
        ('blocked', '已屏蔽'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moments', verbose_name='用户')
    content = models.TextField(max_length=1000, verbose_name='内容')
    images = models.JSONField(default=list, verbose_name='图片列表')
    
    # 位置信息
    location = models.CharField(max_length=200, blank=True, verbose_name='位置')
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')
    
    # 统计信息
    like_count = models.IntegerField(default=0, verbose_name='点赞数')
    comment_count = models.IntegerField(default=0, verbose_name='评论数')
    view_count = models.IntegerField(default=0, verbose_name='查看数')
    share_count = models.IntegerField(default=0, verbose_name='分享数')
    
    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='published', 
                             verbose_name='状态')
    
    # 审核信息
    is_reviewed = models.BooleanField(default=False, verbose_name='是否已审核')
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name='审核时间')
    review_result = models.CharField(max_length=20, blank=True, verbose_name='审核结果')
    
    # 推荐权重
    hot_score = models.FloatField(default=0.0, verbose_name='热度得分')
    quality_score = models.FloatField(default=0.0, verbose_name='质量得分')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'moments'
        verbose_name = '动态'
        verbose_name_plural = '动态'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname}: {self.content[:50]}'
    
    def get_images_list(self):
        """获取图片列表"""
        if isinstance(self.images, str):
            import json
            try:
                return json.loads(self.images)
            except:
                return []
        return self.images or []
    
    def add_image(self, image_url):
        """添加图片"""
        images = self.get_images_list()
        if image_url not in images:
            images.append(image_url)
            self.images = images
            self.save()
    
    def calculate_hot_score(self):
        """计算热度得分"""
        from django.utils import timezone
        import math
        
        # 时间衰减因子
        hours_since_created = (timezone.now() - self.created_at).total_seconds() / 3600
        time_decay = math.exp(-hours_since_created / 24)  # 24小时半衰期
        
        # 互动得分
        interaction_score = self.like_count * 2 + self.comment_count * 3 + self.share_count * 5
        
        # 综合得分
        self.hot_score = interaction_score * time_decay
        self.save()


class MomentLike(models.Model):
    """动态点赞"""
    moment = models.ForeignKey(Moment, on_delete=models.CASCADE, related_name='likes', verbose_name='动态')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moment_likes', verbose_name='用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'moment_likes'
        verbose_name = '动态点赞'
        verbose_name_plural = '动态点赞'
        unique_together = ['moment', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 点赞了 {self.moment.user.nickname} 的动态'


class MomentComment(models.Model):
    """动态评论"""
    moment = models.ForeignKey(Moment, on_delete=models.CASCADE, related_name='comments', verbose_name='动态')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moment_comments', verbose_name='用户')
    content = models.TextField(max_length=500, verbose_name='评论内容')
    
    # 回复相关
    reply_to = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                related_name='replies', verbose_name='回复的评论')
    reply_to_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,
                                     related_name='comment_replies', verbose_name='回复的用户')
    
    # 统计信息
    like_count = models.IntegerField(default=0, verbose_name='点赞数')
    reply_count = models.IntegerField(default=0, verbose_name='回复数')
    
    # 状态
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'moment_comments'
        verbose_name = '动态评论'
        verbose_name_plural = '动态评论'
        ordering = ['created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 评论: {self.content[:30]}'


class MomentCommentLike(models.Model):
    """评论点赞"""
    comment = models.ForeignKey(MomentComment, on_delete=models.CASCADE, related_name='likes', 
                               verbose_name='评论')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comment_likes', 
                            verbose_name='用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'moment_comment_likes'
        verbose_name = '评论点赞'
        verbose_name_plural = '评论点赞'
        unique_together = ['comment', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 点赞了评论'


class MomentView(models.Model):
    """动态查看记录"""
    moment = models.ForeignKey(Moment, on_delete=models.CASCADE, related_name='views', verbose_name='动态')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moment_views', verbose_name='用户')
    view_duration = models.IntegerField(default=0, verbose_name='查看时长(秒)')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'moment_views'
        verbose_name = '动态查看'
        verbose_name_plural = '动态查看'
        unique_together = ['moment', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 查看了动态'


class MomentReport(models.Model):
    """动态举报"""
    REPORT_TYPES = [
        ('inappropriate', '内容不当'),
        ('spam', '垃圾信息'),
        ('harassment', '骚扰'),
        ('fake', '虚假信息'),
        ('copyright', '版权问题'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已处理'),
        ('rejected', '已拒绝'),
    ]
    
    moment = models.ForeignKey(Moment, on_delete=models.CASCADE, related_name='reports', verbose_name='动态')
    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moment_reports', 
                                verbose_name='举报者')
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, verbose_name='举报类型')
    reason = models.TextField(verbose_name='举报原因')
    evidence = models.JSONField(default=list, verbose_name='证据')
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='处理状态')
    admin_note = models.TextField(blank=True, verbose_name='管理员备注')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'moment_reports'
        verbose_name = '动态举报'
        verbose_name_plural = '动态举报'
        unique_together = ['moment', 'reporter']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.reporter.nickname} 举报了动态'


class Topic(models.Model):
    """话题"""
    name = models.CharField(max_length=100, unique=True, verbose_name='话题名称')
    description = models.TextField(blank=True, verbose_name='话题描述')
    cover_image = models.URLField(blank=True, verbose_name='封面图片')
    
    # 统计信息
    moment_count = models.IntegerField(default=0, verbose_name='动态数量')
    participant_count = models.IntegerField(default=0, verbose_name='参与人数')
    
    # 热度信息
    hot_score = models.FloatField(default=0.0, verbose_name='热度得分')
    is_trending = models.BooleanField(default=False, verbose_name='是否热门')
    
    # 管理信息
    is_official = models.BooleanField(default=False, verbose_name='是否官方话题')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'topics'
        verbose_name = '话题'
        verbose_name_plural = '话题'
        ordering = ['-hot_score']
    
    def __str__(self):
        return f'#{self.name}'


class MomentTopic(models.Model):
    """动态话题关联"""
    moment = models.ForeignKey(Moment, on_delete=models.CASCADE, related_name='topics', verbose_name='动态')
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, related_name='moments', verbose_name='话题')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'moment_topics'
        verbose_name = '动态话题'
        verbose_name_plural = '动态话题'
        unique_together = ['moment', 'topic']
    
    def __str__(self):
        return f'{self.moment.user.nickname} - #{self.topic.name}'


class UserStory(models.Model):
    """用户Stories模型"""
    CONTENT_TYPE_CHOICES = [
        ('image', '图片'),
        ('video', '视频'),
        ('text', '文字'),
    ]

    STATUS_CHOICES = [
        ('active', '活跃'),
        ('expired', '过期'),
        ('deleted', '已删除'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='stories', verbose_name='用户')

    # 内容信息
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPE_CHOICES, verbose_name='内容类型')
    content_url = models.URLField(max_length=500, blank=True, verbose_name='内容URL')
    text_content = models.TextField(blank=True, verbose_name='文字内容')
    thumbnail_url = models.URLField(max_length=500, blank=True, verbose_name='缩略图URL')

    # 显示设置
    background_color = models.CharField(max_length=20, default='#000000', verbose_name='背景颜色')
    duration = models.IntegerField(default=15, verbose_name='显示时长(秒)')

    # 统计信息
    view_count = models.IntegerField(default=0, verbose_name='查看次数')
    like_count = models.IntegerField(default=0, verbose_name='点赞次数')

    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')
    expires_at = models.DateTimeField(verbose_name='过期时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_stories'
        verbose_name = '用户Stories'
        verbose_name_plural = '用户Stories'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.nickname} - {self.get_content_type_display()}"

    @property
    def is_expired(self):
        """是否已过期"""
        from django.utils import timezone
        return timezone.now() > self.expires_at


class StoryView(models.Model):
    """Stories查看记录模型"""
    story = models.ForeignKey(UserStory, on_delete=models.CASCADE, related_name='views', verbose_name='Story')
    viewer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='story_views', verbose_name='查看者')
    viewed_at = models.DateTimeField(auto_now_add=True, verbose_name='查看时间')

    class Meta:
        db_table = 'story_views'
        verbose_name = 'Stories查看记录'
        verbose_name_plural = 'Stories查看记录'
        unique_together = ['story', 'viewer']
        ordering = ['-viewed_at']

    def __str__(self):
        return f"{self.viewer.nickname} 查看了 {self.story.user.nickname} 的Story"
