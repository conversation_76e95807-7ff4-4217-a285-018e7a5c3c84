"""
AI智能匹配模块URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# API URL模式
urlpatterns = [
    # AI智能推荐
    path('ai-matching/recommendations/', views.AIRecommendationsAPIView.as_view(), name='ai_recommendations'),
    
    # 用户画像
    path('ai-matching/profile/', views.UserProfileAPIView.as_view(), name='user_profile'),
    
    # 匹配模型管理
    path('ai-matching/models/', views.MatchingModelManagementAPIView.as_view(), name='matching_models'),
    
    # 包含路由器URL
    path('', include(router.urls)),
]
