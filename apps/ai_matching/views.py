"""
AI智能匹配API视图
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.utils import timezone
from django.db.models import Q, Count, Avg
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework import status
from datetime import datetime, timedelta
import logging

from .models import (
    UserProfile, MatchingModel, UserSimilarity, MatchPrediction,
    UserBehaviorPattern, RecommendationExplanation
)
from .matching_engine import AIMatchingEngine
from apps.users.models import User


class AIRecommendationsAPIView(APIView):
    """AI智能推荐API"""
    permission_classes = [IsAuthenticated]
    
    def __init__(self):
        super().__init__()
        self.matching_engine = AIMatchingEngine()
        self.logger = logging.getLogger(__name__)
    
    def get(self, request):
        """获取AI推荐用户列表"""
        user = request.user
        limit = int(request.GET.get('limit', 20))
        refresh = request.GET.get('refresh', 'false').lower() == 'true'
        
        try:
            # 如果需要刷新或者没有缓存，重新生成推荐
            if refresh:
                recommendations = self.matching_engine.generate_recommendations(user, limit)
            else:
                # 先尝试从数据库获取已有推荐
                existing_predictions = MatchPrediction.objects.filter(
                    user=user,
                    created_at__gte=timezone.now() - timedelta(hours=6)  # 6小时内的推荐
                ).order_by('-match_score')[:limit]
                
                if existing_predictions.exists():
                    recommendations = self.format_existing_predictions(existing_predictions)
                else:
                    recommendations = self.matching_engine.generate_recommendations(user, limit)
            
            # 格式化返回数据
            recommendation_data = []
            for rec in recommendations:
                candidate = rec['user'] if isinstance(rec, dict) else rec.candidate
                match_score = rec['match_score'] if isinstance(rec, dict) else rec.match_score
                
                # 获取推荐解释
                explanation = self.get_recommendation_explanation(user, candidate)
                
                recommendation_data.append({
                    'user_id': candidate.id,
                    'username': candidate.username,
                    'nickname': candidate.nickname,
                    'age': candidate.age,
                    'city': candidate.city,
                    'avatar': candidate.avatar.url if candidate.avatar else None,
                    'match_score': round(match_score * 100, 1),  # 转换为百分比
                    'compatibility_level': self.get_compatibility_level(match_score),
                    'recommendation_reason': explanation.get('primary_reason', '算法推荐'),
                    'match_highlights': explanation.get('match_highlights', []),
                    'distance': self.calculate_display_distance(user, candidate)
                })
            
            return Response({
                'code': 200,
                'data': {
                    'recommendations': recommendation_data,
                    'total_count': len(recommendation_data),
                    'algorithm_version': '2.0',
                    'last_updated': timezone.now().isoformat()
                }
            })
            
        except Exception as e:
            self.logger.error(f"AI推荐生成失败: {str(e)}")
            return Response({
                'code': 500,
                'message': '推荐系统暂时不可用，请稍后重试'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """用户反馈推荐结果"""
        user = request.user
        candidate_id = request.data.get('candidate_id')
        action = request.data.get('action')  # like, pass, report
        
        if not candidate_id or not action:
            return Response({
                'code': 400,
                'message': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            candidate = User.objects.get(id=candidate_id)
            
            # 更新预测结果的实际反馈
            prediction = MatchPrediction.objects.filter(
                user=user,
                candidate=candidate
            ).first()
            
            if prediction:
                prediction.actual_result = action
                prediction.save()
                
                # 记录用户反馈用于模型优化
                self.record_user_feedback(user, candidate, action, prediction.match_score)
            
            return Response({
                'code': 200,
                'message': '反馈已记录，将用于优化推荐算法'
            })
            
        except User.DoesNotExist:
            return Response({
                'code': 404,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            self.logger.error(f"记录用户反馈失败: {str(e)}")
            return Response({
                'code': 500,
                'message': '反馈记录失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def format_existing_predictions(self, predictions):
        """格式化已有的预测结果"""
        recommendations = []
        for pred in predictions:
            recommendations.append({
                'user': pred.candidate,
                'match_score': pred.match_score,
                'score_breakdown': pred.score_breakdown
            })
        return recommendations
    
    def get_recommendation_explanation(self, user, candidate):
        """获取推荐解释"""
        try:
            explanation = RecommendationExplanation.objects.get(
                user=user,
                recommended_user=candidate
            )
            return {
                'primary_reason': explanation.primary_reason,
                'secondary_reasons': explanation.secondary_reasons,
                'match_highlights': explanation.match_highlights
            }
        except RecommendationExplanation.DoesNotExist:
            return {
                'primary_reason': '算法推荐的优质用户',
                'secondary_reasons': [],
                'match_highlights': []
            }
    
    def get_compatibility_level(self, score):
        """获取兼容性等级"""
        if score >= 0.8:
            return '极高匹配'
        elif score >= 0.6:
            return '高度匹配'
        elif score >= 0.4:
            return '中等匹配'
        elif score >= 0.2:
            return '一般匹配'
        else:
            return '低匹配度'
    
    def calculate_display_distance(self, user, candidate):
        """计算显示距离"""
        if user.city == candidate.city:
            return '同城'
        elif user.province == candidate.province:
            return '同省'
        else:
            return '异地'
    
    def record_user_feedback(self, user, candidate, action, predicted_score):
        """记录用户反馈用于模型学习"""
        from .models import ModelTrainingData
        
        # 将用户行为转换为标签
        action_labels = {
            'like': 1.0,
            'pass': 0.0,
            'report': -1.0
        }
        
        label = action_labels.get(action, 0.0)
        
        # 提取特征
        features = {
            'predicted_score': predicted_score,
            'user_age': user.age or 25,
            'candidate_age': candidate.age or 25,
            'same_city': 1 if user.city == candidate.city else 0,
            'age_difference': abs((user.age or 25) - (candidate.age or 25))
        }
        
        # 保存训练数据
        ModelTrainingData.objects.create(
            data_type='user_interaction',
            user1=user,
            user2=candidate,
            features=features,
            label=label,
            label_type='user_action'
        )


class UserProfileAPIView(APIView):
    """用户画像API"""
    permission_classes = [IsAuthenticated]
    
    def __init__(self):
        super().__init__()
        self.matching_engine = AIMatchingEngine()
    
    def get(self, request):
        """获取用户画像信息"""
        user = request.user
        
        try:
            # 获取或创建用户画像
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'age_group': self.matching_engine.get_age_group(user.age),
                    'activity_score': self.matching_engine.calculate_activity_score(user),
                    'social_score': self.matching_engine.calculate_social_score(user),
                    'response_rate': self.matching_engine.calculate_response_rate(user),
                    'online_frequency': self.matching_engine.calculate_online_frequency(user)
                }
            )
            
            # 获取行为模式
            try:
                behavior_pattern = user.userbehaviorpattern
            except UserBehaviorPattern.DoesNotExist:
                behavior_pattern = self.create_behavior_pattern(user)
            
            profile_data = {
                'basic_info': {
                    'age_group': profile.age_group,
                    'education_level': profile.education_level,
                    'income_level': profile.income_level,
                    'occupation_category': profile.occupation_category,
                    'location_tier': profile.location_tier
                },
                'behavior_scores': {
                    'activity_score': round(profile.activity_score, 2),
                    'social_score': round(profile.social_score, 2),
                    'response_rate': round(profile.response_rate, 2),
                    'online_frequency': round(profile.online_frequency, 2)
                },
                'preferences': {
                    'preferred_age_range': [profile.preferred_age_min, profile.preferred_age_max],
                    'preferred_education': profile.preferred_education,
                    'preferred_income': profile.preferred_income,
                    'preferred_location': profile.preferred_location
                },
                'interests_and_traits': {
                    'interests': profile.interests,
                    'personality_traits': profile.personality_traits,
                    'lifestyle_tags': profile.lifestyle_tags
                },
                'behavior_patterns': {
                    'active_hours': behavior_pattern.active_hours,
                    'session_duration_avg': behavior_pattern.session_duration_avg,
                    'like_frequency': behavior_pattern.like_frequency,
                    'message_frequency': behavior_pattern.message_frequency,
                    'selectivity': behavior_pattern.selectivity
                },
                'last_updated': profile.updated_at.isoformat()
            }
            
            return Response({
                'code': 200,
                'data': profile_data
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户画像失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """更新用户偏好设置"""
        user = request.user
        
        try:
            profile, created = UserProfile.objects.get_or_create(user=user)
            
            # 更新偏好设置
            if 'preferred_age_min' in request.data:
                profile.preferred_age_min = request.data['preferred_age_min']
            if 'preferred_age_max' in request.data:
                profile.preferred_age_max = request.data['preferred_age_max']
            if 'preferred_education' in request.data:
                profile.preferred_education = request.data['preferred_education']
            if 'preferred_income' in request.data:
                profile.preferred_income = request.data['preferred_income']
            if 'preferred_location' in request.data:
                profile.preferred_location = request.data['preferred_location']
            if 'interests' in request.data:
                profile.interests = request.data['interests']
            if 'personality_traits' in request.data:
                profile.personality_traits = request.data['personality_traits']
            if 'lifestyle_tags' in request.data:
                profile.lifestyle_tags = request.data['lifestyle_tags']
            
            profile.save()
            
            # 清除推荐缓存，因为偏好已更新
            from django.core.cache import cache
            cache.delete(f"recommendations_{user.id}_*")
            
            return Response({
                'code': 200,
                'message': '偏好设置已更新，推荐将根据新偏好调整'
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'更新偏好失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def create_behavior_pattern(self, user):
        """创建用户行为模式"""
        return UserBehaviorPattern.objects.create(
            user=user,
            active_hours=list(range(19, 23)),  # 默认晚上7-11点活跃
            session_duration_avg=15.0,
            like_frequency=0.5,
            message_frequency=0.3,
            response_time_avg=300.0,
            decision_speed=0.5,
            selectivity=0.5
        )


class MatchingModelManagementAPIView(APIView):
    """匹配模型管理API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取模型列表和性能指标"""
        models = MatchingModel.objects.all().order_by('-created_at')
        
        model_data = []
        for model in models:
            model_data.append({
                'id': model.id,
                'name': model.name,
                'model_type': model.get_model_type_display(),
                'version': model.version,
                'is_active': model.is_active,
                'performance': {
                    'accuracy': model.accuracy,
                    'precision': model.precision,
                    'recall': model.recall,
                    'f1_score': model.f1_score
                },
                'training_info': {
                    'data_size': model.training_data_size,
                    'last_trained': model.last_trained_at.isoformat() if model.last_trained_at else None
                },
                'created_at': model.created_at.isoformat()
            })
        
        return Response({
            'code': 200,
            'data': {
                'models': model_data,
                'active_model': next((m for m in model_data if m['is_active']), None)
            }
        })
    
    def post(self, request):
        """训练或更新模型"""
        action = request.data.get('action')
        
        if action == 'train':
            try:
                matching_engine = AIMatchingEngine()
                matching_engine.train_models()
                
                return Response({
                    'code': 200,
                    'message': '模型训练已开始，请稍后查看训练结果'
                })
            except Exception as e:
                return Response({
                    'code': 500,
                    'message': f'模型训练失败: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        elif action == 'activate':
            model_id = request.data.get('model_id')
            try:
                # 停用所有模型
                MatchingModel.objects.update(is_active=False)
                
                # 激活指定模型
                model = MatchingModel.objects.get(id=model_id)
                model.is_active = True
                model.save()
                
                return Response({
                    'code': 200,
                    'message': f'模型 {model.name} 已激活'
                })
            except MatchingModel.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': '模型不存在'
                }, status=status.HTTP_404_NOT_FOUND)
        
        else:
            return Response({
                'code': 400,
                'message': '无效的操作'
            }, status=status.HTTP_400_BAD_REQUEST)
