"""
AI智能匹配模块 - Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html
import json

from .models import (
    UserProfile, MatchingModel, UserSimilarity, MatchPrediction,
    UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
    ModelPerformanceMetric
)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户画像管理"""
    list_display = ['user', 'personality_display', 'interests_display', 'updated_at']
    list_filter = ['updated_at']
    search_fields = ['user__username', 'user__nickname']
    readonly_fields = ['updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'updated_at')
        }),
        ('个性特征', {
            'fields': ('personality_traits', 'interests', 'lifestyle')
        }),
        ('价值观与目标', {
            'fields': ('values', 'communication_style', 'relationship_goals')
        }),
    )
    
    def personality_display(self, obj):
        """显示个性特征"""
        if obj.personality_traits:
            traits = json.loads(obj.personality_traits) if isinstance(obj.personality_traits, str) else obj.personality_traits
            if isinstance(traits, dict):
                return ', '.join([f"{k}: {v}" for k, v in list(traits.items())[:3]])
            elif isinstance(traits, list):
                return ', '.join(traits[:3])
        return '-'
    personality_display.short_description = '个性特征'
    
    def interests_display(self, obj):
        """显示兴趣爱好"""
        if obj.interests:
            interests = json.loads(obj.interests) if isinstance(obj.interests, str) else obj.interests
            return ', '.join(interests[:5])
        return '-'
    interests_display.short_description = '兴趣爱好'


@admin.register(MatchingModel)
class MatchingModelAdmin(admin.ModelAdmin):
    """匹配模型管理"""
    list_display = ['name', 'version', 'model_type', 'accuracy', 'created_at']
    list_filter = ['model_type', 'created_at']
    search_fields = ['name', 'version']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('模型信息', {
            'fields': ('name', 'version', 'model_type', 'description')
        }),
        ('模型状态', {
            'fields': ('accuracy', 'precision', 'recall', 'f1_score')
        }),
        ('配置参数', {
            'fields': ('model_parameters', 'training_config')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(UserSimilarity)
class UserSimilarityAdmin(admin.ModelAdmin):
    """用户相似度管理"""
    list_display = ['user1', 'user2', 'overall_similarity', 'calculated_at']
    list_filter = ['calculated_at']
    search_fields = ['user1__username', 'user2__username']
    readonly_fields = ['calculated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user1', 'user2')


@admin.register(MatchPrediction)
class MatchPredictionAdmin(admin.ModelAdmin):
    """匹配预测管理"""
    list_display = ['user', 'candidate', 'match_score', 'confidence', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'candidate__username']
    readonly_fields = ['created_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'candidate')


@admin.register(UserBehaviorPattern)
class UserBehaviorPatternAdmin(admin.ModelAdmin):
    """用户行为模式管理"""
    list_display = ['user', 'like_frequency', 'message_frequency', 'decision_speed', 'updated_at']
    list_filter = ['updated_at']
    search_fields = ['user__username']
    readonly_fields = ['updated_at']

    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'updated_at')
        }),
        ('活跃模式', {
            'fields': ('active_hours', 'active_days', 'session_duration_avg')
        }),
        ('互动模式', {
            'fields': ('like_frequency', 'message_frequency', 'response_time_avg')
        }),
        ('决策模式', {
            'fields': ('decision_speed', 'selectivity', 'preferred_user_types')
        }),
    )


@admin.register(ModelTrainingData)
class ModelTrainingDataAdmin(admin.ModelAdmin):
    """模型训练数据管理"""
    list_display = ['data_type', 'quality_score', 'created_at']
    list_filter = ['data_type', 'created_at']
    readonly_fields = ['created_at']


@admin.register(RecommendationExplanation)
class RecommendationExplanationAdmin(admin.ModelAdmin):
    """推荐解释管理"""
    list_display = ['user', 'recommended_user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'recommended_user__username']
    readonly_fields = ['created_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'recommended_user')


@admin.register(ModelPerformanceMetric)
class ModelPerformanceMetricAdmin(admin.ModelAdmin):
    """模型性能指标管理"""
    list_display = ['model', 'metric_name', 'metric_value', 'evaluation_date']
    list_filter = ['metric_name', 'evaluation_date']
    search_fields = ['model__name']
    readonly_fields = ['evaluation_date']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('model')
