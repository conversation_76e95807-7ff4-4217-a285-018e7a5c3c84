"""
AI智能匹配算法模型
基于机器学习的用户匹配和推荐系统
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import json

User = get_user_model()


class UserProfile(models.Model):
    """用户画像模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 基础特征
    age_group = models.CharField(max_length=20, blank=True, verbose_name='年龄段')
    education_level = models.CharField(max_length=50, blank=True, verbose_name='教育水平')
    income_level = models.CharField(max_length=50, blank=True, verbose_name='收入水平')
    occupation_category = models.CharField(max_length=100, blank=True, verbose_name='职业类别')
    location_tier = models.CharField(max_length=20, blank=True, verbose_name='城市等级')
    
    # 行为特征
    activity_score = models.FloatField(default=0, verbose_name='活跃度评分')
    social_score = models.FloatField(default=0, verbose_name='社交评分')
    response_rate = models.FloatField(default=0, verbose_name='回复率')
    online_frequency = models.FloatField(default=0, verbose_name='在线频率')
    
    # 偏好特征
    preferred_age_min = models.IntegerField(null=True, blank=True, verbose_name='偏好最小年龄')
    preferred_age_max = models.IntegerField(null=True, blank=True, verbose_name='偏好最大年龄')
    preferred_education = models.JSONField(default=list, verbose_name='偏好教育水平')
    preferred_income = models.JSONField(default=list, verbose_name='偏好收入水平')
    preferred_location = models.JSONField(default=list, verbose_name='偏好地区')
    
    # 兴趣标签
    interests = models.JSONField(default=list, verbose_name='兴趣爱好')
    personality_traits = models.JSONField(default=list, verbose_name='性格特征')
    lifestyle_tags = models.JSONField(default=list, verbose_name='生活方式标签')
    
    # ML特征向量
    feature_vector = models.JSONField(default=list, verbose_name='特征向量')
    embedding_vector = models.JSONField(default=list, verbose_name='嵌入向量')
    
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户画像'
        verbose_name_plural = '用户画像'
        indexes = [
            models.Index(fields=['activity_score']),
            models.Index(fields=['age_group', 'location_tier']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - 用户画像"


class MatchingModel(models.Model):
    """匹配模型配置"""
    MODEL_TYPES = [
        ('collaborative_filtering', '协同过滤'),
        ('content_based', '基于内容'),
        ('deep_learning', '深度学习'),
        ('hybrid', '混合模型'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='模型名称')
    model_type = models.CharField(max_length=30, choices=MODEL_TYPES, verbose_name='模型类型')
    description = models.TextField(verbose_name='模型描述')
    
    # 模型参数
    parameters = models.JSONField(default=dict, verbose_name='模型参数')
    weights = models.JSONField(default=dict, verbose_name='权重配置')
    
    # 模型性能
    accuracy = models.FloatField(default=0, verbose_name='准确率')
    precision = models.FloatField(default=0, verbose_name='精确率')
    recall = models.FloatField(default=0, verbose_name='召回率')
    f1_score = models.FloatField(default=0, verbose_name='F1分数')
    
    # 状态
    is_active = models.BooleanField(default=False, verbose_name='是否启用')
    version = models.CharField(max_length=20, verbose_name='版本号')
    
    # 训练信息
    training_data_size = models.IntegerField(default=0, verbose_name='训练数据量')
    last_trained_at = models.DateTimeField(null=True, blank=True, verbose_name='最后训练时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '匹配模型'
        verbose_name_plural = '匹配模型'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} v{self.version}"


class UserSimilarity(models.Model):
    """用户相似度矩阵"""
    user1 = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='similarity_as_user1',
        verbose_name='用户1'
    )
    user2 = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='similarity_as_user2',
        verbose_name='用户2'
    )
    
    # 相似度分数
    overall_similarity = models.FloatField(verbose_name='总体相似度')
    demographic_similarity = models.FloatField(default=0, verbose_name='人口统计相似度')
    interest_similarity = models.FloatField(default=0, verbose_name='兴趣相似度')
    behavior_similarity = models.FloatField(default=0, verbose_name='行为相似度')
    preference_similarity = models.FloatField(default=0, verbose_name='偏好相似度')
    
    # 计算信息
    calculated_by_model = models.ForeignKey(
        MatchingModel, 
        on_delete=models.SET_NULL, 
        null=True,
        verbose_name='计算模型'
    )
    calculated_at = models.DateTimeField(auto_now=True, verbose_name='计算时间')
    
    class Meta:
        verbose_name = '用户相似度'
        verbose_name_plural = '用户相似度'
        unique_together = ['user1', 'user2']
        indexes = [
            models.Index(fields=['user1', 'overall_similarity']),
            models.Index(fields=['overall_similarity']),
        ]
    
    def __str__(self):
        return f"{self.user1.username} - {self.user2.username}: {self.overall_similarity:.3f}"


class MatchPrediction(models.Model):
    """匹配预测结果"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    candidate = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_predictions',
        verbose_name='候选用户'
    )
    
    # 预测分数
    match_score = models.FloatField(verbose_name='匹配分数')
    compatibility_score = models.FloatField(default=0, verbose_name='兼容性分数')
    attraction_score = models.FloatField(default=0, verbose_name='吸引力分数')
    conversation_potential = models.FloatField(default=0, verbose_name='对话潜力')
    
    # 分数组成
    score_breakdown = models.JSONField(default=dict, verbose_name='分数详情')
    
    # 预测信息
    predicted_by_model = models.ForeignKey(
        MatchingModel, 
        on_delete=models.SET_NULL, 
        null=True,
        verbose_name='预测模型'
    )
    confidence = models.FloatField(default=0, verbose_name='置信度')
    
    # 验证结果
    actual_result = models.CharField(
        max_length=20, 
        choices=[
            ('like', '喜欢'),
            ('pass', '跳过'),
            ('match', '匹配'),
            ('chat', '聊天'),
            ('meet', '见面'),
        ],
        null=True, 
        blank=True,
        verbose_name='实际结果'
    )
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '匹配预测'
        verbose_name_plural = '匹配预测'
        unique_together = ['user', 'candidate']
        indexes = [
            models.Index(fields=['user', 'match_score']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} -> {self.candidate.username}: {self.match_score:.3f}"


class UserBehaviorPattern(models.Model):
    """用户行为模式"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 活跃时间模式
    active_hours = models.JSONField(default=list, verbose_name='活跃时段')
    active_days = models.JSONField(default=list, verbose_name='活跃日期')
    session_duration_avg = models.FloatField(default=0, verbose_name='平均会话时长')
    
    # 互动模式
    like_frequency = models.FloatField(default=0, verbose_name='点赞频率')
    message_frequency = models.FloatField(default=0, verbose_name='消息频率')
    response_time_avg = models.FloatField(default=0, verbose_name='平均回复时间')
    
    # 偏好模式
    preferred_user_types = models.JSONField(default=list, verbose_name='偏好用户类型')
    interaction_patterns = models.JSONField(default=dict, verbose_name='互动模式')
    
    # 决策模式
    decision_speed = models.FloatField(default=0, verbose_name='决策速度')
    selectivity = models.FloatField(default=0, verbose_name='选择性')
    
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户行为模式'
        verbose_name_plural = '用户行为模式'
    
    def __str__(self):
        return f"{self.user.username} - 行为模式"


class ModelTrainingData(models.Model):
    """模型训练数据"""
    DATA_TYPES = [
        ('user_interaction', '用户互动'),
        ('match_result', '匹配结果'),
        ('conversation_data', '对话数据'),
        ('feedback_data', '反馈数据'),
    ]
    
    data_type = models.CharField(max_length=30, choices=DATA_TYPES, verbose_name='数据类型')
    user1 = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='training_data_as_user1',
        verbose_name='用户1'
    )
    user2 = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='training_data_as_user2',
        null=True, 
        blank=True,
        verbose_name='用户2'
    )
    
    # 特征数据
    features = models.JSONField(verbose_name='特征数据')
    
    # 标签数据
    label = models.FloatField(verbose_name='标签值')
    label_type = models.CharField(max_length=50, verbose_name='标签类型')
    
    # 权重
    weight = models.FloatField(default=1.0, verbose_name='样本权重')
    
    # 数据质量
    quality_score = models.FloatField(default=1.0, verbose_name='数据质量分数')
    is_validated = models.BooleanField(default=False, verbose_name='是否已验证')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '模型训练数据'
        verbose_name_plural = '模型训练数据'
        indexes = [
            models.Index(fields=['data_type', 'created_at']),
            models.Index(fields=['is_validated']),
        ]
    
    def __str__(self):
        return f"{self.get_data_type_display()} - {self.created_at.date()}"


class ModelPerformanceMetric(models.Model):
    """模型性能指标"""
    model = models.ForeignKey(MatchingModel, on_delete=models.CASCADE, verbose_name='模型')
    
    # 评估指标
    metric_name = models.CharField(max_length=50, verbose_name='指标名称')
    metric_value = models.FloatField(verbose_name='指标值')
    
    # 评估数据
    test_data_size = models.IntegerField(verbose_name='测试数据量')
    evaluation_date = models.DateTimeField(verbose_name='评估日期')
    
    # 详细结果
    detailed_results = models.JSONField(default=dict, verbose_name='详细结果')
    
    class Meta:
        verbose_name = '模型性能指标'
        verbose_name_plural = '模型性能指标'
        unique_together = ['model', 'metric_name', 'evaluation_date']
    
    def __str__(self):
        return f"{self.model.name} - {self.metric_name}: {self.metric_value}"


class RecommendationExplanation(models.Model):
    """推荐解释"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    recommended_user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_explanations',
        verbose_name='推荐用户'
    )
    
    # 推荐原因
    primary_reason = models.CharField(max_length=100, verbose_name='主要原因')
    secondary_reasons = models.JSONField(default=list, verbose_name='次要原因')
    
    # 匹配亮点
    match_highlights = models.JSONField(default=list, verbose_name='匹配亮点')
    
    # 相似度说明
    similarity_explanation = models.TextField(verbose_name='相似度说明')
    
    # 推荐强度
    recommendation_strength = models.FloatField(verbose_name='推荐强度')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '推荐解释'
        verbose_name_plural = '推荐解释'
        unique_together = ['user', 'recommended_user']
    
    def __str__(self):
        return f"{self.user.username} -> {self.recommended_user.username}: {self.primary_reason}"
