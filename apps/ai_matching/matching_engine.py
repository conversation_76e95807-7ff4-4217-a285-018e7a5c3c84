"""
AI智能匹配引擎
基于机器学习的用户匹配和推荐算法
"""

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import joblib
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.core.cache import cache

from .models import (
    UserProfile, MatchingModel, UserSimilarity, MatchPrediction,
    UserBehaviorPattern, ModelTrainingData, RecommendationExplanation
)
from apps.users.models import User
from apps.matching.models import Like, Match


class AIMatchingEngine:
    """AI匹配引擎主类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scaler = StandardScaler()
        self.models = {}
        self.load_models()
    
    def load_models(self):
        """加载训练好的模型"""
        try:
            # 加载协同过滤模型
            self.models['collaborative'] = joblib.load('models/collaborative_model.pkl')
            
            # 加载基于内容的模型
            self.models['content_based'] = joblib.load('models/content_based_model.pkl')
            
            # 加载深度学习模型
            self.models['deep_learning'] = joblib.load('models/deep_learning_model.pkl')
            
            self.logger.info("AI匹配模型加载成功")
        except FileNotFoundError:
            self.logger.warning("模型文件不存在，将使用默认算法")
            self.initialize_default_models()
    
    def initialize_default_models(self):
        """初始化默认模型"""
        self.models['collaborative'] = RandomForestRegressor(n_estimators=100, random_state=42)
        self.models['content_based'] = RandomForestRegressor(n_estimators=100, random_state=42)
        self.models['deep_learning'] = RandomForestRegressor(n_estimators=100, random_state=42)
    
    def extract_user_features(self, user):
        """提取用户特征向量"""
        try:
            profile = UserProfile.objects.get(user=user)
        except UserProfile.DoesNotExist:
            profile = self.create_user_profile(user)
        
        # 基础特征
        features = {
            'age': user.age or 25,
            'gender': 1 if user.gender == 'M' else 0,
            'education_score': self.encode_education(profile.education_level),
            'income_score': self.encode_income(profile.income_level),
            'location_tier': self.encode_location_tier(profile.location_tier),
            'activity_score': profile.activity_score,
            'social_score': profile.social_score,
            'response_rate': profile.response_rate,
            'online_frequency': profile.online_frequency,
        }
        
        # 兴趣特征
        interest_vector = self.encode_interests(profile.interests)
        features.update({f'interest_{i}': v for i, v in enumerate(interest_vector)})
        
        # 行为特征
        behavior_features = self.extract_behavior_features(user)
        features.update(behavior_features)
        
        return np.array(list(features.values()))
    
    def create_user_profile(self, user):
        """创建用户画像"""
        profile = UserProfile.objects.create(
            user=user,
            age_group=self.get_age_group(user.age),
            activity_score=self.calculate_activity_score(user),
            social_score=self.calculate_social_score(user),
            response_rate=self.calculate_response_rate(user),
            online_frequency=self.calculate_online_frequency(user)
        )
        
        # 更新特征向量
        features = self.extract_user_features(user)
        profile.feature_vector = features.tolist()
        profile.save()
        
        return profile
    
    def calculate_compatibility_score(self, user1, user2):
        """计算用户兼容性分数"""
        # 获取用户特征
        features1 = self.extract_user_features(user1)
        features2 = self.extract_user_features(user2)
        
        # 计算各维度相似度
        demographic_sim = self.calculate_demographic_similarity(user1, user2)
        interest_sim = self.calculate_interest_similarity(user1, user2)
        behavior_sim = self.calculate_behavior_similarity(user1, user2)
        preference_sim = self.calculate_preference_similarity(user1, user2)
        
        # 加权计算总体相似度
        weights = {
            'demographic': 0.25,
            'interest': 0.30,
            'behavior': 0.25,
            'preference': 0.20
        }
        
        overall_similarity = (
            demographic_sim * weights['demographic'] +
            interest_sim * weights['interest'] +
            behavior_sim * weights['behavior'] +
            preference_sim * weights['preference']
        )
        
        # 保存相似度结果
        self.save_similarity_result(user1, user2, {
            'overall': overall_similarity,
            'demographic': demographic_sim,
            'interest': interest_sim,
            'behavior': behavior_sim,
            'preference': preference_sim
        })
        
        return overall_similarity
    
    def predict_match_probability(self, user, candidate):
        """预测匹配概率"""
        # 获取特征
        user_features = self.extract_user_features(user)
        candidate_features = self.extract_user_features(candidate)
        
        # 构建配对特征
        pair_features = np.concatenate([
            user_features,
            candidate_features,
            self.calculate_interaction_features(user, candidate)
        ])
        
        # 使用集成模型预测
        predictions = {}
        for model_name, model in self.models.items():
            try:
                pred = model.predict([pair_features])[0]
                predictions[model_name] = max(0, min(1, pred))  # 限制在[0,1]范围
            except Exception as e:
                self.logger.error(f"模型{model_name}预测失败: {str(e)}")
                predictions[model_name] = 0.5  # 默认值
        
        # 集成预测结果
        ensemble_weights = {
            'collaborative': 0.4,
            'content_based': 0.3,
            'deep_learning': 0.3
        }
        
        final_score = sum(
            predictions[model] * ensemble_weights.get(model, 0)
            for model in predictions
        )
        
        return final_score, predictions
    
    def generate_recommendations(self, user, limit=20):
        """为用户生成推荐列表"""
        cache_key = f"recommendations_{user.id}_{limit}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # 获取候选用户
        candidates = self.get_candidate_users(user)
        
        # 计算匹配分数
        recommendations = []
        for candidate in candidates:
            match_score, score_breakdown = self.predict_match_probability(user, candidate)
            
            recommendations.append({
                'user': candidate,
                'match_score': match_score,
                'score_breakdown': score_breakdown,
                'explanation': self.generate_explanation(user, candidate, score_breakdown)
            })
        
        # 排序并限制数量
        recommendations.sort(key=lambda x: x['match_score'], reverse=True)
        recommendations = recommendations[:limit]
        
        # 保存推荐结果
        self.save_recommendations(user, recommendations)
        
        # 缓存结果
        cache.set(cache_key, recommendations, 3600)  # 缓存1小时
        
        return recommendations
    
    def get_candidate_users(self, user):
        """获取候选用户"""
        # 基础过滤条件
        candidates = User.objects.filter(
            is_active=True,
            gender=self.get_opposite_gender(user.gender)
        ).exclude(
            id=user.id
        )
        
        # 年龄过滤
        if user.age:
            age_range = 5  # 年龄范围
            candidates = candidates.filter(
                age__gte=user.age - age_range,
                age__lte=user.age + age_range
            )
        
        # 排除已经互动过的用户
        interacted_users = Like.objects.filter(
            user=user
        ).values_list('target_user_id', flat=True)
        
        candidates = candidates.exclude(id__in=interacted_users)
        
        # 地理位置过滤（如果有位置信息）
        if user.city:
            candidates = candidates.filter(
                Q(city=user.city) | Q(city__isnull=True)
            )
        
        return candidates[:1000]  # 限制候选数量
    
    def calculate_demographic_similarity(self, user1, user2):
        """计算人口统计学相似度"""
        similarity = 0.0
        factors = 0
        
        # 年龄相似度
        if user1.age and user2.age:
            age_diff = abs(user1.age - user2.age)
            age_sim = max(0, 1 - age_diff / 20)  # 20岁差距为0相似度
            similarity += age_sim
            factors += 1
        
        # 教育水平相似度
        edu1 = getattr(user1.userprofile, 'education_level', '')
        edu2 = getattr(user2.userprofile, 'education_level', '')
        if edu1 and edu2:
            edu_sim = 1.0 if edu1 == edu2 else 0.5
            similarity += edu_sim
            factors += 1
        
        # 收入水平相似度
        income1 = getattr(user1.userprofile, 'income_level', '')
        income2 = getattr(user2.userprofile, 'income_level', '')
        if income1 and income2:
            income_sim = 1.0 if income1 == income2 else 0.3
            similarity += income_sim
            factors += 1
        
        return similarity / factors if factors > 0 else 0.5
    
    def calculate_interest_similarity(self, user1, user2):
        """计算兴趣相似度"""
        try:
            profile1 = user1.userprofile
            profile2 = user2.userprofile
            
            interests1 = set(profile1.interests)
            interests2 = set(profile2.interests)
            
            if not interests1 or not interests2:
                return 0.5
            
            # Jaccard相似度
            intersection = len(interests1.intersection(interests2))
            union = len(interests1.union(interests2))
            
            return intersection / union if union > 0 else 0
        except:
            return 0.5
    
    def calculate_behavior_similarity(self, user1, user2):
        """计算行为相似度"""
        try:
            pattern1 = user1.userbehaviorpattern
            pattern2 = user2.userbehaviorpattern
            
            # 活跃时间相似度
            active_hours1 = set(pattern1.active_hours)
            active_hours2 = set(pattern2.active_hours)
            
            time_similarity = len(active_hours1.intersection(active_hours2)) / 24
            
            # 互动频率相似度
            freq_diff = abs(pattern1.like_frequency - pattern2.like_frequency)
            freq_similarity = max(0, 1 - freq_diff)
            
            return (time_similarity + freq_similarity) / 2
        except:
            return 0.5
    
    def calculate_preference_similarity(self, user1, user2):
        """计算偏好匹配度"""
        try:
            profile1 = user1.userprofile
            profile2 = user2.userprofile
            
            # 年龄偏好匹配
            age_match = 0
            if (profile1.preferred_age_min and profile1.preferred_age_max and 
                user2.age and profile1.preferred_age_min <= user2.age <= profile1.preferred_age_max):
                age_match += 0.5
            
            if (profile2.preferred_age_min and profile2.preferred_age_max and 
                user1.age and profile2.preferred_age_min <= user1.age <= profile2.preferred_age_max):
                age_match += 0.5
            
            return age_match
        except:
            return 0.5
    
    def calculate_interaction_features(self, user, candidate):
        """计算交互特征"""
        features = []
        
        # 共同好友数量
        common_friends = 0  # 这里可以实现共同好友逻辑
        features.append(common_friends)
        
        # 地理距离
        distance = self.calculate_distance(user, candidate)
        features.append(distance)
        
        # 在线时间重叠
        time_overlap = self.calculate_time_overlap(user, candidate)
        features.append(time_overlap)
        
        return np.array(features)
    
    def generate_explanation(self, user, candidate, score_breakdown):
        """生成推荐解释"""
        reasons = []
        
        # 分析主要匹配原因
        if score_breakdown.get('content_based', 0) > 0.7:
            reasons.append("你们有很多共同兴趣")
        
        if score_breakdown.get('collaborative', 0) > 0.7:
            reasons.append("和你相似的用户都喜欢TA")
        
        if abs(user.age - candidate.age) <= 3:
            reasons.append("年龄很匹配")
        
        if user.city == candidate.city:
            reasons.append("你们在同一个城市")
        
        primary_reason = reasons[0] if reasons else "算法推荐的优质用户"
        
        return {
            'primary_reason': primary_reason,
            'secondary_reasons': reasons[1:],
            'match_highlights': self.get_match_highlights(user, candidate)
        }
    
    def save_recommendations(self, user, recommendations):
        """保存推荐结果"""
        for rec in recommendations:
            MatchPrediction.objects.update_or_create(
                user=user,
                candidate=rec['user'],
                defaults={
                    'match_score': rec['match_score'],
                    'score_breakdown': rec['score_breakdown'],
                    'confidence': min(rec['score_breakdown'].values()) if rec['score_breakdown'] else 0.5
                }
            )
            
            # 保存推荐解释
            RecommendationExplanation.objects.update_or_create(
                user=user,
                recommended_user=rec['user'],
                defaults={
                    'primary_reason': rec['explanation']['primary_reason'],
                    'secondary_reasons': rec['explanation']['secondary_reasons'],
                    'match_highlights': rec['explanation']['match_highlights'],
                    'recommendation_strength': rec['match_score']
                }
            )
    
    def train_models(self):
        """训练匹配模型"""
        self.logger.info("开始训练AI匹配模型...")
        
        # 准备训练数据
        training_data = self.prepare_training_data()
        
        if len(training_data) < 100:
            self.logger.warning("训练数据不足，跳过模型训练")
            return
        
        # 分割数据
        X = training_data['features']
        y = training_data['labels']
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 训练各个模型
        for model_name, model in self.models.items():
            try:
                model.fit(X_train, y_train)
                score = model.score(X_test, y_test)
                
                self.logger.info(f"模型{model_name}训练完成，准确率: {score:.3f}")
                
                # 保存模型
                joblib.dump(model, f'models/{model_name}_model.pkl')
                
            except Exception as e:
                self.logger.error(f"模型{model_name}训练失败: {str(e)}")
    
    def prepare_training_data(self):
        """准备训练数据"""
        # 从历史互动数据中提取训练样本
        training_samples = []
        
        # 正样本：匹配成功的用户对
        matches = Match.objects.all()[:1000]
        for match in matches:
            features = np.concatenate([
                self.extract_user_features(match.user1),
                self.extract_user_features(match.user2),
                self.calculate_interaction_features(match.user1, match.user2)
            ])
            training_samples.append((features, 1.0))  # 正样本标签为1
        
        # 负样本：没有互动的用户对
        likes = Like.objects.filter(is_mutual=False)[:1000]
        for like in likes:
            features = np.concatenate([
                self.extract_user_features(like.user),
                self.extract_user_features(like.target_user),
                self.calculate_interaction_features(like.user, like.target_user)
            ])
            training_samples.append((features, 0.0))  # 负样本标签为0
        
        if not training_samples:
            return {'features': [], 'labels': []}
        
        features, labels = zip(*training_samples)
        
        return {
            'features': np.array(features),
            'labels': np.array(labels)
        }
    
    # 辅助方法
    def get_age_group(self, age):
        if not age:
            return 'unknown'
        if age < 25:
            return '18-24'
        elif age < 30:
            return '25-29'
        elif age < 35:
            return '30-34'
        elif age < 40:
            return '35-39'
        else:
            return '40+'
    
    def encode_education(self, education):
        education_scores = {
            '高中': 1, '大专': 2, '本科': 3, '硕士': 4, '博士': 5
        }
        return education_scores.get(education, 2)
    
    def encode_income(self, income):
        income_scores = {
            '3k以下': 1, '3k-5k': 2, '5k-8k': 3, '8k-12k': 4, 
            '12k-20k': 5, '20k-30k': 6, '30k以上': 7
        }
        return income_scores.get(income, 3)
    
    def encode_location_tier(self, location):
        tier_scores = {'一线': 3, '二线': 2, '三线': 1}
        return tier_scores.get(location, 1)
    
    def encode_interests(self, interests):
        # 简化的兴趣编码，实际应该使用更复杂的嵌入
        all_interests = [
            '运动', '音乐', '电影', '读书', '旅行', '美食', '摄影', '游戏',
            '艺术', '科技', '时尚', '宠物', '健身', '瑜伽', '舞蹈', '绘画'
        ]
        vector = [1 if interest in interests else 0 for interest in all_interests]
        return vector
    
    def get_opposite_gender(self, gender):
        return 'F' if gender == 'M' else 'M'
    
    def calculate_distance(self, user1, user2):
        # 简化的距离计算，实际应该使用地理坐标
        if user1.city == user2.city:
            return 0.1
        else:
            return 0.9
    
    def calculate_time_overlap(self, user1, user2):
        # 简化的时间重叠计算
        return 0.5
    
    def get_match_highlights(self, user1, user2):
        highlights = []
        
        if abs(user1.age - user2.age) <= 2:
            highlights.append("年龄相近")
        
        if user1.city == user2.city:
            highlights.append("同城")
        
        return highlights

    def calculate_activity_score(self, user):
        """计算用户活跃度分数"""
        # 基于最近30天的活动计算
        recent_date = timezone.now() - timedelta(days=30)

        # 登录频率
        login_count = user.last_login and (timezone.now() - user.last_login).days < 7
        login_score = 0.3 if login_count else 0

        # 点赞数量
        like_count = Like.objects.filter(user=user, created_at__gte=recent_date).count()
        like_score = min(like_count / 50, 0.4)  # 最多0.4分

        # 消息数量
        from apps.chat.models import ChatMessage
        message_count = ChatMessage.objects.filter(sender=user, created_at__gte=recent_date).count()
        message_score = min(message_count / 100, 0.3)  # 最多0.3分

        return login_score + like_score + message_score

    def calculate_social_score(self, user):
        """计算社交分数"""
        # 匹配成功率
        total_likes = Like.objects.filter(user=user).count()
        mutual_likes = Like.objects.filter(user=user, is_mutual=True).count()

        if total_likes > 0:
            match_rate = mutual_likes / total_likes
        else:
            match_rate = 0

        # 被喜欢数量
        received_likes = Like.objects.filter(target_user=user).count()
        popularity_score = min(received_likes / 100, 0.5)

        return match_rate * 0.5 + popularity_score

    def calculate_response_rate(self, user):
        """计算回复率"""
        from apps.chat.models import ChatMessage, ChatSession

        # 获取用户参与的对话
        sessions = ChatSession.objects.filter(
            Q(user1=user) | Q(user2=user)
        )

        if not sessions.exists():
            return 0.5  # 默认值

        total_received = 0
        total_replied = 0

        for session in sessions:
            other_user = session.user2 if session.user1 == user else session.user1

            # 收到的消息
            received_messages = ChatMessage.objects.filter(
                session=session,
                sender=other_user
            ).order_by('created_at')

            # 回复的消息
            replied_messages = ChatMessage.objects.filter(
                session=session,
                sender=user
            )

            total_received += received_messages.count()

            # 计算实际回复数
            for msg in received_messages:
                if replied_messages.filter(created_at__gt=msg.created_at).exists():
                    total_replied += 1

        return total_replied / total_received if total_received > 0 else 0.5

    def calculate_online_frequency(self, user):
        """计算在线频率"""
        if not user.last_login:
            return 0.1

        days_since_login = (timezone.now() - user.last_login).days

        if days_since_login == 0:
            return 1.0
        elif days_since_login <= 3:
            return 0.8
        elif days_since_login <= 7:
            return 0.6
        elif days_since_login <= 14:
            return 0.4
        elif days_since_login <= 30:
            return 0.2
        else:
            return 0.1

    def extract_behavior_features(self, user):
        """提取行为特征"""
        try:
            pattern = user.userbehaviorpattern
            return {
                'session_duration': pattern.session_duration_avg,
                'like_frequency': pattern.like_frequency,
                'message_frequency': pattern.message_frequency,
                'response_time': pattern.response_time_avg,
                'decision_speed': pattern.decision_speed,
                'selectivity': pattern.selectivity
            }
        except:
            return {
                'session_duration': 15.0,  # 默认15分钟
                'like_frequency': 0.5,
                'message_frequency': 0.3,
                'response_time': 300.0,  # 默认5分钟
                'decision_speed': 0.5,
                'selectivity': 0.5
            }

    def save_similarity_result(self, user1, user2, similarities):
        """保存相似度计算结果"""
        UserSimilarity.objects.update_or_create(
            user1=user1,
            user2=user2,
            defaults={
                'overall_similarity': similarities['overall'],
                'demographic_similarity': similarities['demographic'],
                'interest_similarity': similarities['interest'],
                'behavior_similarity': similarities['behavior'],
                'preference_similarity': similarities['preference']
            }
        )
