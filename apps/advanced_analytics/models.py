"""
高级数据分析引擎模型
用户画像、行为预测、商业智能等高级分析功能
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class UserSegment(models.Model):
    """用户分群"""
    SEGMENT_TYPES = [
        ('demographic', '人口统计'),
        ('behavioral', '行为特征'),
        ('psychographic', '心理特征'),
        ('geographic', '地理位置'),
        ('value_based', '价值导向'),
        ('lifecycle', '生命周期'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='分群名称')
    description = models.TextField(verbose_name='分群描述')
    segment_type = models.CharField(max_length=20, choices=SEGMENT_TYPES, verbose_name='分群类型')
    
    # 分群条件
    conditions = models.JSONField(verbose_name='分群条件')
    
    # 分群统计
    user_count = models.IntegerField(default=0, verbose_name='用户数量')
    last_calculated_at = models.DateTimeField(null=True, blank=True, verbose_name='最后计算时间')
    
    # 分群属性
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_dynamic = models.BooleanField(default=True, verbose_name='是否动态更新')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户分群'
        verbose_name_plural = '用户分群'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.user_count}人)"


class UserSegmentMembership(models.Model):
    """用户分群成员关系"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    segment = models.ForeignKey(UserSegment, on_delete=models.CASCADE, verbose_name='分群')
    
    # 成员属性
    confidence_score = models.FloatField(default=1.0, verbose_name='置信度')
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name='加入时间')
    
    # 成员特征
    segment_features = models.JSONField(default=dict, verbose_name='分群特征')
    
    class Meta:
        verbose_name = '用户分群成员'
        verbose_name_plural = '用户分群成员'
        unique_together = ['user', 'segment']
        indexes = [
            models.Index(fields=['segment', 'confidence_score']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.segment.name}"


class UserLifecycleStage(models.Model):
    """用户生命周期阶段"""
    LIFECYCLE_STAGES = [
        ('visitor', '访客'),
        ('lead', '潜在用户'),
        ('new_user', '新用户'),
        ('active_user', '活跃用户'),
        ('engaged_user', '深度用户'),
        ('power_user', '核心用户'),
        ('at_risk', '流失风险'),
        ('dormant', '休眠用户'),
        ('churned', '已流失'),
        ('reactivated', '重新激活'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    current_stage = models.CharField(max_length=20, choices=LIFECYCLE_STAGES, verbose_name='当前阶段')
    previous_stage = models.CharField(max_length=20, choices=LIFECYCLE_STAGES, null=True, blank=True, verbose_name='上一阶段')
    
    # 阶段属性
    stage_score = models.FloatField(default=0, verbose_name='阶段评分')
    days_in_stage = models.IntegerField(default=0, verbose_name='在当前阶段天数')
    
    # 预测信息
    predicted_next_stage = models.CharField(max_length=20, choices=LIFECYCLE_STAGES, null=True, blank=True, verbose_name='预测下一阶段')
    stage_transition_probability = models.FloatField(default=0, verbose_name='阶段转换概率')
    
    # 时间信息
    stage_entered_at = models.DateTimeField(auto_now_add=True, verbose_name='进入阶段时间')
    last_updated_at = models.DateTimeField(auto_now=True, verbose_name='最后更新时间')
    
    class Meta:
        verbose_name = '用户生命周期'
        verbose_name_plural = '用户生命周期'
        indexes = [
            models.Index(fields=['current_stage']),
            models.Index(fields=['stage_score']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_current_stage_display()}"


class UserValueScore(models.Model):
    """用户价值评分"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 价值评分维度
    monetary_value = models.FloatField(default=0, verbose_name='货币价值')
    engagement_value = models.FloatField(default=0, verbose_name='参与价值')
    social_value = models.FloatField(default=0, verbose_name='社交价值')
    content_value = models.FloatField(default=0, verbose_name='内容价值')
    referral_value = models.FloatField(default=0, verbose_name='推荐价值')
    
    # 综合评分
    total_value_score = models.FloatField(default=0, verbose_name='总价值评分')
    value_tier = models.CharField(
        max_length=20,
        choices=[
            ('bronze', '青铜'),
            ('silver', '白银'),
            ('gold', '黄金'),
            ('platinum', '铂金'),
            ('diamond', '钻石'),
        ],
        default='bronze',
        verbose_name='价值等级'
    )
    
    # 预测信息
    predicted_ltv = models.FloatField(default=0, verbose_name='预测生命周期价值')
    churn_risk_score = models.FloatField(default=0, verbose_name='流失风险评分')
    
    # 计算信息
    calculated_at = models.DateTimeField(auto_now=True, verbose_name='计算时间')
    calculation_version = models.CharField(max_length=20, default='1.0', verbose_name='计算版本')
    
    class Meta:
        verbose_name = '用户价值评分'
        verbose_name_plural = '用户价值评分'
        indexes = [
            models.Index(fields=['total_value_score']),
            models.Index(fields=['value_tier']),
            models.Index(fields=['churn_risk_score']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.value_tier} ({self.total_value_score})"


class BehaviorPrediction(models.Model):
    """行为预测"""
    PREDICTION_TYPES = [
        ('churn', '流失预测'),
        ('purchase', '购买预测'),
        ('engagement', '参与预测'),
        ('conversion', '转化预测'),
        ('retention', '留存预测'),
        ('upgrade', '升级预测'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    prediction_type = models.CharField(max_length=20, choices=PREDICTION_TYPES, verbose_name='预测类型')
    
    # 预测结果
    prediction_score = models.FloatField(verbose_name='预测分数')
    confidence_level = models.FloatField(verbose_name='置信度')
    
    # 预测详情
    prediction_details = models.JSONField(default=dict, verbose_name='预测详情')
    contributing_factors = models.JSONField(default=list, verbose_name='影响因素')
    
    # 预测时间
    prediction_horizon = models.IntegerField(verbose_name='预测时间范围(天)')
    predicted_date = models.DateTimeField(null=True, blank=True, verbose_name='预测发生时间')
    
    # 模型信息
    model_name = models.CharField(max_length=100, verbose_name='模型名称')
    model_version = models.CharField(max_length=20, verbose_name='模型版本')
    
    # 验证结果
    actual_outcome = models.BooleanField(null=True, blank=True, verbose_name='实际结果')
    outcome_verified_at = models.DateTimeField(null=True, blank=True, verbose_name='结果验证时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '行为预测'
        verbose_name_plural = '行为预测'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'prediction_type']),
            models.Index(fields=['prediction_score']),
            models.Index(fields=['predicted_date']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_prediction_type_display()} ({self.prediction_score})"


class CohortAnalysis(models.Model):
    """队列分析"""
    cohort_name = models.CharField(max_length=100, verbose_name='队列名称')
    cohort_period = models.CharField(
        max_length=20,
        choices=[
            ('daily', '日队列'),
            ('weekly', '周队列'),
            ('monthly', '月队列'),
        ],
        verbose_name='队列周期'
    )
    
    # 队列定义
    cohort_date = models.DateField(verbose_name='队列日期')
    cohort_size = models.IntegerField(verbose_name='队列大小')
    
    # 队列数据
    retention_data = models.JSONField(default=dict, verbose_name='留存数据')
    revenue_data = models.JSONField(default=dict, verbose_name='收入数据')
    engagement_data = models.JSONField(default=dict, verbose_name='参与数据')
    
    # 计算信息
    calculated_at = models.DateTimeField(auto_now=True, verbose_name='计算时间')
    
    class Meta:
        verbose_name = '队列分析'
        verbose_name_plural = '队列分析'
        unique_together = ['cohort_period', 'cohort_date']
        ordering = ['-cohort_date']
    
    def __str__(self):
        return f"{self.cohort_name} - {self.cohort_date}"


class FunnelAnalysis(models.Model):
    """漏斗分析"""
    funnel_name = models.CharField(max_length=100, verbose_name='漏斗名称')
    description = models.TextField(verbose_name='漏斗描述')
    
    # 漏斗步骤
    steps = models.JSONField(verbose_name='漏斗步骤')
    
    # 分析结果
    conversion_data = models.JSONField(default=dict, verbose_name='转化数据')
    drop_off_analysis = models.JSONField(default=dict, verbose_name='流失分析')
    
    # 分析配置
    date_range_start = models.DateTimeField(verbose_name='分析开始时间')
    date_range_end = models.DateTimeField(verbose_name='分析结束时间')
    
    # 分群过滤
    segment_filters = models.JSONField(default=dict, verbose_name='分群过滤')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '漏斗分析'
        verbose_name_plural = '漏斗分析'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.funnel_name


class ABTestExperiment(models.Model):
    """A/B测试实验"""
    EXPERIMENT_STATUS = [
        ('draft', '草稿'),
        ('running', '运行中'),
        ('paused', '已暂停'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='实验名称')
    description = models.TextField(verbose_name='实验描述')
    hypothesis = models.TextField(verbose_name='实验假设')
    
    # 实验配置
    control_group_ratio = models.FloatField(default=0.5, verbose_name='对照组比例')
    treatment_groups = models.JSONField(default=list, verbose_name='实验组配置')
    
    # 实验指标
    primary_metric = models.CharField(max_length=100, verbose_name='主要指标')
    secondary_metrics = models.JSONField(default=list, verbose_name='次要指标')
    
    # 实验状态
    status = models.CharField(max_length=20, choices=EXPERIMENT_STATUS, default='draft', verbose_name='实验状态')
    
    # 时间配置
    start_date = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 样本配置
    target_sample_size = models.IntegerField(verbose_name='目标样本量')
    current_sample_size = models.IntegerField(default=0, verbose_name='当前样本量')
    
    # 实验结果
    results = models.JSONField(default=dict, verbose_name='实验结果')
    statistical_significance = models.FloatField(null=True, blank=True, verbose_name='统计显著性')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = 'A/B测试实验'
        verbose_name_plural = 'A/B测试实验'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"


class ABTestParticipant(models.Model):
    """A/B测试参与者"""
    experiment = models.ForeignKey(ABTestExperiment, on_delete=models.CASCADE, verbose_name='实验')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 分组信息
    group_name = models.CharField(max_length=50, verbose_name='分组名称')
    group_type = models.CharField(
        max_length=20,
        choices=[
            ('control', '对照组'),
            ('treatment', '实验组'),
        ],
        verbose_name='分组类型'
    )
    
    # 参与信息
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name='分配时间')
    first_exposure_at = models.DateTimeField(null=True, blank=True, verbose_name='首次曝光时间')
    
    # 转化信息
    has_converted = models.BooleanField(default=False, verbose_name='是否转化')
    conversion_value = models.FloatField(null=True, blank=True, verbose_name='转化价值')
    converted_at = models.DateTimeField(null=True, blank=True, verbose_name='转化时间')
    
    # 参与数据
    participation_data = models.JSONField(default=dict, verbose_name='参与数据')
    
    class Meta:
        verbose_name = 'A/B测试参与者'
        verbose_name_plural = 'A/B测试参与者'
        unique_together = ['experiment', 'user']
        indexes = [
            models.Index(fields=['experiment', 'group_name']),
            models.Index(fields=['has_converted']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.experiment.name} - {self.group_name}"


class BusinessIntelligenceReport(models.Model):
    """商业智能报告"""
    REPORT_TYPES = [
        ('executive_summary', '执行摘要'),
        ('user_analytics', '用户分析'),
        ('revenue_analysis', '收入分析'),
        ('engagement_report', '参与度报告'),
        ('retention_analysis', '留存分析'),
        ('conversion_funnel', '转化漏斗'),
        ('cohort_report', '队列报告'),
        ('predictive_analytics', '预测分析'),
    ]
    
    REPORT_FREQUENCY = [
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月'),
        ('quarterly', '每季度'),
        ('yearly', '每年'),
        ('ad_hoc', '临时'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='报告名称')
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES, verbose_name='报告类型')
    frequency = models.CharField(max_length=20, choices=REPORT_FREQUENCY, verbose_name='报告频率')
    
    # 报告配置
    data_sources = models.JSONField(default=list, verbose_name='数据源')
    filters = models.JSONField(default=dict, verbose_name='过滤条件')
    metrics = models.JSONField(default=list, verbose_name='指标配置')
    
    # 报告内容
    report_data = models.JSONField(default=dict, verbose_name='报告数据')
    insights = models.JSONField(default=list, verbose_name='洞察发现')
    recommendations = models.JSONField(default=list, verbose_name='建议措施')
    
    # 报告状态
    is_automated = models.BooleanField(default=False, verbose_name='是否自动生成')
    is_published = models.BooleanField(default=False, verbose_name='是否已发布')
    
    # 时间信息
    report_period_start = models.DateTimeField(verbose_name='报告周期开始')
    report_period_end = models.DateTimeField(verbose_name='报告周期结束')
    generated_at = models.DateTimeField(auto_now_add=True, verbose_name='生成时间')
    
    class Meta:
        verbose_name = '商业智能报告'
        verbose_name_plural = '商业智能报告'
        ordering = ['-generated_at']
        indexes = [
            models.Index(fields=['report_type', 'frequency']),
            models.Index(fields=['report_period_start', 'report_period_end']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.report_period_start.date()}"


class DataQualityMetric(models.Model):
    """数据质量指标"""
    METRIC_TYPES = [
        ('completeness', '完整性'),
        ('accuracy', '准确性'),
        ('consistency', '一致性'),
        ('timeliness', '及时性'),
        ('validity', '有效性'),
        ('uniqueness', '唯一性'),
    ]
    
    data_source = models.CharField(max_length=100, verbose_name='数据源')
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES, verbose_name='指标类型')
    
    # 质量分数
    quality_score = models.FloatField(verbose_name='质量分数')
    threshold = models.FloatField(verbose_name='阈值')
    
    # 详细信息
    total_records = models.IntegerField(verbose_name='总记录数')
    valid_records = models.IntegerField(verbose_name='有效记录数')
    invalid_records = models.IntegerField(verbose_name='无效记录数')
    
    # 问题详情
    issues_found = models.JSONField(default=list, verbose_name='发现的问题')
    
    # 检查信息
    checked_at = models.DateTimeField(auto_now_add=True, verbose_name='检查时间')
    
    class Meta:
        verbose_name = '数据质量指标'
        verbose_name_plural = '数据质量指标'
        ordering = ['-checked_at']
        indexes = [
            models.Index(fields=['data_source', 'metric_type']),
            models.Index(fields=['quality_score']),
        ]
    
    def __str__(self):
        return f"{self.data_source} - {self.get_metric_type_display()} ({self.quality_score})"


class PredictiveModel(models.Model):
    """预测模型"""
    MODEL_TYPES = [
        ('classification', '分类模型'),
        ('regression', '回归模型'),
        ('clustering', '聚类模型'),
        ('time_series', '时间序列'),
        ('recommendation', '推荐模型'),
        ('anomaly_detection', '异常检测'),
    ]

    MODEL_ALGORITHMS = [
        ('random_forest', '随机森林'),
        ('gradient_boosting', '梯度提升'),
        ('neural_network', '神经网络'),
        ('svm', '支持向量机'),
        ('logistic_regression', '逻辑回归'),
        ('kmeans', 'K均值'),
        ('lstm', 'LSTM'),
        ('collaborative_filtering', '协同过滤'),
    ]

    name = models.CharField(max_length=100, verbose_name='模型名称')
    description = models.TextField(verbose_name='模型描述')
    model_type = models.CharField(max_length=30, choices=MODEL_TYPES, verbose_name='模型类型')
    algorithm = models.CharField(max_length=30, choices=MODEL_ALGORITHMS, verbose_name='算法')

    # 模型配置
    features = models.JSONField(default=list, verbose_name='特征列表')
    target_variable = models.CharField(max_length=100, verbose_name='目标变量')
    hyperparameters = models.JSONField(default=dict, verbose_name='超参数')

    # 模型性能
    training_accuracy = models.FloatField(null=True, blank=True, verbose_name='训练准确率')
    validation_accuracy = models.FloatField(null=True, blank=True, verbose_name='验证准确率')
    test_accuracy = models.FloatField(null=True, blank=True, verbose_name='测试准确率')

    # 模型状态
    is_active = models.BooleanField(default=False, verbose_name='是否启用')
    version = models.CharField(max_length=20, verbose_name='版本号')

    # 训练信息
    training_data_size = models.IntegerField(default=0, verbose_name='训练数据量')
    last_trained_at = models.DateTimeField(null=True, blank=True, verbose_name='最后训练时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '预测模型'
        verbose_name_plural = '预测模型'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} v{self.version}"
