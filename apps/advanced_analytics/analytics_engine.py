"""
高级数据分析引擎
用户画像、行为预测、商业智能等高级分析功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.core.cache import cache
import logging
from typing import Dict, List, Optional, Tuple

from .models import (
    UserSegment, UserSegmentMembership, UserLifecycleStage,
    UserValueScore, BehaviorPrediction, CohortAnalysis,
    FunnelAnalysis, ABTestExperiment, BusinessIntelligenceReport,
    PredictiveModel
)
from apps.users.models import User
from apps.matching.models import Like, Match
from apps.chat.models import ChatMessage

logger = logging.getLogger(__name__)


class AdvancedAnalyticsEngine:
    """高级分析引擎主类"""
    
    def __init__(self):
        self.cache_prefix = 'analytics'
        self.cache_timeout = 3600  # 1小时缓存
    
    def calculate_user_segments(self):
        """计算用户分群"""
        logger.info("开始计算用户分群...")
        
        # 获取所有活跃的分群
        segments = UserSegment.objects.filter(is_active=True)
        
        for segment in segments:
            try:
                # 根据分群条件筛选用户
                users = self.filter_users_by_conditions(segment.conditions)
                
                # 清除旧的成员关系
                UserSegmentMembership.objects.filter(segment=segment).delete()
                
                # 创建新的成员关系
                memberships = []
                for user in users:
                    confidence_score = self.calculate_segment_confidence(user, segment)
                    memberships.append(UserSegmentMembership(
                        user=user,
                        segment=segment,
                        confidence_score=confidence_score,
                        segment_features=self.extract_segment_features(user, segment)
                    ))
                
                # 批量创建
                UserSegmentMembership.objects.bulk_create(memberships, batch_size=1000)
                
                # 更新分群统计
                segment.user_count = len(memberships)
                segment.last_calculated_at = timezone.now()
                segment.save()
                
                logger.info(f"分群 {segment.name} 计算完成，用户数: {segment.user_count}")
                
            except Exception as e:
                logger.error(f"计算分群 {segment.name} 失败: {str(e)}")
    
    def filter_users_by_conditions(self, conditions: Dict) -> List[User]:
        """根据条件筛选用户"""
        queryset = User.objects.filter(is_active=True)
        
        # 年龄条件
        if 'age_min' in conditions:
            queryset = queryset.filter(age__gte=conditions['age_min'])
        if 'age_max' in conditions:
            queryset = queryset.filter(age__lte=conditions['age_max'])
        
        # 性别条件
        if 'gender' in conditions:
            queryset = queryset.filter(gender=conditions['gender'])
        
        # 地理位置条件
        if 'cities' in conditions:
            queryset = queryset.filter(city__in=conditions['cities'])
        
        # 注册时间条件
        if 'registration_days_ago' in conditions:
            cutoff_date = timezone.now() - timedelta(days=conditions['registration_days_ago'])
            queryset = queryset.filter(date_joined__gte=cutoff_date)
        
        # VIP状态条件
        if 'is_vip' in conditions:
            queryset = queryset.filter(is_vip=conditions['is_vip'])
        
        # 活跃度条件
        if 'min_login_days' in conditions:
            # 这里需要根据实际的活跃度计算逻辑
            pass
        
        return list(queryset)
    
    def calculate_segment_confidence(self, user: User, segment: UserSegment) -> float:
        """计算用户对分群的置信度"""
        # 简化的置信度计算
        confidence = 1.0
        
        conditions = segment.conditions
        
        # 年龄匹配度
        if 'age_min' in conditions and 'age_max' in conditions and user.age:
            age_range = conditions['age_max'] - conditions['age_min']
            if age_range > 0:
                age_center = (conditions['age_min'] + conditions['age_max']) / 2
                age_distance = abs(user.age - age_center)
                age_confidence = max(0, 1 - (age_distance / (age_range / 2)))
                confidence *= age_confidence
        
        return confidence
    
    def extract_segment_features(self, user: User, segment: UserSegment) -> Dict:
        """提取用户在分群中的特征"""
        features = {
            'age': user.age,
            'gender': user.gender,
            'city': user.city,
            'is_vip': user.is_vip,
            'registration_date': user.date_joined.isoformat(),
        }
        
        # 添加行为特征
        features.update(self.calculate_user_behavior_features(user))
        
        return features
    
    def calculate_user_behavior_features(self, user: User) -> Dict:
        """计算用户行为特征"""
        recent_date = timezone.now() - timedelta(days=30)
        
        # 点赞行为
        likes_sent = Like.objects.filter(user=user, created_at__gte=recent_date).count()
        likes_received = Like.objects.filter(target_user=user, created_at__gte=recent_date).count()
        
        # 匹配行为
        matches_count = Match.objects.filter(
            Q(user1=user) | Q(user2=user),
            created_at__gte=recent_date
        ).count()
        
        # 消息行为
        messages_sent = ChatMessage.objects.filter(sender=user, created_at__gte=recent_date).count()
        
        return {
            'likes_sent_30d': likes_sent,
            'likes_received_30d': likes_received,
            'matches_30d': matches_count,
            'messages_sent_30d': messages_sent,
            'activity_score': (likes_sent + matches_count + messages_sent) / 30
        }
    
    def update_user_lifecycle_stages(self):
        """更新用户生命周期阶段"""
        logger.info("开始更新用户生命周期阶段...")
        
        users = User.objects.filter(is_active=True)
        
        for user in users:
            try:
                current_stage = self.determine_lifecycle_stage(user)
                
                lifecycle, created = UserLifecycleStage.objects.get_or_create(
                    user=user,
                    defaults={'current_stage': current_stage}
                )
                
                if not created and lifecycle.current_stage != current_stage:
                    # 阶段发生变化
                    lifecycle.previous_stage = lifecycle.current_stage
                    lifecycle.current_stage = current_stage
                    lifecycle.stage_entered_at = timezone.now()
                    lifecycle.days_in_stage = 0
                else:
                    # 更新在当前阶段的天数
                    days_in_stage = (timezone.now() - lifecycle.stage_entered_at).days
                    lifecycle.days_in_stage = days_in_stage
                
                # 计算阶段评分
                lifecycle.stage_score = self.calculate_stage_score(user, current_stage)
                
                # 预测下一阶段
                next_stage, transition_prob = self.predict_next_lifecycle_stage(user, current_stage)
                lifecycle.predicted_next_stage = next_stage
                lifecycle.stage_transition_probability = transition_prob
                
                lifecycle.save()
                
            except Exception as e:
                logger.error(f"更新用户 {user.username} 生命周期失败: {str(e)}")
    
    def determine_lifecycle_stage(self, user: User) -> str:
        """确定用户生命周期阶段"""
        # 计算用户活跃度指标
        days_since_registration = (timezone.now() - user.date_joined).days
        days_since_last_login = (timezone.now() - user.last_login).days if user.last_login else 999
        
        # 获取用户行为数据
        behavior_features = self.calculate_user_behavior_features(user)
        activity_score = behavior_features['activity_score']
        
        # 根据规则确定阶段
        if days_since_registration <= 7:
            if activity_score > 2:
                return 'active_user'
            else:
                return 'new_user'
        elif days_since_last_login > 30:
            if days_since_last_login > 90:
                return 'churned'
            else:
                return 'dormant'
        elif activity_score > 5:
            return 'power_user'
        elif activity_score > 2:
            return 'engaged_user'
        elif activity_score > 0.5:
            return 'active_user'
        else:
            return 'at_risk'
    
    def calculate_stage_score(self, user: User, stage: str) -> float:
        """计算阶段评分"""
        behavior_features = self.calculate_user_behavior_features(user)
        
        # 根据不同阶段计算不同的评分
        if stage == 'power_user':
            return min(100, behavior_features['activity_score'] * 20)
        elif stage == 'engaged_user':
            return min(100, behavior_features['activity_score'] * 25)
        elif stage == 'active_user':
            return min(100, behavior_features['activity_score'] * 30)
        elif stage == 'at_risk':
            return max(0, 50 - behavior_features['activity_score'] * 10)
        else:
            return 50  # 默认评分
    
    def predict_next_lifecycle_stage(self, user: User, current_stage: str) -> Tuple[str, float]:
        """预测下一个生命周期阶段"""
        # 简化的预测逻辑
        behavior_features = self.calculate_user_behavior_features(user)
        activity_score = behavior_features['activity_score']
        
        stage_transitions = {
            'new_user': {
                'active_user': 0.6 if activity_score > 1 else 0.3,
                'at_risk': 0.4 if activity_score < 1 else 0.1
            },
            'active_user': {
                'engaged_user': 0.5 if activity_score > 2 else 0.2,
                'at_risk': 0.3 if activity_score < 0.5 else 0.1
            },
            'engaged_user': {
                'power_user': 0.4 if activity_score > 4 else 0.1,
                'active_user': 0.3,
                'at_risk': 0.2 if activity_score < 1 else 0.05
            },
            'power_user': {
                'engaged_user': 0.3 if activity_score < 3 else 0.1,
                'at_risk': 0.1 if activity_score < 2 else 0.02
            },
            'at_risk': {
                'active_user': 0.4 if activity_score > 1 else 0.1,
                'dormant': 0.5 if activity_score < 0.2 else 0.2
            },
            'dormant': {
                'churned': 0.6,
                'reactivated': 0.2 if activity_score > 0.5 else 0.05
            }
        }
        
        transitions = stage_transitions.get(current_stage, {})
        if not transitions:
            return current_stage, 0.0
        
        # 找到概率最高的下一阶段
        next_stage = max(transitions.items(), key=lambda x: x[1])
        return next_stage[0], next_stage[1]
    
    def calculate_user_value_scores(self):
        """计算用户价值评分"""
        logger.info("开始计算用户价值评分...")
        
        users = User.objects.filter(is_active=True)
        
        for user in users:
            try:
                # 计算各维度价值
                monetary_value = self.calculate_monetary_value(user)
                engagement_value = self.calculate_engagement_value(user)
                social_value = self.calculate_social_value(user)
                content_value = self.calculate_content_value(user)
                referral_value = self.calculate_referral_value(user)
                
                # 计算总价值评分
                total_value = (
                    monetary_value * 0.3 +
                    engagement_value * 0.25 +
                    social_value * 0.2 +
                    content_value * 0.15 +
                    referral_value * 0.1
                )
                
                # 确定价值等级
                value_tier = self.determine_value_tier(total_value)
                
                # 预测生命周期价值
                predicted_ltv = self.predict_lifetime_value(user)
                
                # 计算流失风险
                churn_risk = self.calculate_churn_risk(user)
                
                # 更新或创建价值评分记录
                value_score, created = UserValueScore.objects.get_or_create(
                    user=user,
                    defaults={
                        'monetary_value': monetary_value,
                        'engagement_value': engagement_value,
                        'social_value': social_value,
                        'content_value': content_value,
                        'referral_value': referral_value,
                        'total_value_score': total_value,
                        'value_tier': value_tier,
                        'predicted_ltv': predicted_ltv,
                        'churn_risk_score': churn_risk
                    }
                )
                
                if not created:
                    value_score.monetary_value = monetary_value
                    value_score.engagement_value = engagement_value
                    value_score.social_value = social_value
                    value_score.content_value = content_value
                    value_score.referral_value = referral_value
                    value_score.total_value_score = total_value
                    value_score.value_tier = value_tier
                    value_score.predicted_ltv = predicted_ltv
                    value_score.churn_risk_score = churn_risk
                    value_score.save()
                
            except Exception as e:
                logger.error(f"计算用户 {user.username} 价值评分失败: {str(e)}")
    
    def calculate_monetary_value(self, user: User) -> float:
        """计算货币价值"""
        # 这里应该根据实际的付费数据计算
        # 简化实现：VIP用户有基础货币价值
        if user.is_vip:
            return 100.0
        else:
            return 0.0
    
    def calculate_engagement_value(self, user: User) -> float:
        """计算参与价值"""
        behavior_features = self.calculate_user_behavior_features(user)
        activity_score = behavior_features['activity_score']
        
        # 基于活跃度计算参与价值
        return min(100, activity_score * 20)
    
    def calculate_social_value(self, user: User) -> float:
        """计算社交价值"""
        behavior_features = self.calculate_user_behavior_features(user)
        
        # 基于匹配和互动计算社交价值
        social_score = (
            behavior_features['matches_30d'] * 10 +
            behavior_features['likes_received_30d'] * 2 +
            behavior_features['messages_sent_30d'] * 1
        )
        
        return min(100, social_score)
    
    def calculate_content_value(self, user: User) -> float:
        """计算内容价值"""
        # 这里应该基于用户发布的内容质量计算
        # 简化实现
        return 50.0
    
    def calculate_referral_value(self, user: User) -> float:
        """计算推荐价值"""
        # 这里应该基于用户的推荐行为计算
        # 简化实现
        return 30.0
    
    def determine_value_tier(self, total_value: float) -> str:
        """确定价值等级"""
        if total_value >= 80:
            return 'diamond'
        elif total_value >= 60:
            return 'platinum'
        elif total_value >= 40:
            return 'gold'
        elif total_value >= 20:
            return 'silver'
        else:
            return 'bronze'
    
    def predict_lifetime_value(self, user: User) -> float:
        """预测生命周期价值"""
        # 简化的LTV预测
        current_value = self.calculate_monetary_value(user)
        engagement_multiplier = self.calculate_engagement_value(user) / 100
        
        # 假设用户平均生命周期为2年
        predicted_ltv = current_value * 24 * (1 + engagement_multiplier)
        
        return predicted_ltv
    
    def calculate_churn_risk(self, user: User) -> float:
        """计算流失风险"""
        days_since_last_login = (timezone.now() - user.last_login).days if user.last_login else 999
        behavior_features = self.calculate_user_behavior_features(user)
        activity_score = behavior_features['activity_score']
        
        # 基于最后登录时间和活跃度计算流失风险
        login_risk = min(100, days_since_last_login * 3)
        activity_risk = max(0, 100 - activity_score * 20)
        
        churn_risk = (login_risk + activity_risk) / 2
        return min(100, churn_risk)
