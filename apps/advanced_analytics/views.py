"""
高级分析模块视图
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework import status
from django.utils import timezone
from django.db.models import Count, Avg

from .models import (
    UserSegment, UserValueScore, BehaviorPrediction, 
    CohortAnalysis, FunnelAnalysis, ABTestExperiment
)
from .analytics_engine import AdvancedAnalyticsEngine


class UserSegmentAPIView(APIView):
    """用户分群API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取用户分群"""
        try:
            segments = UserSegment.objects.filter(is_active=True).order_by('-created_at')
            
            segment_data = []
            for segment in segments:
                segment_data.append({
                    'id': segment.id,
                    'name': segment.name,
                    'description': segment.description,
                    'segment_type': segment.segment_type,
                    'user_count': segment.user_count,
                    'last_calculated_at': segment.last_calculated_at.isoformat() if segment.last_calculated_at else None,
                    'is_dynamic': segment.is_dynamic,
                    'created_at': segment.created_at.isoformat()
                })
            
            return Response({
                'code': 200,
                'message': '获取用户分群成功',
                'data': {
                    'segments': segment_data,
                    'total': len(segment_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户分群失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserValueAPIView(APIView):
    """用户价值API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户价值评分"""
        try:
            user = request.user
            
            # 获取用户价值评分
            try:
                value_score = UserValueScore.objects.get(user=user)
                
                return Response({
                    'code': 200,
                    'message': '获取用户价值评分成功',
                    'data': {
                        'monetary_value': value_score.monetary_value,
                        'engagement_value': value_score.engagement_value,
                        'social_value': value_score.social_value,
                        'content_value': value_score.content_value,
                        'referral_value': value_score.referral_value,
                        'total_value_score': value_score.total_value_score,
                        'value_tier': value_score.value_tier,
                        'predicted_ltv': value_score.predicted_ltv,
                        'churn_risk_score': value_score.churn_risk_score,
                        'calculated_at': value_score.calculated_at.isoformat()
                    }
                })
                
            except UserValueScore.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': '用户价值评分不存在',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户价值评分失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BehaviorPredictionAPIView(APIView):
    """行为预测API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户行为预测"""
        try:
            user = request.user
            prediction_type = request.GET.get('type')
            
            # 构建查询
            queryset = BehaviorPrediction.objects.filter(user=user)
            
            if prediction_type:
                queryset = queryset.filter(prediction_type=prediction_type)
            
            # 获取预测列表
            predictions = queryset.order_by('-created_at')[:10]
            
            prediction_data = []
            for prediction in predictions:
                prediction_data.append({
                    'id': prediction.id,
                    'prediction_type': prediction.prediction_type,
                    'prediction_score': prediction.prediction_score,
                    'confidence_level': prediction.confidence_level,
                    'prediction_horizon': prediction.prediction_horizon,
                    'predicted_date': prediction.predicted_date.isoformat() if prediction.predicted_date else None,
                    'model_name': prediction.model_name,
                    'model_version': prediction.model_version,
                    'created_at': prediction.created_at.isoformat()
                })
            
            return Response({
                'code': 200,
                'message': '获取行为预测成功',
                'data': {
                    'predictions': prediction_data,
                    'total': len(prediction_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取行为预测失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CohortAnalysisAPIView(APIView):
    """队列分析API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取队列分析"""
        try:
            cohort_period = request.GET.get('period', 'monthly')
            limit = int(request.GET.get('limit', 12))
            
            # 获取队列分析数据
            cohorts = CohortAnalysis.objects.filter(
                cohort_period=cohort_period
            ).order_by('-cohort_date')[:limit]
            
            cohort_data = []
            for cohort in cohorts:
                cohort_data.append({
                    'cohort_name': cohort.cohort_name,
                    'cohort_period': cohort.cohort_period,
                    'cohort_date': cohort.cohort_date.isoformat(),
                    'cohort_size': cohort.cohort_size,
                    'retention_data': cohort.retention_data,
                    'revenue_data': cohort.revenue_data,
                    'engagement_data': cohort.engagement_data,
                    'calculated_at': cohort.calculated_at.isoformat()
                })
            
            return Response({
                'code': 200,
                'message': '获取队列分析成功',
                'data': {
                    'cohorts': cohort_data,
                    'total': len(cohort_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取队列分析失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FunnelAnalysisAPIView(APIView):
    """漏斗分析API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取漏斗分析"""
        try:
            funnels = FunnelAnalysis.objects.order_by('-created_at')[:10]
            
            funnel_data = []
            for funnel in funnels:
                funnel_data.append({
                    'id': funnel.id,
                    'funnel_name': funnel.funnel_name,
                    'description': funnel.description,
                    'steps': funnel.steps,
                    'conversion_data': funnel.conversion_data,
                    'drop_off_analysis': funnel.drop_off_analysis,
                    'date_range_start': funnel.date_range_start.isoformat(),
                    'date_range_end': funnel.date_range_end.isoformat(),
                    'created_at': funnel.created_at.isoformat()
                })
            
            return Response({
                'code': 200,
                'message': '获取漏斗分析成功',
                'data': {
                    'funnels': funnel_data,
                    'total': len(funnel_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取漏斗分析失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ABTestAPIView(APIView):
    """A/B测试API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取A/B测试实验"""
        try:
            experiments = ABTestExperiment.objects.order_by('-created_at')[:20]
            
            experiment_data = []
            for experiment in experiments:
                experiment_data.append({
                    'id': experiment.id,
                    'name': experiment.name,
                    'description': experiment.description,
                    'hypothesis': experiment.hypothesis,
                    'status': experiment.status,
                    'primary_metric': experiment.primary_metric,
                    'secondary_metrics': experiment.secondary_metrics,
                    'start_date': experiment.start_date.isoformat() if experiment.start_date else None,
                    'end_date': experiment.end_date.isoformat() if experiment.end_date else None,
                    'target_sample_size': experiment.target_sample_size,
                    'current_sample_size': experiment.current_sample_size,
                    'statistical_significance': experiment.statistical_significance,
                    'created_at': experiment.created_at.isoformat()
                })
            
            return Response({
                'code': 200,
                'message': '获取A/B测试实验成功',
                'data': {
                    'experiments': experiment_data,
                    'total': len(experiment_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取A/B测试实验失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BusinessIntelligenceAPIView(APIView):
    """商业智能API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取商业智能报告"""
        try:
            # 初始化分析引擎
            analytics_engine = AdvancedAnalyticsEngine()
            
            # 获取关键指标
            key_metrics = {
                'total_users': UserValueScore.objects.count(),
                'avg_user_value': UserValueScore.objects.aggregate(
                    avg_value=Avg('total_value_score')
                )['avg_value'] or 0,
                'high_value_users': UserValueScore.objects.filter(
                    value_tier__in=['diamond', 'platinum']
                ).count(),
                'active_segments': UserSegment.objects.filter(is_active=True).count(),
                'running_experiments': ABTestExperiment.objects.filter(
                    status='running'
                ).count()
            }
            
            return Response({
                'code': 200,
                'message': '获取商业智能报告成功',
                'data': {
                    'key_metrics': key_metrics,
                    'generated_at': timezone.now().isoformat()
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取商业智能报告失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
