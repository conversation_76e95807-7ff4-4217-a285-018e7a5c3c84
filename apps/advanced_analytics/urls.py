"""
高级分析模块URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# API URL模式
urlpatterns = [
    # 用户分群
    path('analytics/segments/', views.UserSegmentAPIView.as_view(), name='user_segments'),
    
    # 用户价值评分
    path('analytics/user-values/', views.UserValueAPIView.as_view(), name='user_values'),
    
    # 行为预测
    path('analytics/predictions/', views.BehaviorPredictionAPIView.as_view(), name='behavior_predictions'),
    
    # 队列分析
    path('analytics/cohorts/', views.CohortAnalysisAPIView.as_view(), name='cohort_analysis'),
    
    # 漏斗分析
    path('analytics/funnels/', views.FunnelAnalysisAPIView.as_view(), name='funnel_analysis'),
    
    # A/B测试
    path('analytics/ab-tests/', views.ABTestAPIView.as_view(), name='ab_tests'),
    
    # 商业智能报告
    path('analytics/reports/', views.BusinessIntelligenceAPIView.as_view(), name='bi_reports'),
    
    # 包含路由器URL
    path('', include(router.urls)),
]
