"""
高级分析模块 - Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html
import json

from .models import (
    UserSegment, UserSegmentMembership, UserValueScore, BehaviorPrediction,
    UserLifecycleStage, CohortAnalysis, FunnelAnalysis, ABTestExperiment,
    ABTestParticipant, BusinessIntelligenceReport, PredictiveModel,
    DataQualityMetric
)


@admin.register(UserSegment)
class UserSegmentAdmin(admin.ModelAdmin):
    """用户分群管理"""
    list_display = ['name', 'segment_type', 'user_count', 'is_active', 'is_dynamic', 'last_calculated_at']
    list_filter = ['segment_type', 'is_active', 'is_dynamic', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['user_count', 'created_at', 'last_calculated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'segment_type')
        }),
        ('分群条件', {
            'fields': ('criteria', 'sql_query')
        }),
        ('状态信息', {
            'fields': ('is_active', 'is_dynamic', 'user_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'last_calculated_at')
        }),
    )


@admin.register(UserSegmentMembership)
class UserSegmentMembershipAdmin(admin.ModelAdmin):
    """用户分群成员管理"""
    list_display = ['user', 'segment', 'joined_at']
    list_filter = ['segment', 'joined_at']
    search_fields = ['user__username', 'segment__name']
    readonly_fields = ['joined_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'segment')


@admin.register(UserValueScore)
class UserValueScoreAdmin(admin.ModelAdmin):
    """用户价值评分管理"""
    list_display = ['user', 'total_value_score_display', 'value_tier', 'predicted_ltv_display', 'churn_risk_display', 'calculated_at']
    list_filter = ['value_tier', 'calculated_at']
    search_fields = ['user__username']
    readonly_fields = ['calculated_at']
    
    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'calculated_at')
        }),
        ('价值评分', {
            'fields': ('monetary_value', 'engagement_value', 'social_value', 'content_value', 'referral_value')
        }),
        ('综合评估', {
            'fields': ('total_value_score', 'value_tier', 'predicted_ltv', 'churn_risk_score')
        }),
    )
    
    def total_value_score_display(self, obj):
        """显示总价值评分"""
        score = obj.total_value_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color,
            score
        )
    total_value_score_display.short_description = '总价值评分'
    
    def predicted_ltv_display(self, obj):
        """显示预测LTV"""
        return f"¥{obj.predicted_ltv:.2f}" if obj.predicted_ltv else '-'
    predicted_ltv_display.short_description = '预测LTV'
    
    def churn_risk_display(self, obj):
        """显示流失风险"""
        risk = obj.churn_risk_score
        if risk >= 70:
            color = 'red'
        elif risk >= 40:
            color = 'orange'
        else:
            color = 'green'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color,
            risk
        )
    churn_risk_display.short_description = '流失风险'


@admin.register(BehaviorPrediction)
class BehaviorPredictionAdmin(admin.ModelAdmin):
    """行为预测管理"""
    list_display = ['user', 'prediction_type', 'prediction_score_display', 'confidence_level', 'predicted_date', 'created_at']
    list_filter = ['prediction_type', 'confidence_level', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at']
    
    def prediction_score_display(self, obj):
        """显示预测分数"""
        score = obj.prediction_score
        if score >= 0.8:
            color = 'green'
        elif score >= 0.6:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.2f}</span>',
            color,
            score
        )
    prediction_score_display.short_description = '预测分数'


@admin.register(CohortAnalysis)
class CohortAnalysisAdmin(admin.ModelAdmin):
    """队列分析管理"""
    list_display = ['cohort_name', 'cohort_period', 'cohort_date', 'cohort_size', 'calculated_at']
    list_filter = ['cohort_period', 'cohort_date', 'calculated_at']
    search_fields = ['cohort_name']
    readonly_fields = ['calculated_at']
    
    def has_add_permission(self, request):
        """禁止手动添加队列分析"""
        return False


@admin.register(FunnelAnalysis)
class FunnelAnalysisAdmin(admin.ModelAdmin):
    """漏斗分析管理"""
    list_display = ['funnel_name', 'steps_count', 'date_range_display', 'created_at']
    list_filter = ['created_at']
    search_fields = ['funnel_name', 'description']
    readonly_fields = ['created_at']
    
    def steps_count(self, obj):
        """显示步骤数量"""
        if obj.steps:
            steps = json.loads(obj.steps) if isinstance(obj.steps, str) else obj.steps
            return len(steps)
        return 0
    steps_count.short_description = '步骤数量'
    
    def date_range_display(self, obj):
        """显示日期范围"""
        return f"{obj.date_range_start.strftime('%Y-%m-%d')} ~ {obj.date_range_end.strftime('%Y-%m-%d')}"
    date_range_display.short_description = '日期范围'


@admin.register(ABTestExperiment)
class ABTestExperimentAdmin(admin.ModelAdmin):
    """A/B测试实验管理"""
    list_display = ['name', 'status_display', 'primary_metric', 'sample_size_progress', 'statistical_significance_display', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['name', 'description', 'hypothesis']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('实验信息', {
            'fields': ('name', 'description', 'hypothesis')
        }),
        ('实验设置', {
            'fields': ('status', 'primary_metric', 'secondary_metrics')
        }),
        ('样本设置', {
            'fields': ('target_sample_size', 'current_sample_size', 'traffic_allocation')
        }),
        ('时间设置', {
            'fields': ('start_date', 'end_date', 'expected_duration_days')
        }),
        ('结果分析', {
            'fields': ('statistical_significance', 'confidence_interval', 'results_summary')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def status_display(self, obj):
        """显示状态"""
        colors = {
            'draft': 'gray',
            'running': 'green',
            'paused': 'orange',
            'completed': 'blue',
            'cancelled': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def sample_size_progress(self, obj):
        """显示样本进度"""
        if obj.target_sample_size > 0:
            progress = (obj.current_sample_size / obj.target_sample_size) * 100
            return f"{obj.current_sample_size}/{obj.target_sample_size} ({progress:.1f}%)"
        return f"{obj.current_sample_size}/未设置"
    sample_size_progress.short_description = '样本进度'
    
    def statistical_significance_display(self, obj):
        """显示统计显著性"""
        if obj.statistical_significance:
            if obj.statistical_significance >= 0.95:
                color = 'green'
            elif obj.statistical_significance >= 0.90:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.2f}</span>',
                color,
                obj.statistical_significance
            )
        return '-'
    statistical_significance_display.short_description = '统计显著性'


@admin.register(ABTestParticipant)
class ABTestParticipantAdmin(admin.ModelAdmin):
    """A/B测试参与者管理"""
    list_display = ['user', 'experiment', 'group_name', 'assigned_at', 'has_converted']
    list_filter = ['experiment', 'group_type', 'has_converted', 'assigned_at']
    search_fields = ['user__username', 'experiment__name']
    readonly_fields = ['assigned_at', 'converted_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'experiment')


@admin.register(BusinessIntelligenceReport)
class BusinessIntelligenceReportAdmin(admin.ModelAdmin):
    """商业智能报告管理"""
    list_display = ['name', 'report_type', 'generated_at']
    list_filter = ['report_type', 'generated_at']
    search_fields = ['name', 'description']
    readonly_fields = ['generated_at']
    
    def has_change_permission(self, request, obj=None):
        """禁止修改已生成的报告"""
        return False


@admin.register(PredictiveModel)
class PredictiveModelAdmin(admin.ModelAdmin):
    """预测模型管理"""
    list_display = ['name', 'model_type', 'accuracy_display', 'last_trained_at']
    list_filter = ['model_type', 'last_trained_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'last_trained_at']
    
    def accuracy_display(self, obj):
        """显示准确率"""
        if obj.accuracy:
            if obj.accuracy >= 0.9:
                color = 'green'
            elif obj.accuracy >= 0.8:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{:.2f}%</span>',
                color,
                obj.accuracy * 100
            )
        return '-'
    accuracy_display.short_description = '准确率'


@admin.register(DataQualityMetric)
class DataQualityMetricAdmin(admin.ModelAdmin):
    """数据质量指标管理"""
    list_display = ['metric_type', 'data_source', 'quality_score_display', 'checked_at']
    list_filter = ['data_source', 'metric_type', 'checked_at']
    search_fields = ['metric_type', 'data_source']
    readonly_fields = ['checked_at']
    
    def quality_score_display(self, obj):
        """显示质量分数"""
        score = obj.quality_score
        if score >= 0.9:
            color = 'green'
        elif score >= 0.7:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.2f}</span>',
            color,
            score
        )
    quality_score_display.short_description = '质量分数'
    
    def has_add_permission(self, request):
        """禁止手动添加数据质量指标"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改数据质量指标"""
        return False
