"""
国际化模块 - Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html
from django.forms import Textarea

from .models import (
    Language, TranslationKey, Translation, TranslationCategory,
    UserLanguagePreference, TranslationMemory, TranslationStatistics,
    LocalizationConfig, AutoTranslation
)


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    """语言管理"""
    list_display = ['name', 'code', 'native_name', 'is_active', 'is_default']
    list_filter = ['is_active', 'is_default', 'is_rtl']
    search_fields = ['name', 'code', 'native_name']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'native_name', 'flag_emoji')
        }),
        ('状态设置', {
            'fields': ('is_active', 'is_default', 'is_rtl')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )


@admin.register(TranslationCategory)
class TranslationCategoryAdmin(admin.ModelAdmin):
    """翻译分类管理"""
    list_display = ['name', 'description']
    search_fields = ['name', 'description']
    
    def translation_count(self, obj):
        """显示翻译数量"""
        return obj.translation_keys.count()
    translation_count.short_description = '翻译数量'


@admin.register(TranslationKey)
class TranslationKeyAdmin(admin.ModelAdmin):
    """翻译键管理"""
    list_display = ['key', 'category', 'description', 'is_active', 'translation_count']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['key', 'description', 'default_value']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('key', 'category', 'description')
        }),
        ('默认值', {
            'fields': ('default_value',)
        }),
        ('状态信息', {
            'fields': ('is_active', 'requires_review')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def translation_count(self, obj):
        """显示翻译数量"""
        return obj.translations.count()
    translation_count.short_description = '翻译数量'


class TranslationInline(admin.TabularInline):
    """翻译内联编辑"""
    model = Translation
    extra = 0
    fields = ['language', 'value', 'status']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Translation)
class TranslationAdmin(admin.ModelAdmin):
    """翻译管理"""
    list_display = ['translation_key', 'language', 'status_display', 'translator', 'updated_at']
    list_filter = ['language', 'status', 'updated_at']
    search_fields = ['translation_key__key', 'value']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('翻译信息', {
            'fields': ('translation_key', 'language', 'value')
        }),
        ('状态信息', {
            'fields': ('status', 'translator', 'reviewer')
        }),
        ('质量信息', {
            'fields': ('quality_score',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    formfield_overrides = {
        Translation._meta.get_field('value'): {'widget': Textarea(attrs={'rows': 4, 'cols': 80})},
    }
    
    def status_display(self, obj):
        """显示状态"""
        colors = {
            'draft': 'gray',
            'pending': 'orange',
            'approved': 'green',
            'rejected': 'red'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(UserLanguagePreference)
class UserLanguagePreferenceAdmin(admin.ModelAdmin):
    """用户语言偏好管理"""
    list_display = ['user', 'primary_language', 'auto_translate', 'updated_at']
    list_filter = ['primary_language', 'auto_translate', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'primary_language')


@admin.register(TranslationMemory)
class TranslationMemoryAdmin(admin.ModelAdmin):
    """翻译记忆库管理"""
    list_display = ['source_text_short', 'target_text_short', 'source_language', 'target_language', 'similarity_score', 'usage_count']
    list_filter = ['source_language', 'target_language', 'created_at']
    search_fields = ['source_text', 'target_text']
    readonly_fields = ['created_at', 'last_used_at']
    
    def source_text_short(self, obj):
        """显示源文本（简短）"""
        return obj.source_text[:50] + '...' if len(obj.source_text) > 50 else obj.source_text
    source_text_short.short_description = '源文本'
    
    def target_text_short(self, obj):
        """显示目标文本（简短）"""
        return obj.target_text[:50] + '...' if len(obj.target_text) > 50 else obj.target_text
    target_text_short.short_description = '目标文本'


@admin.register(TranslationStatistics)
class TranslationStatisticsAdmin(admin.ModelAdmin):
    """翻译统计管理"""
    list_display = ['language', 'total_keys', 'translated_keys', 'completion_rate_display', 'date']
    list_filter = ['language', 'date']
    readonly_fields = ['date']
    
    def completion_rate_display(self, obj):
        """显示完成率"""
        rate = obj.completion_rate
        if rate >= 90:
            color = 'green'
        elif rate >= 70:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color,
            rate
        )
    completion_rate_display.short_description = '完成率'
    
    def has_add_permission(self, request):
        """禁止手动添加统计数据"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改统计数据"""
        return False


@admin.register(LocalizationConfig)
class LocalizationConfigAdmin(admin.ModelAdmin):
    """本地化配置管理"""
    list_display = ['language', 'date_format', 'currency_symbol', 'updated_at']
    list_filter = ['language', 'updated_at']
    search_fields = ['language__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(AutoTranslation)
class AutoTranslationAdmin(admin.ModelAdmin):
    """自动翻译管理"""
    list_display = ['source_text_short', 'translated_text_short', 'source_language', 'target_language', 'engine', 'confidence_score', 'created_at']
    list_filter = ['source_language', 'target_language', 'engine', 'created_at']
    search_fields = ['source_text', 'translated_text']
    readonly_fields = ['created_at']

    def source_text_short(self, obj):
        """显示源文本（简短）"""
        return obj.source_text[:30] + '...' if len(obj.source_text) > 30 else obj.source_text
    source_text_short.short_description = '源文本'

    def translated_text_short(self, obj):
        """显示翻译文本（简短）"""
        return obj.translated_text[:30] + '...' if len(obj.translated_text) > 30 else obj.translated_text
    translated_text_short.short_description = '翻译文本'
    
    def has_change_permission(self, request, obj=None):
        """禁止修改自动翻译记录"""
        return False
