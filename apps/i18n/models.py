"""
国际化多语言支持模型
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import json

User = get_user_model()


class Language(models.Model):
    """支持的语言"""
    code = models.CharField(max_length=10, unique=True, verbose_name='语言代码')  # zh-cn, en-us, ja-jp
    name = models.CharField(max_length=100, verbose_name='语言名称')
    native_name = models.CharField(max_length=100, verbose_name='本地名称')
    
    # 语言属性
    is_rtl = models.BooleanField(default=False, verbose_name='是否从右到左')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_default = models.BooleanField(default=False, verbose_name='是否默认语言')
    
    # 显示顺序
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    
    # 语言图标
    flag_icon = models.Char<PERSON>ield(max_length=50, blank=True, verbose_name='国旗图标')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '语言'
        verbose_name_plural = '语言'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def save(self, *args, **kwargs):
        # 确保只有一个默认语言
        if self.is_default:
            Language.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class TranslationCategory(models.Model):
    """翻译分类"""
    name = models.CharField(max_length=100, unique=True, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    
    # 分类属性
    is_system = models.BooleanField(default=False, verbose_name='是否系统分类')
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        verbose_name='父分类'
    )
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '翻译分类'
        verbose_name_plural = '翻译分类'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class TranslationKey(models.Model):
    """翻译键"""
    key = models.CharField(max_length=255, unique=True, verbose_name='翻译键')
    category = models.ForeignKey(
        TranslationCategory, 
        on_delete=models.CASCADE,
        verbose_name='分类'
    )
    
    # 键属性
    description = models.TextField(blank=True, verbose_name='键描述')
    context = models.CharField(max_length=255, blank=True, verbose_name='上下文')
    
    # 默认值
    default_value = models.TextField(verbose_name='默认值')
    
    # 键类型
    KEY_TYPES = [
        ('text', '文本'),
        ('html', 'HTML'),
        ('plural', '复数'),
        ('format', '格式化'),
    ]
    key_type = models.CharField(max_length=20, choices=KEY_TYPES, default='text', verbose_name='键类型')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '翻译键'
        verbose_name_plural = '翻译键'
        ordering = ['category', 'key']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['category', 'is_active']),
        ]
    
    def __str__(self):
        return self.key


class Translation(models.Model):
    """翻译内容"""
    translation_key = models.ForeignKey(
        TranslationKey, 
        on_delete=models.CASCADE,
        verbose_name='翻译键'
    )
    language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        verbose_name='语言'
    )
    
    # 翻译内容
    value = models.TextField(verbose_name='翻译值')
    
    # 复数形式（用于支持复数的语言）
    plural_forms = models.JSONField(default=dict, verbose_name='复数形式')
    
    # 翻译状态
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('pending', '待审核'),
        ('approved', '已审核'),
        ('rejected', '已拒绝'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    
    # 翻译质量
    quality_score = models.FloatField(default=0, verbose_name='质量评分')
    
    # 翻译者信息
    translator = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='translations',
        verbose_name='翻译者'
    )
    reviewer = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='reviewed_translations',
        verbose_name='审核者'
    )
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name='审核时间')
    
    class Meta:
        verbose_name = '翻译'
        verbose_name_plural = '翻译'
        unique_together = ['translation_key', 'language']
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['language', 'status']),
            models.Index(fields=['translation_key', 'language']),
        ]
    
    def __str__(self):
        return f"{self.translation_key.key} - {self.language.code}"


class UserLanguagePreference(models.Model):
    """用户语言偏好"""
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE,
        verbose_name='用户'
    )
    primary_language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        related_name='primary_users',
        verbose_name='主要语言'
    )
    secondary_languages = models.ManyToManyField(
        Language,
        blank=True,
        related_name='secondary_users',
        verbose_name='次要语言'
    )
    
    # 自动检测设置
    auto_detect = models.BooleanField(default=True, verbose_name='自动检测语言')
    
    # 翻译偏好
    show_original = models.BooleanField(default=False, verbose_name='显示原文')
    auto_translate = models.BooleanField(default=True, verbose_name='自动翻译')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户语言偏好'
        verbose_name_plural = '用户语言偏好'
    
    def __str__(self):
        return f"{self.user.username} - {self.primary_language.name}"


class AutoTranslation(models.Model):
    """自动翻译记录"""
    TRANSLATION_ENGINES = [
        ('google', 'Google翻译'),
        ('baidu', '百度翻译'),
        ('tencent', '腾讯翻译'),
        ('microsoft', '微软翻译'),
        ('deepl', 'DeepL翻译'),
    ]
    
    source_text = models.TextField(verbose_name='源文本')
    source_language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        related_name='auto_translations_source',
        verbose_name='源语言'
    )
    target_language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        related_name='auto_translations_target',
        verbose_name='目标语言'
    )
    
    # 翻译结果
    translated_text = models.TextField(verbose_name='翻译文本')
    engine = models.CharField(max_length=20, choices=TRANSLATION_ENGINES, verbose_name='翻译引擎')
    
    # 质量评估
    confidence_score = models.FloatField(default=0, verbose_name='置信度')
    is_reviewed = models.BooleanField(default=False, verbose_name='是否已审核')
    human_rating = models.IntegerField(null=True, blank=True, verbose_name='人工评分')
    
    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '自动翻译'
        verbose_name_plural = '自动翻译'
        indexes = [
            models.Index(fields=['source_language', 'target_language']),
            models.Index(fields=['engine', 'confidence_score']),
        ]
    
    def __str__(self):
        return f"{self.source_language.code} -> {self.target_language.code}"


class TranslationMemory(models.Model):
    """翻译记忆库"""
    source_text = models.TextField(verbose_name='源文本')
    target_text = models.TextField(verbose_name='目标文本')
    
    source_language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        related_name='memory_source',
        verbose_name='源语言'
    )
    target_language = models.ForeignKey(
        Language, 
        on_delete=models.CASCADE,
        related_name='memory_target',
        verbose_name='目标语言'
    )
    
    # 匹配度
    similarity_score = models.FloatField(default=1.0, verbose_name='相似度')
    
    # 来源信息
    source_type = models.CharField(
        max_length=20,
        choices=[
            ('manual', '人工翻译'),
            ('auto', '自动翻译'),
            ('import', '导入'),
            ('api', 'API翻译'),
        ],
        default='manual',
        verbose_name='来源类型'
    )
    
    # 质量信息
    quality_rating = models.IntegerField(default=5, verbose_name='质量评级')
    is_verified = models.BooleanField(default=False, verbose_name='是否已验证')
    
    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    last_used_at = models.DateTimeField(null=True, blank=True, verbose_name='最后使用时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '翻译记忆'
        verbose_name_plural = '翻译记忆'
        indexes = [
            models.Index(fields=['source_language', 'target_language']),
            models.Index(fields=['similarity_score']),
        ]
    
    def __str__(self):
        return f"{self.source_text[:50]}... -> {self.target_text[:50]}..."


class LocalizationConfig(models.Model):
    """本地化配置"""
    language = models.OneToOneField(
        Language, 
        on_delete=models.CASCADE,
        verbose_name='语言'
    )
    
    # 日期时间格式
    date_format = models.CharField(max_length=50, default='Y-m-d', verbose_name='日期格式')
    time_format = models.CharField(max_length=50, default='H:i:s', verbose_name='时间格式')
    datetime_format = models.CharField(max_length=50, default='Y-m-d H:i:s', verbose_name='日期时间格式')
    
    # 数字格式
    decimal_separator = models.CharField(max_length=5, default='.', verbose_name='小数分隔符')
    thousand_separator = models.CharField(max_length=5, default=',', verbose_name='千位分隔符')
    
    # 货币格式
    currency_symbol = models.CharField(max_length=10, default='¥', verbose_name='货币符号')
    currency_position = models.CharField(
        max_length=10,
        choices=[
            ('before', '符号在前'),
            ('after', '符号在后'),
        ],
        default='before',
        verbose_name='货币符号位置'
    )
    
    # 地址格式
    address_format = models.TextField(
        default='{country} {province} {city} {district} {street}',
        verbose_name='地址格式'
    )
    
    # 姓名格式
    name_format = models.CharField(
        max_length=20,
        choices=[
            ('first_last', '名 姓'),
            ('last_first', '姓 名'),
        ],
        default='last_first',
        verbose_name='姓名格式'
    )
    
    # 其他配置
    config_data = models.JSONField(default=dict, verbose_name='其他配置')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '本地化配置'
        verbose_name_plural = '本地化配置'
    
    def __str__(self):
        return f"{self.language.name} - 本地化配置"


class TranslationStatistics(models.Model):
    """翻译统计"""
    language = models.ForeignKey(Language, on_delete=models.CASCADE, verbose_name='语言')
    date = models.DateField(verbose_name='统计日期')
    
    # 翻译数量统计
    total_keys = models.IntegerField(default=0, verbose_name='总键数')
    translated_keys = models.IntegerField(default=0, verbose_name='已翻译键数')
    pending_keys = models.IntegerField(default=0, verbose_name='待翻译键数')
    
    # 翻译质量统计
    avg_quality_score = models.FloatField(default=0, verbose_name='平均质量分')
    approved_translations = models.IntegerField(default=0, verbose_name='已审核翻译数')
    
    # 翻译完成度
    completion_rate = models.FloatField(default=0, verbose_name='完成率(%)')
    
    # 翻译活动统计
    new_translations = models.IntegerField(default=0, verbose_name='新增翻译数')
    updated_translations = models.IntegerField(default=0, verbose_name='更新翻译数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '翻译统计'
        verbose_name_plural = '翻译统计'
        unique_together = ['language', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.language.name} - {self.date} - {self.completion_rate}%"
