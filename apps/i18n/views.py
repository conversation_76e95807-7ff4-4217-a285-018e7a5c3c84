"""
国际化模块视图
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils import timezone

from .models import Language, Translation, UserLanguagePreference, AutoTranslation
from .translation_service import translation_service


class TranslationAPIView(APIView):
    """翻译API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取翻译"""
        try:
            key = request.GET.get('key')
            language_code = request.GET.get('language', 'zh-cn')
            
            if not key:
                return Response({
                    'code': 400,
                    'message': '翻译键不能为空',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取翻译
            translated_text = translation_service.get_translation(key, language_code)
            
            return Response({
                'code': 200,
                'message': '获取翻译成功',
                'data': {
                    'key': key,
                    'language': language_code,
                    'translation': translated_text
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取翻译失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LanguageAPIView(APIView):
    """语言API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取支持的语言列表"""
        try:
            languages = translation_service.get_available_languages()
            
            return Response({
                'code': 200,
                'message': '获取语言列表成功',
                'data': {
                    'languages': languages,
                    'total': len(languages)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取语言列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserLanguagePreferenceAPIView(APIView):
    """用户语言偏好API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户语言偏好"""
        try:
            user = request.user
            user_language = translation_service.get_user_language(user)
            
            return Response({
                'code': 200,
                'message': '获取用户语言偏好成功',
                'data': {
                    'language_code': user_language.code,
                    'language_name': user_language.name,
                    'native_name': user_language.native_name
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户语言偏好失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """设置用户语言偏好"""
        try:
            user = request.user
            language_code = request.data.get('language_code')
            
            if not language_code:
                return Response({
                    'code': 400,
                    'message': '语言代码不能为空',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 设置用户语言
            translation_service.set_user_language(user, language_code)
            
            return Response({
                'code': 200,
                'message': '设置用户语言偏好成功',
                'data': None
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'设置用户语言偏好失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AutoTranslationAPIView(APIView):
    """自动翻译API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """执行自动翻译"""
        try:
            text = request.data.get('text')
            source_lang = request.data.get('source_lang', 'zh-cn')
            target_lang = request.data.get('target_lang', 'en-us')
            
            if not text:
                return Response({
                    'code': 400,
                    'message': '翻译文本不能为空',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 执行自动翻译
            translated_text = translation_service.perform_auto_translation(
                text, source_lang, target_lang
            )
            
            if translated_text:
                return Response({
                    'code': 200,
                    'message': '自动翻译成功',
                    'data': {
                        'original_text': text,
                        'translated_text': translated_text,
                        'source_lang': source_lang,
                        'target_lang': target_lang
                    }
                })
            else:
                return Response({
                    'code': 500,
                    'message': '自动翻译失败',
                    'data': None
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'自动翻译失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
