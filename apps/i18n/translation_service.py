"""
翻译服务
提供多语言翻译、自动翻译、翻译记忆等功能
"""

import re
import json
import hashlib
import logging
from typing import Dict, List, Optional, Tuple
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.db.models import Q

from .models import (
    Language, TranslationKey, Translation, AutoTranslation,
    TranslationMemory, UserLanguagePreference, LocalizationConfig
)

logger = logging.getLogger(__name__)


class TranslationService:
    """翻译服务主类"""
    
    def __init__(self):
        self.cache_prefix = 'translation'
        self.cache_timeout = 3600 * 24  # 24小时缓存
        self.auto_translators = {
            'google': GoogleTranslator(),
            'baidu': BaiduTranslator(),
            'tencent': TencentTranslator(),
        }
        self.default_language = self.get_default_language()
    
    def get_default_language(self) -> Language:
        """获取默认语言"""
        try:
            return Language.objects.get(is_default=True)
        except Language.DoesNotExist:
            # 如果没有设置默认语言，返回中文
            return Language.objects.filter(code='zh-cn').first()
    
    def get_translation(self, key: str, language_code: str = None, **kwargs) -> str:
        """获取翻译"""
        if not language_code:
            language_code = self.default_language.code
        
        # 尝试从缓存获取
        cache_key = f"{self.cache_prefix}:{key}:{language_code}"
        cached_translation = cache.get(cache_key)
        if cached_translation:
            return self.format_translation(cached_translation, **kwargs)
        
        try:
            # 从数据库获取翻译
            translation_key = TranslationKey.objects.get(key=key, is_active=True)
            language = Language.objects.get(code=language_code, is_active=True)
            
            translation = Translation.objects.filter(
                translation_key=translation_key,
                language=language,
                status='approved'
            ).first()
            
            if translation:
                result = translation.value
            else:
                # 如果没有找到翻译，尝试自动翻译
                result = self.get_auto_translation(translation_key, language)
                if not result:
                    # 返回默认值
                    result = translation_key.default_value
            
            # 缓存结果
            cache.set(cache_key, result, self.cache_timeout)
            
            return self.format_translation(result, **kwargs)
            
        except (TranslationKey.DoesNotExist, Language.DoesNotExist):
            logger.warning(f"翻译键或语言不存在: {key}, {language_code}")
            return key  # 返回键本身作为后备
    
    def format_translation(self, text: str, **kwargs) -> str:
        """格式化翻译文本"""
        if not kwargs:
            return text
        
        try:
            # 支持Python字符串格式化
            return text.format(**kwargs)
        except (KeyError, ValueError) as e:
            logger.warning(f"翻译格式化失败: {text}, {kwargs}, {str(e)}")
            return text
    
    def get_auto_translation(self, translation_key: TranslationKey, target_language: Language) -> Optional[str]:
        """获取自动翻译"""
        source_text = translation_key.default_value
        source_language = self.default_language
        
        # 检查是否已有自动翻译
        auto_translation = AutoTranslation.objects.filter(
            source_text=source_text,
            source_language=source_language,
            target_language=target_language
        ).first()
        
        if auto_translation:
            auto_translation.usage_count += 1
            auto_translation.save()
            return auto_translation.translated_text
        
        # 尝试从翻译记忆库获取
        memory_translation = self.get_from_translation_memory(
            source_text, source_language, target_language
        )
        if memory_translation:
            return memory_translation
        
        # 执行自动翻译
        translated_text = self.perform_auto_translation(
            source_text, source_language.code, target_language.code
        )
        
        if translated_text:
            # 保存自动翻译结果
            AutoTranslation.objects.create(
                source_text=source_text,
                source_language=source_language,
                target_language=target_language,
                translated_text=translated_text,
                engine='google',  # 默认使用Google翻译
                confidence_score=0.8
            )
            
            # 添加到翻译记忆库
            self.add_to_translation_memory(
                source_text, translated_text,
                source_language, target_language,
                source_type='auto'
            )
        
        return translated_text
    
    def get_from_translation_memory(self, source_text: str, source_language: Language, 
                                   target_language: Language, min_similarity: float = 0.8) -> Optional[str]:
        """从翻译记忆库获取翻译"""
        # 精确匹配
        exact_match = TranslationMemory.objects.filter(
            source_text=source_text,
            source_language=source_language,
            target_language=target_language
        ).first()
        
        if exact_match:
            exact_match.usage_count += 1
            exact_match.last_used_at = timezone.now()
            exact_match.save()
            return exact_match.target_text
        
        # 模糊匹配（简化实现）
        similar_memories = TranslationMemory.objects.filter(
            source_language=source_language,
            target_language=target_language,
            similarity_score__gte=min_similarity
        )
        
        for memory in similar_memories:
            similarity = self.calculate_text_similarity(source_text, memory.source_text)
            if similarity >= min_similarity:
                return memory.target_text
        
        return None
    
    def add_to_translation_memory(self, source_text: str, target_text: str,
                                 source_language: Language, target_language: Language,
                                 source_type: str = 'manual', quality_rating: int = 5):
        """添加到翻译记忆库"""
        TranslationMemory.objects.get_or_create(
            source_text=source_text,
            target_text=target_text,
            source_language=source_language,
            target_language=target_language,
            defaults={
                'source_type': source_type,
                'quality_rating': quality_rating,
                'similarity_score': 1.0
            }
        )
    
    def perform_auto_translation(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """执行自动翻译"""
        # 尝试不同的翻译引擎
        for engine_name, translator in self.auto_translators.items():
            try:
                result = translator.translate(text, source_lang, target_lang)
                if result:
                    return result
            except Exception as e:
                logger.error(f"翻译引擎 {engine_name} 失败: {str(e)}")
                continue
        
        return None
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化实现）"""
        if text1 == text2:
            return 1.0
        
        # 使用Jaccard相似度
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def get_user_language(self, user) -> Language:
        """获取用户语言偏好"""
        if not user or not user.is_authenticated:
            return self.default_language
        
        try:
            preference = UserLanguagePreference.objects.get(user=user)
            return preference.primary_language
        except UserLanguagePreference.DoesNotExist:
            return self.default_language
    
    def set_user_language(self, user, language_code: str):
        """设置用户语言偏好"""
        try:
            language = Language.objects.get(code=language_code, is_active=True)
            preference, created = UserLanguagePreference.objects.get_or_create(
                user=user,
                defaults={'primary_language': language}
            )
            if not created:
                preference.primary_language = language
                preference.save()
        except Language.DoesNotExist:
            logger.warning(f"语言不存在: {language_code}")
    
    def get_available_languages(self) -> List[Dict]:
        """获取可用语言列表"""
        cache_key = f"{self.cache_prefix}:available_languages"
        cached_languages = cache.get(cache_key)
        if cached_languages:
            return cached_languages
        
        languages = Language.objects.filter(is_active=True).order_by('sort_order', 'name')
        result = []
        
        for lang in languages:
            result.append({
                'code': lang.code,
                'name': lang.name,
                'native_name': lang.native_name,
                'is_rtl': lang.is_rtl,
                'is_default': lang.is_default,
                'flag_icon': lang.flag_icon
            })
        
        cache.set(cache_key, result, self.cache_timeout)
        return result
    
    def get_localization_config(self, language_code: str) -> Dict:
        """获取本地化配置"""
        cache_key = f"{self.cache_prefix}:localization:{language_code}"
        cached_config = cache.get(cache_key)
        if cached_config:
            return cached_config
        
        try:
            language = Language.objects.get(code=language_code)
            config = LocalizationConfig.objects.get(language=language)
            
            result = {
                'date_format': config.date_format,
                'time_format': config.time_format,
                'datetime_format': config.datetime_format,
                'decimal_separator': config.decimal_separator,
                'thousand_separator': config.thousand_separator,
                'currency_symbol': config.currency_symbol,
                'currency_position': config.currency_position,
                'address_format': config.address_format,
                'name_format': config.name_format,
                'config_data': config.config_data
            }
            
            cache.set(cache_key, result, self.cache_timeout)
            return result
            
        except (Language.DoesNotExist, LocalizationConfig.DoesNotExist):
            # 返回默认配置
            return {
                'date_format': 'Y-m-d',
                'time_format': 'H:i:s',
                'datetime_format': 'Y-m-d H:i:s',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_symbol': '¥',
                'currency_position': 'before',
                'address_format': '{country} {province} {city} {district} {street}',
                'name_format': 'last_first',
                'config_data': {}
            }
    
    def clear_translation_cache(self, key: str = None, language_code: str = None):
        """清除翻译缓存"""
        if key and language_code:
            cache_key = f"{self.cache_prefix}:{key}:{language_code}"
            cache.delete(cache_key)
        else:
            # 清除所有翻译缓存
            cache.delete_many([
                f"{self.cache_prefix}:*",
                f"{self.cache_prefix}:available_languages",
                f"{self.cache_prefix}:localization:*"
            ])


class BaseTranslator:
    """翻译器基类"""
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """翻译文本"""
        raise NotImplementedError
    
    def detect_language(self, text: str) -> Optional[str]:
        """检测语言"""
        raise NotImplementedError


class GoogleTranslator(BaseTranslator):
    """Google翻译器"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'GOOGLE_TRANSLATE_API_KEY', None)
        self.base_url = 'https://translation.googleapis.com/language/translate/v2'
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """使用Google翻译API"""
        if not self.api_key:
            logger.warning("Google翻译API密钥未配置")
            return None
        
        try:
            import requests
            
            params = {
                'key': self.api_key,
                'q': text,
                'source': source_lang,
                'target': target_lang,
                'format': 'text'
            }
            
            response = requests.post(self.base_url, data=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if 'data' in result and 'translations' in result['data']:
                return result['data']['translations'][0]['translatedText']
            
        except Exception as e:
            logger.error(f"Google翻译失败: {str(e)}")
        
        return None


class BaiduTranslator(BaseTranslator):
    """百度翻译器"""
    
    def __init__(self):
        self.app_id = getattr(settings, 'BAIDU_TRANSLATE_APP_ID', None)
        self.secret_key = getattr(settings, 'BAIDU_TRANSLATE_SECRET_KEY', None)
        self.base_url = 'https://fanyi-api.baidu.com/api/trans/vip/translate'
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """使用百度翻译API"""
        if not self.app_id or not self.secret_key:
            logger.warning("百度翻译API配置未完整")
            return None
        
        try:
            import requests
            import random
            import time
            
            # 生成签名
            salt = str(random.randint(32768, 65536))
            sign_str = f"{self.app_id}{text}{salt}{self.secret_key}"
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            
            params = {
                'q': text,
                'from': self.convert_language_code(source_lang, 'baidu'),
                'to': self.convert_language_code(target_lang, 'baidu'),
                'appid': self.app_id,
                'salt': salt,
                'sign': sign
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if 'trans_result' in result:
                return result['trans_result'][0]['dst']
            
        except Exception as e:
            logger.error(f"百度翻译失败: {str(e)}")
        
        return None
    
    def convert_language_code(self, code: str, platform: str) -> str:
        """转换语言代码"""
        # 简化的语言代码转换
        mapping = {
            'baidu': {
                'zh-cn': 'zh',
                'en-us': 'en',
                'ja-jp': 'jp',
                'ko-kr': 'kor',
                'fr-fr': 'fra',
                'de-de': 'de',
                'es-es': 'spa',
                'ru-ru': 'ru'
            }
        }
        
        return mapping.get(platform, {}).get(code, code.split('-')[0])


class TencentTranslator(BaseTranslator):
    """腾讯翻译器"""
    
    def __init__(self):
        self.secret_id = getattr(settings, 'TENCENT_TRANSLATE_SECRET_ID', None)
        self.secret_key = getattr(settings, 'TENCENT_TRANSLATE_SECRET_KEY', None)
        self.region = getattr(settings, 'TENCENT_TRANSLATE_REGION', 'ap-beijing')
    
    def translate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """使用腾讯翻译API"""
        if not self.secret_id or not self.secret_key:
            logger.warning("腾讯翻译API配置未完整")
            return None
        
        try:
            # 这里应该实现腾讯云翻译API调用
            # 由于需要复杂的签名算法，这里只是示例
            logger.info("腾讯翻译功能需要完整的SDK支持")
            return None
            
        except Exception as e:
            logger.error(f"腾讯翻译失败: {str(e)}")
        
        return None


# 全局翻译服务实例
translation_service = TranslationService()


def _(key: str, language_code: str = None, **kwargs) -> str:
    """翻译函数的简化接口"""
    return translation_service.get_translation(key, language_code, **kwargs)


def get_user_language(user) -> str:
    """获取用户语言代码"""
    language = translation_service.get_user_language(user)
    return language.code if language else 'zh-cn'


def set_user_language(user, language_code: str):
    """设置用户语言"""
    translation_service.set_user_language(user, language_code)
