"""
国际化模块URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# API URL模式
urlpatterns = [
    # 翻译管理
    path('i18n/translations/', views.TranslationAPIView.as_view(), name='translations'),
    
    # 语言管理
    path('i18n/languages/', views.LanguageAPIView.as_view(), name='languages'),
    
    # 用户语言偏好
    path('i18n/user-preferences/', views.UserLanguagePreferenceAPIView.as_view(), name='user_language_preferences'),
    
    # 自动翻译
    path('i18n/auto-translate/', views.AutoTranslationAPIView.as_view(), name='auto_translation'),
    
    # 包含路由器URL
    path('', include(router.urls)),
]
