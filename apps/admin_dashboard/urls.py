"""
管理员后台URL配置
"""

from django.urls import path
from . import views

app_name = 'admin_dashboard'

urlpatterns = [
    # 管理员仪表板主页
    path('', views.AdminDashboardView.as_view(), name='dashboard'),

    # 高级功能管理页面
    path('advanced-features/', views.AdvancedFeaturesView.as_view(), name='advanced_features'),

    # 原有API接口
    path('api/stats/', views.AdminStatsAPIView.as_view(), name='admin_stats'),
    path('api/users/', views.UserManagementAPIView.as_view(), name='user_management'),
    path('api/content/', views.ContentModerationAPIView.as_view(), name='content_moderation'),
    path('api/config/', views.SystemConfigAPIView.as_view(), name='system_config'),
    path('api/reports/', views.ReportsAPIView.as_view(), name='reports'),

    # 新增高级功能API接口
    path('api/ai-matching/', views.AIMatchingManagementAPIView.as_view(), name='ai_matching_api'),
    # path('api/realtime-monitor/', views.RealtimeMonitorAPIView.as_view(), name='realtime_monitor_api'),  # 暂时注释，等待实现
    path('api/security-monitor/', views.SecurityMonitorAPIView.as_view(), name='security_monitor_api'),
]
