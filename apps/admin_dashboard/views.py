"""
管理员后台运营管理视图
提供综合性的运营数据分析和管理功能
"""

from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.db.models import Count, Q, Avg, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser

from apps.users.models import User, UserStatistics, UserActivityLog
from apps.matching.models import Like, Match, UserRecommendation
from apps.chat.models import ChatMessage, ChatSession
from apps.moments.models import Moment, UserStory
from apps.payment.models import UserVipRecord, PaymentRecord

# 新增高级功能模块导入
# 暂时注释高级功能模块的导入，等待模块启用
# from apps.ai_matching.models import UserProfile, MatchPrediction, UserSimilarity
# from apps.realtime.models import WebSocketConnection, VoiceCall, VideoCall, UserOnlineStatus
# from apps.security.models import SecurityEvent, IPBlacklist, UserSecurityProfile
# from apps.i18n.models import Language, Translation, UserLanguagePreference
# from apps.advanced_analytics.models import UserSegment, UserValueScore, BehaviorPrediction


@method_decorator(staff_member_required, name='dispatch')
class AdminDashboardView(TemplateView):
    """管理员仪表板主页"""
    template_name = 'admin_dashboard/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取基础统计数据
        context.update({
            'total_users': User.objects.filter(is_active=True).count(),
            'today_new_users': self.get_today_new_users(),
            'total_matches': Match.objects.count(),
            'today_matches': self.get_today_matches(),
            'total_messages': ChatMessage.objects.count(),
            'today_messages': self.get_today_messages(),
            'vip_users': UserVipRecord.objects.filter(is_active=True).count(),
            'total_revenue': self.get_total_revenue(),
        })
        
        return context
    
    def get_today_new_users(self):
        """获取今日新增用户数"""
        today = timezone.now().date()
        return User.objects.filter(date_joined__date=today).count()
    
    def get_today_matches(self):
        """获取今日匹配数"""
        today = timezone.now().date()
        return Match.objects.filter(created_at__date=today).count()
    
    def get_today_messages(self):
        """获取今日消息数"""
        today = timezone.now().date()
        return ChatMessage.objects.filter(created_at__date=today).count()
    
    def get_total_revenue(self):
        """获取总收入"""
        return PaymentRecord.objects.filter(
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or 0


class AdminStatsAPIView(APIView):
    """管理员统计数据API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取详细统计数据"""
        stats_type = request.GET.get('type', 'overview')
        
        if stats_type == 'overview':
            return self.get_overview_stats()
        elif stats_type == 'users':
            return self.get_user_stats()
        elif stats_type == 'activity':
            return self.get_activity_stats()
        elif stats_type == 'revenue':
            return self.get_revenue_stats()
        else:
            return Response({'error': '无效的统计类型'}, status=400)
    
    def get_overview_stats(self):
        """获取概览统计"""
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        last_week = today - timedelta(days=7)
        last_month = today - timedelta(days=30)
        
        # 用户统计
        total_users = User.objects.filter(is_active=True).count()
        today_new_users = User.objects.filter(date_joined__date=today).count()
        yesterday_new_users = User.objects.filter(date_joined__date=yesterday).count()
        week_new_users = User.objects.filter(date_joined__date__gte=last_week).count()
        
        # 活跃用户统计
        today_active_users = User.objects.filter(last_login__date=today).count()
        week_active_users = User.objects.filter(last_login__date__gte=last_week).count()
        
        # 匹配统计
        total_matches = Match.objects.count()
        today_matches = Match.objects.filter(created_at__date=today).count()
        week_matches = Match.objects.filter(created_at__date__gte=last_week).count()
        
        # 消息统计
        total_messages = ChatMessage.objects.count()
        today_messages = ChatMessage.objects.filter(created_at__date=today).count()
        week_messages = ChatMessage.objects.filter(created_at__date__gte=last_week).count()
        
        # VIP统计
        vip_users = UserVipRecord.objects.filter(is_active=True).count()
        vip_conversion_rate = (vip_users / total_users * 100) if total_users > 0 else 0
        
        # 收入统计
        total_revenue = PaymentRecord.objects.filter(
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        month_revenue = PaymentRecord.objects.filter(
            status='completed',
            created_at__date__gte=last_month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        return Response({
            'code': 200,
            'data': {
                'users': {
                    'total': total_users,
                    'today_new': today_new_users,
                    'yesterday_new': yesterday_new_users,
                    'week_new': week_new_users,
                    'today_active': today_active_users,
                    'week_active': week_active_users,
                    'growth_rate': ((today_new_users - yesterday_new_users) / yesterday_new_users * 100) if yesterday_new_users > 0 else 0
                },
                'matches': {
                    'total': total_matches,
                    'today': today_matches,
                    'week': week_matches,
                    'success_rate': (total_matches / total_users * 100) if total_users > 0 else 0
                },
                'messages': {
                    'total': total_messages,
                    'today': today_messages,
                    'week': week_messages,
                    'avg_per_user': (total_messages / total_users) if total_users > 0 else 0
                },
                'vip': {
                    'total': vip_users,
                    'conversion_rate': round(vip_conversion_rate, 2)
                },
                'revenue': {
                    'total': float(total_revenue),
                    'month': float(month_revenue),
                    'arpu': float(total_revenue / total_users) if total_users > 0 else 0
                }
            }
        })
    
    def get_user_stats(self):
        """获取用户详细统计"""
        # 用户年龄分布
        age_distribution = User.objects.filter(
            is_active=True,
            age__isnull=False
        ).values('age').annotate(count=Count('id')).order_by('age')
        
        # 用户性别分布
        gender_distribution = User.objects.filter(
            is_active=True
        ).values('gender').annotate(count=Count('id'))
        
        # 用户地区分布
        city_distribution = User.objects.filter(
            is_active=True,
            city__isnull=False
        ).values('city').annotate(count=Count('id')).order_by('-count')[:10]
        
        # 用户注册趋势（最近30天）
        registration_trend = []
        for i in range(30):
            date = timezone.now().date() - timedelta(days=i)
            count = User.objects.filter(date_joined__date=date).count()
            registration_trend.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': count
            })
        
        return Response({
            'code': 200,
            'data': {
                'age_distribution': list(age_distribution),
                'gender_distribution': list(gender_distribution),
                'city_distribution': list(city_distribution),
                'registration_trend': registration_trend[::-1]  # 倒序，最早的在前
            }
        })
    
    def get_activity_stats(self):
        """获取活跃度统计"""
        today = timezone.now().date()
        last_week = today - timedelta(days=7)
        
        # 每日活跃用户趋势
        daily_active_users = []
        for i in range(7):
            date = today - timedelta(days=i)
            count = User.objects.filter(last_login__date=date).count()
            daily_active_users.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': count
            })
        
        # 用户留存率
        retention_rates = self.calculate_retention_rates()
        
        # 功能使用统计
        feature_usage = {
            'likes_sent': Like.objects.filter(created_at__date__gte=last_week).count(),
            'messages_sent': ChatMessage.objects.filter(created_at__date__gte=last_week).count(),
            'moments_posted': Moment.objects.filter(created_at__date__gte=last_week).count(),
            'stories_posted': UserStory.objects.filter(created_at__date__gte=last_week).count(),
        }
        
        return Response({
            'code': 200,
            'data': {
                'daily_active_users': daily_active_users[::-1],
                'retention_rates': retention_rates,
                'feature_usage': feature_usage
            }
        })
    
    def calculate_retention_rates(self):
        """计算用户留存率"""
        today = timezone.now().date()
        
        # 1日留存率
        yesterday_new_users = User.objects.filter(
            date_joined__date=today - timedelta(days=1)
        ).count()
        yesterday_retained = User.objects.filter(
            date_joined__date=today - timedelta(days=1),
            last_login__date=today
        ).count()
        day1_retention = (yesterday_retained / yesterday_new_users * 100) if yesterday_new_users > 0 else 0
        
        # 7日留存率
        week_ago_new_users = User.objects.filter(
            date_joined__date=today - timedelta(days=7)
        ).count()
        week_retained = User.objects.filter(
            date_joined__date=today - timedelta(days=7),
            last_login__date__gte=today - timedelta(days=1)
        ).count()
        day7_retention = (week_retained / week_ago_new_users * 100) if week_ago_new_users > 0 else 0
        
        # 30日留存率
        month_ago_new_users = User.objects.filter(
            date_joined__date=today - timedelta(days=30)
        ).count()
        month_retained = User.objects.filter(
            date_joined__date=today - timedelta(days=30),
            last_login__date__gte=today - timedelta(days=7)
        ).count()
        day30_retention = (month_retained / month_ago_new_users * 100) if month_ago_new_users > 0 else 0
        
        return {
            'day1': round(day1_retention, 2),
            'day7': round(day7_retention, 2),
            'day30': round(day30_retention, 2)
        }
    
    def get_revenue_stats(self):
        """获取收入统计"""
        today = timezone.now().date()
        last_month = today - timedelta(days=30)
        
        # 每日收入趋势
        daily_revenue = []
        for i in range(30):
            date = today - timedelta(days=i)
            revenue = PaymentRecord.objects.filter(
                status='completed',
                created_at__date=date
            ).aggregate(total=Sum('amount'))['total'] or 0
            daily_revenue.append({
                'date': date.strftime('%Y-%m-%d'),
                'revenue': float(revenue)
            })
        
        # 收入来源分布
        revenue_sources = PaymentRecord.objects.filter(
            status='completed',
            created_at__date__gte=last_month
        ).values('product_type').annotate(
            total=Sum('amount'),
            count=Count('id')
        ).order_by('-total')
        
        # VIP会员收入统计
        vip_revenue = PaymentRecord.objects.filter(
            status='completed',
            product_type='vip_membership',
            created_at__date__gte=last_month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        return Response({
            'code': 200,
            'data': {
                'daily_revenue': daily_revenue[::-1],
                'revenue_sources': list(revenue_sources),
                'vip_revenue': float(vip_revenue),
                'total_month_revenue': sum(item['revenue'] for item in daily_revenue)
            }
        })


class UserManagementAPIView(APIView):
    """用户管理API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取用户列表"""
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        search = request.GET.get('search', '')
        status = request.GET.get('status', 'all')
        
        # 构建查询条件
        queryset = User.objects.all()
        
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(nickname__icontains=search) |
                Q(phone__icontains=search)
            )
        
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'vip':
            queryset = queryset.filter(
                vip_memberships__is_active=True
            ).distinct()
        
        # 分页
        total = queryset.count()
        offset = (page - 1) * limit
        users = queryset.order_by('-date_joined')[offset:offset + limit]
        
        # 格式化用户数据
        user_data = []
        for user in users:
            user_data.append({
                'id': user.id,
                'username': user.username,
                'nickname': user.nickname,
                'phone': user.phone,
                'gender': user.get_gender_display(),
                'age': user.age,
                'city': user.city,
                'is_active': user.is_active,
                'is_vip': hasattr(user, 'vip_membership') and user.vip_membership.is_active,
                'date_joined': user.date_joined.strftime('%Y-%m-%d %H:%M'),
                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '从未登录'
            })
        
        return Response({
            'code': 200,
            'data': {
                'users': user_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': (total + limit - 1) // limit
                }
            }
        })
    
    def post(self, request):
        """用户操作（禁用/启用/删除等）"""
        action = request.data.get('action')
        user_ids = request.data.get('user_ids', [])
        
        if not action or not user_ids:
            return Response({'error': '缺少必要参数'}, status=400)
        
        users = User.objects.filter(id__in=user_ids)
        
        if action == 'disable':
            users.update(is_active=False)
            message = f'成功禁用 {users.count()} 个用户'
        elif action == 'enable':
            users.update(is_active=True)
            message = f'成功启用 {users.count()} 个用户'
        elif action == 'delete':
            count = users.count()
            users.delete()
            message = f'成功删除 {count} 个用户'
        else:
            return Response({'error': '无效的操作'}, status=400)
        
        return Response({
            'code': 200,
            'message': message
        })


class ContentModerationAPIView(APIView):
    """内容审核管理API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取待审核内容列表"""
        content_type = request.GET.get('type', 'all')  # all, moments, stories, photos
        status = request.GET.get('status', 'pending')  # pending, approved, rejected
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))

        # 构建待审核内容列表
        pending_content = []

        if content_type in ['all', 'moments']:
            # 获取待审核动态
            moments = Moment.objects.filter(
                status='pending' if status == 'pending' else status
            ).order_by('-created_at')[:limit]

            for moment in moments:
                pending_content.append({
                    'id': moment.id,
                    'type': 'moment',
                    'user': {
                        'id': moment.user.id,
                        'username': moment.user.username,
                        'nickname': moment.user.nickname
                    },
                    'content': moment.content[:100] + '...' if len(moment.content) > 100 else moment.content,
                    'images': moment.images[:3] if moment.images else [],
                    'status': moment.status,
                    'created_at': moment.created_at.strftime('%Y-%m-%d %H:%M'),
                    'report_count': getattr(moment, 'report_count', 0)
                })

        if content_type in ['all', 'stories']:
            # 获取待审核Stories
            stories = UserStory.objects.filter(
                status='pending' if status == 'pending' else status
            ).order_by('-created_at')[:limit]

            for story in stories:
                pending_content.append({
                    'id': story.id,
                    'type': 'story',
                    'user': {
                        'id': story.user.id,
                        'username': story.user.username,
                        'nickname': story.user.nickname
                    },
                    'content': story.text_content or '图片/视频内容',
                    'content_url': story.content_url,
                    'thumbnail_url': story.thumbnail_url,
                    'status': story.status,
                    'created_at': story.created_at.strftime('%Y-%m-%d %H:%M'),
                    'view_count': story.view_count
                })

        # 按时间排序
        pending_content.sort(key=lambda x: x['created_at'], reverse=True)

        return Response({
            'code': 200,
            'data': {
                'content': pending_content[:limit],
                'total': len(pending_content),
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'has_next': len(pending_content) > limit
                }
            }
        })

    def post(self, request):
        """审核内容（通过/拒绝）"""
        action = request.data.get('action')  # approve, reject
        content_type = request.data.get('content_type')  # moment, story
        content_ids = request.data.get('content_ids', [])
        reason = request.data.get('reason', '')

        if not action or not content_type or not content_ids:
            return Response({'error': '缺少必要参数'}, status=400)

        success_count = 0

        if content_type == 'moment':
            moments = Moment.objects.filter(id__in=content_ids)
            if action == 'approve':
                moments.update(status='published')
                success_count = moments.count()
            elif action == 'reject':
                moments.update(status='rejected')
                success_count = moments.count()

        elif content_type == 'story':
            stories = UserStory.objects.filter(id__in=content_ids)
            if action == 'approve':
                stories.update(status='active')
                success_count = stories.count()
            elif action == 'reject':
                stories.update(status='rejected')
                success_count = stories.count()

        action_text = '通过' if action == 'approve' else '拒绝'
        return Response({
            'code': 200,
            'message': f'成功{action_text} {success_count} 条内容'
        })


class SystemConfigAPIView(APIView):
    """系统配置管理API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取系统配置"""
        from django.conf import settings

        # 模拟系统配置数据
        config = {
            'app_settings': {
                'app_name': '相亲交友App',
                'version': '1.0.0',
                'maintenance_mode': False,
                'registration_enabled': True,
                'guest_browsing_enabled': True
            },
            'matching_settings': {
                'max_daily_likes': 50,
                'vip_max_daily_likes': -1,  # 无限制
                'recommendation_refresh_hours': 6,
                'match_expiry_days': 30
            },
            'content_settings': {
                'max_photos_per_user': 9,
                'max_story_duration': 24,  # 小时
                'auto_moderation_enabled': True,
                'sensitive_word_filter_enabled': True
            },
            'payment_settings': {
                'vip_monthly_price': 29.9,
                'vip_quarterly_price': 79.9,
                'vip_yearly_price': 299.9,
                'payment_methods': ['wechat', 'alipay']
            },
            'notification_settings': {
                'push_notifications_enabled': True,
                'email_notifications_enabled': False,
                'sms_notifications_enabled': True
            }
        }

        return Response({
            'code': 200,
            'data': config
        })

    def post(self, request):
        """更新系统配置"""
        config_type = request.data.get('config_type')
        config_data = request.data.get('config_data', {})

        if not config_type:
            return Response({'error': '缺少配置类型'}, status=400)

        # 这里应该将配置保存到数据库或配置文件
        # 为了演示，我们只是返回成功消息

        return Response({
            'code': 200,
            'message': f'{config_type} 配置更新成功'
        })


class ReportsAPIView(APIView):
    """数据报表API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取数据报表"""
        report_type = request.GET.get('type', 'summary')
        date_range = request.GET.get('range', '30')  # 天数

        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=int(date_range))

        if report_type == 'summary':
            return self.get_summary_report(start_date, end_date)
        elif report_type == 'user_behavior':
            return self.get_user_behavior_report(start_date, end_date)
        elif report_type == 'revenue_detail':
            return self.get_revenue_detail_report(start_date, end_date)
        else:
            return Response({'error': '无效的报表类型'}, status=400)

    def get_summary_report(self, start_date, end_date):
        """获取汇总报表"""
        # 用户数据
        total_users = User.objects.filter(is_active=True).count()
        new_users = User.objects.filter(
            date_joined__date__range=[start_date, end_date]
        ).count()

        # 活跃度数据
        active_users = User.objects.filter(
            last_login__date__range=[start_date, end_date]
        ).count()

        # 匹配数据
        total_matches = Match.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        # 消息数据
        total_messages = ChatMessage.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        # 收入数据
        total_revenue = PaymentRecord.objects.filter(
            status='completed',
            created_at__date__range=[start_date, end_date]
        ).aggregate(total=Sum('amount'))['total'] or 0

        return Response({
            'code': 200,
            'data': {
                'period': f'{start_date} 至 {end_date}',
                'summary': {
                    'total_users': total_users,
                    'new_users': new_users,
                    'active_users': active_users,
                    'total_matches': total_matches,
                    'total_messages': total_messages,
                    'total_revenue': float(total_revenue),
                    'user_activity_rate': (active_users / total_users * 100) if total_users > 0 else 0,
                    'avg_matches_per_user': (total_matches / active_users) if active_users > 0 else 0,
                    'avg_messages_per_user': (total_messages / active_users) if active_users > 0 else 0
                }
            }
        })

    def get_user_behavior_report(self, start_date, end_date):
        """获取用户行为报表"""
        # 功能使用统计
        likes_sent = Like.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        moments_posted = Moment.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        stories_posted = UserStory.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).count()

        # 用户留存分析
        retention_data = self.calculate_detailed_retention(start_date, end_date)

        return Response({
            'code': 200,
            'data': {
                'period': f'{start_date} 至 {end_date}',
                'behavior_stats': {
                    'likes_sent': likes_sent,
                    'moments_posted': moments_posted,
                    'stories_posted': stories_posted
                },
                'retention_analysis': retention_data
            }
        })

    def calculate_detailed_retention(self, start_date, end_date):
        """计算详细留存数据"""
        # 这里应该实现更复杂的留存计算逻辑
        # 为了演示，返回模拟数据
        return {
            'day1_retention': 65.5,
            'day3_retention': 45.2,
            'day7_retention': 32.8,
            'day14_retention': 25.6,
            'day30_retention': 18.9
        }


@method_decorator(staff_member_required, name='dispatch')
class AdvancedFeaturesView(TemplateView):
    """高级功能管理页面"""
    template_name = 'admin_dashboard/advanced_features.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # AI功能统计（模拟数据）
        context['ai_stats'] = {
            'user_profiles': 1250,  # 模拟数据
            'match_predictions': 8500,  # 模拟数据
            'user_similarities': 15600,  # 模拟数据
            'avg_match_score': 0.78  # 模拟数据
        }

        # 实时通讯统计（模拟数据）
        context['realtime_stats'] = {
            'active_connections': 156,  # 模拟数据
            'voice_calls_today': 89,  # 模拟数据
            'video_calls_today': 67,  # 模拟数据
            'online_users': 234  # 模拟数据
        }

        # 安全防护统计（模拟数据）
        context['security_stats'] = {
            'security_events_today': 12,  # 模拟数据
            'blocked_ips': 45,  # 模拟数据
            'high_risk_users': 8,  # 模拟数据
            'threat_detection_rate': 96.2  # 模拟数据
        }

        # 国际化统计（模拟数据）
        context['i18n_stats'] = {
            'supported_languages': 12,  # 模拟数据
            'total_translations': 3456,  # 模拟数据
            'users_with_language_pref': 890,  # 模拟数据
            'translation_completion': 87.5  # 模拟数据
        }

        # 高级分析统计（模拟数据）
        context['analytics_stats'] = {
            'user_segments': 15,  # 模拟数据
            'value_scored_users': 1180,  # 模拟数据
            'behavior_predictions': 2340,  # 模拟数据
            'avg_user_value': 68.5  # 模拟数据
        }

        return context


class AIMatchingManagementAPIView(APIView):
    """AI匹配管理API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取AI匹配统计数据"""
        # 匹配模型性能（模拟数据）
        model_performance = {
            'total_predictions': 8500,
            'avg_confidence': 0.82,
            'high_confidence_predictions': 6800
        }

        # 用户画像覆盖率（模拟数据）
        total_users = User.objects.filter(is_active=True).count()
        profile_coverage = 85.6

        # 相似度计算统计（模拟数据）
        similarity_stats = {
            'total_similarities': 15600,
            'avg_similarity': 0.74
        }

        return Response({
            'code': 200,
            'data': {
                'model_performance': model_performance,
                'profile_coverage': profile_coverage,
                'similarity_stats': similarity_stats,
                'last_updated': timezone.now().isoformat()
            }
        })

    def post(self, request):
        """执行AI模型操作"""
        action = request.data.get('action')

        if action == 'retrain_model':
            # 触发模型重训练
            return Response({
                'code': 200,
                'message': '模型重训练任务已启动'
            })
        elif action == 'update_profiles':
            # 更新用户画像
            return Response({
                'code': 200,
                'message': '用户画像更新任务已启动'
            })
        else:
            return Response({
                'code': 400,
                'message': '无效的操作'
            })


# 其他高级功能API视图暂时注释，等待模块启用后再实现
    """实时通讯监控API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取实时通讯监控数据"""
        # WebSocket连接统计
        connection_stats = {
            'total_connections': WebSocketConnection.objects.count(),
            'active_connections': WebSocketConnection.objects.filter(
                status='connected'
            ).count(),
            'connection_types': WebSocketConnection.objects.values(
                'connection_type'
            ).annotate(count=Count('id'))
        }

        # 通话统计
        today = timezone.now().date()
        call_stats = {
            'voice_calls_today': VoiceCall.objects.filter(
                initiated_at__date=today
            ).count(),
            'video_calls_today': VideoCall.objects.filter(
                initiated_at__date=today
            ).count(),
            'avg_call_duration': VoiceCall.objects.filter(
                duration__isnull=False
            ).aggregate(avg_duration=Avg('duration'))['avg_duration']
        }

        # 在线用户统计
        online_stats = {
            'online_users': UserOnlineStatus.objects.filter(
                status='online'
            ).count(),
            'status_distribution': UserOnlineStatus.objects.values(
                'status'
            ).annotate(count=Count('id'))
        }

        return Response({
            'code': 200,
            'data': {
                'connection_stats': connection_stats,
                'call_stats': call_stats,
                'online_stats': online_stats,
                'timestamp': timezone.now().isoformat()
            }
        })


class SecurityMonitorAPIView(APIView):
    """安全监控API"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """获取安全监控数据"""
        # 安全事件统计
        today = timezone.now().date()
        recent_date = timezone.now() - timedelta(days=7)

        event_stats = {
            'events_today': SecurityEvent.objects.filter(
                created_at__date=today
            ).count(),
            'events_week': SecurityEvent.objects.filter(
                created_at__gte=recent_date
            ).count(),
            'high_severity_events': SecurityEvent.objects.filter(
                severity='high',
                created_at__gte=recent_date
            ).count(),
            'event_types': SecurityEvent.objects.filter(
                created_at__gte=recent_date
            ).values('event_type').annotate(count=Count('id'))
        }

        # IP黑名单统计
        blacklist_stats = {
            'total_blocked_ips': IPBlacklist.objects.filter(is_active=True).count(),
            'auto_blocked': IPBlacklist.objects.filter(
                is_active=True,
                reason__startswith='auto'
            ).count(),
            'manual_blocked': IPBlacklist.objects.filter(
                is_active=True,
                reason='manual'
            ).count()
        }

        # 用户风险评估
        risk_stats = {
            'high_risk_users': UserSecurityProfile.objects.filter(
                risk_score__gte=70
            ).count(),
            'medium_risk_users': UserSecurityProfile.objects.filter(
                risk_score__gte=40,
                risk_score__lt=70
            ).count(),
            'low_risk_users': UserSecurityProfile.objects.filter(
                risk_score__lt=40
            ).count()
        }

        return Response({
            'code': 200,
            'data': {
                'event_stats': event_stats,
                'blacklist_stats': blacklist_stats,
                'risk_stats': risk_stats,
                'last_updated': timezone.now().isoformat()
            }
        })
