"""
性能优化中间件
"""

import time
import logging
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class QueryOptimizationMiddleware(MiddlewareMixin):
    """查询优化中间件"""
    
    def process_request(self, request):
        """请求开始时记录"""
        request._query_start_time = time.time()
        request._query_count_start = len(connection.queries)
        return None
    
    def process_response(self, request, response):
        """请求结束时分析性能"""
        if hasattr(request, '_query_start_time'):
            # 计算执行时间
            total_time = time.time() - request._query_start_time
            
            # 计算查询次数
            query_count = len(connection.queries) - request._query_count_start
            
            # 记录慢查询
            if total_time > 1.0:  # 超过1秒的请求
                logger.warning(
                    f"慢请求: {request.path} - 耗时: {total_time:.2f}s, 查询次数: {query_count}"
                )
            
            # 记录过多查询
            if query_count > 20:  # 超过20次查询
                logger.warning(
                    f"查询过多: {request.path} - 查询次数: {query_count}, 耗时: {total_time:.2f}s"
                )
            
            # 添加性能头信息（开发环境）
            if settings.DEBUG:
                response['X-Query-Count'] = str(query_count)
                response['X-Query-Time'] = f"{total_time:.3f}s"
        
        return response


class CacheOptimizationMiddleware(MiddlewareMixin):
    """缓存优化中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """检查页面缓存"""
        # 只对GET请求进行缓存
        if request.method != 'GET':
            return None

        # 排除admin、API和静态文件请求
        if (request.path.startswith('/admin/') or
            request.path.startswith('/api/') or
            request.path.startswith('/static/') or
            request.path.startswith('/media/')):
            return None
        
        # 生成缓存键
        cache_key = f"page_cache:{request.get_full_path()}"
        
        # 尝试从缓存获取
        cached_response = cache.get(cache_key)
        if cached_response:
            logger.debug(f"缓存命中: {request.path}")
            return cached_response
        
        return None
    
    def process_response(self, request, response):
        """缓存页面响应"""
        # 只缓存成功的GET请求，排除admin、API和静态文件
        if (request.method == 'GET' and
            response.status_code == 200 and
            not request.path.startswith('/admin/') and
            not request.path.startswith('/api/') and
            not request.path.startswith('/static/') and
            not request.path.startswith('/media/')):

            cache_key = f"page_cache:{request.get_full_path()}"
            try:
                cache.set(cache_key, response, timeout=300)  # 缓存5分钟
                logger.debug(f"页面已缓存: {request.path}")
            except Exception as e:
                logger.warning(f"缓存页面失败 {request.path}: {e}")

        return response


class DatabaseOptimizationMiddleware(MiddlewareMixin):
    """数据库优化中间件"""

    def process_request(self, request):
        """请求开始时的数据库优化"""
        # 设置数据库连接参数
        if hasattr(connection, 'cursor'):
            with connection.cursor() as cursor:
                try:
                    # 检查MySQL版本并设置相应的优化参数
                    cursor.execute("SELECT VERSION()")
                    version = cursor.fetchone()[0]

                    # MySQL 8.0+ 不支持query_cache，使用其他优化
                    if version.startswith('8.') or version.startswith('9.'):
                        # MySQL 8.0+ 优化设置
                        cursor.execute("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'")
                        cursor.execute("SET SESSION innodb_lock_wait_timeout = 50")
                    else:
                        # MySQL 5.7及以下版本的优化设置
                        cursor.execute("SET SESSION query_cache_type = ON")
                        cursor.execute("SET SESSION query_cache_size = 67108864")  # 64MB

                except Exception as e:
                    # 如果设置失败，记录日志但不影响正常流程
                    logger.debug(f"数据库优化设置失败: {e}")

        return None


class AdminOptimizationMiddleware(MiddlewareMixin):
    """Admin后台优化中间件"""
    
    def process_request(self, request):
        """Admin请求优化"""
        if not request.path.startswith('/admin/'):
            return None
        
        # 为admin请求设置特殊的缓存策略
        request._admin_cache_timeout = 600  # 10分钟缓存
        
        return None
    
    def process_response(self, request, response):
        """Admin响应优化"""
        if not request.path.startswith('/admin/'):
            return response
        
        # 添加缓存头
        if response.status_code == 200:
            response['Cache-Control'] = 'public, max-age=300'  # 5分钟缓存
            response['Vary'] = 'Accept-Encoding'
        
        return response


class CompressionMiddleware(MiddlewareMixin):
    """响应压缩中间件"""
    
    def process_response(self, request, response):
        """压缩响应内容"""
        # 检查是否支持gzip
        if 'gzip' not in request.META.get('HTTP_ACCEPT_ENCODING', ''):
            return response
        
        # 只压缩文本内容
        content_type = response.get('Content-Type', '')
        if not any(ct in content_type for ct in ['text/', 'application/json', 'application/javascript']):
            return response
        
        # 小文件不压缩，FileResponse没有content属性
        try:
            if hasattr(response, 'content') and len(response.content) < 1024:  # 小于1KB
                return response
        except AttributeError:
            # FileResponse等流式响应不压缩
            return response
        
        try:
            # 只压缩有content属性的响应
            if not hasattr(response, 'content'):
                return response

            import gzip
            compressed_content = gzip.compress(response.content)

            # 只有压缩效果明显才使用
            if len(compressed_content) < len(response.content) * 0.9:
                response.content = compressed_content
                response['Content-Encoding'] = 'gzip'
                response['Content-Length'] = str(len(compressed_content))
                logger.debug(f"响应已压缩: {request.path}")
        
        except Exception as e:
            logger.error(f"压缩失败: {e}")
        
        return response


class PerformanceMonitoringMiddleware(MiddlewareMixin):
    """性能监控中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.slow_requests = []
        super().__init__(get_response)
    
    def process_request(self, request):
        """开始监控"""
        request._perf_start = time.time()
        return None
    
    def process_response(self, request, response):
        """结束监控并记录"""
        if hasattr(request, '_perf_start'):
            duration = time.time() - request._perf_start
            
            # 记录慢请求
            if duration > 2.0:  # 超过2秒
                slow_request = {
                    'path': request.path,
                    'method': request.method,
                    'duration': duration,
                    'timestamp': time.time()
                }
                self.slow_requests.append(slow_request)
                
                # 只保留最近100个慢请求
                if len(self.slow_requests) > 100:
                    self.slow_requests = self.slow_requests[-100:]
                
                logger.warning(f"慢请求检测: {request.path} - {duration:.2f}s")
            
            # 添加性能监控头
            if settings.DEBUG:
                response['X-Response-Time'] = f"{duration:.3f}s"
        
        return response
    
    def get_slow_requests(self):
        """获取慢请求列表"""
        return self.slow_requests[-20:]  # 返回最近20个


# 性能监控实例 - 延迟初始化
performance_monitor = None
