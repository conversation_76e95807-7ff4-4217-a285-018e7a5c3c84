from django.urls import path
from . import views, performance_views

app_name = 'dashboard'

urlpatterns = [
    path('', views.dashboard_view, name='index'),

    # 性能监控API
    path('api/performance/report/', performance_views.performance_report, name='performance-report'),
    path('api/performance/database/', performance_views.database_analysis, name='database-analysis'),
    path('api/performance/suggestions/', performance_views.optimization_suggestions, name='optimization-suggestions'),
    path('api/performance/cache/', performance_views.cache_statistics, name='cache-statistics'),
    path('api/performance/cache/clear/', performance_views.clear_cache, name='clear-cache'),
    path('api/performance/health/', performance_views.system_health, name='system-health'),
]
