"""
性能分析视图
提供系统性能监控和分析的API
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db import connection
from django.core.cache import cache

from utils.performance_monitor import performance_monitor
from utils.db_optimizer import db_optimizer


@api_view(['GET'])
@permission_classes([IsAdminUser])
def performance_report(request):
    """获取系统性能报告"""
    hours = int(request.GET.get('hours', 24))
    
    try:
        # 获取性能报告
        report = performance_monitor.get_performance_report(hours)
        
        # 获取优化建议
        suggestions = performance_monitor.get_optimization_suggestions(report)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'report': report,
                'suggestions': suggestions
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取性能报告失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def database_analysis(request):
    """数据库分析"""
    try:
        # 获取表统计信息
        table_stats = db_optimizer.get_table_statistics()
        
        # 获取缺失索引建议
        missing_indexes = db_optimizer.get_missing_indexes()
        
        # 获取慢查询日志
        slow_queries = db_optimizer.get_slow_query_log()
        
        # 分析最近的查询
        recent_queries = connection.queries[-50:] if connection.queries else []
        query_suggestions = db_optimizer.analyze_queries(recent_queries)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'table_statistics': table_stats,
                'missing_indexes': missing_indexes,
                'slow_queries': slow_queries,
                'query_suggestions': query_suggestions,
                'total_queries': len(connection.queries)
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'数据库分析失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def optimization_suggestions(request):
    """获取优化建议"""
    model_name = request.GET.get('model')
    
    try:
        if model_name:
            # 为特定模型提供优化建议
            suggestions = db_optimizer.optimize_queryset_suggestions(model_name)
            code_examples = db_optimizer.generate_optimization_code(model_name, suggestions)
            
            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'model': model_name,
                    'suggestions': suggestions,
                    'code_examples': code_examples
                }
            })
        else:
            # 提供通用优化建议
            general_suggestions = [
                {
                    'category': '数据库查询优化',
                    'suggestions': [
                        '使用select_related()优化外键查询',
                        '使用prefetch_related()优化多对多查询',
                        '使用only()和defer()控制字段加载',
                        '为常用查询字段添加索引',
                        '避免在循环中执行查询'
                    ]
                },
                {
                    'category': 'API性能优化',
                    'suggestions': [
                        '使用缓存减少重复计算',
                        '实现分页避免大量数据传输',
                        '使用异步任务处理耗时操作',
                        '压缩API响应数据',
                        '添加API限流保护'
                    ]
                },
                {
                    'category': '前端性能优化',
                    'suggestions': [
                        '实现图片懒加载',
                        '使用CDN加速静态资源',
                        '压缩JavaScript和CSS',
                        '使用浏览器缓存',
                        '减少HTTP请求数量'
                    ]
                },
                {
                    'category': '系统架构优化',
                    'suggestions': [
                        '使用Redis缓存热点数据',
                        '实现数据库读写分离',
                        '使用消息队列处理异步任务',
                        '实现负载均衡',
                        '监控系统资源使用情况'
                    ]
                }
            ]
            
            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'general_suggestions': general_suggestions
                }
            })
            
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取优化建议失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def cache_statistics(request):
    """获取缓存统计信息"""
    try:
        # 获取缓存信息（这里需要根据实际缓存后端实现）
        cache_info = {
            'backend': str(cache.__class__),
            'location': getattr(cache, '_cache', {}).get('location', 'unknown'),
            'version': getattr(cache, 'version', 'unknown')
        }
        
        # 测试缓存性能
        test_key = 'performance_test'
        test_value = {'timestamp': timezone.now().isoformat(), 'data': list(range(1000))}
        
        # 写入测试
        start_time = timezone.now()
        cache.set(test_key, test_value, 60)
        write_time = (timezone.now() - start_time).total_seconds() * 1000
        
        # 读取测试
        start_time = timezone.now()
        cached_value = cache.get(test_key)
        read_time = (timezone.now() - start_time).total_seconds() * 1000
        
        # 清理测试数据
        cache.delete(test_key)
        
        performance_test = {
            'write_time_ms': round(write_time, 3),
            'read_time_ms': round(read_time, 3),
            'data_integrity': cached_value == test_value
        }
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'cache_info': cache_info,
                'performance_test': performance_test
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'获取缓存统计失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def clear_cache(request):
    """清理缓存"""
    cache_type = request.data.get('type', 'all')
    
    try:
        if cache_type == 'all':
            cache.clear()
            message = '所有缓存已清理'
        else:
            # 清理特定类型的缓存
            pattern = request.data.get('pattern', '')
            if pattern:
                # 这里需要根据缓存后端实现模式匹配删除
                # Redis示例：cache.delete_pattern(pattern)
                message = f'匹配模式 {pattern} 的缓存已清理'
            else:
                message = '请提供缓存模式'
        
        return Response({
            'code': 200,
            'message': message
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'清理缓存失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def system_health(request):
    """系统健康检查"""
    try:
        health_checks = []
        
        # 数据库连接检查
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_checks.append({
                    'component': 'database',
                    'status': 'healthy',
                    'message': '数据库连接正常'
                })
        except Exception as e:
            health_checks.append({
                'component': 'database',
                'status': 'unhealthy',
                'message': f'数据库连接失败: {str(e)}'
            })
        
        # 缓存检查
        try:
            test_key = 'health_check'
            cache.set(test_key, 'test', 10)
            value = cache.get(test_key)
            cache.delete(test_key)
            
            if value == 'test':
                health_checks.append({
                    'component': 'cache',
                    'status': 'healthy',
                    'message': '缓存系统正常'
                })
            else:
                health_checks.append({
                    'component': 'cache',
                    'status': 'unhealthy',
                    'message': '缓存数据不一致'
                })
        except Exception as e:
            health_checks.append({
                'component': 'cache',
                'status': 'unhealthy',
                'message': f'缓存系统异常: {str(e)}'
            })
        
        # 整体健康状态
        overall_status = 'healthy' if all(check['status'] == 'healthy' for check in health_checks) else 'unhealthy'
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'overall_status': overall_status,
                'checks': health_checks,
                'timestamp': timezone.now().isoformat()
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'健康检查失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
