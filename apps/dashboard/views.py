from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from apps.users.models import User
from apps.matching.models import Match
from apps.chat.models import ChatMessage
from apps.payment.models import Order, VipPackage
import json


@login_required
def dashboard_view(request):
    """控制台首页视图"""
    
    # 获取统计数据
    stats = get_dashboard_stats()
    
    # 获取图表数据
    chart_data = get_chart_data()
    
    context = {
        'stats': stats,
        'chart_data': chart_data,
    }
    
    return render(request, 'dashboard/index.html', context)


def get_dashboard_stats():
    """获取控制台统计数据"""
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    this_month = today.replace(day=1)
    last_month = (this_month - timedelta(days=1)).replace(day=1)

    # 用户统计
    total_users = User.objects.count()
    today_users = User.objects.filter(created_at__date=today).count()
    male_users = User.objects.filter(gender=1).count()  # 1为男性
    female_users = User.objects.filter(gender=2).count()  # 2为女性

    # 匹配统计
    total_matches = Match.objects.filter(status='matched').count()
    today_matches = Match.objects.filter(
        status='matched',
        created_at__date=today
    ).count()

    # 消息统计
    total_messages = ChatMessage.objects.count()
    today_messages = ChatMessage.objects.filter(created_at__date=today).count()

    # VIP统计 - 通过订单统计活跃VIP
    active_vip = Order.objects.filter(
        status='completed',
        product_type='vip',
        created_at__gte=timezone.now() - timedelta(days=30)  # 最近30天的VIP订单
    ).values('user').distinct().count()

    # 收入统计
    total_revenue = Order.objects.filter(
        status='completed'
    ).aggregate(total=Sum('final_amount'))['total'] or 0

    month_revenue = Order.objects.filter(
        status='completed',
        created_at__gte=timezone.make_aware(datetime.combine(this_month, datetime.min.time()))
    ).aggregate(total=Sum('final_amount'))['total'] or 0

    # 更多统计数据
    yesterday = today - timedelta(days=1)
    yesterday_users = User.objects.filter(created_at__date=yesterday).count()

    # 活跃会话数（模拟数据）
    active_sessions = max(1, total_users // 10)

    # 匹配成功率
    total_actions = max(1, total_users * 5)  # 假设每用户平均5次操作
    match_success_rate = round((total_matches / total_actions) * 100, 1) if total_actions > 0 else 0

    # 日均消息数
    days_since_launch = max(1, (timezone.now().date() - timezone.now().date().replace(month=1, day=1)).days)
    avg_daily_messages = round(total_messages / days_since_launch, 0) if days_since_launch > 0 else 0

    # VIP转化率
    vip_conversion_rate = round((active_vip / max(1, total_users)) * 100, 1)

    # 平台增长率（环比上月）
    last_month_start = (this_month - timedelta(days=1)).replace(day=1)
    last_month_users = User.objects.filter(
        created_at__gte=timezone.make_aware(datetime.combine(last_month_start, datetime.min.time())),
        created_at__lt=timezone.make_aware(datetime.combine(this_month, datetime.min.time()))
    ).count()

    platform_growth = round(((today_users - last_month_users) / max(1, last_month_users)) * 100, 1) if last_month_users > 0 else 100

    return {
        'total_users': total_users,
        'today_users': today_users,
        'male_users': male_users,
        'female_users': female_users,
        'total_matches': total_matches,
        'today_matches': today_matches,
        'total_messages': total_messages,
        'today_messages': today_messages,
        'active_vip': active_vip,
        'total_revenue': total_revenue,
        'month_revenue': month_revenue,
        # 新增统计数据
        'today_registrations': today_users,
        'yesterday_registrations': yesterday_users,
        'active_sessions': active_sessions,
        'match_success_rate': match_success_rate,
        'avg_daily_messages': int(avg_daily_messages),
        'vip_conversion_rate': vip_conversion_rate,
        'platform_growth': platform_growth,
    }


def get_chart_data():
    """获取图表数据"""
    # 获取最近7天的数据
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=6)

    # 订单趋势数据
    order_trend = []
    for i in range(7):
        date = start_date + timedelta(days=i)
        date_start = timezone.make_aware(datetime.combine(date, datetime.min.time()))
        date_end = timezone.make_aware(datetime.combine(date, datetime.max.time()))
        count = Order.objects.filter(
            created_at__gte=date_start,
            created_at__lte=date_end,
            status='completed'
        ).count()
        order_trend.append({
            'date': date.strftime('%m-%d'),
            'count': count
        })

    # 订单状态分布
    order_status = [
        {
            'name': '已完成',
            'value': Order.objects.filter(status='completed').count()
        },
        {
            'name': '待支付',
            'value': Order.objects.filter(status='pending').count()
        },
        {
            'name': '已取消',
            'value': Order.objects.filter(status='cancelled').count()
        },
        {
            'name': '已退款',
            'value': Order.objects.filter(status='refunded').count()
        }
    ]

    # 用户分析
    total_users = User.objects.count()
    male_users = User.objects.filter(gender=1).count()  # 1为男性
    female_users = User.objects.filter(gender=2).count()  # 2为女性
    vip_users = Order.objects.filter(
        status='completed',
        product_type='vip',
        created_at__gte=timezone.now() - timedelta(days=30)
    ).values('user').distinct().count()

    user_analysis = [
        {
            'name': '男性用户',
            'value': male_users
        },
        {
            'name': '女性用户',
            'value': female_users
        },
        {
            'name': 'VIP用户',
            'value': vip_users
        },
        {
            'name': '普通用户',
            'value': total_users - vip_users
        }
    ]

    return {
        'order_trend': json.dumps(order_trend),
        'order_status': json.dumps(order_status),
        'user_analysis': json.dumps(user_analysis),
    }
