"""
实时通讯模块URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# API URL模式
urlpatterns = [
    # WebSocket连接管理
    path('realtime/connections/', views.ConnectionManagementAPIView.as_view(), name='connection_management'),
    
    # 通话管理
    path('realtime/calls/', views.CallManagementAPIView.as_view(), name='call_management'),
    
    # 语音消息
    path('realtime/voice-messages/', views.VoiceMessageAPIView.as_view(), name='voice_messages'),
    
    # 在线状态
    path('realtime/online-status/', views.OnlineStatusAPIView.as_view(), name='online_status'),
    
    # 包含路由器URL
    path('', include(router.urls)),
]
