"""
WebSocket消费者
处理实时通讯、语音通话、视频通话等WebSocket连接
"""

import json
import logging
from datetime import datetime, timedelta
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from django.contrib.auth import get_user_model

from .models import (
    WebSocketConnection, VoiceCall, VideoCall, VoiceMessage,
    RealtimeNotification, UserOnlineStatus
)

User = get_user_model()
logger = logging.getLogger(__name__)


class ChatConsumer(AsyncWebsocketConsumer):
    """聊天WebSocket消费者"""
    
    async def connect(self):
        """WebSocket连接"""
        self.user = self.scope["user"]
        if not self.user.is_authenticated:
            await self.close()
            return
        
        # 获取聊天会话ID
        self.session_id = self.scope['url_route']['kwargs']['session_id']
        self.room_group_name = f'chat_{self.session_id}'
        
        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        # 接受WebSocket连接
        await self.accept()
        
        # 记录连接
        await self.create_connection_record()
        
        # 更新在线状态
        await self.update_online_status('online')
        
        # 发送连接成功消息
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': '连接成功',
            'timestamp': timezone.now().isoformat()
        }))
        
        logger.info(f"用户 {self.user.username} 连接到聊天室 {self.session_id}")
    
    async def disconnect(self, close_code):
        """WebSocket断开连接"""
        # 离开房间组
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        # 更新连接记录
        await self.update_connection_record()
        
        # 更新在线状态
        await self.update_online_status('offline')
        
        logger.info(f"用户 {self.user.username} 断开聊天室 {self.session_id} 连接")
    
    async def receive(self, text_data):
        """接收WebSocket消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'chat_message':
                await self.handle_chat_message(data)
            elif message_type == 'typing_indicator':
                await self.handle_typing_indicator(data)
            elif message_type == 'read_receipt':
                await self.handle_read_receipt(data)
            elif message_type == 'heartbeat':
                await self.handle_heartbeat(data)
            else:
                await self.send_error('未知的消息类型')
                
        except json.JSONDecodeError:
            await self.send_error('无效的JSON格式')
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {str(e)}")
            await self.send_error('消息处理失败')
    
    async def handle_chat_message(self, data):
        """处理聊天消息"""
        content = data.get('content', '').strip()
        if not content:
            await self.send_error('消息内容不能为空')
            return
        
        # 保存消息到数据库
        message = await self.save_chat_message(content, data.get('message_type', 'text'))
        
        # 广播消息到房间组
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message_broadcast',
                'message': {
                    'id': message.id,
                    'content': content,
                    'sender_id': self.user.id,
                    'sender_username': self.user.username,
                    'message_type': data.get('message_type', 'text'),
                    'timestamp': message.created_at.isoformat()
                }
            }
        )
        
        # 发送推送通知给离线用户
        await self.send_push_notification(message)
    
    async def handle_typing_indicator(self, data):
        """处理打字指示器"""
        is_typing = data.get('is_typing', False)
        
        # 广播打字状态
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator_broadcast',
                'user_id': self.user.id,
                'username': self.user.username,
                'is_typing': is_typing
            }
        )
    
    async def handle_read_receipt(self, data):
        """处理已读回执"""
        message_id = data.get('message_id')
        if message_id:
            await self.mark_message_as_read(message_id)
            
            # 广播已读状态
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'read_receipt_broadcast',
                    'message_id': message_id,
                    'reader_id': self.user.id,
                    'read_at': timezone.now().isoformat()
                }
            )
    
    async def handle_heartbeat(self, data):
        """处理心跳消息"""
        await self.update_heartbeat()
        await self.send(text_data=json.dumps({
            'type': 'heartbeat_response',
            'timestamp': timezone.now().isoformat()
        }))
    
    # 广播处理方法
    async def chat_message_broadcast(self, event):
        """广播聊天消息"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message']
        }))
    
    async def typing_indicator_broadcast(self, event):
        """广播打字指示器"""
        # 不向发送者广播自己的打字状态
        if event['user_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'typing_indicator',
                'user_id': event['user_id'],
                'username': event['username'],
                'is_typing': event['is_typing']
            }))
    
    async def read_receipt_broadcast(self, event):
        """广播已读回执"""
        await self.send(text_data=json.dumps({
            'type': 'read_receipt',
            'message_id': event['message_id'],
            'reader_id': event['reader_id'],
            'read_at': event['read_at']
        }))
    
    async def notification_broadcast(self, event):
        """广播通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))
    
    # 数据库操作方法
    @database_sync_to_async
    def create_connection_record(self):
        """创建连接记录"""
        return WebSocketConnection.objects.create(
            user=self.user,
            connection_type='chat',
            channel_name=self.channel_name,
            client_ip=self.get_client_ip(),
            user_agent=self.get_user_agent()
        )
    
    @database_sync_to_async
    def update_connection_record(self):
        """更新连接记录"""
        try:
            connection = WebSocketConnection.objects.get(
                user=self.user,
                channel_name=self.channel_name,
                status='connected'
            )
            connection.status = 'disconnected'
            connection.disconnected_at = timezone.now()
            connection.connection_duration = connection.disconnected_at - connection.created_at
            connection.save()
        except WebSocketConnection.DoesNotExist:
            pass
    
    @database_sync_to_async
    def update_heartbeat(self):
        """更新心跳时间"""
        try:
            connection = WebSocketConnection.objects.get(
                user=self.user,
                channel_name=self.channel_name,
                status='connected'
            )
            connection.last_heartbeat = timezone.now()
            connection.save()
        except WebSocketConnection.DoesNotExist:
            pass
    
    @database_sync_to_async
    def save_chat_message(self, content, message_type):
        """保存聊天消息"""
        from apps.chat.models import ChatMessage, ChatSession
        
        try:
            session = ChatSession.objects.get(id=self.session_id)
            message = ChatMessage.objects.create(
                session=session,
                sender=self.user,
                content=content,
                message_type=message_type
            )
            return message
        except ChatSession.DoesNotExist:
            raise Exception("聊天会话不存在")
    
    @database_sync_to_async
    def mark_message_as_read(self, message_id):
        """标记消息为已读"""
        from apps.chat.models import ChatMessage
        
        try:
            message = ChatMessage.objects.get(id=message_id)
            if message.receiver == self.user:
                message.is_read = True
                message.read_at = timezone.now()
                message.save()
        except ChatMessage.DoesNotExist:
            pass
    
    @database_sync_to_async
    def update_online_status(self, status):
        """更新在线状态"""
        online_status, created = UserOnlineStatus.objects.get_or_create(
            user=self.user,
            defaults={'status': status}
        )
        if not created:
            online_status.status = status
            online_status.last_seen = timezone.now()
            online_status.save()
    
    async def send_push_notification(self, message):
        """发送推送通知"""
        # 这里应该实现推送通知逻辑
        # 可以集成极光推送、友盟推送等第三方服务
        pass
    
    async def send_error(self, error_message):
        """发送错误消息"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': error_message,
            'timestamp': timezone.now().isoformat()
        }))
    
    def get_client_ip(self):
        """获取客户端IP"""
        headers = dict(self.scope["headers"])
        if b"x-forwarded-for" in headers:
            return headers[b"x-forwarded-for"].decode().split(",")[0].strip()
        elif b"x-real-ip" in headers:
            return headers[b"x-real-ip"].decode()
        else:
            return self.scope["client"][0]
    
    def get_user_agent(self):
        """获取用户代理"""
        headers = dict(self.scope["headers"])
        return headers.get(b"user-agent", b"").decode()


class CallConsumer(AsyncWebsocketConsumer):
    """通话WebSocket消费者"""
    
    async def connect(self):
        """WebSocket连接"""
        self.user = self.scope["user"]
        if not self.user.is_authenticated:
            await self.close()
            return
        
        # 获取通话ID和类型
        self.call_id = self.scope['url_route']['kwargs']['call_id']
        self.call_type = self.scope['url_route']['kwargs'].get('call_type', 'voice')
        self.room_group_name = f'call_{self.call_id}'
        
        # 加入房间组
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # 记录连接
        await self.create_connection_record()
        
        logger.info(f"用户 {self.user.username} 连接到{self.call_type}通话 {self.call_id}")
    
    async def disconnect(self, close_code):
        """WebSocket断开连接"""
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        
        # 更新连接记录
        await self.update_connection_record()
        
        # 结束通话
        await self.end_call('disconnected')
        
        logger.info(f"用户 {self.user.username} 断开{self.call_type}通话 {self.call_id} 连接")
    
    async def receive(self, text_data):
        """接收WebSocket消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'call_offer':
                await self.handle_call_offer(data)
            elif message_type == 'call_answer':
                await self.handle_call_answer(data)
            elif message_type == 'call_candidate':
                await self.handle_call_candidate(data)
            elif message_type == 'call_end':
                await self.handle_call_end(data)
            elif message_type == 'call_reject':
                await self.handle_call_reject(data)
            else:
                await self.send_error('未知的消息类型')
                
        except json.JSONDecodeError:
            await self.send_error('无效的JSON格式')
        except Exception as e:
            logger.error(f"处理通话消息失败: {str(e)}")
            await self.send_error('消息处理失败')
    
    async def handle_call_offer(self, data):
        """处理通话邀请"""
        # 广播通话邀请
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'call_offer_broadcast',
                'offer': data.get('offer'),
                'caller_id': self.user.id,
                'caller_username': self.user.username
            }
        )
        
        # 更新通话状态
        await self.update_call_status('ringing')
    
    async def handle_call_answer(self, data):
        """处理通话接听"""
        # 广播通话接听
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'call_answer_broadcast',
                'answer': data.get('answer'),
                'callee_id': self.user.id,
                'callee_username': self.user.username
            }
        )
        
        # 更新通话状态
        await self.update_call_status('connected')
    
    async def handle_call_candidate(self, data):
        """处理ICE候选"""
        # 广播ICE候选
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'call_candidate_broadcast',
                'candidate': data.get('candidate'),
                'user_id': self.user.id
            }
        )
    
    async def handle_call_end(self, data):
        """处理通话结束"""
        await self.end_call('ended')
        
        # 广播通话结束
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'call_end_broadcast',
                'user_id': self.user.id,
                'reason': data.get('reason', 'user_ended')
            }
        )
    
    async def handle_call_reject(self, data):
        """处理通话拒绝"""
        await self.update_call_status('rejected')
        
        # 广播通话拒绝
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'call_reject_broadcast',
                'user_id': self.user.id,
                'reason': data.get('reason', 'user_rejected')
            }
        )
    
    # 广播处理方法
    async def call_offer_broadcast(self, event):
        """广播通话邀请"""
        if event['caller_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'call_offer',
                'offer': event['offer'],
                'caller_id': event['caller_id'],
                'caller_username': event['caller_username']
            }))
    
    async def call_answer_broadcast(self, event):
        """广播通话接听"""
        if event['callee_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'call_answer',
                'answer': event['answer'],
                'callee_id': event['callee_id'],
                'callee_username': event['callee_username']
            }))
    
    async def call_candidate_broadcast(self, event):
        """广播ICE候选"""
        if event['user_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'call_candidate',
                'candidate': event['candidate']
            }))
    
    async def call_end_broadcast(self, event):
        """广播通话结束"""
        await self.send(text_data=json.dumps({
            'type': 'call_end',
            'user_id': event['user_id'],
            'reason': event['reason']
        }))
    
    async def call_reject_broadcast(self, event):
        """广播通话拒绝"""
        await self.send(text_data=json.dumps({
            'type': 'call_reject',
            'user_id': event['user_id'],
            'reason': event['reason']
        }))
    
    # 数据库操作方法
    @database_sync_to_async
    def create_connection_record(self):
        """创建连接记录"""
        connection_type = 'video_call' if self.call_type == 'video' else 'voice_call'
        return WebSocketConnection.objects.create(
            user=self.user,
            connection_type=connection_type,
            channel_name=self.channel_name,
            client_ip=self.get_client_ip(),
            user_agent=self.get_user_agent()
        )
    
    @database_sync_to_async
    def update_connection_record(self):
        """更新连接记录"""
        try:
            connection = WebSocketConnection.objects.get(
                user=self.user,
                channel_name=self.channel_name,
                status='connected'
            )
            connection.status = 'disconnected'
            connection.disconnected_at = timezone.now()
            connection.connection_duration = connection.disconnected_at - connection.created_at
            connection.save()
        except WebSocketConnection.DoesNotExist:
            pass
    
    @database_sync_to_async
    def update_call_status(self, status):
        """更新通话状态"""
        try:
            if self.call_type == 'video':
                call = VideoCall.objects.get(call_id=self.call_id)
            else:
                call = VoiceCall.objects.get(call_id=self.call_id)
            
            call.status = status
            if status == 'connected' and not call.answered_at:
                call.answered_at = timezone.now()
            call.save()
        except (VoiceCall.DoesNotExist, VideoCall.DoesNotExist):
            pass
    
    @database_sync_to_async
    def end_call(self, reason):
        """结束通话"""
        try:
            if self.call_type == 'video':
                call = VideoCall.objects.get(call_id=self.call_id)
            else:
                call = VoiceCall.objects.get(call_id=self.call_id)
            
            if call.status not in ['ended', 'rejected', 'failed']:
                call.status = 'ended'
                call.ended_at = timezone.now()
                call.end_reason = reason
                
                if call.answered_at:
                    call.duration = call.ended_at - call.answered_at
                
                call.save()
        except (VoiceCall.DoesNotExist, VideoCall.DoesNotExist):
            pass
    
    async def send_error(self, error_message):
        """发送错误消息"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': error_message,
            'timestamp': timezone.now().isoformat()
        }))
    
    def get_client_ip(self):
        """获取客户端IP"""
        headers = dict(self.scope["headers"])
        if b"x-forwarded-for" in headers:
            return headers[b"x-forwarded-for"].decode().split(",")[0].strip()
        elif b"x-real-ip" in headers:
            return headers[b"x-real-ip"].decode()
        else:
            return self.scope["client"][0]
    
    def get_user_agent(self):
        """获取用户代理"""
        headers = dict(self.scope["headers"])
        return headers.get(b"user-agent", b"").decode()
