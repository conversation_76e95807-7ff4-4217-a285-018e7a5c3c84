"""
实时通讯模块视图
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils import timezone
from django.db.models import Count, Avg, Q
from django.db import models

from .models import WebSocketConnection, VoiceCall, VideoCall, VoiceMessage, UserOnlineStatus


class ConnectionManagementAPIView(APIView):
    """WebSocket连接管理API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取连接状态"""
        try:
            user = request.user
            
            # 获取用户的连接信息
            connections = WebSocketConnection.objects.filter(
                user=user,
                status='connected'
            )
            
            connection_data = []
            for conn in connections:
                connection_data.append({
                    'connection_id': str(conn.connection_id),
                    'connection_type': conn.connection_type,
                    'created_at': conn.created_at.isoformat(),
                    'last_heartbeat': conn.last_heartbeat.isoformat(),
                    'messages_sent': conn.messages_sent,
                    'messages_received': conn.messages_received
                })
            
            return Response({
                'code': 200,
                'message': '获取连接状态成功',
                'data': {
                    'connections': connection_data,
                    'total_connections': len(connection_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取连接状态失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CallManagementAPIView(APIView):
    """通话管理API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取通话记录"""
        try:
            user = request.user
            call_type = request.GET.get('type', 'all')  # voice, video, all
            limit = int(request.GET.get('limit', 20))
            
            # 获取语音通话记录
            voice_calls = []
            if call_type in ['voice', 'all']:
                voice_call_qs = VoiceCall.objects.filter(
                    Q(caller=user) | Q(callee=user)
                ).order_by('-initiated_at')[:limit]
                
                for call in voice_call_qs:
                    voice_calls.append({
                        'call_id': str(call.call_id),
                        'type': 'voice',
                        'caller_id': call.caller.id,
                        'callee_id': call.callee.id,
                        'status': call.status,
                        'initiated_at': call.initiated_at.isoformat(),
                        'answered_at': call.answered_at.isoformat() if call.answered_at else None,
                        'ended_at': call.ended_at.isoformat() if call.ended_at else None,
                        'duration': str(call.duration) if call.duration else None,
                        'call_quality': call.call_quality
                    })
            
            # 获取视频通话记录
            video_calls = []
            if call_type in ['video', 'all']:
                video_call_qs = VideoCall.objects.filter(
                    Q(caller=user) | Q(callee=user)
                ).order_by('-initiated_at')[:limit]
                
                for call in video_call_qs:
                    video_calls.append({
                        'call_id': str(call.call_id),
                        'type': 'video',
                        'caller_id': call.caller.id,
                        'callee_id': call.callee.id,
                        'status': call.status,
                        'initiated_at': call.initiated_at.isoformat(),
                        'answered_at': call.answered_at.isoformat() if call.answered_at else None,
                        'ended_at': call.ended_at.isoformat() if call.ended_at else None,
                        'duration': str(call.duration) if call.duration else None,
                        'call_quality': call.call_quality,
                        'video_resolution': call.video_resolution
                    })
            
            return Response({
                'code': 200,
                'message': '获取通话记录成功',
                'data': {
                    'voice_calls': voice_calls,
                    'video_calls': video_calls,
                    'total_calls': len(voice_calls) + len(video_calls)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取通话记录失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VoiceMessageAPIView(APIView):
    """语音消息API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取语音消息"""
        try:
            user = request.user
            limit = int(request.GET.get('limit', 20))
            
            # 获取用户的语音消息
            voice_messages = VoiceMessage.objects.filter(
                Q(sender=user) | Q(receiver=user)
            ).order_by('-created_at')[:limit]
            
            message_data = []
            for msg in voice_messages:
                message_data.append({
                    'message_id': str(msg.message_id),
                    'sender_id': msg.sender.id,
                    'receiver_id': msg.receiver.id,
                    'duration': msg.duration,
                    'file_size': msg.file_size,
                    'format': msg.format,
                    'is_read': msg.is_read,
                    'transcription': msg.transcription,
                    'created_at': msg.created_at.isoformat(),
                    'read_at': msg.read_at.isoformat() if msg.read_at else None
                })
            
            return Response({
                'code': 200,
                'message': '获取语音消息成功',
                'data': {
                    'voice_messages': message_data,
                    'total_messages': len(message_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取语音消息失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OnlineStatusAPIView(APIView):
    """在线状态API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取在线状态"""
        try:
            user = request.user
            
            # 获取用户的详细在线状态
            online_status, created = UserOnlineStatus.objects.get_or_create(
                user=user,
                defaults={'status': 'offline'}
            )
            
            return Response({
                'code': 200,
                'message': '获取在线状态成功',
                'data': {
                    'status': online_status.status,
                    'last_seen': online_status.last_seen.isoformat(),
                    'current_activity': online_status.current_activity,
                    'device_type': online_status.device_type,
                    'app_version': online_status.app_version
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取在线状态失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """更新在线状态"""
        try:
            user = request.user
            new_status = request.data.get('status', 'online')
            current_activity = request.data.get('current_activity', '')
            device_type = request.data.get('device_type', '')
            app_version = request.data.get('app_version', '')
            
            # 更新用户的在线状态
            online_status, created = UserOnlineStatus.objects.get_or_create(
                user=user,
                defaults={
                    'status': new_status,
                    'current_activity': current_activity,
                    'device_type': device_type,
                    'app_version': app_version
                }
            )
            
            if not created:
                online_status.status = new_status
                online_status.current_activity = current_activity
                online_status.device_type = device_type
                online_status.app_version = app_version
                online_status.save()
            
            return Response({
                'code': 200,
                'message': '更新在线状态成功',
                'data': None
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'更新在线状态失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
