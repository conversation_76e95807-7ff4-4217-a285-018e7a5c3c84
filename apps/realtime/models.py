"""
实时通讯系统模型
支持WebSocket实时聊天、语音通话、视频通话等功能
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class WebSocketConnection(models.Model):
    """WebSocket连接管理"""
    CONNECTION_TYPES = [
        ('chat', '聊天连接'),
        ('video_call', '视频通话'),
        ('voice_call', '语音通话'),
        ('notification', '通知连接'),
    ]
    
    STATUS_CHOICES = [
        ('connected', '已连接'),
        ('disconnected', '已断开'),
        ('reconnecting', '重连中'),
    ]
    
    connection_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='连接ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    connection_type = models.CharField(max_length=20, choices=CONNECTION_TYPES, verbose_name='连接类型')
    
    # 连接信息
    channel_name = models.CharField(max_length=255, verbose_name='频道名称')
    client_ip = models.GenericIPAddressField(verbose_name='客户端IP')
    user_agent = models.TextField(verbose_name='用户代理')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='connected', verbose_name='连接状态')
    last_heartbeat = models.DateTimeField(auto_now=True, verbose_name='最后心跳时间')
    
    # 统计信息
    messages_sent = models.IntegerField(default=0, verbose_name='发送消息数')
    messages_received = models.IntegerField(default=0, verbose_name='接收消息数')
    connection_duration = models.DurationField(null=True, blank=True, verbose_name='连接时长')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='连接时间')
    disconnected_at = models.DateTimeField(null=True, blank=True, verbose_name='断开时间')
    
    class Meta:
        verbose_name = 'WebSocket连接'
        verbose_name_plural = 'WebSocket连接'
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['connection_type', 'status']),
            models.Index(fields=['last_heartbeat']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_connection_type_display()}"


class VoiceCall(models.Model):
    """语音通话记录"""
    CALL_STATUS = [
        ('initiating', '发起中'),
        ('ringing', '响铃中'),
        ('connected', '通话中'),
        ('ended', '已结束'),
        ('missed', '未接听'),
        ('rejected', '已拒绝'),
        ('failed', '通话失败'),
    ]
    
    CALL_QUALITY = [
        ('excellent', '优秀'),
        ('good', '良好'),
        ('fair', '一般'),
        ('poor', '较差'),
    ]
    
    call_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='通话ID')
    caller = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='initiated_voice_calls',
        verbose_name='发起人'
    )
    callee = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_voice_calls',
        verbose_name='接听人'
    )
    
    # 通话状态
    status = models.CharField(max_length=20, choices=CALL_STATUS, default='initiating', verbose_name='通话状态')
    
    # 时间信息
    initiated_at = models.DateTimeField(auto_now_add=True, verbose_name='发起时间')
    answered_at = models.DateTimeField(null=True, blank=True, verbose_name='接听时间')
    ended_at = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    duration = models.DurationField(null=True, blank=True, verbose_name='通话时长')
    
    # 通话质量
    call_quality = models.CharField(max_length=20, choices=CALL_QUALITY, null=True, blank=True, verbose_name='通话质量')
    network_quality_caller = models.FloatField(null=True, blank=True, verbose_name='发起人网络质量')
    network_quality_callee = models.FloatField(null=True, blank=True, verbose_name='接听人网络质量')
    
    # 技术信息
    signaling_server = models.CharField(max_length=100, blank=True, verbose_name='信令服务器')
    media_server = models.CharField(max_length=100, blank=True, verbose_name='媒体服务器')
    codec_used = models.CharField(max_length=50, blank=True, verbose_name='使用编解码器')
    
    # 结束原因
    end_reason = models.CharField(max_length=100, blank=True, verbose_name='结束原因')
    
    class Meta:
        verbose_name = '语音通话'
        verbose_name_plural = '语音通话'
        ordering = ['-initiated_at']
        indexes = [
            models.Index(fields=['caller', 'status']),
            models.Index(fields=['callee', 'status']),
            models.Index(fields=['initiated_at']),
        ]
    
    def __str__(self):
        return f"{self.caller.username} -> {self.callee.username} ({self.get_status_display()})"


class VideoCall(models.Model):
    """视频通话记录"""
    CALL_STATUS = [
        ('initiating', '发起中'),
        ('ringing', '响铃中'),
        ('connected', '通话中'),
        ('ended', '已结束'),
        ('missed', '未接听'),
        ('rejected', '已拒绝'),
        ('failed', '通话失败'),
    ]
    
    CALL_QUALITY = [
        ('excellent', '优秀'),
        ('good', '良好'),
        ('fair', '一般'),
        ('poor', '较差'),
    ]
    
    call_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='通话ID')
    caller = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='initiated_video_calls',
        verbose_name='发起人'
    )
    callee = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_video_calls',
        verbose_name='接听人'
    )
    
    # 通话状态
    status = models.CharField(max_length=20, choices=CALL_STATUS, default='initiating', verbose_name='通话状态')
    
    # 时间信息
    initiated_at = models.DateTimeField(auto_now_add=True, verbose_name='发起时间')
    answered_at = models.DateTimeField(null=True, blank=True, verbose_name='接听时间')
    ended_at = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    duration = models.DurationField(null=True, blank=True, verbose_name='通话时长')
    
    # 视频质量
    call_quality = models.CharField(max_length=20, choices=CALL_QUALITY, null=True, blank=True, verbose_name='通话质量')
    video_resolution = models.CharField(max_length=20, blank=True, verbose_name='视频分辨率')
    frame_rate = models.IntegerField(null=True, blank=True, verbose_name='帧率')
    bitrate = models.IntegerField(null=True, blank=True, verbose_name='比特率')
    
    # 网络质量
    network_quality_caller = models.FloatField(null=True, blank=True, verbose_name='发起人网络质量')
    network_quality_callee = models.FloatField(null=True, blank=True, verbose_name='接听人网络质量')
    packet_loss_rate = models.FloatField(null=True, blank=True, verbose_name='丢包率')
    
    # 技术信息
    signaling_server = models.CharField(max_length=100, blank=True, verbose_name='信令服务器')
    media_server = models.CharField(max_length=100, blank=True, verbose_name='媒体服务器')
    video_codec = models.CharField(max_length=50, blank=True, verbose_name='视频编解码器')
    audio_codec = models.CharField(max_length=50, blank=True, verbose_name='音频编解码器')
    
    # 结束原因
    end_reason = models.CharField(max_length=100, blank=True, verbose_name='结束原因')
    
    # 录制信息
    is_recorded = models.BooleanField(default=False, verbose_name='是否录制')
    recording_url = models.URLField(blank=True, verbose_name='录制文件URL')
    
    class Meta:
        verbose_name = '视频通话'
        verbose_name_plural = '视频通话'
        ordering = ['-initiated_at']
        indexes = [
            models.Index(fields=['caller', 'status']),
            models.Index(fields=['callee', 'status']),
            models.Index(fields=['initiated_at']),
        ]
    
    def __str__(self):
        return f"{self.caller.username} -> {self.callee.username} ({self.get_status_display()})"


class VoiceMessage(models.Model):
    """语音消息"""
    message_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='消息ID')
    sender = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='sent_voice_messages',
        verbose_name='发送者'
    )
    receiver = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_voice_messages',
        verbose_name='接收者'
    )
    
    # 语音文件信息
    audio_file = models.FileField(upload_to='voice_messages/', verbose_name='语音文件')
    duration = models.FloatField(verbose_name='时长(秒)')
    file_size = models.IntegerField(verbose_name='文件大小(字节)')
    
    # 音频参数
    sample_rate = models.IntegerField(default=16000, verbose_name='采样率')
    bit_rate = models.IntegerField(default=128, verbose_name='比特率')
    format = models.CharField(max_length=10, default='mp3', verbose_name='音频格式')
    
    # 状态信息
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    # 语音识别结果
    transcription = models.TextField(blank=True, verbose_name='语音转文字')
    transcription_confidence = models.FloatField(null=True, blank=True, verbose_name='识别置信度')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='发送时间')
    
    class Meta:
        verbose_name = '语音消息'
        verbose_name_plural = '语音消息'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['sender', 'receiver']),
            models.Index(fields=['is_read', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.sender.username} -> {self.receiver.username} ({self.duration}s)"


class RealtimeNotification(models.Model):
    """实时通知"""
    NOTIFICATION_TYPES = [
        ('message', '新消息'),
        ('like', '新喜欢'),
        ('match', '新匹配'),
        ('voice_call', '语音通话'),
        ('video_call', '视频通话'),
        ('system', '系统通知'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    notification_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='通知ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='接收用户')
    
    # 通知内容
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name='通知类型')
    title = models.CharField(max_length=200, verbose_name='通知标题')
    content = models.TextField(verbose_name='通知内容')
    
    # 优先级和状态
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal', verbose_name='优先级')
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    is_delivered = models.BooleanField(default=False, verbose_name='是否已送达')
    
    # 相关数据
    related_user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='triggered_notifications',
        verbose_name='相关用户'
    )
    related_object_type = models.CharField(max_length=50, blank=True, verbose_name='相关对象类型')
    related_object_id = models.IntegerField(null=True, blank=True, verbose_name='相关对象ID')
    
    # 推送信息
    push_data = models.JSONField(default=dict, verbose_name='推送数据')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='送达时间')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    class Meta:
        verbose_name = '实时通知'
        verbose_name_plural = '实时通知'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['priority', 'is_delivered']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.title}"


class UserOnlineStatus(models.Model):
    """用户详细在线状态"""
    STATUS_CHOICES = [
        ('online', '在线'),
        ('away', '离开'),
        ('busy', '忙碌'),
        ('invisible', '隐身'),
        ('offline', '离线'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户', related_name='detailed_online_status')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='offline', verbose_name='在线状态')

    # 状态信息
    last_seen = models.DateTimeField(auto_now=True, verbose_name='最后在线时间')
    current_activity = models.CharField(max_length=100, blank=True, verbose_name='当前活动')

    # 设备信息
    device_type = models.CharField(max_length=50, blank=True, verbose_name='设备类型')
    app_version = models.CharField(max_length=20, blank=True, verbose_name='应用版本')

    # 位置信息
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')
    location_updated_at = models.DateTimeField(null=True, blank=True, verbose_name='位置更新时间')

    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '详细在线状态'
        verbose_name_plural = '详细在线状态'
        indexes = [
            models.Index(fields=['status', 'last_seen']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_status_display()}"


class CallStatistics(models.Model):
    """通话统计"""
    date = models.DateField(verbose_name='统计日期')
    
    # 语音通话统计
    voice_calls_total = models.IntegerField(default=0, verbose_name='语音通话总数')
    voice_calls_answered = models.IntegerField(default=0, verbose_name='语音通话接听数')
    voice_calls_missed = models.IntegerField(default=0, verbose_name='语音通话未接数')
    voice_call_duration_total = models.DurationField(null=True, blank=True, verbose_name='语音通话总时长')
    voice_call_duration_avg = models.DurationField(null=True, blank=True, verbose_name='语音通话平均时长')
    
    # 视频通话统计
    video_calls_total = models.IntegerField(default=0, verbose_name='视频通话总数')
    video_calls_answered = models.IntegerField(default=0, verbose_name='视频通话接听数')
    video_calls_missed = models.IntegerField(default=0, verbose_name='视频通话未接数')
    video_call_duration_total = models.DurationField(null=True, blank=True, verbose_name='视频通话总时长')
    video_call_duration_avg = models.DurationField(null=True, blank=True, verbose_name='视频通话平均时长')
    
    # 语音消息统计
    voice_messages_sent = models.IntegerField(default=0, verbose_name='语音消息发送数')
    voice_messages_received = models.IntegerField(default=0, verbose_name='语音消息接收数')
    
    # 质量统计
    avg_call_quality = models.FloatField(null=True, blank=True, verbose_name='平均通话质量')
    network_issues_count = models.IntegerField(default=0, verbose_name='网络问题次数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '通话统计'
        verbose_name_plural = '通话统计'
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.date} - 通话统计"
