"""
实时通讯模块 - Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html

from .models import (
    WebSocketConnection, VoiceCall, VideoCall, VoiceMessage,
    RealtimeNotification, UserOnlineStatus
)


@admin.register(WebSocketConnection)
class WebSocketConnectionAdmin(admin.ModelAdmin):
    """WebSocket连接管理"""
    list_display = ['user', 'connection_type', 'status', 'created_at', 'last_heartbeat']
    list_filter = ['connection_type', 'status', 'created_at']
    search_fields = ['user__username', 'connection_id']
    readonly_fields = ['connection_id', 'created_at', 'last_heartbeat']
    
    fieldsets = (
        ('连接信息', {
            'fields': ('user', 'connection_id', 'connection_type', 'status')
        }),
        ('统计信息', {
            'fields': ('messages_sent', 'messages_received', 'data_transferred')
        }),
        ('时间信息', {
            'fields': ('created_at', 'last_heartbeat', 'disconnected_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(VoiceCall)
class VoiceCallAdmin(admin.ModelAdmin):
    """语音通话管理"""
    list_display = ['caller', 'callee', 'status', 'duration_display', 'call_quality', 'initiated_at']
    list_filter = ['status', 'call_quality', 'initiated_at']
    search_fields = ['caller__username', 'callee__username']
    readonly_fields = ['call_id', 'initiated_at', 'answered_at', 'ended_at']
    
    fieldsets = (
        ('通话信息', {
            'fields': ('caller', 'callee', 'call_id', 'status')
        }),
        ('通话质量', {
            'fields': ('call_quality', 'audio_codec', 'bandwidth_used')
        }),
        ('时间信息', {
            'fields': ('initiated_at', 'answered_at', 'ended_at', 'duration')
        }),
    )
    
    def duration_display(self, obj):
        """显示通话时长"""
        if obj.duration:
            total_seconds = int(obj.duration.total_seconds())
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            return f"{minutes}分{seconds}秒"
        return '-'
    duration_display.short_description = '通话时长'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('caller', 'callee')


@admin.register(VideoCall)
class VideoCallAdmin(admin.ModelAdmin):
    """视频通话管理"""
    list_display = ['caller', 'callee', 'status', 'duration_display', 'video_resolution', 'initiated_at']
    list_filter = ['status', 'video_resolution', 'initiated_at']
    search_fields = ['caller__username', 'callee__username']
    readonly_fields = ['call_id', 'initiated_at', 'answered_at', 'ended_at']
    
    fieldsets = (
        ('通话信息', {
            'fields': ('caller', 'callee', 'call_id', 'status')
        }),
        ('视频质量', {
            'fields': ('call_quality', 'video_resolution', 'video_codec', 'audio_codec')
        }),
        ('网络信息', {
            'fields': ('bandwidth_used', 'packet_loss_rate')
        }),
        ('时间信息', {
            'fields': ('initiated_at', 'answered_at', 'ended_at', 'duration')
        }),
    )
    
    def duration_display(self, obj):
        """显示通话时长"""
        if obj.duration:
            total_seconds = int(obj.duration.total_seconds())
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            return f"{minutes}分{seconds}秒"
        return '-'
    duration_display.short_description = '通话时长'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('caller', 'callee')


@admin.register(VoiceMessage)
class VoiceMessageAdmin(admin.ModelAdmin):
    """语音消息管理"""
    list_display = ['sender', 'receiver', 'duration', 'file_size_display', 'is_read', 'created_at']
    list_filter = ['is_read', 'format', 'created_at']
    search_fields = ['sender__username', 'receiver__username']
    readonly_fields = ['message_id', 'created_at', 'read_at']
    
    fieldsets = (
        ('消息信息', {
            'fields': ('sender', 'receiver', 'message_id')
        }),
        ('语音信息', {
            'fields': ('duration', 'file_size', 'format', 'file_path')
        }),
        ('状态信息', {
            'fields': ('is_read', 'transcription')
        }),
        ('时间信息', {
            'fields': ('created_at', 'read_at')
        }),
    )
    
    def file_size_display(self, obj):
        """显示文件大小"""
        if obj.file_size:
            if obj.file_size < 1024:
                return f"{obj.file_size} B"
            elif obj.file_size < 1024 * 1024:
                return f"{obj.file_size / 1024:.1f} KB"
            else:
                return f"{obj.file_size / (1024 * 1024):.1f} MB"
        return '-'
    file_size_display.short_description = '文件大小'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('sender', 'receiver')


@admin.register(RealtimeNotification)
class RealtimeNotificationAdmin(admin.ModelAdmin):
    """实时通知管理"""
    list_display = ['user', 'notification_type', 'title', 'is_read', 'created_at']
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['user__username', 'title']
    readonly_fields = ['created_at', 'read_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(UserOnlineStatus)
class UserOnlineStatusAdmin(admin.ModelAdmin):
    """用户在线状态管理"""
    list_display = ['user', 'status', 'current_activity', 'device_type', 'last_seen']
    list_filter = ['status', 'device_type', 'last_seen']
    search_fields = ['user__username']
    readonly_fields = ['last_seen', 'updated_at']
    
    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'status', 'current_activity')
        }),
        ('设备信息', {
            'fields': ('device_type', 'app_version')
        }),
        ('位置信息', {
            'fields': ('latitude', 'longitude', 'location_updated_at')
        }),
        ('时间信息', {
            'fields': ('last_seen', 'updated_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
