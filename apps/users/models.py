from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import json


class User(AbstractUser):
    """用户模型"""
    GENDER_CHOICES = [
        (1, '男'),
        (2, '女'),
    ]

    # 解决与Django默认User模型的冲突
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='xiangqin_users',
        related_query_name='xiangqin_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='xiangqin_users',
        related_query_name='xiangqin_user',
    )
    
    MARRIAGE_STATUS_CHOICES = [
        (1, '未婚'),
        (2, '离异'),
        (3, '丧偶'),
    ]
    
    EDUCATION_CHOICES = [
        (1, '高中及以下'),
        (2, '大专'),
        (3, '本科'),
        (4, '硕士'),
        (5, '博士'),
    ]
    
    INCOME_CHOICES = [
        (1, '3000以下'),
        (2, '3000-5000'),
        (3, '5000-8000'),
        (4, '8000-12000'),
        (5, '12000-20000'),
        (6, '20000以上'),
    ]
    
    VIP_LEVEL_CHOICES = [
        (0, '普通用户'),
        (1, '月度VIP'),
        (2, '季度VIP'),
        (3, '年度VIP'),
    ]
    
    STATUS_CHOICES = [
        (1, '正常'),
        (2, '封禁'),
        (3, '注销'),
    ]

    USER_TYPE_CHOICES = [
        ('normal', '普通用户'),
        ('parent', '家长用户'),
        ('matchmaker', '红娘'),
    ]
    
    # 微信相关
    openid = models.CharField(max_length=100, unique=True, verbose_name='微信OpenID')
    unionid = models.CharField(max_length=100, blank=True, null=True, verbose_name='微信UnionID')
    
    # 基本信息
    nickname = models.CharField(max_length=50, verbose_name='昵称')
    avatar = models.URLField(blank=True, null=True, verbose_name='头像')
    gender = models.IntegerField(choices=GENDER_CHOICES, default=1, verbose_name='性别')
    age = models.IntegerField(validators=[MinValueValidator(18), MaxValueValidator(100)], 
                             null=True, blank=True, verbose_name='年龄')
    birthday = models.DateField(null=True, blank=True, verbose_name='生日')
    
    # 详细信息
    location = models.CharField(max_length=100, blank=True, verbose_name='位置')
    profession = models.CharField(max_length=100, blank=True, verbose_name='职业')
    education = models.IntegerField(choices=EDUCATION_CHOICES, null=True, blank=True, verbose_name='学历')
    height = models.IntegerField(validators=[MinValueValidator(100), MaxValueValidator(250)], 
                                null=True, blank=True, verbose_name='身高(cm)')
    weight = models.IntegerField(validators=[MinValueValidator(30), MaxValueValidator(200)], 
                                null=True, blank=True, verbose_name='体重(kg)')
    income = models.IntegerField(choices=INCOME_CHOICES, null=True, blank=True, verbose_name='收入')
    marriage_status = models.IntegerField(choices=MARRIAGE_STATUS_CHOICES, default=1, verbose_name='婚姻状况')
    
    # 个人介绍
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    photos = models.JSONField(default=list, verbose_name='相册')
    
    # 认证状态
    is_verified = models.BooleanField(default=False, verbose_name='是否认证')
    verified_at = models.DateTimeField(null=True, blank=True, verbose_name='认证时间')
    
    # VIP相关
    vip_level = models.IntegerField(choices=VIP_LEVEL_CHOICES, default=0, verbose_name='VIP等级')
    vip_expire_time = models.DateTimeField(null=True, blank=True, verbose_name='VIP过期时间')
    
    # 积分金币
    points = models.IntegerField(default=0, verbose_name='积分')
    coins = models.IntegerField(default=0, verbose_name='金币')
    
    # 状态
    status = models.IntegerField(choices=STATUS_CHOICES, default=1, verbose_name='状态')
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='normal', verbose_name='用户类型')
    
    # 位置信息
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')

    # 新增字段支持前端功能
    interests = models.JSONField(default=list, verbose_name='兴趣标签列表')
    income_range = models.CharField(max_length=20, blank=True, verbose_name='收入范围')
    education_level = models.CharField(max_length=20, blank=True, verbose_name='学历水平')
    simple_online_status = models.CharField(max_length=20, choices=[('online', '在线'), ('offline', '离线'), ('busy', '忙碌')], default='offline', verbose_name='简单在线状态')
    last_location_update = models.DateTimeField(null=True, blank=True, verbose_name='最后位置更新时间')
    
    # 统计信息
    profile_views = models.IntegerField(default=0, verbose_name='资料查看次数')
    likes_received = models.IntegerField(default=0, verbose_name='收到喜欢数')
    likes_sent = models.IntegerField(default=0, verbose_name='发出喜欢数')
    matches_count = models.IntegerField(default=0, verbose_name='匹配数')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    last_active = models.DateTimeField(auto_now=True, verbose_name='最后活跃时间')
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.nickname or self.username
    
    @property
    def is_vip(self):
        """是否是VIP用户"""
        from django.utils import timezone
        return self.vip_level > 0 and (self.vip_expire_time is None or self.vip_expire_time > timezone.now())
    
    def get_photos_list(self):
        """获取相册列表"""
        if isinstance(self.photos, str):
            try:
                return json.loads(self.photos)
            except:
                return []
        return self.photos or []
    
    def add_photo(self, photo_url):
        """添加照片"""
        photos = self.get_photos_list()
        if photo_url not in photos:
            photos.append(photo_url)
            self.photos = photos
            self.save()
    
    def remove_photo(self, photo_url):
        """删除照片"""
        photos = self.get_photos_list()
        if photo_url in photos:
            photos.remove(photo_url)
            self.photos = photos
            self.save()


class UserPreference(models.Model):
    """用户偏好设置"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preference', verbose_name='用户')
    
    # 年龄偏好
    min_age = models.IntegerField(default=18, validators=[MinValueValidator(18), MaxValueValidator(100)], 
                                 verbose_name='最小年龄')
    max_age = models.IntegerField(default=35, validators=[MinValueValidator(18), MaxValueValidator(100)], 
                                 verbose_name='最大年龄')
    
    # 性别偏好
    preferred_gender = models.IntegerField(choices=User.GENDER_CHOICES, null=True, blank=True, 
                                         verbose_name='偏好性别')
    
    # 地区偏好
    preferred_location = models.CharField(max_length=100, blank=True, verbose_name='偏好地区')
    distance_range = models.IntegerField(default=50, verbose_name='距离范围(km)')
    
    # 学历偏好
    preferred_education = models.IntegerField(choices=User.EDUCATION_CHOICES, null=True, blank=True, 
                                            verbose_name='偏好学历')
    
    # 收入偏好
    preferred_income = models.IntegerField(choices=User.INCOME_CHOICES, null=True, blank=True, 
                                         verbose_name='偏好收入')
    
    # 身高偏好
    min_height = models.IntegerField(null=True, blank=True, verbose_name='最小身高')
    max_height = models.IntegerField(null=True, blank=True, verbose_name='最大身高')
    
    # 其他偏好
    only_verified = models.BooleanField(default=False, verbose_name='只看认证用户')
    only_with_photos = models.BooleanField(default=True, verbose_name='只看有照片用户')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_preferences'
        verbose_name = '用户偏好'
        verbose_name_plural = '用户偏好'
    
    def __str__(self):
        return f'{self.user.nickname}的偏好设置'


class UserAction(models.Model):
    """用户行为记录"""
    ACTION_CHOICES = [
        ('like', '喜欢'),
        ('pass', '跳过'),
        ('super_like', '超级喜欢'),
        ('visit', '访问'),
        ('block', '拉黑'),
        ('report', '举报'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='actions', verbose_name='用户')
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_actions', 
                                   verbose_name='目标用户')
    action_type = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name='行为类型')
    message = models.TextField(blank=True, verbose_name='附言')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'user_actions'
        verbose_name = '用户行为'
        verbose_name_plural = '用户行为'
        unique_together = ['user', 'target_user', 'action_type']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} {self.get_action_type_display()} {self.target_user.nickname}'


class UserVerification(models.Model):
    """用户认证记录"""
    VERIFICATION_TYPES = [
        ('id_card', '身份证认证'),
        ('phone', '手机认证'),
        ('education', '学历认证'),
        ('profession', '职业认证'),
        ('photo', '真人认证'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verifications', verbose_name='用户')
    verification_type = models.CharField(max_length=20, choices=VERIFICATION_TYPES, verbose_name='认证类型')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    data = models.JSONField(default=dict, verbose_name='认证数据')
    reason = models.TextField(blank=True, verbose_name='拒绝原因')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_verifications'
        verbose_name = '用户认证'
        verbose_name_plural = '用户认证'
        unique_together = ['user', 'verification_type']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.get_verification_type_display()}'


class ParentProfile(models.Model):
    """家长资料"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='parent_profile', verbose_name='用户')

    # 家长基本信息
    real_name = models.CharField(max_length=50, verbose_name='真实姓名')
    relationship = models.CharField(max_length=20, default='父母', verbose_name='与子女关系')
    description = models.TextField(max_length=500, blank=True, verbose_name='家长介绍')

    # 成功案例
    success_count = models.IntegerField(default=0, verbose_name='成功案例数')

    # 认证状态
    is_verified = models.BooleanField(default=False, verbose_name='是否认证')
    verified_at = models.DateTimeField(null=True, blank=True, verbose_name='认证时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'parent_profiles'
        verbose_name = '家长资料'
        verbose_name_plural = '家长资料'

    def __str__(self):
        return f'{self.real_name}({self.user.nickname})'


class ChildProfile(models.Model):
    """子女资料"""
    parent = models.ForeignKey(ParentProfile, on_delete=models.CASCADE, related_name='children', verbose_name='家长')

    # 基本信息
    name = models.CharField(max_length=50, verbose_name='姓名')
    avatar = models.URLField(blank=True, null=True, verbose_name='头像')
    gender = models.IntegerField(choices=User.GENDER_CHOICES, verbose_name='性别')
    age = models.IntegerField(validators=[MinValueValidator(18), MaxValueValidator(100)], verbose_name='年龄')
    birthday = models.DateField(null=True, blank=True, verbose_name='生日')

    # 详细信息
    location = models.CharField(max_length=100, verbose_name='位置')
    profession = models.CharField(max_length=100, verbose_name='职业')
    education = models.IntegerField(choices=User.EDUCATION_CHOICES, verbose_name='学历')
    height = models.IntegerField(validators=[MinValueValidator(100), MaxValueValidator(250)],
                                null=True, blank=True, verbose_name='身高(cm)')
    weight = models.IntegerField(validators=[MinValueValidator(30), MaxValueValidator(200)],
                                null=True, blank=True, verbose_name='体重(kg)')
    income = models.IntegerField(choices=User.INCOME_CHOICES, null=True, blank=True, verbose_name='收入')
    marriage_status = models.IntegerField(choices=User.MARRIAGE_STATUS_CHOICES, default=1, verbose_name='婚姻状况')

    # 个人介绍
    bio = models.TextField(max_length=500, blank=True, verbose_name='个人简介')
    photos = models.JSONField(default=list, verbose_name='相册')

    # 择偶要求
    requirements = models.TextField(max_length=500, blank=True, verbose_name='择偶要求')

    # 状态
    STATUS_CHOICES = [
        ('active', '正在匹配'),
        ('inactive', '暂停匹配'),
        ('matched', '已匹配'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='状态')

    # 统计信息
    profile_views = models.IntegerField(default=0, verbose_name='资料查看次数')
    likes_received = models.IntegerField(default=0, verbose_name='收到喜欢数')
    matches_count = models.IntegerField(default=0, verbose_name='匹配数')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'child_profiles'
        verbose_name = '子女资料'
        verbose_name_plural = '子女资料'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.name}({self.parent.real_name}的子女)'

    def get_photos_list(self):
        """获取相册列表"""
        if isinstance(self.photos, str):
            try:
                return json.loads(self.photos)
            except:
                return []
        return self.photos or []


class MatchmakerProfile(models.Model):
    """红娘资料"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='matchmaker_profile', verbose_name='用户')

    # 红娘基本信息
    real_name = models.CharField(max_length=50, verbose_name='真实姓名')
    company = models.CharField(max_length=100, blank=True, verbose_name='所属公司')
    experience_years = models.IntegerField(default=0, verbose_name='从业年限')

    # 等级和评级
    LEVEL_CHOICES = [
        ('bronze', '铜牌红娘'),
        ('silver', '银牌红娘'),
        ('gold', '金牌红娘'),
        ('diamond', '钻石红娘'),
    ]
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='bronze', verbose_name='红娘等级')
    rating = models.FloatField(default=5.0, validators=[MinValueValidator(0), MaxValueValidator(5)], verbose_name='评分')

    # 专业领域
    specialties = models.JSONField(default=list, verbose_name='专业领域')

    # 服务统计
    total_clients = models.IntegerField(default=0, verbose_name='总客户数')
    active_clients = models.IntegerField(default=0, verbose_name='活跃客户数')
    successful_matches = models.IntegerField(default=0, verbose_name='成功匹配数')
    success_rate = models.FloatField(default=0.0, verbose_name='成功率')

    # 收入统计
    monthly_income = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='月收入')
    total_income = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='总收入')

    # 认证状态
    is_certified = models.BooleanField(default=False, verbose_name='是否认证')
    certified_at = models.DateTimeField(null=True, blank=True, verbose_name='认证时间')
    certification_number = models.CharField(max_length=50, blank=True, verbose_name='认证编号')

    # 服务设置
    service_areas = models.JSONField(default=list, verbose_name='服务地区')
    min_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0, verbose_name='最低服务费')
    max_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0, verbose_name='最高服务费')

    # 状态
    STATUS_CHOICES = [
        ('active', '正常服务'),
        ('busy', '忙碌中'),
        ('inactive', '暂停服务'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='服务状态')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'matchmaker_profiles'
        verbose_name = '红娘资料'
        verbose_name_plural = '红娘资料'

    def __str__(self):
        return f'{self.real_name}({self.get_level_display()})'

    def get_specialties_list(self):
        """获取专业领域列表"""
        if isinstance(self.specialties, str):
            try:
                return json.loads(self.specialties)
            except:
                return []
        return self.specialties or []

    def get_service_areas_list(self):
        """获取服务地区列表"""
        if isinstance(self.service_areas, str):
            try:
                return json.loads(self.service_areas)
            except:
                return []
        return self.service_areas or []


class MatchmakerClient(models.Model):
    """红娘客户"""
    matchmaker = models.ForeignKey(MatchmakerProfile, on_delete=models.CASCADE, related_name='clients', verbose_name='红娘')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matchmaker_services', verbose_name='客户')

    # 服务信息
    SERVICE_PACKAGE_CHOICES = [
        ('basic', '基础版'),
        ('standard', '标准版'),
        ('premium', '高级版'),
        ('vip', 'VIP版'),
    ]
    service_package = models.CharField(max_length=20, choices=SERVICE_PACKAGE_CHOICES, verbose_name='服务套餐')
    service_fee = models.DecimalField(max_digits=8, decimal_places=2, verbose_name='服务费用')

    # 客户状态
    STATUS_CHOICES = [
        ('new', '新客户'),
        ('consulting', '咨询中'),
        ('matching', '匹配中'),
        ('matched', '已匹配'),
        ('completed', '服务完成'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new', verbose_name='客户状态')

    # 优先级
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
    ]
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium', verbose_name='优先级')

    # 服务时间
    start_date = models.DateTimeField(auto_now_add=True, verbose_name='开始服务时间')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='结束服务时间')
    last_contact = models.DateTimeField(null=True, blank=True, verbose_name='最后联系时间')

    # 客户需求
    requirements = models.TextField(max_length=1000, blank=True, verbose_name='客户需求')
    budget = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='预算')

    # 服务记录
    notes = models.TextField(blank=True, verbose_name='服务备注')
    satisfaction_rating = models.IntegerField(null=True, blank=True,
                                            validators=[MinValueValidator(1), MaxValueValidator(5)],
                                            verbose_name='满意度评分')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'matchmaker_clients'
        verbose_name = '红娘客户'
        verbose_name_plural = '红娘客户'
        unique_together = ['matchmaker', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.matchmaker.real_name} - {self.user.nickname}'


class ServiceRecord(models.Model):
    """服务记录"""
    client = models.ForeignKey(MatchmakerClient, on_delete=models.CASCADE, related_name='service_records', verbose_name='客户')

    # 服务类型
    SERVICE_TYPE_CHOICES = [
        ('consultation', '咨询'),
        ('profile_optimization', '资料优化'),
        ('matching', '匹配推荐'),
        ('date_arrangement', '约会安排'),
        ('follow_up', '跟进服务'),
        ('feedback', '反馈收集'),
        ('other', '其他'),
    ]
    service_type = models.CharField(max_length=30, choices=SERVICE_TYPE_CHOICES, verbose_name='服务类型')

    # 服务内容
    title = models.CharField(max_length=200, verbose_name='服务标题')
    content = models.TextField(verbose_name='服务内容')

    # 服务时间
    service_date = models.DateTimeField(verbose_name='服务时间')
    duration = models.IntegerField(default=0, verbose_name='服务时长(分钟)')

    # 服务结果
    result = models.TextField(blank=True, verbose_name='服务结果')
    next_action = models.TextField(blank=True, verbose_name='下一步行动')

    # 附件
    attachments = models.JSONField(default=list, verbose_name='附件')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'service_records'
        verbose_name = '服务记录'
        verbose_name_plural = '服务记录'
        ordering = ['-service_date']

    def __str__(self):
        return f'{self.client.user.nickname} - {self.title}'


class UserPrivacySettings(models.Model):
    """用户隐私设置"""
    VISIBILITY_CHOICES = [
        ('public', '公开'),
        ('friends', '仅好友'),
        ('private', '私密'),
    ]

    CONTACT_PERMISSION_CHOICES = [
        ('everyone', '所有人'),
        ('matched', '仅匹配用户'),
        ('vip', '仅VIP用户'),
        ('none', '不允许'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='privacy_settings', verbose_name='用户')

    # 资料可见性
    profile_visibility = models.CharField(max_length=10, choices=VISIBILITY_CHOICES, default='public', verbose_name='资料可见性')
    photo_visibility = models.CharField(max_length=10, choices=VISIBILITY_CHOICES, default='public', verbose_name='照片可见性')

    # 联系权限
    message_permission = models.CharField(max_length=10, choices=CONTACT_PERMISSION_CHOICES, default='everyone', verbose_name='消息权限')
    voice_call_permission = models.CharField(max_length=10, choices=CONTACT_PERMISSION_CHOICES, default='matched', verbose_name='语音通话权限')
    video_call_permission = models.CharField(max_length=10, choices=CONTACT_PERMISSION_CHOICES, default='matched', verbose_name='视频通话权限')

    # 位置隐私
    show_distance = models.BooleanField(default=True, verbose_name='显示距离')
    show_last_active = models.BooleanField(default=True, verbose_name='显示最后活跃时间')

    # 搜索设置
    allow_search_by_phone = models.BooleanField(default=False, verbose_name='允许通过手机号搜索')
    allow_search_by_wechat = models.BooleanField(default=False, verbose_name='允许通过微信号搜索')

    # 推荐设置
    appear_in_recommendations = models.BooleanField(default=True, verbose_name='出现在推荐中')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_privacy_settings'
        verbose_name = '用户隐私设置'
        verbose_name_plural = '用户隐私设置'

    def __str__(self):
        return f'{self.user.nickname} - 隐私设置'


class UserNotificationSettings(models.Model):
    """用户通知设置"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_settings', verbose_name='用户')

    # 匹配通知
    new_match_notification = models.BooleanField(default=True, verbose_name='新匹配通知')
    like_notification = models.BooleanField(default=True, verbose_name='被喜欢通知')
    super_like_notification = models.BooleanField(default=True, verbose_name='超级喜欢通知')

    # 消息通知
    new_message_notification = models.BooleanField(default=True, verbose_name='新消息通知')
    voice_message_notification = models.BooleanField(default=True, verbose_name='语音消息通知')

    # 社交通知
    moment_like_notification = models.BooleanField(default=True, verbose_name='动态点赞通知')
    moment_comment_notification = models.BooleanField(default=True, verbose_name='动态评论通知')

    # 礼品通知
    gift_received_notification = models.BooleanField(default=True, verbose_name='收到礼品通知')

    # 活动通知
    activity_notification = models.BooleanField(default=True, verbose_name='活动通知')
    promotion_notification = models.BooleanField(default=False, verbose_name='推广通知')

    # 系统通知
    system_notification = models.BooleanField(default=True, verbose_name='系统通知')
    security_notification = models.BooleanField(default=True, verbose_name='安全通知')

    # 通知时间设置
    quiet_hours_enabled = models.BooleanField(default=False, verbose_name='免打扰时间')
    quiet_start_time = models.TimeField(null=True, blank=True, verbose_name='免打扰开始时间')
    quiet_end_time = models.TimeField(null=True, blank=True, verbose_name='免打扰结束时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_notification_settings'
        verbose_name = '用户通知设置'
        verbose_name_plural = '用户通知设置'

    def __str__(self):
        return f'{self.user.nickname} - 通知设置'


class UserRecommendationSettings(models.Model):
    """用户推荐偏好设置"""
    EDUCATION_CHOICES = [
        ('any', '不限'),
        ('high_school', '高中'),
        ('college', '大专'),
        ('bachelor', '本科'),
        ('master', '硕士'),
        ('doctor', '博士'),
    ]

    INCOME_CHOICES = [
        ('any', '不限'),
        ('below_5k', '5K以下'),
        ('5k_10k', '5K-10K'),
        ('10k_20k', '10K-20K'),
        ('20k_50k', '20K-50K'),
        ('above_50k', '50K以上'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='recommendation_settings', verbose_name='用户')

    # 基本偏好
    preferred_gender = models.CharField(max_length=10, choices=User.GENDER_CHOICES, default='opposite', verbose_name='偏好性别')
    min_age = models.IntegerField(default=18, verbose_name='最小年龄')
    max_age = models.IntegerField(default=60, verbose_name='最大年龄')

    # 地理偏好
    max_distance = models.IntegerField(default=50, verbose_name='最大距离(公里)')
    preferred_cities = models.JSONField(default=list, verbose_name='偏好城市')

    # 教育和职业偏好
    preferred_education = models.CharField(max_length=20, choices=EDUCATION_CHOICES, default='any', verbose_name='偏好学历')
    preferred_income = models.CharField(max_length=20, choices=INCOME_CHOICES, default='any', verbose_name='偏好收入')
    preferred_occupations = models.JSONField(default=list, verbose_name='偏好职业')

    # 外貌偏好
    min_height = models.IntegerField(null=True, blank=True, verbose_name='最小身高')
    max_height = models.IntegerField(null=True, blank=True, verbose_name='最大身高')
    preferred_body_types = models.JSONField(default=list, verbose_name='偏好体型')

    # 生活方式偏好
    smoking_preference = models.CharField(max_length=20, default='any', verbose_name='吸烟偏好')
    drinking_preference = models.CharField(max_length=20, default='any', verbose_name='饮酒偏好')

    # 兴趣爱好偏好
    preferred_interests = models.JSONField(default=list, verbose_name='偏好兴趣')

    # 其他偏好
    has_children_preference = models.CharField(max_length=20, default='any', verbose_name='是否有孩子偏好')
    marriage_intention = models.CharField(max_length=20, default='any', verbose_name='结婚意向偏好')

    # 推荐算法设置
    enable_smart_recommendation = models.BooleanField(default=True, verbose_name='启用智能推荐')
    recommendation_frequency = models.CharField(max_length=20, default='normal', verbose_name='推荐频率')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_recommendation_settings'
        verbose_name = '用户推荐偏好设置'
        verbose_name_plural = '用户推荐偏好设置'

    def __str__(self):
        return f'{self.user.nickname} - 推荐偏好'


class UserBlacklist(models.Model):
    """用户黑名单"""
    BLOCK_REASONS = [
        ('harassment', '骚扰'),
        ('inappropriate', '不当行为'),
        ('spam', '垃圾信息'),
        ('fake_profile', '虚假资料'),
        ('other', '其他'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blocked_users', verbose_name='用户')
    blocked_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blocked_by_users', verbose_name='被拉黑用户')
    reason = models.CharField(max_length=20, choices=BLOCK_REASONS, verbose_name='拉黑原因')
    note = models.TextField(blank=True, verbose_name='备注')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'user_blacklist'
        verbose_name = '用户黑名单'
        verbose_name_plural = '用户黑名单'
        unique_together = ['user', 'blocked_user']
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.nickname} 拉黑了 {self.blocked_user.nickname}'


class VisitorRecord(models.Model):
    """访客记录"""
    visitor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='visit_records', verbose_name='访客')
    visited_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='visitor_records', verbose_name='被访问用户')

    # 访问信息
    visit_count = models.IntegerField(default=1, verbose_name='访问次数')
    first_visit_at = models.DateTimeField(auto_now_add=True, verbose_name='首次访问时间')
    last_visit_at = models.DateTimeField(auto_now=True, verbose_name='最后访问时间')

    # 访问来源
    source = models.CharField(max_length=20, default='recommendation', verbose_name='访问来源')  # recommendation, search, moments, etc.

    # 访问时长（秒）
    duration = models.IntegerField(default=0, verbose_name='访问时长')

    # 是否匿名访问（VIP特权）
    is_anonymous = models.BooleanField(default=False, verbose_name='匿名访问')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'visitor_records'
        verbose_name = '访客记录'
        verbose_name_plural = '访客记录'
        unique_together = ['visitor', 'visited_user']
        ordering = ['-last_visit_at']

    def __str__(self):
        return f'{self.visitor.nickname} 访问了 {self.visited_user.nickname}'


class UserAnalytics(models.Model):
    """用户分析数据"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='analytics', verbose_name='用户')

    # 资料统计
    profile_views = models.IntegerField(default=0, verbose_name='资料浏览量')
    profile_likes = models.IntegerField(default=0, verbose_name='收到喜欢数')
    profile_super_likes = models.IntegerField(default=0, verbose_name='收到超级喜欢数')

    # 匹配统计
    total_matches = models.IntegerField(default=0, verbose_name='总匹配数')
    successful_matches = models.IntegerField(default=0, verbose_name='成功匹配数')

    # 活跃度统计
    login_days = models.IntegerField(default=0, verbose_name='登录天数')
    active_hours = models.IntegerField(default=0, verbose_name='活跃小时数')
    last_active_at = models.DateTimeField(null=True, blank=True, verbose_name='最后活跃时间')

    # 社交统计
    sent_messages = models.IntegerField(default=0, verbose_name='发送消息数')
    received_messages = models.IntegerField(default=0, verbose_name='接收消息数')
    published_moments = models.IntegerField(default=0, verbose_name='发布动态数')

    # 礼品统计
    gifts_sent = models.IntegerField(default=0, verbose_name='发送礼品数')
    gifts_received = models.IntegerField(default=0, verbose_name='接收礼品数')

    # 其他统计
    new_friends = models.IntegerField(default=0, verbose_name='新增好友数')
    chat_sessions = models.IntegerField(default=0, verbose_name='聊天会话数')

    # 时间统计
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_analytics'
        verbose_name = '用户分析数据'
        verbose_name_plural = '用户分析数据'

    def __str__(self):
        return f'{self.user.nickname} - 分析数据'


class DailyAnalytics(models.Model):
    """每日分析数据"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='daily_analytics', verbose_name='用户')
    date = models.DateField(verbose_name='日期')

    # 每日数据
    profile_views = models.IntegerField(default=0, verbose_name='资料浏览量')
    received_likes = models.IntegerField(default=0, verbose_name='收到喜欢数')
    sent_likes = models.IntegerField(default=0, verbose_name='发出喜欢数')
    new_matches = models.IntegerField(default=0, verbose_name='新匹配数')
    messages_sent = models.IntegerField(default=0, verbose_name='发送消息数')
    messages_received = models.IntegerField(default=0, verbose_name='接收消息数')

    # 活跃度数据
    login_count = models.IntegerField(default=0, verbose_name='登录次数')
    active_minutes = models.IntegerField(default=0, verbose_name='活跃分钟数')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'daily_analytics'
        verbose_name = '每日分析数据'
        verbose_name_plural = '每日分析数据'
        unique_together = ['user', 'date']
        ordering = ['-date']

    def __str__(self):
        return f'{self.user.nickname} - {self.date}'


class Notification(models.Model):
    """通知消息"""
    NOTIFICATION_TYPES = [
        ('like', '被喜欢'),
        ('super_like', '超级喜欢'),
        ('match', '匹配成功'),
        ('message', '新消息'),
        ('gift', '收到礼品'),
        ('moment_like', '动态点赞'),
        ('moment_comment', '动态评论'),
        ('visitor', '访客'),
        ('system', '系统通知'),
        ('activity', '活动通知'),
        ('vip', 'VIP通知'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications', verbose_name='用户')
    type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name='通知类型')
    title = models.CharField(max_length=100, verbose_name='标题')
    content = models.TextField(verbose_name='内容')

    # 关联对象
    related_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='sent_notifications', verbose_name='相关用户')
    related_object_id = models.IntegerField(null=True, blank=True, verbose_name='关联对象ID')
    related_object_type = models.CharField(max_length=50, null=True, blank=True, verbose_name='关联对象类型')

    # 状态
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')

    # 额外数据
    extra_data = models.JSONField(default=dict, verbose_name='额外数据')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')

    class Meta:
        db_table = 'notifications'
        verbose_name = '通知消息'
        verbose_name_plural = '通知消息'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'is_read']),
            models.Index(fields=['user', 'type']),
        ]

    def __str__(self):
        return f'{self.user.nickname} - {self.title}'

    def mark_as_read(self):
        """标记为已读"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])


class SystemMessage(models.Model):
    """系统消息"""
    MESSAGE_TYPES = [
        ('announcement', '公告'),
        ('maintenance', '维护通知'),
        ('feature', '功能更新'),
        ('promotion', '活动推广'),
        ('security', '安全提醒'),
    ]

    title = models.CharField(max_length=100, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, verbose_name='消息类型')

    # 发送设置
    target_users = models.ManyToManyField(User, blank=True, verbose_name='目标用户')
    send_to_all = models.BooleanField(default=False, verbose_name='发送给所有用户')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_sent = models.BooleanField(default=False, verbose_name='是否已发送')

    # 时间
    send_at = models.DateTimeField(null=True, blank=True, verbose_name='发送时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'system_messages'
        verbose_name = '系统消息'
        verbose_name_plural = '系统消息'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class WechatConfig(models.Model):
    """微信小程序配置"""
    name = models.CharField(max_length=100, verbose_name='配置名称', default='默认配置')
    app_id = models.CharField(max_length=100, verbose_name='小程序APPID')
    app_secret = models.CharField(max_length=100, verbose_name='小程序AppSecret')

    # 微信支付配置
    mch_id = models.CharField(max_length=50, verbose_name='商户号', blank=True)
    mch_key = models.CharField(max_length=100, verbose_name='商户密钥', blank=True)
    notify_url = models.URLField(verbose_name='支付回调地址', blank=True)

    # 其他配置
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'wechat_config'
        verbose_name = '微信配置'
        verbose_name_plural = '微信配置'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.app_id}"

    def save(self, *args, **kwargs):
        # 确保只有一个配置是激活的
        if self.is_active:
            WechatConfig.objects.filter(is_active=True).update(is_active=False)
        super().save(*args, **kwargs)

    @classmethod
    def get_active_config(cls):
        """获取当前激活的配置"""
        return cls.objects.filter(is_active=True).first()


class AppConfig(models.Model):
    """应用配置"""
    CONFIG_TYPES = [
        ('system', '系统配置'),
        ('wechat', '微信配置'),
        ('payment', '支付配置'),
        ('sms', '短信配置'),
        ('email', '邮件配置'),
    ]

    config_type = models.CharField(max_length=20, choices=CONFIG_TYPES, verbose_name='配置类型')
    key = models.CharField(max_length=100, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    description = models.CharField(max_length=200, verbose_name='配置描述', blank=True)
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'app_config'
        verbose_name = '应用配置'
        verbose_name_plural = '应用配置'
        unique_together = ['config_type', 'key']
        ordering = ['config_type', 'key']

    def __str__(self):
        return f"{self.get_config_type_display()} - {self.key}"

    @classmethod
    def get_config(cls, config_type, key, default=None):
        """获取配置值"""
        try:
            config = cls.objects.get(config_type=config_type, key=key, is_active=True)
            return config.value
        except cls.DoesNotExist:
            return default

    @classmethod
    def set_config(cls, config_type, key, value, description=''):
        """设置配置值"""
        config, created = cls.objects.get_or_create(
            config_type=config_type,
            key=key,
            defaults={'value': value, 'description': description}
        )
        if not created:
            config.value = value
            config.description = description
            config.save()
        return config


class UserStatistics(models.Model):
    """用户统计数据模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='statistics', verbose_name='用户')

    # 日常统计
    today_recommendations = models.IntegerField(default=0, verbose_name='今日推荐数')
    today_likes_received = models.IntegerField(default=0, verbose_name='今日收到喜欢')
    today_likes_sent = models.IntegerField(default=0, verbose_name='今日发出喜欢')
    today_matches = models.IntegerField(default=0, verbose_name='今日匹配数')
    today_messages = models.IntegerField(default=0, verbose_name='今日消息数')
    today_profile_views = models.IntegerField(default=0, verbose_name='今日资料查看')

    # 月度统计
    monthly_likes_received = models.IntegerField(default=0, verbose_name='本月收到喜欢')
    monthly_matches = models.IntegerField(default=0, verbose_name='本月匹配数')
    monthly_visitors = models.IntegerField(default=0, verbose_name='本月访客数')
    monthly_messages = models.IntegerField(default=0, verbose_name='本月消息数')

    # 总体统计
    total_likes_received = models.IntegerField(default=0, verbose_name='总收到喜欢')
    total_likes_sent = models.IntegerField(default=0, verbose_name='总发出喜欢')
    total_matches = models.IntegerField(default=0, verbose_name='总匹配数')
    total_profile_views = models.IntegerField(default=0, verbose_name='总资料查看')

    # 活跃度相关
    activity_score = models.DecimalField(max_digits=3, decimal_places=1, default=Decimal('0.0'), verbose_name='活跃度评分')
    profile_completeness = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)], verbose_name='资料完整度百分比')
    last_active_date = models.DateField(null=True, blank=True, verbose_name='最后活跃日期')

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_statistics'
        verbose_name = '用户统计数据'
        verbose_name_plural = '用户统计数据'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.username} - 统计数据"


class UserActivityLog(models.Model):
    """用户活跃度记录模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activity_logs', verbose_name='用户')
    activity_date = models.DateField(verbose_name='活动日期')

    # 每日活跃度数据
    login_count = models.IntegerField(default=0, verbose_name='登录次数')
    browse_count = models.IntegerField(default=0, verbose_name='浏览次数')
    like_count = models.IntegerField(default=0, verbose_name='点赞次数')
    message_count = models.IntegerField(default=0, verbose_name='消息次数')
    profile_update_count = models.IntegerField(default=0, verbose_name='资料更新次数')

    # 活跃度得分
    daily_score = models.IntegerField(default=0, verbose_name='当日活跃度得分')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'user_activity_logs'
        verbose_name = '用户活跃度记录'
        verbose_name_plural = '用户活跃度记录'
        unique_together = ['user', 'activity_date']
        ordering = ['-activity_date']

    def __str__(self):
        return f"{self.user.username} - {self.activity_date}"


class UserLocation(models.Model):
    """用户位置记录模型"""
    LOCATION_TYPE_CHOICES = [
        ('current', '当前位置'),
        ('home', '家庭地址'),
        ('work', '工作地址'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='locations', verbose_name='用户')

    # 位置信息
    latitude = models.DecimalField(max_digits=10, decimal_places=8, verbose_name='纬度')
    longitude = models.DecimalField(max_digits=11, decimal_places=8, verbose_name='经度')
    address = models.CharField(max_length=500, blank=True, verbose_name='详细地址')
    city = models.CharField(max_length=100, blank=True, verbose_name='城市')
    district = models.CharField(max_length=100, blank=True, verbose_name='区域')

    # 位置类型
    location_type = models.CharField(max_length=20, choices=LOCATION_TYPE_CHOICES, default='current', verbose_name='位置类型')

    # 精度信息
    accuracy = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='定位精度(米)')

    # 隐私设置
    is_public = models.BooleanField(default=True, verbose_name='是否公开')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'user_locations'
        verbose_name = '用户位置记录'
        verbose_name_plural = '用户位置记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_location_type_display()}"


class UserVerification(models.Model):
    """用户认证记录模型"""
    VERIFICATION_TYPE_CHOICES = [
        ('identity', '身份认证'),
        ('photo', '照片认证'),
        ('education', '学历认证'),
        ('profession', '职业认证'),
        ('income', '收入认证'),
    ]

    STATUS_CHOICES = [
        ('not_started', '未开始'),
        ('pending', '审核中'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verifications', verbose_name='用户')

    # 认证类型
    verification_type = models.CharField(max_length=20, choices=VERIFICATION_TYPE_CHOICES, verbose_name='认证类型')

    # 认证状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started', verbose_name='认证状态')

    # 认证材料
    documents = models.JSONField(default=list, verbose_name='认证文件URLs')
    submitted_data = models.JSONField(default=dict, verbose_name='提交的认证数据')

    # 审核信息
    reviewer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_verifications', verbose_name='审核员')
    review_notes = models.TextField(blank=True, verbose_name='审核备注')
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name='审核时间')

    # 时间信息
    submitted_at = models.DateTimeField(null=True, blank=True, verbose_name='提交时间')
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name='通过时间')
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_verifications'
        verbose_name = '用户认证记录'
        verbose_name_plural = '用户认证记录'
        unique_together = ['user', 'verification_type']
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_verification_type_display()}: {self.get_status_display()}"
