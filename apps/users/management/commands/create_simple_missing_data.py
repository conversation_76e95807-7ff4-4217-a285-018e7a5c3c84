#!/usr/bin/env python3
"""
为缺少demo数据的后台导航菜单页面创建简单演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为缺少demo数据的后台导航菜单页面创建简单演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为缺少数据的导航页面创建简单演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_existing_model_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 缺少数据的导航页面演示数据创建完成！'))

    def create_existing_model_data(self):
        """为现有模型创建演示数据"""
        self.stdout.write('📊 为现有模型创建演示数据...')
        
        # 1. 创建用户行为日志数据
        try:
            from apps.users.models import UserAction
            
            if UserAction.objects.count() < 50:
                action_types = ['like', 'pass', 'super_like', 'visit', 'block', 'report']
                
                for i in range(30):
                    user, target_user = random.sample(self.users, 2)
                    action_type = random.choice(action_types)
                    
                    if not UserAction.objects.filter(user=user, target_user=target_user, action_type=action_type).exists():
                        UserAction.objects.create(
                            user=user,
                            target_user=target_user,
                            action_type=action_type,
                            message=f'演示{action_type}行为',
                            created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户行为数据: {UserAction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户行为数据创建失败: {e}'))

        # 2. 创建用户偏好数据
        try:
            from apps.users.models import UserPreference
            
            for user in self.users:
                if not UserPreference.objects.filter(user=user).exists():
                    UserPreference.objects.create(
                        user=user,
                        min_age=random.randint(18, 25),
                        max_age=random.randint(30, 45),
                        preferred_gender=random.choice([1, 2]),
                        preferred_location=random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                        distance_range=random.randint(10, 100),
                        preferred_education=random.choice([1, 2, 3, 4, 5])
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户偏好数据: {UserPreference.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户偏好数据创建失败: {e}'))

        # 3. 创建VIP记录数据
        try:
            from apps.payments.models import VIPRecord, VIPPackage
            
            vip_packages = list(VIPPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, min(8, len(self.users))):
                    if not VIPRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        start_date = timezone.now() - timedelta(days=random.randint(1, 90))
                        
                        VIPRecord.objects.create(
                            user=user,
                            package=package,
                            start_date=start_date,
                            end_date=start_date + timedelta(days=30),
                            is_active=random.choice([True, False]),
                            auto_renew=random.choice([True, False])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ VIP记录数据: {VIPRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ VIP记录数据创建失败: {e}'))

        # 4. 创建用户礼物盒数据
        try:
            from apps.gifts.models import UserGiftBox, Gift
            
            gifts = list(Gift.objects.all())
            if gifts:
                for user in self.users:
                    user_gifts = random.sample(gifts, random.randint(1, min(3, len(gifts))))
                    
                    for gift in user_gifts:
                        if not UserGiftBox.objects.filter(user=user, gift=gift).exists():
                            UserGiftBox.objects.create(
                                user=user,
                                gift=gift,
                                quantity=random.randint(1, 5),
                                obtained_date=timezone.now() - timedelta(days=random.randint(1, 30))
                            )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户礼物盒数据: {UserGiftBox.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户礼物盒数据创建失败: {e}'))

        # 5. 创建举报记录数据
        try:
            from apps.moments.models import ReportRecord
            
            if ReportRecord.objects.count() < 20:
                report_types = ['inappropriate', 'spam', 'harassment', 'fake', 'other']
                
                for i in range(15):
                    reporter = random.choice(self.users)
                    reported_user = random.choice([u for u in self.users if u != reporter])
                    
                    ReportRecord.objects.create(
                        reporter=reporter,
                        reported_user=reported_user,
                        report_type=random.choice(report_types),
                        reason=f'举报原因 #{i+1}',
                        status=random.choice(['pending', 'resolved', 'rejected']),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报记录数据: {ReportRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报记录数据创建失败: {e}'))

        # 6. 创建黑名单数据
        try:
            from apps.users.models import UserBlacklist
            
            if UserBlacklist.objects.count() < 15:
                for i in range(10):
                    user, blocked_user = random.sample(self.users, 2)
                    
                    if not UserBlacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                        UserBlacklist.objects.create(
                            user=user,
                            blocked_user=blocked_user,
                            reason=random.choice(['harassment', 'spam', 'inappropriate', 'other']),
                            created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 黑名单数据: {UserBlacklist.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 黑名单数据创建失败: {e}'))

        # 7. 创建实时通话数据
        try:
            from apps.realtime.models import VoiceCall, VideoCall
            
            # 语音通话
            if VoiceCall.objects.count() < 20:
                for i in range(15):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(30, 1800)
                    
                    VoiceCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'voice_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            # 视频通话
            if VideoCall.objects.count() < 15:
                for i in range(10):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(60, 3600)
                    
                    VideoCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'video_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        video_quality=random.choice(['720p', '1080p', '480p']),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 语音通话数据: {VoiceCall.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'  ✅ 视频通话数据: {VideoCall.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 通话数据创建失败: {e}'))

        # 8. 创建在线状态数据
        try:
            from apps.realtime.models import UserOnlineStatus
            
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet']),
                        location_info={
                            'city': random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                            'country': '中国'
                        }
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 在线状态数据: {UserOnlineStatus.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 在线状态数据创建失败: {e}'))

        # 9. 创建安全相关数据
        try:
            from apps.security.models import SecurityEvent, IPBlacklist
            
            # 安全事件
            if SecurityEvent.objects.count() < 30:
                event_types = ['login_attempt', 'failed_login', 'suspicious_activity', 'rate_limit_exceeded']
                
                for i in range(20):
                    SecurityEvent.objects.create(
                        event_type=random.choice(event_types),
                        severity=random.choice(['low', 'medium', 'high', 'critical']),
                        ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                        user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                        referer='https://xiangqin.weixinjishu.top/',
                        request_method=random.choice(['GET', 'POST']),
                        request_path='/api/users/',
                        user=random.choice(self.users) if random.choice([True, False]) else None,
                        is_blocked=random.choice([True, False])
                    )
            
            # IP黑名单
            if IPBlacklist.objects.count() < 10:
                for i in range(8):
                    IPBlacklist.objects.create(
                        ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                        reason=random.choice(['manual', 'auto_brute_force', 'auto_suspicious']),
                        description=f'IP黑名单 #{i+1}',
                        is_active=random.choice([True, False]),
                        blocked_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全事件数据: {SecurityEvent.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'  ✅ IP黑名单数据: {IPBlacklist.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))
