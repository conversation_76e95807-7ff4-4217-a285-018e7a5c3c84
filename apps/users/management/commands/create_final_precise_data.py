#!/usr/bin/env python3
"""
基于确切模型字段创建最终精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '基于确切模型字段创建最终精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始创建最终精确的演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_analytics_data()
        self.create_matching_data()
        self.create_payment_data()
        self.create_gifts_data()
        self.create_system_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最终精确演示数据创建完成！'))

    def create_analytics_data(self):
        """创建分析模块精确数据"""
        self.stdout.write('📈 创建分析模块精确数据...')
        
        try:
            from apps.analytics.models import UserBehaviorLog, RevenueRecord
            
            # 1. 用户行为日志 - 使用确切字段
            action_types = ['login', 'logout', 'view_profile', 'like', 'super_like', 'pass', 'match', 'send_message']
            for i in range(50):
                user = random.choice(self.users)
                target_user = random.choice([u for u in self.users if u != user]) if random.choice([True, False]) else None
                
                UserBehaviorLog.objects.create(
                    user=user,
                    action_type=random.choice(action_types),
                    target_user=target_user,
                    details={
                        'page': random.choice(['home', 'profile', 'chat', 'discover']),
                        'duration': random.randint(10, 300)
                    },
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    location=random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                    latitude=round(random.uniform(20, 50), 6),
                    longitude=round(random.uniform(100, 130), 6)
                )
            
            # 2. 收入记录 - 使用确切字段
            revenue_types = ['vip', 'gift', 'recharge', 'matchmaker', 'activity']
            for i in range(30):
                user = random.choice(self.users)
                revenue_type = random.choice(revenue_types)
                amount = Decimal(str(round(random.uniform(10, 500), 2)))
                
                RevenueRecord.objects.create(
                    user=user,
                    revenue_type=revenue_type,
                    amount=amount,
                    order_id=f'order_{uuid.uuid4().hex[:12]}',
                    payment_method=random.choice(['wechat_pay', 'alipay', 'bank_card']),
                    product_name=f'{revenue_type}_product_{i}',
                    product_details={'description': f'产品详情 #{i}'},
                    payment_time=timezone.now() - timedelta(days=random.randint(1, 30))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 分析数据: UserBehaviorLog {UserBehaviorLog.objects.count()}条, RevenueRecord {RevenueRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 分析数据创建失败: {e}'))

    def create_matching_data(self):
        """创建匹配模块精确数据"""
        self.stdout.write('💕 创建匹配模块精确数据...')
        
        try:
            from apps.matching.models import MatchingAlgorithmConfig, UserCompatibility, DailyRecommendation
            
            # 1. 匹配算法配置 - 使用确切字段
            configs = [
                {'name': '年龄权重配置', 'age_weight': 0.3, 'education_weight': 0.2, 'income_weight': 0.15},
                {'name': '教育权重配置', 'age_weight': 0.25, 'education_weight': 0.3, 'income_weight': 0.2},
                {'name': '收入权重配置', 'age_weight': 0.2, 'education_weight': 0.25, 'income_weight': 0.3}
            ]
            
            for i, config in enumerate(configs):
                if not MatchingAlgorithmConfig.objects.filter(name=config['name']).exists():
                    MatchingAlgorithmConfig.objects.create(
                        name=config['name'],
                        version=f'v1.{i}',
                        age_weight=config['age_weight'],
                        education_weight=config['education_weight'],
                        income_weight=config['income_weight'],
                        height_weight=0.1,
                        interest_weight=0.15,
                        location_weight=0.1,
                        max_distance=100,
                        min_match_score=0.6,
                        is_active=True
                    )
            
            # 2. 用户兼容性 - 使用确切字段
            for i in range(25):
                user1, user2 = random.sample(self.users, 2)
                if not UserCompatibility.objects.filter(user1=user1, user2=user2).exists():
                    UserCompatibility.objects.create(
                        user1=user1,
                        user2=user2,
                        age_score=round(random.uniform(0.0, 1.0), 3),
                        location_score=round(random.uniform(0.0, 1.0), 3),
                        education_score=round(random.uniform(0.0, 1.0), 3),
                        income_score=round(random.uniform(0.0, 1.0), 3),
                        height_score=round(random.uniform(0.0, 1.0), 3),
                        interest_score=round(random.uniform(0.0, 1.0), 3),
                        total_score=round(random.uniform(0.1, 0.95), 3),
                        analysis_details={
                            'calculation_method': 'weighted_average',
                            'factors_considered': ['age', 'education', 'location', 'interests']
                        }
                    )
            
            # 3. 每日推荐 - 使用确切字段
            for user in self.users:
                for days_ago in range(5):  # 最近5天
                    recommendation_date = date.today() - timedelta(days=days_ago)
                    if not DailyRecommendation.objects.filter(user=user, date=recommendation_date).exists():
                        recommended_users_ids = [u.id for u in random.sample([u for u in self.users if u != user], random.randint(3, 6))]
                        
                        DailyRecommendation.objects.create(
                            user=user,
                            recommended_users=recommended_users_ids,  # 存储ID列表
                            date=recommendation_date,
                            total_count=len(recommended_users_ids),
                            viewed_count=random.randint(0, len(recommended_users_ids)),
                            liked_count=random.randint(0, 2),
                            matched_count=random.randint(0, 1)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 匹配数据: MatchingAlgorithmConfig {MatchingAlgorithmConfig.objects.count()}条, UserCompatibility {UserCompatibility.objects.count()}条, DailyRecommendation {DailyRecommendation.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 匹配数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块精确数据"""
        self.stdout.write('💰 创建支付模块精确数据...')
        
        try:
            from apps.payment.models import UserVipRecord, VipPackage
            
            # 用户VIP记录 - 使用确切字段
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(6, 10)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            is_active=random.choice([True, False])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))

    def create_gifts_data(self):
        """创建礼物模块精确数据"""
        self.stdout.write('🎁 创建礼物模块精确数据...')
        
        try:
            from apps.gifts.models import UserGiftBox, GiftActivityParticipant, Gift, GiftActivity
            
            # 1. 用户礼物盒 - 使用确切字段
            gifts = list(Gift.objects.all())
            if gifts:
                for user in self.users:
                    user_gifts = random.sample(gifts, random.randint(1, min(3, len(gifts))))
                    for gift in user_gifts:
                        if not UserGiftBox.objects.filter(user=user, gift=gift).exists():
                            UserGiftBox.objects.create(
                                user=user,
                                gift=gift,
                                quantity=random.randint(1, 10),
                                source=random.choice(['purchase', 'gift_received', 'activity_reward', 'daily_check_in'])
                            )
            
            # 2. 活动参与者 - 使用确切字段
            activities = list(GiftActivity.objects.all())
            if activities:
                for activity in activities:
                    participants = random.sample(self.users, random.randint(5, 8))
                    for user in participants:
                        if not GiftActivityParticipant.objects.filter(activity=activity, user=user).exists():
                            GiftActivityParticipant.objects.create(
                                activity=activity,
                                user=user,
                                rewards_received=random.choice([True, False])
                            )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 礼物数据: UserGiftBox {UserGiftBox.objects.count()}条, GiftActivityParticipant {GiftActivityParticipant.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 礼物数据创建失败: {e}'))

    def create_system_data(self):
        """创建系统模块精确数据"""
        self.stdout.write('⚙️ 创建系统模块精确数据...')
        
        try:
            from apps.system.models import AppVersion, UserFeedback
            
            # 1. 应用版本 - 使用确切字段
            versions = [
                {'version_name': '版本 1.0.0', 'version_code': 100, 'platform': 'android'},
                {'version_name': '版本 1.1.0', 'version_code': 110, 'platform': 'android'},
                {'version_name': '版本 1.2.0', 'version_code': 120, 'platform': 'android'},
                {'version_name': '版本 2.0.0', 'version_code': 200, 'platform': 'ios'},
                {'version_name': '版本 2.1.0', 'version_code': 210, 'platform': 'ios'}
            ]
            
            for version in versions:
                if not AppVersion.objects.filter(version_code=version['version_code']).exists():
                    AppVersion.objects.create(
                        version_name=version['version_name'],
                        version_code=version['version_code'],
                        platform=version['platform'],
                        update_content=f'{version["version_name"]} 更新说明',
                        download_url=f'https://download.example.com/app_{version["version_code"]}.apk',
                        is_force_update=random.choice([True, False]),
                        is_active=True,
                        download_count=random.randint(100, 10000)
                    )
            
            # 2. 用户反馈 - 使用确切字段
            feedback_types = ['bug_report', 'feature_request', 'general_feedback', 'complaint']
            for i in range(20):
                UserFeedback.objects.create(
                    user=random.choice(self.users),
                    feedback_type=random.choice(feedback_types),
                    title=f'用户反馈 #{i+1}',
                    content=f'反馈内容详情 #{i+1}',
                    rating=random.randint(1, 5),
                    status=random.choice(['new', 'in_progress', 'resolved', 'closed']),
                    admin_response=f'管理员回复 #{i+1}' if random.choice([True, False]) else '',
                    contact_email=f'user{i}@example.com'
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 系统数据: AppVersion {AppVersion.objects.count()}条, UserFeedback {UserFeedback.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 系统数据创建失败: {e}'))
