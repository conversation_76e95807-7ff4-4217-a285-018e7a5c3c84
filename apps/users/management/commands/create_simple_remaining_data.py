#!/usr/bin/env python3
"""
为剩余模型创建简化的演示数据 - 只使用基本字段
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal
from django.core.files.base import ContentFile

User = get_user_model()

class Command(BaseCommand):
    help = '为剩余模型创建简化的演示数据 - 只使用基本字段'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为剩余模型创建简化演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_realtime_data()
        self.create_reports_data()
        self.create_payment_data()
        self.create_system_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 简化演示数据创建完成！'))

    def create_realtime_data(self):
        """创建实时通讯模块简化数据"""
        self.stdout.write('⚡ 创建实时通讯模块简化数据...')
        
        try:
            from apps.realtime.models import VoiceCall, VideoCall, VoiceMessage, RealtimeNotification
            
            # 1. 语音通话 - 只使用基本字段
            for i in range(25):
                caller, callee = random.sample(self.users, 2)
                duration_seconds = random.randint(30, 1800)
                duration = timedelta(seconds=duration_seconds)
                
                initiated_time = timezone.now() - timedelta(hours=random.randint(1, 168))
                
                VoiceCall.objects.create(
                    caller=caller,
                    callee=callee,
                    status=random.choice(['ended', 'missed', 'rejected', 'failed']),
                    initiated_at=initiated_time,
                    ended_at=initiated_time + duration,
                    duration=duration,
                    call_quality=random.choice(['excellent', 'good', 'fair', 'poor'])
                )
            
            # 2. 视频通话 - 只使用基本字段
            for i in range(20):
                caller, callee = random.sample(self.users, 2)
                duration_seconds = random.randint(60, 3600)
                duration = timedelta(seconds=duration_seconds)
                
                initiated_time = timezone.now() - timedelta(hours=random.randint(1, 168))
                
                VideoCall.objects.create(
                    caller=caller,
                    callee=callee,
                    status=random.choice(['ended', 'missed', 'rejected', 'failed']),
                    initiated_at=initiated_time,
                    ended_at=initiated_time + duration,
                    duration=duration,
                    call_quality=random.choice(['excellent', 'good', 'fair', 'poor'])
                )
            
            # 3. 语音消息 - 只使用基本字段
            for i in range(40):
                sender, receiver = random.sample(self.users, 2)
                duration = round(random.uniform(5, 60), 1)
                file_size = random.randint(50000, 500000)
                
                # 创建一个虚拟的音频文件
                audio_content = b'fake_audio_content_' + str(i).encode()
                audio_file = ContentFile(audio_content, name=f'voice_{uuid.uuid4().hex[:8]}.mp3')
                
                VoiceMessage.objects.create(
                    sender=sender,
                    receiver=receiver,
                    audio_file=audio_file,
                    duration=duration,
                    file_size=file_size,
                    is_read=random.choice([True, False])
                )
            
            # 4. 实时通知 - 只使用基本字段
            notification_types = ['message', 'like', 'match', 'voice_call', 'video_call', 'system']
            for i in range(50):
                RealtimeNotification.objects.create(
                    user=random.choice(self.users),
                    notification_type=random.choice(notification_types),
                    title=f'通知标题 #{i+1}',
                    content=f'通知内容详情 #{i+1}',
                    priority=random.choice(['low', 'normal', 'high', 'urgent']),
                    is_read=random.choice([True, False])
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通讯数据: VoiceCall {VoiceCall.objects.count()}条, VideoCall {VideoCall.objects.count()}条, VoiceMessage {VoiceMessage.objects.count()}条, RealtimeNotification {RealtimeNotification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块简化数据"""
        self.stdout.write('🚨 创建举报模块简化数据...')
        
        try:
            from apps.reports.models import Report, UserPunishment, AppealRecord
            
            # 1. 举报记录 - 只使用基本字段
            report_types = ['inappropriate_content', 'harassment', 'fake_profile', 'spam', 'fraud']
            content_types = ['user', 'moment', 'message', 'comment']
            
            for i in range(30):
                reporter = random.choice(self.users)
                reported_user = random.choice([u for u in self.users if u != reporter])
                
                Report.objects.create(
                    reporter=reporter,
                    reported_user=reported_user,
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    report_type=random.choice(report_types),
                    reason=f'举报原因详情 #{i+1}',
                    status=random.choice(['pending', 'processing', 'resolved', 'rejected'])
                )
            
            # 2. 用户处罚 - 只使用基本字段
            punishment_types = ['warning', 'mute', 'ban', 'permanent_ban', 'content_removal', 'feature_restriction']
            
            for i in range(20):
                user = random.choice(self.users)
                punishment_type = random.choice(punishment_types)
                
                start_time = timezone.now() - timedelta(days=random.randint(1, 30))
                end_time = start_time + timedelta(days=random.randint(1, 30)) if punishment_type != 'permanent_ban' else None
                
                UserPunishment.objects.create(
                    user=user,
                    punishment_type=punishment_type,
                    reason=f'处罚原因 #{i+1}',
                    start_time=start_time,
                    end_time=end_time,
                    status=random.choice(['active', 'expired', 'revoked']),
                    admin_user=random.choice(self.users)
                )
            
            # 3. 申诉记录 - 只使用基本字段
            punishments = list(UserPunishment.objects.all())
            if punishments:
                for punishment in random.sample(punishments, min(10, len(punishments))):
                    if not AppealRecord.objects.filter(punishment=punishment).exists():
                        AppealRecord.objects.create(
                            user=punishment.user,
                            punishment=punishment,
                            status=random.choice(['pending', 'processing', 'approved', 'rejected'])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据: Report {Report.objects.count()}条, UserPunishment {UserPunishment.objects.count()}条, AppealRecord {AppealRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块简化数据"""
        self.stdout.write('💰 创建支付模块简化数据...')
        
        try:
            from apps.payment.models import UserVipRecord, VipPackage
            
            # 用户VIP记录 - 只使用基本字段
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(8, 12)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            is_active=random.choice([True, False])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))

    def create_system_data(self):
        """创建系统模块简化数据"""
        self.stdout.write('⚙️ 创建系统模块简化数据...')
        
        try:
            from apps.system.models import UserFeedback, FeedbackCategory
            
            # 获取反馈分类
            categories = list(FeedbackCategory.objects.all())
            if not categories:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到反馈分类，跳过用户反馈数据创建'))
                return
            
            # 用户反馈 - 只使用基本字段
            feedback_types = ['bug_report', 'feature_request', 'general_feedback', 'complaint']
            
            for i in range(25):
                UserFeedback.objects.create(
                    user=random.choice(self.users),
                    feedback_type=random.choice(feedback_types),
                    title=f'用户反馈 #{i+1}',
                    content=f'反馈内容详情 #{i+1}',
                    status=random.choice(['new', 'in_progress', 'resolved', 'closed']),
                    category=random.choice(categories)
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 系统数据: UserFeedback {UserFeedback.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 系统数据创建失败: {e}'))
