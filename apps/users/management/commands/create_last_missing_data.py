#!/usr/bin/env python3
"""
为最后25个缺少数据的模型创建精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最后25个缺少数据的模型创建精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最后25个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_advanced_analytics_data()
        self.create_ai_matching_data()
        self.create_payment_data()
        self.create_reports_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最后25个模型演示数据创建完成！'))

    def create_advanced_analytics_data(self):
        """创建高级分析模块精确数据"""
        self.stdout.write('📊 创建高级分析模块精确数据...')
        
        try:
            from apps.advanced_analytics.models import (
                CohortAnalysis, FunnelAnalysis, ABTestExperiment, ABTestParticipant,
                BusinessIntelligenceReport, PredictiveModel, DataQualityMetric
            )
            
            # 1. 队列分析 - 使用确切字段
            cohort_periods = ['daily', 'weekly', 'monthly']
            for i, period in enumerate(cohort_periods):
                for j in range(3):  # 每种周期创建3个队列
                    cohort_date = date.today() - timedelta(days=30*j + i*7)
                    cohort_size = random.randint(50, 200)
                    
                    if not CohortAnalysis.objects.filter(cohort_period=period, cohort_date=cohort_date).exists():
                        CohortAnalysis.objects.create(
                            cohort_name=f'{period.title()} Cohort {cohort_date}',
                            cohort_period=period,
                            cohort_date=cohort_date,
                            cohort_size=cohort_size,
                            retention_data={
                                'day_1': round(random.uniform(0.8, 0.95), 3),
                                'day_7': round(random.uniform(0.6, 0.8), 3),
                                'day_30': round(random.uniform(0.3, 0.6), 3),
                                'day_90': round(random.uniform(0.1, 0.3), 3)
                            },
                            revenue_data={
                                'day_1': round(random.uniform(100, 500), 2),
                                'day_7': round(random.uniform(200, 800), 2),
                                'day_30': round(random.uniform(500, 2000), 2)
                            },
                            engagement_data={
                                'sessions_per_user': round(random.uniform(2, 10), 2),
                                'avg_session_duration': round(random.uniform(5, 30), 2)
                            }
                        )
            
            # 2. 漏斗分析 - 使用确切字段
            funnels = [
                {
                    'name': '用户注册漏斗',
                    'description': '从访问到完成注册的转化漏斗',
                    'steps': ['访问首页', '点击注册', '填写信息', '验证手机', '完成注册']
                },
                {
                    'name': '付费转化漏斗',
                    'description': '从免费用户到付费用户的转化漏斗',
                    'steps': ['免费用户', '查看VIP功能', '点击购买', '选择套餐', '完成支付']
                }
            ]
            
            for funnel in funnels:
                if not FunnelAnalysis.objects.filter(funnel_name=funnel['name']).exists():
                    total_users = random.randint(1000, 5000)
                    conversion_data = {}
                    
                    for i, step in enumerate(funnel['steps']):
                        if i == 0:
                            users_count = total_users
                            conversion_rate = 1.0
                        else:
                            conversion_rate = round(random.uniform(0.3, 0.8), 3)
                            users_count = int(conversion_data[funnel['steps'][i-1]]['users'] * conversion_rate)
                        
                        conversion_data[step] = {
                            'users': users_count,
                            'conversion_rate': conversion_rate
                        }
                    
                    FunnelAnalysis.objects.create(
                        funnel_name=funnel['name'],
                        description=funnel['description'],
                        steps=funnel['steps'],
                        conversion_data=conversion_data,
                        drop_off_analysis={
                            'highest_drop_off_step': random.choice(funnel['steps'][1:]),
                            'improvement_suggestions': ['优化页面设计', '简化流程', '增加引导']
                        },
                        date_range_start=timezone.now() - timedelta(days=30),
                        date_range_end=timezone.now(),
                        segment_filters={'user_type': 'all'}
                    )
            
            # 3. A/B测试实验 - 使用确切字段
            experiments = [
                {
                    'name': '首页布局优化实验',
                    'description': '测试新的首页布局对用户注册转化率的影响',
                    'hypothesis': '新的首页布局将提高用户注册转化率15%'
                }
            ]
            
            for exp_data in experiments:
                if not ABTestExperiment.objects.filter(name=exp_data['name']).exists():
                    experiment = ABTestExperiment.objects.create(
                        name=exp_data['name'],
                        description=exp_data['description'],
                        hypothesis=exp_data['hypothesis'],
                        control_group_ratio=0.5,
                        treatment_groups=[
                            {'name': 'treatment_a', 'ratio': 0.5, 'config': {'version': 'new_layout'}}
                        ],
                        primary_metric='conversion_rate',
                        secondary_metrics=['engagement_rate', 'retention_rate'],
                        status=random.choice(['running', 'completed']),
                        start_date=timezone.now() - timedelta(days=random.randint(7, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 30)),
                        target_sample_size=random.randint(1000, 5000),
                        current_sample_size=random.randint(500, 2500),
                        results={
                            'control_conversion': round(random.uniform(0.1, 0.2), 3),
                            'treatment_conversion': round(random.uniform(0.12, 0.25), 3),
                            'lift': round(random.uniform(0.05, 0.25), 3)
                        },
                        statistical_significance=round(random.uniform(0.8, 0.99), 3)
                    )
                    
                    # 为实验添加参与者
                    for user in random.sample(self.users, random.randint(8, 12)):
                        ABTestParticipant.objects.create(
                            experiment=experiment,
                            user=user,
                            group_name=random.choice(['control', 'treatment_a']),
                            group_type=random.choice(['control', 'treatment']),
                            first_exposure_at=timezone.now() - timedelta(days=random.randint(1, 15)),
                            has_converted=random.choice([True, False]),
                            conversion_value=round(random.uniform(0, 100), 2) if random.choice([True, False]) else None,
                            converted_at=timezone.now() - timedelta(days=random.randint(1, 10)) if random.choice([True, False]) else None,
                            participation_data={'sessions': random.randint(1, 10)}
                        )
            
            # 4. 商业智能报告 - 使用确切字段
            report_types = ['user_growth', 'revenue_analysis']
            frequencies = ['weekly', 'monthly']
            
            for report_type in report_types:
                for frequency in frequencies:
                    if not BusinessIntelligenceReport.objects.filter(name=f'{report_type}_{frequency}').exists():
                        period_start = timezone.now() - timedelta(days=30)
                        period_end = timezone.now()
                        
                        BusinessIntelligenceReport.objects.create(
                            name=f'{report_type}_{frequency}',
                            report_type=report_type,
                            frequency=frequency,
                            data_sources=['user_data', 'transaction_data'],
                            filters={'date_range': '30_days'},
                            metrics=['conversion_rate', 'retention_rate'],
                            report_data={
                                'total_users': random.randint(1000, 10000),
                                'new_users': random.randint(100, 1000),
                                'revenue': round(random.uniform(10000, 100000), 2)
                            },
                            insights=[
                                '用户增长率较上月提升15%',
                                '付费转化率有所下降，需要优化'
                            ],
                            recommendations=[
                                '加强新用户引导',
                                '优化付费流程'
                            ],
                            is_automated=True,
                            is_published=True,
                            report_period_start=period_start,
                            report_period_end=period_end
                        )
            
            # 5. 预测模型 - 使用确切字段
            model_types = ['churn_prediction', 'ltv_prediction']
            algorithms = ['random_forest', 'gradient_boosting']
            
            for i, model_type in enumerate(model_types):
                if not PredictiveModel.objects.filter(name=f'{model_type}_model').exists():
                    PredictiveModel.objects.create(
                        name=f'{model_type}_model',
                        description=f'预测模型用于{model_type}',
                        model_type=model_type,
                        algorithm=random.choice(algorithms),
                        features=['age', 'activity_score', 'message_count'],
                        target_variable=model_type.split('_')[0],
                        hyperparameters={
                            'n_estimators': 100,
                            'max_depth': 10
                        },
                        training_accuracy=round(random.uniform(0.75, 0.95), 3),
                        validation_accuracy=round(random.uniform(0.70, 0.90), 3),
                        test_accuracy=round(random.uniform(0.65, 0.85), 3),
                        is_active=True,
                        version=f'1.{i}',
                        training_data_size=random.randint(10000, 50000),
                        last_trained_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            # 6. 数据质量指标 - 使用确切字段
            data_sources = ['user_data', 'transaction_data']
            metric_types = ['completeness', 'accuracy']
            
            for data_source in data_sources:
                for metric_type in metric_types:
                    if not DataQualityMetric.objects.filter(data_source=data_source, metric_type=metric_type).exists():
                        total_records = random.randint(1000, 10000)
                        valid_records = random.randint(int(total_records * 0.7), total_records)
                        
                        DataQualityMetric.objects.create(
                            data_source=data_source,
                            metric_type=metric_type,
                            quality_score=round(valid_records / total_records, 3),
                            threshold=0.95,
                            total_records=total_records,
                            valid_records=valid_records,
                            invalid_records=total_records - valid_records,
                            issues_found=[
                                {'type': 'missing_values', 'count': random.randint(0, 100)}
                            ]
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 高级分析数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级分析数据创建失败: {e}'))

    def create_ai_matching_data(self):
        """创建AI匹配模块精确数据"""
        self.stdout.write('🧠 创建AI匹配模块精确数据...')
        
        try:
            from apps.ai_matching.models import (
                UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
                ModelPerformanceMetric, MatchingModel
            )
            
            # 1. 用户行为模式 - 使用确切字段
            for user in self.users:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        active_hours=random.sample(range(24), random.randint(6, 12)),
                        active_days=random.sample(range(7), random.randint(3, 7)),
                        session_duration_avg=round(random.uniform(10, 60), 2),
                        like_frequency=round(random.uniform(0.1, 2.0), 2),
                        message_frequency=round(random.uniform(0.5, 5.0), 2),
                        response_time_avg=round(random.uniform(5, 120), 2),
                        preferred_user_types=['active', 'social'],
                        interaction_patterns={
                            'morning_activity': round(random.uniform(0.1, 1.0), 2),
                            'evening_activity': round(random.uniform(0.1, 1.0), 2)
                        }
                    )
            
            # 2. 模型训练数据 - 使用确切字段
            data_types = ['user_interactions', 'match_outcomes']
            for data_type in data_types:
                if not ModelTrainingData.objects.filter(data_type=data_type).exists():
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        data_source=f'production_db_{data_type}',
                        data_size=random.randint(10000, 100000),
                        quality_score=round(random.uniform(0.7, 0.95), 3),
                        feature_count=random.randint(50, 200),
                        training_accuracy=round(random.uniform(0.75, 0.95), 3),
                        validation_accuracy=round(random.uniform(0.70, 0.90), 3),
                        preprocessing_steps=['normalization', 'feature_extraction']
                    )
            
            # 3. 推荐解释 - 使用确切字段
            for i in range(15):
                user, recommended_user = random.sample(self.users, 2)
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        explanation_text=f'基于共同兴趣推荐',
                        explanation_factors={
                            'common_interests': ['音乐', '电影'],
                            'similarity_score': round(random.uniform(0.6, 0.9), 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        algorithm_version='v2.1.0'
                    )
            
            # 4. 模型性能指标 - 使用确切字段
            models = list(MatchingModel.objects.all())
            metrics = ['accuracy', 'precision', 'recall']
            
            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=round(random.uniform(0.6, 0.95), 4),
                            evaluation_dataset='test_set_2025',
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                            baseline_value=round(random.uniform(0.5, 0.8), 4),
                            improvement_percentage=round(random.uniform(5, 25), 2)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ AI匹配数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块精确数据"""
        self.stdout.write('💰 创建支付模块精确数据...')
        
        try:
            from apps.payment.models import UserVipRecord, VipPackage
            
            # 用户VIP记录 - 使用确切字段
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(8, 12)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            is_active=random.choice([True, False])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块精确数据"""
        self.stdout.write('🚨 创建举报模块精确数据...')
        
        try:
            from apps.reports.models import Blacklist, ContentModeration, SecurityLog
            
            # 1. 黑名单 - 使用确切字段
            for i in range(15):
                user, blocked_user = random.sample(self.users, 2)
                if not Blacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    Blacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'spam', 'inappropriate']),
                        notes=f'黑名单备注 #{i+1}'
                    )
            
            # 2. 内容审核 - 使用确切字段
            content_types = ['profile', 'moment', 'message']
            for i in range(25):
                ContentModeration.objects.create(
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    user=random.choice(self.users),
                    status=random.choice(['pending', 'approved', 'rejected']),
                    moderation_reason=f'审核原因 #{i+1}',
                    moderator=random.choice(self.users)
                )
            
            # 3. 安全日志 - 使用确切字段
            log_types = ['login_attempt', 'password_change', 'suspicious_activity']
            for i in range(30):
                SecurityLog.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    log_type=random.choice(log_types),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    details={'action': f'安全日志详情 #{i+1}'},
                    risk_level=random.choice(['low', 'medium', 'high'])
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))
