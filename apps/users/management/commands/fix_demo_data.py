#!/usr/bin/env python3
"""
修复并补充后台管理演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid

User = get_user_model()

class Command(BaseCommand):
    help = '修复并补充后台管理演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始修复并补充后台管理演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all()[:15])
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 修复各模块数据
        self.fix_user_preferences()
        self.fix_user_actions()
        self.fix_realtime_data()
        self.fix_security_data()
        self.create_missing_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 后台管理演示数据修复完成！'))

    def fix_user_preferences(self):
        """修复用户偏好数据"""
        self.stdout.write('👤 修复用户偏好数据...')
        
        try:
            from apps.users.models import UserPreference
            
            for user in self.users:
                if not UserPreference.objects.filter(user=user).exists():
                    UserPreference.objects.create(
                        user=user,
                        min_age=random.randint(18, 25),
                        max_age=random.randint(30, 45),
                        preferred_gender=random.choice([1, 2]),
                        preferred_location=random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                        distance_range=random.randint(10, 100),
                        preferred_education=random.choice([1, 2, 3, 4, 5])
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 用户偏好数据修复完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户偏好数据修复失败: {e}'))

    def fix_user_actions(self):
        """修复用户行为数据"""
        self.stdout.write('📊 修复用户行为数据...')
        
        try:
            from apps.users.models import UserAction
            
            # 检查现有数据量
            existing_count = UserAction.objects.count()
            if existing_count < 30:
                action_types = ['login', 'profile_view', 'like', 'message', 'search']
                for i in range(30 - existing_count):
                    UserAction.objects.create(
                        user=random.choice(self.users),
                        action_type=random.choice(action_types),
                        target_user=random.choice(self.users) if random.choice([True, False]) else None,
                        action_data={'page': 'home', 'duration': random.randint(10, 300)},
                        ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                        user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                        created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 用户行为数据修复完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户行为数据修复失败: {e}'))

    def fix_realtime_data(self):
        """修复实时通讯数据"""
        self.stdout.write('⚡ 修复实时通讯数据...')
        
        try:
            from apps.realtime.models import VoiceCall, VideoCall, VoiceMessage, RealtimeNotification, UserOnlineStatus
            
            # 补充语音通话数据（使用正确的字段名）
            if VoiceCall.objects.count() < 10:
                for i in range(10):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(30, 1800)
                    VoiceCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'voice_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            # 补充视频通话数据
            if VideoCall.objects.count() < 8:
                for i in range(8):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(60, 3600)
                    VideoCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'video_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        video_quality=random.choice(['720p', '1080p', '480p']),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            # 补充实时通知数据
            if RealtimeNotification.objects.count() < 20:
                notification_types = ['new_message', 'new_match', 'profile_view', 'gift_received', 'system_update']
                for i in range(20):
                    RealtimeNotification.objects.create(
                        user=random.choice(self.users),
                        notification_type=random.choice(notification_types),
                        title=f'通知标题 #{i+1}',
                        content=f'这是一条演示通知内容 #{i+1}',
                        is_read=random.choice([True, False]),
                        priority=random.choice(['low', 'medium', 'high']),
                        read_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None
                    )
            
            # 补充用户在线状态数据
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet']),
                        location_info={
                            'city': random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                            'country': '中国'
                        }
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 实时通讯数据修复完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据修复失败: {e}'))

    def fix_security_data(self):
        """修复安全防护数据"""
        self.stdout.write('🛡️ 修复安全防护数据...')
        
        try:
            from apps.security.models import UserSecurityProfile, SecurityConfiguration
            
            # 补充用户安全档案数据
            for user in self.users:
                if not UserSecurityProfile.objects.filter(user=user).exists():
                    UserSecurityProfile.objects.create(
                        user=user,
                        risk_score=random.randint(0, 100),
                        trust_level=random.choice(['low', 'medium', 'high']),
                        failed_login_count=random.randint(0, 5),
                        last_failed_login=timezone.now() - timedelta(hours=random.randint(1, 168)) if random.choice([True, False]) else None,
                        account_locked_until=timezone.now() + timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                        phone_verified=random.choice([True, False]),
                        email_verified=random.choice([True, False]),
                        identity_verified=random.choice([True, False]),
                        two_factor_enabled=random.choice([True, False]),
                        security_events_count=random.randint(0, 10)
                    )
            
            # 补充安全配置数据
            security_configs = [
                {'category': 'login', 'key': 'max_failed_attempts', 'value': '5'},
                {'category': 'login', 'key': 'lockout_duration', 'value': '30'},
                {'category': 'password', 'key': 'min_length', 'value': '8'},
                {'category': 'password', 'key': 'require_special_chars', 'value': 'true'},
                {'category': 'session', 'key': 'timeout_minutes', 'value': '120'},
                {'category': 'rate_limit', 'key': 'api_requests_per_minute', 'value': '100'}
            ]
            
            for config in security_configs:
                if not SecurityConfiguration.objects.filter(config_key=config['key']).exists():
                    SecurityConfiguration.objects.create(
                        category=config['category'],
                        config_key=config['key'],
                        config_value=config['value'],
                        description=f'{config["key"]}配置项',
                        is_active=True,
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 安全防护数据修复完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全防护数据修复失败: {e}'))

    def create_missing_data(self):
        """创建缺失的基础数据"""
        self.stdout.write('🔧 创建缺失的基础数据...')
        
        try:
            # 创建用户验证数据
            from apps.users.models import UserVerification
            
            verification_types = ['phone', 'email', 'identity', 'education', 'income']
            for user in self.users[:8]:
                for verification_type in random.sample(verification_types, random.randint(1, 3)):
                    if not UserVerification.objects.filter(user=user, verification_type=verification_type).exists():
                        UserVerification.objects.create(
                            user=user,
                            verification_type=verification_type,
                            status=random.choice(['pending', 'approved', 'rejected']),
                            submitted_data={'info': f'验证信息_{verification_type}'},
                            admin_notes=f'{verification_type}验证备注',
                            submitted_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                            reviewed_at=timezone.now() - timedelta(days=random.randint(1, 15)) if random.choice([True, False]) else None
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 基础数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 基础数据创建失败: {e}'))
