#!/usr/bin/env python3
"""
为最后17个缺少数据的模型创建精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最后17个缺少数据的模型创建精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最后17个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_ai_matching_data()
        self.create_payment_data()
        self.create_reports_data()
        self.create_security_data()
        self.create_operations_data()
        self.create_i18n_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最后17个模型演示数据创建完成！'))

    def create_ai_matching_data(self):
        """创建AI匹配模块精确数据"""
        self.stdout.write('🧠 创建AI匹配模块精确数据...')
        
        try:
            from apps.ai_matching.models import (
                ModelTrainingData, RecommendationExplanation, ModelPerformanceMetric, MatchingModel
            )
            
            # 1. 模型训练数据 - 使用确切字段
            data_types = ['user_interaction', 'match_result', 'conversation_data', 'feedback_data']
            
            for data_type in data_types:
                for i in range(10):  # 每种类型创建10条数据
                    user1, user2 = random.sample(self.users, 2)
                    
                    # 根据数据类型生成不同的特征
                    if data_type == 'user_interaction':
                        features = {
                            'user1_age': user1.age or 25,
                            'user2_age': user2.age or 25,
                            'age_difference': abs((user1.age or 25) - (user2.age or 25)),
                            'same_city': 1 if getattr(user1, 'city', None) == getattr(user2, 'city', None) else 0,
                            'interaction_time': timezone.now().hour
                        }
                        label = random.uniform(0, 1)  # 互动强度
                        label_type = 'interaction_strength'
                    elif data_type == 'match_result':
                        features = {
                            'compatibility_score': round(random.uniform(0.5, 1.0), 3),
                            'common_interests': random.randint(0, 5),
                            'education_match': random.choice([0, 1]),
                            'location_distance': random.randint(1, 100)
                        }
                        label = random.choice([0, 1])  # 是否匹配
                        label_type = 'match_success'
                    else:
                        features = {
                            'feature1': round(random.uniform(0, 1), 3),
                            'feature2': round(random.uniform(0, 1), 3),
                            'feature3': random.randint(1, 10)
                        }
                        label = random.uniform(0, 1)
                        label_type = 'general_score'
                    
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        user1=user1,
                        user2=user2,
                        features=features,
                        label=label,
                        label_type=label_type,
                        weight=round(random.uniform(0.5, 1.0), 2),
                        quality_score=round(random.uniform(0.7, 1.0), 2),
                        is_validated=random.choice([True, False])
                    )
            
            # 2. 推荐解释 - 使用确切字段
            for i in range(20):
                user, recommended_user = random.sample(self.users, 2)
                
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    primary_reasons = ['共同兴趣', '年龄匹配', '地理位置相近', '教育背景相似', '性格互补']
                    secondary_reasons = ['活跃时间相似', '回复速度快', '照片质量高', '资料完整']
                    match_highlights = ['都喜欢音乐', '同在一个城市', '年龄相差不大', '都是本科学历']
                    
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        primary_reason=random.choice(primary_reasons),
                        secondary_reasons=random.sample(secondary_reasons, random.randint(1, 3)),
                        match_highlights=random.sample(match_highlights, random.randint(1, 3)),
                        similarity_explanation=f'你们在多个维度上都很相似，特别是{random.choice(primary_reasons)}方面',
                        recommendation_strength=round(random.uniform(0.6, 0.95), 3)
                    )
            
            # 3. 模型性能指标 - 使用确切字段
            models = list(MatchingModel.objects.all())
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc']
            
            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        test_data_size = random.randint(1000, 10000)
                        metric_value = round(random.uniform(0.6, 0.95), 4)
                        
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=metric_value,
                            test_data_size=test_data_size,
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                            detailed_results={
                                'confusion_matrix': [[80, 20], [15, 85]],
                                'class_report': {
                                    'class_0': {'precision': 0.84, 'recall': 0.80, 'f1-score': 0.82},
                                    'class_1': {'precision': 0.81, 'recall': 0.85, 'f1-score': 0.83}
                                },
                                'feature_importance': {
                                    'age_similarity': 0.25,
                                    'interest_overlap': 0.30,
                                    'location_distance': 0.20,
                                    'activity_score': 0.25
                                }
                            }
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ AI匹配数据: ModelTrainingData {ModelTrainingData.objects.count()}条, RecommendationExplanation {RecommendationExplanation.objects.count()}条, ModelPerformanceMetric {ModelPerformanceMetric.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块精确数据"""
        self.stdout.write('💰 创建支付模块精确数据...')
        
        try:
            from apps.payment.models import UserVipRecord, VipPackage, Order
            
            # 获取VIP套餐和订单
            vip_packages = list(VipPackage.objects.all())
            orders = list(Order.objects.filter(status='paid'))  # 只获取已支付的订单
            
            if vip_packages and orders:
                for user in random.sample(self.users, random.randint(8, 12)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        order = random.choice(orders)
                        
                        # 设置VIP时间
                        start_time = timezone.now() - timedelta(days=random.randint(1, 30))
                        end_time = start_time + timedelta(days=package.duration)
                        
                        UserVipRecord.objects.create(
                            user=user,
                            order=order,
                            package=package,
                            start_time=start_time,
                            end_time=end_time,
                            duration=package.duration,
                            is_active=random.choice([True, False])
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块精确数据"""
        self.stdout.write('🚨 创建举报模块精确数据...')
        
        try:
            from apps.reports.models import Blacklist, ContentModeration, SecurityLog
            
            # 1. 黑名单 - 使用确切字段
            for i in range(15):
                user, blocked_user = random.sample(self.users, 2)
                if not Blacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    Blacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'spam', 'inappropriate', 'fake_profile']),
                        notes=f'黑名单备注 #{i+1}',
                        created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                    )
            
            # 2. 内容审核 - 使用确切字段
            content_types = ['profile', 'moment', 'message', 'comment']
            for i in range(25):
                ContentModeration.objects.create(
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    user=random.choice(self.users),
                    status=random.choice(['pending', 'approved', 'rejected']),
                    moderation_reason=f'审核原因 #{i+1}',
                    moderator=random.choice(self.users),
                    flagged_keywords=['敏感词1', '敏感词2'] if random.choice([True, False]) else [],
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )
            
            # 3. 安全日志 - 使用确切字段
            log_types = ['login_attempt', 'password_change', 'suspicious_activity', 'data_access']
            for i in range(30):
                SecurityLog.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    log_type=random.choice(log_types),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    details={'action': f'安全日志详情 #{i+1}', 'result': 'success'},
                    risk_level=random.choice(['low', 'medium', 'high', 'critical']),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据: Blacklist {Blacklist.objects.count()}条, ContentModeration {ContentModeration.objects.count()}条, SecurityLog {SecurityLog.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全模块精确数据"""
        self.stdout.write('🛡️ 创建安全模块精确数据...')
        
        try:
            from apps.security.models import SecurityAuditLog, DataEncryption, RateLimitRecord
            
            # 1. 安全审计日志 - 使用确切字段
            action_types = ['login', 'logout', 'password_change', 'profile_update', 'admin_action', 'data_export']
            
            for i in range(40):
                SecurityAuditLog.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(action_types),
                    action_description=f'安全审计日志描述 #{i+1}',
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    resource_accessed=f'/api/resource_{random.randint(1, 10)}',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    response_status=random.choice([200, 201, 400, 401, 403, 404, 500]),
                    session_id=f'session_{uuid.uuid4().hex[:8]}',
                    success=random.choice([True, False]),
                    risk_level=random.choice(['low', 'medium', 'high', 'critical']),
                    additional_data={'extra_info': f'额外信息 #{i+1}'},
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )
            
            # 2. 数据加密记录 - 使用确切字段
            data_types = ['user_profile', 'chat_message', 'payment_info', 'personal_data', 'system_config']
            algorithms = ['AES-256-GCM', 'RSA-2048', 'ChaCha20-Poly1305']
            
            for i, data_type in enumerate(data_types):
                for j in range(5):
                    DataEncryption.objects.create(
                        data_type=data_type,
                        data_id=f'{data_type}_{i}_{j}',
                        algorithm=random.choice(algorithms),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        key_version=random.randint(1, 5),
                        encryption_status='encrypted',
                        data_size=random.randint(1024, 1048576),
                        checksum=f'sha256_{uuid.uuid4().hex[:16]}',
                        access_level=random.choice(['public', 'internal', 'confidential', 'top_secret']),
                        expires_at=timezone.now() + timedelta(days=random.randint(30, 365)),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            # 3. 频率限制记录 - 使用确切字段
            limit_types = ['api_request', 'login_attempt', 'message_send', 'profile_view', 'search_query']
            
            for i in range(30):
                RateLimitRecord.objects.create(
                    identifier_type=random.choice(['ip', 'user', 'device']),
                    identifier_value=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    limit_type=random.choice(limit_types),
                    limit_count=random.randint(10, 100),
                    time_window=random.randint(60, 3600),
                    current_count=random.randint(0, 50),
                    is_exceeded=random.choice([True, False]),
                    exceeded_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                    reset_at=timezone.now() + timedelta(seconds=random.randint(60, 3600)),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 48))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全数据: SecurityAuditLog {SecurityAuditLog.objects.count()}条, DataEncryption {DataEncryption.objects.count()}条, RateLimitRecord {RateLimitRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))

    def create_operations_data(self):
        """创建运营模块精确数据"""
        self.stdout.write('🎯 创建运营模块精确数据...')

        try:
            from apps.operations.models import CampaignParticipant, ABTest, MarketingCampaign

            # 1. 活动参与者 - 使用确切字段
            campaigns = list(MarketingCampaign.objects.all())
            if campaigns:
                for campaign in campaigns:
                    participants = random.sample(self.users, random.randint(8, 12))
                    for user in participants:
                        if not CampaignParticipant.objects.filter(campaign=campaign, user=user).exists():
                            CampaignParticipant.objects.create(
                                campaign=campaign,
                                user=user,
                                participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                                conversion_achieved=random.choice([True, False]),
                                conversion_value=Decimal(str(round(random.uniform(0, 100), 2))),
                                source=random.choice(['organic', 'paid', 'referral', 'social']),
                                utm_source=random.choice(['google', 'facebook', 'wechat', 'direct']),
                                utm_medium=random.choice(['cpc', 'social', 'email', 'organic']),
                                utm_campaign=f'campaign_{campaign.id}',
                                created_at=timezone.now() - timedelta(days=random.randint(1, 15))
                            )

            # 2. A/B测试 - 使用确切字段
            tests = [
                {
                    'name': '首页布局A/B测试',
                    'description': '测试不同的首页布局对用户转化的影响',
                    'hypothesis': '新的首页布局将提高用户转化率15%'
                },
                {
                    'name': '注册流程优化测试',
                    'description': '测试简化注册流程的效果',
                    'hypothesis': '简化的注册流程将提高注册完成率20%'
                }
            ]

            for test_data in tests:
                if not ABTest.objects.filter(name=test_data['name']).exists():
                    ABTest.objects.create(
                        name=test_data['name'],
                        description=test_data['description'],
                        hypothesis=test_data['hypothesis'],
                        status=random.choice(['draft', 'running', 'completed', 'paused']),
                        control_group_size=random.randint(100, 500),
                        test_group_size=random.randint(100, 500),
                        conversion_rate_control=round(random.uniform(0.1, 0.3), 3),
                        conversion_rate_test=round(random.uniform(0.1, 0.3), 3),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        confidence_level=0.95,
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 30)),
                        created_by=random.choice(self.users),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 运营数据: CampaignParticipant {CampaignParticipant.objects.count()}条, ABTest {ABTest.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 运营数据创建失败: {e}'))

    def create_i18n_data(self):
        """创建国际化模块精确数据"""
        self.stdout.write('🌍 创建国际化模块精确数据...')

        try:
            from apps.i18n.models import (
                UserLanguagePreference, TranslationMemory, TranslationStatistics,
                LocalizationConfig, AutoTranslation, Language
            )

            # 获取语言列表
            languages = list(Language.objects.all())
            if not languages:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到语言数据，跳过国际化数据创建'))
                return

            # 1. 用户语言偏好 - 使用确切字段
            for user in self.users:
                if not UserLanguagePreference.objects.filter(user=user).exists():
                    UserLanguagePreference.objects.create(
                        user=user,
                        primary_language=random.choice(languages),
                        secondary_language=random.choice(languages),
                        auto_translate=random.choice([True, False]),
                        preferred_timezone=random.choice([
                            'Asia/Shanghai', 'Asia/Tokyo', 'America/New_York', 'Europe/London'
                        ]),
                        date_format=random.choice(['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY']),
                        time_format=random.choice(['24h', '12h']),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )

            # 2. 翻译记忆 - 使用确切字段
            for i in range(30):
                source_lang, target_lang = random.sample(languages, 2)
                TranslationMemory.objects.create(
                    source_text=f'源文本内容 #{i+1}',
                    target_text=f'目标文本内容 #{i+1}',
                    source_language=source_lang,
                    target_language=target_lang,
                    quality_score=round(random.uniform(0.7, 1.0), 2),
                    usage_count=random.randint(1, 50),
                    context=f'翻译上下文 #{i+1}',
                    domain=random.choice(['general', 'technical', 'marketing', 'legal']),
                    created_by=random.choice(self.users),
                    created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                )

            # 3. 翻译统计 - 使用确切字段
            for language in languages:
                if not TranslationStatistics.objects.filter(language=language).exists():
                    total_keys = random.randint(100, 500)
                    translated_keys = random.randint(50, total_keys)
                    TranslationStatistics.objects.create(
                        language=language,
                        date=timezone.now().date(),
                        total_keys=total_keys,
                        translated_keys=translated_keys,
                        pending_keys=total_keys - translated_keys,
                        completion_rate=round((translated_keys / total_keys) * 100, 2),
                        quality_score=round(random.uniform(0.7, 0.95), 2),
                        last_updated_by=random.choice(self.users),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )

            # 4. 本地化配置 - 使用确切字段
            for language in languages[:5]:
                if not LocalizationConfig.objects.filter(language=language).exists():
                    LocalizationConfig.objects.create(
                        language=language,
                        date_format='%Y-%m-%d',
                        time_format='%H:%M:%S',
                        currency_symbol=random.choice(['¥', '$', '€', '£']),
                        currency_code=random.choice(['CNY', 'USD', 'EUR', 'GBP']),
                        number_format='1,234.56',
                        decimal_separator='.',
                        thousand_separator=',',
                        first_day_of_week=random.choice([0, 1]),
                        rtl_support=random.choice([True, False]),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )

            # 5. 自动翻译 - 使用确切字段
            for i in range(25):
                source_lang, target_lang = random.sample(languages, 2)
                AutoTranslation.objects.create(
                    source_text=f'自动翻译源文本 #{i+1}',
                    translated_text=f'自动翻译结果 #{i+1}',
                    source_language=source_lang,
                    target_language=target_lang,
                    translation_service=random.choice(['google_translate', 'baidu_translate', 'tencent_translate']),
                    confidence_score=round(random.uniform(0.6, 0.95), 2),
                    is_reviewed=random.choice([True, False]),
                    reviewed_by=random.choice(self.users) if random.choice([True, False]) else None,
                    cost=Decimal(str(round(random.uniform(0.01, 0.1), 3))),
                    created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 国际化数据: UserLanguagePreference {UserLanguagePreference.objects.count()}条, TranslationMemory {TranslationMemory.objects.count()}条, TranslationStatistics {TranslationStatistics.objects.count()}条, LocalizationConfig {LocalizationConfig.objects.count()}条, AutoTranslation {AutoTranslation.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据创建失败: {e}'))
