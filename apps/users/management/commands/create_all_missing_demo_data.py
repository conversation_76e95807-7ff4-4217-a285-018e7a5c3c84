#!/usr/bin/env python3
"""
为所有缺少demo数据的后台导航菜单页面创建完整的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为所有缺少demo数据的后台导航菜单页面创建完整的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为所有缺少数据的导航页面创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_algorithm_config_data()
        self.create_user_compatibility_data()
        self.create_daily_recommendation_data()
        self.create_message_template_data()
        self.create_chat_statistics_data()
        self.create_online_status_data()
        self.create_comment_like_data()
        self.create_vip_record_data()
        self.create_user_gift_box_data()
        self.create_activity_participant_data()
        self.create_report_record_data()
        self.create_blacklist_data()
        self.create_user_punishment_data()
        self.create_appeal_record_data()
        self.create_content_audit_data()
        self.create_security_log_data()
        self.create_behavior_pattern_data()
        self.create_training_data()
        self.create_recommendation_explanation_data()
        self.create_performance_metric_data()
        self.create_voice_call_data()
        self.create_video_call_data()
        self.create_voice_message_data()
        self.create_realtime_notification_data()
        self.create_audit_log_data()
        self.create_data_encryption_data()
        self.create_rate_limit_data()
        self.create_user_value_score_data()
        self.create_behavior_prediction_data()
        self.create_cohort_analysis_data()
        self.create_funnel_analysis_data()
        self.create_ab_test_experiment_data()
        self.create_ab_test_participant_data()
        self.create_business_intelligence_report_data()
        self.create_predictive_model_data()
        self.create_data_quality_metric_data()
        self.create_platform_statistics_data()
        self.create_user_behavior_log_data()
        self.create_revenue_record_data()
        self.create_system_alert_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 所有缺少数据的导航页面演示数据创建完成！'))

    def create_algorithm_config_data(self):
        """创建算法配置数据"""
        self.stdout.write('⚙️ 创建算法配置数据...')
        
        try:
            from apps.matching.models import AlgorithmConfig
            
            configs = [
                {
                    'name': '协同过滤权重配置',
                    'algorithm_type': 'collaborative_filtering',
                    'config_key': 'user_similarity_weight',
                    'config_value': '0.7',
                    'description': '用户相似度在协同过滤算法中的权重'
                },
                {
                    'name': '内容推荐权重配置',
                    'algorithm_type': 'content_based',
                    'config_key': 'interest_match_weight',
                    'config_value': '0.6',
                    'description': '兴趣匹配在内容推荐中的权重'
                },
                {
                    'name': '深度学习模型参数',
                    'algorithm_type': 'deep_learning',
                    'config_key': 'learning_rate',
                    'config_value': '0.001',
                    'description': '深度学习模型的学习率参数'
                },
                {
                    'name': '推荐系统阈值',
                    'algorithm_type': 'hybrid',
                    'config_key': 'recommendation_threshold',
                    'config_value': '0.75',
                    'description': '推荐系统的最低匹配阈值'
                },
                {
                    'name': '用户活跃度权重',
                    'algorithm_type': 'behavioral',
                    'config_key': 'activity_weight',
                    'config_value': '0.3',
                    'description': '用户活跃度在算法中的权重'
                }
            ]
            
            for config in configs:
                if not AlgorithmConfig.objects.filter(config_key=config['config_key']).exists():
                    AlgorithmConfig.objects.create(
                        name=config['name'],
                        algorithm_type=config['algorithm_type'],
                        config_key=config['config_key'],
                        config_value=config['config_value'],
                        description=config['description'],
                        is_active=True,
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 算法配置数据创建完成 - 总计: {AlgorithmConfig.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 算法配置数据创建失败: {e}'))

    def create_user_compatibility_data(self):
        """创建用户兼容性数据"""
        self.stdout.write('🤝 创建用户兼容性数据...')
        
        try:
            from apps.matching.models import UserCompatibility
            
            # 为用户对创建兼容性数据
            for i in range(30):
                user1, user2 = random.sample(self.users, 2)
                if not UserCompatibility.objects.filter(user1=user1, user2=user2).exists():
                    UserCompatibility.objects.create(
                        user1=user1,
                        user2=user2,
                        compatibility_score=round(random.uniform(0.1, 0.95), 3),
                        personality_match=round(random.uniform(0.0, 1.0), 3),
                        interest_match=round(random.uniform(0.0, 1.0), 3),
                        lifestyle_match=round(random.uniform(0.0, 1.0), 3),
                        value_match=round(random.uniform(0.0, 1.0), 3),
                        communication_match=round(random.uniform(0.0, 1.0), 3),
                        calculated_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户兼容性数据创建完成 - 总计: {UserCompatibility.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户兼容性数据创建失败: {e}'))

    def create_daily_recommendation_data(self):
        """创建每日推荐数据"""
        self.stdout.write('📅 创建每日推荐数据...')
        
        try:
            from apps.matching.models import DailyRecommendation
            
            # 为每个用户创建最近几天的推荐记录
            for user in self.users:
                for days_ago in range(7):  # 最近7天
                    recommendation_date = timezone.now().date() - timedelta(days=days_ago)
                    
                    if not DailyRecommendation.objects.filter(user=user, recommendation_date=recommendation_date).exists():
                        recommended_users = random.sample([u for u in self.users if u != user], random.randint(3, 8))
                        
                        DailyRecommendation.objects.create(
                            user=user,
                            recommendation_date=recommendation_date,
                            recommended_users=recommended_users,
                            algorithm_used=random.choice(['collaborative_filtering', 'content_based', 'hybrid', 'deep_learning']),
                            total_recommendations=len(recommended_users),
                            viewed_count=random.randint(0, len(recommended_users)),
                            liked_count=random.randint(0, 3),
                            matched_count=random.randint(0, 1),
                            recommendation_quality_score=round(random.uniform(0.6, 0.95), 3)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 每日推荐数据创建完成 - 总计: {DailyRecommendation.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 每日推荐数据创建失败: {e}'))

    def create_message_template_data(self):
        """创建消息模板数据"""
        self.stdout.write('💬 创建消息模板数据...')
        
        try:
            from apps.chat.models import MessageTemplate
            
            templates = [
                {
                    'name': '欢迎新用户',
                    'template_type': 'welcome',
                    'content': '欢迎来到相亲交友平台！祝您找到心仪的另一半。',
                    'variables': ['user_name']
                },
                {
                    'name': '匹配成功通知',
                    'template_type': 'match_success',
                    'content': '恭喜！您与{match_user}匹配成功，快去聊天吧！',
                    'variables': ['match_user']
                },
                {
                    'name': '新消息提醒',
                    'template_type': 'new_message',
                    'content': '您收到了来自{sender}的新消息，快去查看吧！',
                    'variables': ['sender']
                },
                {
                    'name': 'VIP到期提醒',
                    'template_type': 'vip_expiry',
                    'content': '您的VIP会员将在{days}天后到期，请及时续费。',
                    'variables': ['days']
                },
                {
                    'name': '活动邀请',
                    'template_type': 'activity_invitation',
                    'content': '精彩活动{activity_name}即将开始，快来参加吧！',
                    'variables': ['activity_name']
                },
                {
                    'name': '系统维护通知',
                    'template_type': 'system_maintenance',
                    'content': '系统将于{time}进行维护，预计持续{duration}小时。',
                    'variables': ['time', 'duration']
                }
            ]
            
            for template in templates:
                if not MessageTemplate.objects.filter(name=template['name']).exists():
                    MessageTemplate.objects.create(
                        name=template['name'],
                        template_type=template['template_type'],
                        content=template['content'],
                        variables=template['variables'],
                        is_active=True,
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 消息模板数据创建完成 - 总计: {MessageTemplate.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 消息模板数据创建失败: {e}'))

    def create_chat_statistics_data(self):
        """创建聊天统计数据"""
        self.stdout.write('📊 创建聊天统计数据...')

        try:
            from apps.chat.models import ChatStatistics

            # 为每个用户创建聊天统计数据
            for user in self.users:
                if not ChatStatistics.objects.filter(user=user).exists():
                    ChatStatistics.objects.create(
                        user=user,
                        total_conversations=random.randint(5, 50),
                        total_messages_sent=random.randint(100, 1000),
                        total_messages_received=random.randint(80, 800),
                        average_response_time=random.randint(30, 300),  # 秒
                        active_conversations=random.randint(1, 10),
                        last_message_time=timezone.now() - timedelta(hours=random.randint(1, 48)),
                        favorite_emoji=random.choice(['😊', '❤️', '👍', '😂', '🥰']),
                        most_active_hour=random.randint(9, 23),
                        conversation_success_rate=round(random.uniform(0.3, 0.8), 2)
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 聊天统计数据创建完成 - 总计: {ChatStatistics.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 聊天统计数据创建失败: {e}'))

    def create_online_status_data(self):
        """创建在线状态数据"""
        self.stdout.write('🟢 创建在线状态数据...')

        try:
            from apps.realtime.models import UserOnlineStatus

            # 为每个用户创建在线状态记录
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet']),
                        location_info={
                            'city': random.choice(['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉']),
                            'country': '中国'
                        },
                        is_invisible=random.choice([True, False])
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 在线状态数据创建完成 - 总计: {UserOnlineStatus.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 在线状态数据创建失败: {e}'))

    def create_comment_like_data(self):
        """创建评论点赞数据"""
        self.stdout.write('👍 创建评论点赞数据...')

        try:
            from apps.moments.models import CommentLike
            from apps.moments.models import Comment

            # 获取现有评论
            comments = list(Comment.objects.all())
            if not comments:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到评论，跳过评论点赞数据创建'))
                return

            # 为评论创建点赞记录
            for i in range(50):
                comment = random.choice(comments)
                user = random.choice(self.users)

                if not CommentLike.objects.filter(comment=comment, user=user).exists():
                    CommentLike.objects.create(
                        comment=comment,
                        user=user,
                        created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 评论点赞数据创建完成 - 总计: {CommentLike.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 评论点赞数据创建失败: {e}'))

    def create_vip_record_data(self):
        """创建VIP记录数据"""
        self.stdout.write('💎 创建VIP记录数据...')

        try:
            from apps.payments.models import VIPRecord
            from apps.payments.models import VIPPackage

            # 获取VIP套餐
            vip_packages = list(VIPPackage.objects.all())
            if not vip_packages:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到VIP套餐，跳过VIP记录数据创建'))
                return

            # 为用户创建VIP记录
            for user in random.sample(self.users, random.randint(8, 12)):
                package = random.choice(vip_packages)

                if not VIPRecord.objects.filter(user=user).exists():
                    start_date = timezone.now() - timedelta(days=random.randint(1, 90))

                    VIPRecord.objects.create(
                        user=user,
                        package=package,
                        start_date=start_date,
                        end_date=start_date + timedelta(days=package.duration_days),
                        is_active=random.choice([True, False]),
                        auto_renew=random.choice([True, False]),
                        purchase_price=package.price,
                        payment_method=random.choice(['wechat', 'alipay', 'card']),
                        created_at=start_date
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ VIP记录数据创建完成 - 总计: {VIPRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ VIP记录数据创建失败: {e}'))

    def create_user_gift_box_data(self):
        """创建用户礼物盒数据"""
        self.stdout.write('🎁 创建用户礼物盒数据...')

        try:
            from apps.gifts.models import UserGiftBox
            from apps.gifts.models import Gift

            # 获取礼物
            gifts = list(Gift.objects.all())
            if not gifts:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到礼物，跳过用户礼物盒数据创建'))
                return

            # 为用户创建礼物盒记录
            for user in self.users:
                # 每个用户随机拥有1-5种礼物
                user_gifts = random.sample(gifts, random.randint(1, min(5, len(gifts))))

                for gift in user_gifts:
                    if not UserGiftBox.objects.filter(user=user, gift=gift).exists():
                        UserGiftBox.objects.create(
                            user=user,
                            gift=gift,
                            quantity=random.randint(1, 10),
                            obtained_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                            source=random.choice(['purchase', 'gift_received', 'activity_reward', 'daily_check_in'])
                        )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户礼物盒数据创建完成 - 总计: {UserGiftBox.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户礼物盒数据创建失败: {e}'))

    def create_activity_participant_data(self):
        """创建活动参与者数据"""
        self.stdout.write('🎪 创建活动参与者数据...')

        try:
            from apps.gifts.models import GiftActivity, ActivityParticipant

            # 获取礼物活动
            activities = list(GiftActivity.objects.all())
            if not activities:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到礼物活动，跳过活动参与者数据创建'))
                return

            # 为活动创建参与者记录
            for activity in activities:
                participants = random.sample(self.users, random.randint(5, 12))

                for user in participants:
                    if not ActivityParticipant.objects.filter(activity=activity, user=user).exists():
                        ActivityParticipant.objects.create(
                            activity=activity,
                            user=user,
                            participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                            points_earned=random.randint(10, 100),
                            rewards_received=random.choice([True, False]),
                            completion_status=random.choice(['completed', 'in_progress', 'not_started'])
                        )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 活动参与者数据创建完成 - 总计: {ActivityParticipant.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 活动参与者数据创建失败: {e}'))

    def create_report_record_data(self):
        """创建举报记录数据"""
        self.stdout.write('🚨 创建举报记录数据...')

        try:
            from apps.reports.models import ReportRecord

            report_types = ['inappropriate_content', 'harassment', 'fake_profile', 'spam', 'other']
            statuses = ['pending', 'investigating', 'resolved', 'rejected']

            # 创建举报记录
            for i in range(25):
                reporter = random.choice(self.users)
                reported_user = random.choice([u for u in self.users if u != reporter])

                ReportRecord.objects.create(
                    reporter=reporter,
                    reported_user=reported_user,
                    report_type=random.choice(report_types),
                    description=f'举报描述 #{i+1} - 用户行为不当',
                    evidence_urls=[f'/media/reports/evidence_{i}_{j}.jpg' for j in range(random.randint(1, 3))],
                    status=random.choice(statuses),
                    admin_notes=f'管理员备注 #{i+1}' if random.choice([True, False]) else '',
                    created_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                    resolved_at=timezone.now() - timedelta(days=random.randint(1, 15)) if random.choice([True, False]) else None
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报记录数据创建完成 - 总计: {ReportRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报记录数据创建失败: {e}'))

    def create_blacklist_data(self):
        """创建黑名单数据"""
        self.stdout.write('🚫 创建黑名单数据...')

        try:
            from apps.users.models import UserBlacklist

            # 为用户创建黑名单记录
            for i in range(20):
                user, blocked_user = random.sample(self.users, 2)

                if not UserBlacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    UserBlacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'inappropriate_behavior', 'spam', 'fake_profile', 'other']),
                        notes=f'黑名单备注 #{i+1}',
                        created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 黑名单数据创建完成 - 总计: {UserBlacklist.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 黑名单数据创建失败: {e}'))

    def create_user_punishment_data(self):
        """创建用户处罚数据"""
        self.stdout.write('⚖️ 创建用户处罚数据...')

        try:
            from apps.users.models import UserPunishment

            punishment_types = ['warning', 'mute', 'temporary_ban', 'permanent_ban', 'feature_restriction']

            # 创建用户处罚记录
            for i in range(15):
                user = random.choice(self.users)
                punishment_type = random.choice(punishment_types)

                duration_hours = None
                if punishment_type in ['mute', 'temporary_ban', 'feature_restriction']:
                    duration_hours = random.randint(24, 168)  # 1-7天

                UserPunishment.objects.create(
                    user=user,
                    punishment_type=punishment_type,
                    reason=f'违规行为处罚 #{i+1}',
                    description=f'用户因违反社区规定被处罚',
                    duration_hours=duration_hours,
                    is_active=random.choice([True, False]),
                    admin_user=random.choice(self.users),
                    created_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                    expires_at=timezone.now() + timedelta(hours=duration_hours) if duration_hours else None
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户处罚数据创建完成 - 总计: {UserPunishment.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户处罚数据创建失败: {e}'))

    def create_appeal_record_data(self):
        """创建申诉记录数据"""
        self.stdout.write('📝 创建申诉记录数据...')

        try:
            from apps.users.models import AppealRecord, UserPunishment

            # 获取处罚记录
            punishments = list(UserPunishment.objects.all())
            if not punishments:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到处罚记录，跳过申诉记录数据创建'))
                return

            statuses = ['pending', 'under_review', 'approved', 'rejected']

            # 为部分处罚创建申诉记录
            for punishment in random.sample(punishments, min(8, len(punishments))):
                if not AppealRecord.objects.filter(punishment=punishment).exists():
                    AppealRecord.objects.create(
                        punishment=punishment,
                        user=punishment.user,
                        appeal_reason='我认为处罚不公正，请重新审核',
                        evidence_description='提供相关证据说明情况',
                        evidence_urls=[f'/media/appeals/evidence_{punishment.id}_{i}.jpg' for i in range(random.randint(1, 3))],
                        status=random.choice(statuses),
                        admin_response='管理员回复内容' if random.choice([True, False]) else '',
                        created_at=timezone.now() - timedelta(days=random.randint(1, 15)),
                        resolved_at=timezone.now() - timedelta(days=random.randint(1, 10)) if random.choice([True, False]) else None
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 申诉记录数据创建完成 - 总计: {AppealRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 申诉记录数据创建失败: {e}'))
