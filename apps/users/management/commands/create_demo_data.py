# apps/users/management/commands/create_demo_data.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random
from decimal import Decimal

from apps.users.models import ParentProfile, ChildProfile, MatchmakerProfile, WechatConfig, AppConfig
from apps.matching.models import Like, Match, RecommendationLog, MatchingStatistics
from apps.chat.models import ChatSession, ChatMessage
from apps.payment.models import VipPackage, CoinPackage, Order, PaymentRecord, UserVipRecord, CoinTransaction
from apps.gifts.models import GiftCategory, Gift, GiftRecord, UserGiftBox, GiftStatistics, GiftActivity
from apps.moments.models import Moment, MomentLike, MomentComment, MomentView, MomentReport, Topic, MomentTopic

User = get_user_model()


class Command(BaseCommand):
    help = '创建演示数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有演示数据',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.clear_demo_data()
            
        self.stdout.write('开始创建演示数据...')
        
        # 创建演示用户
        self.create_demo_users()
        
        # 创建VIP套餐
        self.create_vip_packages()
        
        # 创建金币套餐
        self.create_coin_packages()
        
        # 创建礼物分类和礼物
        self.create_gifts()
        
        # 创建红娘档案
        self.create_matchmaker_profiles()
        
        # 创建匹配记录
        self.create_match_records()
        
        # 创建订单记录
        self.create_orders()
        
        # 创建聊天记录
        self.create_chat_records()
        
        # 创建礼物记录
        self.create_gift_records()
        
        # 创建礼物活动
        self.create_gift_activities()

        # 创建动态话题和内容
        self.create_moments_data()

        self.stdout.write(self.style.SUCCESS('✅ 演示数据创建完成！'))

    def clear_demo_data(self):
        self.stdout.write('清除现有演示数据...')
        
        # 清除各种记录（保留配置数据）
        # 清除动态相关数据
        MomentLike.objects.all().delete()
        MomentComment.objects.all().delete()
        MomentView.objects.all().delete()
        MomentReport.objects.all().delete()
        MomentTopic.objects.all().delete()
        Moment.objects.all().delete()
        Topic.objects.all().delete()

        # 清除礼物相关数据
        GiftRecord.objects.all().delete()
        UserGiftBox.objects.all().delete()
        GiftStatistics.objects.all().delete()
        GiftActivity.objects.all().delete()
        
        ChatMessage.objects.all().delete()
        ChatSession.objects.all().delete()
        
        PaymentRecord.objects.all().delete()
        Order.objects.all().delete()
        UserVipRecord.objects.all().delete()
        CoinTransaction.objects.all().delete()

        # 清除匹配相关数据
        Like.objects.all().delete()
        Match.objects.all().delete()
        RecommendationLog.objects.all().delete()
        MatchingStatistics.objects.all().delete()
        MatchmakerProfile.objects.all().delete()
        
        # 删除演示用户（保留超级用户）
        User.objects.filter(is_superuser=False).delete()
        
        self.stdout.write(self.style.SUCCESS('✓ 演示数据清除完成'))

    def create_demo_users(self):
        self.stdout.write('创建演示用户...')

        # 创建普通用户
        users_data = [
            {'username': 'demo_user_1', 'nickname': '小美', 'gender': 2, 'age': 25, 'city': '北京'},
            {'username': 'demo_user_2', 'nickname': '小明', 'gender': 1, 'age': 28, 'city': '上海'},
            {'username': 'demo_user_3', 'nickname': '小丽', 'gender': 2, 'age': 26, 'city': '深圳'},
            {'username': 'demo_user_4', 'nickname': '小强', 'gender': 1, 'age': 30, 'city': '广州'},
            {'username': 'demo_user_5', 'nickname': '小雨', 'gender': 2, 'age': 24, 'city': '杭州'},
            {'username': 'demo_user_6', 'nickname': '小华', 'gender': 1, 'age': 32, 'city': '成都'},
            {'username': 'demo_user_7', 'nickname': '小芳', 'gender': 2, 'age': 27, 'city': '武汉'},
            {'username': 'demo_user_8', 'nickname': '小刚', 'gender': 1, 'age': 29, 'city': '西安'},
        ]

        # 创建家长用户
        parent_users_data = [
            {'username': 'parent_1', 'nickname': '张妈妈', 'real_name': '张丽华', 'city': '北京'},
            {'username': 'parent_2', 'nickname': '李爸爸', 'real_name': '李建国', 'city': '上海'},
            {'username': 'parent_3', 'nickname': '王妈妈', 'real_name': '王秀英', 'city': '深圳'},
        ]

        self.demo_users = []

        # 创建普通用户
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'openid': f'openid_{user_data["username"]}',
                    'nickname': user_data['nickname'],
                    'gender': user_data['gender'],
                    'age': user_data['age'],
                    'location': user_data['city'],
                    'bio': f'我是{user_data["nickname"]}，来自{user_data["city"]}，希望在这里找到真爱。',
                    'height': random.randint(155, 185),
                    'education': random.randint(1, 5),
                    'profession': random.choice(['程序员', '设计师', '教师', '医生', '律师', '工程师']),
                    'income': random.randint(1, 6),
                    'coins': random.randint(100, 1000),
                    'vip_level': random.randint(0, 3),
                    'is_active': True
                }
            )

            # 为VIP用户创建VIP记录
            if created and user.vip_level > 0:
                user.vip_expire_time = timezone.now() + timedelta(days=random.randint(30, 365))
                user.save()

                # 创建VIP记录（如果有VIP套餐的话）
                # UserVipRecord.objects.create(
                #     user=user,
                #     package_id=1,
                #     start_time=timezone.now() - timedelta(days=random.randint(1, 30)),
                #     end_time=user.vip_expire_time,
                #     duration=30,
                #     is_active=True
                # )

            self.demo_users.append(user)

        # 创建家长用户和档案
        for parent_data in parent_users_data:
            user, created = User.objects.get_or_create(
                username=parent_data['username'],
                defaults={
                    'openid': f'openid_{parent_data["username"]}',
                    'nickname': parent_data['nickname'],
                    'user_type': 'parent',
                    'location': parent_data['city'],
                    'is_active': True
                }
            )

            if created:
                # 创建家长档案
                parent_profile = ParentProfile.objects.create(
                    user=user,
                    real_name=parent_data['real_name'],
                    relationship='父母',
                    description=f'我们是来自{parent_data["city"]}的普通家庭，希望为孩子找到合适的对象。',
                    success_count=random.randint(0, 5),
                    is_verified=True
                )

                # 为家长创建子女档案
                child_name = random.choice(['小明', '小红', '小华', '小丽'])
                ChildProfile.objects.create(
                    parent=parent_profile,
                    name=child_name,
                    gender=random.randint(1, 2),
                    age=random.randint(22, 35),
                    birthday=timezone.now().date() - timedelta(days=random.randint(8000, 12000)),
                    location=parent_data['city'],
                    profession=random.choice(['程序员', '设计师', '教师', '医生', '律师']),
                    education=random.randint(3, 5),
                    height=random.randint(155, 185),
                    weight=random.randint(45, 80),
                    income=random.randint(3, 6),
                    marriage_status=1,
                    bio=f'我是{child_name}，希望找到一个有共同话题的人',
                    photos=['photo1.jpg', 'photo2.jpg'],
                    requirements='希望对方人品好，有责任心',
                    status='active'
                )

            self.demo_users.append(user)

        self.stdout.write(self.style.SUCCESS(f'✓ 创建了 {len(self.demo_users)} 个演示用户（包括家长用户）'))

    def create_vip_packages(self):
        self.stdout.write('创建VIP套餐...')
        
        packages_data = [
            {
                'name': 'VIP月卡',
                'description': '享受一个月的VIP特权服务',
                'duration': 30,
                'original_price': Decimal('99.00'),
                'price': Decimal('68.00'),
                'features': ['无限点赞', '查看访客', '隐身浏览', '撤销操作', '优先匹配'],
                'daily_likes_limit': 999,
                'daily_super_likes': 5,
                'can_see_visitors': True,
                'can_invisible_browse': True,
                'can_undo_actions': True,
                'priority_matching': True,
                'is_popular': True,
                'sort_order': 1
            },
            {
                'name': 'VIP季卡',
                'description': '享受三个月的VIP特权服务，更优惠',
                'duration': 90,
                'original_price': Decimal('297.00'),
                'price': Decimal('168.00'),
                'features': ['无限点赞', '查看访客', '隐身浏览', '撤销操作', '优先匹配', '专属客服'],
                'daily_likes_limit': 999,
                'daily_super_likes': 8,
                'can_see_visitors': True,
                'can_invisible_browse': True,
                'can_undo_actions': True,
                'priority_matching': True,
                'is_popular': False,
                'sort_order': 2
            },
            {
                'name': 'VIP年卡',
                'description': '享受一年的VIP特权服务，最超值',
                'duration': 365,
                'original_price': Decimal('1188.00'),
                'price': Decimal('588.00'),
                'features': ['无限点赞', '查看访客', '隐身浏览', '撤销操作', '优先匹配', '专属客服', '专属标识'],
                'daily_likes_limit': 999,
                'daily_super_likes': 10,
                'can_see_visitors': True,
                'can_invisible_browse': True,
                'can_undo_actions': True,
                'priority_matching': True,
                'is_popular': True,
                'sort_order': 3
            }
        ]
        
        for package_data in packages_data:
            VipPackage.objects.get_or_create(
                name=package_data['name'],
                defaults=package_data
            )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 3 个VIP套餐'))

    def create_coin_packages(self):
        self.stdout.write('创建金币套餐...')
        
        packages_data = [
            {
                'name': '金币小包',
                'description': '100金币，适合轻度使用',
                'coin_amount': 100,
                'bonus_coins': 0,
                'price': Decimal('10.00'),
                'sort_order': 1
            },
            {
                'name': '金币中包',
                'description': '500金币+50赠送，性价比之选',
                'coin_amount': 500,
                'bonus_coins': 50,
                'price': Decimal('50.00'),
                'is_popular': True,
                'sort_order': 2
            },
            {
                'name': '金币大包',
                'description': '1000金币+150赠送，超值优惠',
                'coin_amount': 1000,
                'bonus_coins': 150,
                'price': Decimal('100.00'),
                'sort_order': 3
            },
            {
                'name': '金币超值包',
                'description': '2000金币+400赠送，最划算',
                'coin_amount': 2000,
                'bonus_coins': 400,
                'price': Decimal('200.00'),
                'sort_order': 4
            }
        ]
        
        for package_data in packages_data:
            CoinPackage.objects.get_or_create(
                name=package_data['name'],
                defaults=package_data
            )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 4 个金币套餐'))

    def create_gifts(self):
        self.stdout.write('创建礼物分类和礼物...')
        
        # 创建礼物分类
        categories_data = [
            {'name': '鲜花', 'description': '浪漫鲜花，表达心意', 'icon': '🌹', 'sort_order': 1},
            {'name': '饰品', 'description': '精美饰品，彰显品味', 'icon': '💎', 'sort_order': 2},
            {'name': '美食', 'description': '美味佳肴，分享快乐', 'icon': '🍰', 'sort_order': 3},
            {'name': '奢侈品', 'description': '奢华礼品，尊贵体验', 'icon': '👑', 'sort_order': 4},
        ]
        
        categories = {}
        for cat_data in categories_data:
            category, _ = GiftCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            categories[cat_data['name']] = category
        
        # 创建礼物
        gifts_data = [
            {'category': '鲜花', 'name': '红玫瑰', 'price': 10, 'description': '一朵红玫瑰，代表我的心'},
            {'category': '鲜花', 'name': '玫瑰花束', 'price': 99, 'original_price': 120, 'description': '99朵玫瑰，爱你久久', 'is_popular': True},
            {'category': '饰品', 'name': '钻石戒指', 'price': 999, 'description': '闪耀钻石，永恒承诺', 'is_rare': True},
            {'category': '饰品', 'name': '项链', 'price': 299, 'description': '精美项链，装点美丽'},
            {'category': '美食', 'name': '巧克力', 'price': 50, 'description': '甜蜜巧克力，甜蜜心情'},
            {'category': '美食', 'name': '生日蛋糕', 'price': 188, 'description': '生日快乐，甜蜜祝福'},
            {'category': '奢侈品', 'name': '豪华游艇', 'price': 9999, 'description': '豪华游艇，尊贵享受', 'is_rare': True, 'is_limited': True, 'limited_quantity': 10},
            {'category': '奢侈品', 'name': '跑车', 'price': 8888, 'description': '炫酷跑车，速度与激情', 'is_rare': True},
        ]
        
        for gift_data in gifts_data:
            category = categories[gift_data.pop('category')]
            Gift.objects.get_or_create(
                category=category,
                name=gift_data['name'],
                defaults={**gift_data, 'send_count': random.randint(0, 100)}
            )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 4 个礼物分类和 8 个礼物'))

    def create_matchmaker_profiles(self):
        self.stdout.write('创建红娘档案...')

        # 创建红娘用户
        matchmaker_data = [
            {'username': 'matchmaker_1', 'nickname': '张红娘', 'experience_years': 5, 'success_rate': 89.5, 'specialties': ['高端匹配', '海外华人']},
            {'username': 'matchmaker_2', 'nickname': '李红娘', 'experience_years': 3, 'success_rate': 82.3, 'specialties': ['年轻群体', '互联网行业']},
            {'username': 'matchmaker_3', 'nickname': '王红娘', 'experience_years': 8, 'success_rate': 91.2, 'specialties': ['企业高管', '医生律师']},
            {'username': 'matchmaker_4', 'nickname': '陈红娘', 'experience_years': 2, 'success_rate': 78.9, 'specialties': ['艺术工作者', '创意人群']},
        ]

        for mm_data in matchmaker_data:
            user, created = User.objects.get_or_create(
                username=mm_data['username'],
                defaults={
                    'openid': f'openid_{mm_data["username"]}',
                    'nickname': mm_data['nickname'],
                    'user_type': 'matchmaker',
                    'is_active': True
                }
            )

            if created:
                MatchmakerProfile.objects.create(
                    user=user,
                    real_name=mm_data['nickname'],
                    company='专业红娘服务中心',
                    experience_years=mm_data['experience_years'],
                    level=random.choice(['bronze', 'silver', 'gold', 'diamond']),
                    rating=round(random.uniform(4.5, 5.0), 1),
                    specialties=mm_data['specialties'],
                    total_clients=random.randint(50, 200),
                    active_clients=random.randint(10, 50),
                    successful_matches=random.randint(30, 150),
                    success_rate=mm_data['success_rate'],
                    monthly_income=Decimal(str(random.randint(5000, 20000))),
                    total_income=Decimal(str(random.randint(50000, 500000))),
                    service_areas=['北京', '上海', '深圳'],
                    min_fee=Decimal('500'),
                    max_fee=Decimal('5000'),
                    is_certified=True,
                    status='active'
                )

        self.stdout.write(self.style.SUCCESS('✓ 创建了 4 个红娘档案'))

    def create_match_records(self):
        self.stdout.write('创建匹配记录...')

        if len(self.demo_users) < 4:
            self.stdout.write(self.style.WARNING('用户数量不足，跳过匹配记录创建'))
            return

        # 创建点赞记录
        for i in range(15):
            user1 = random.choice(self.demo_users)
            user2 = random.choice([u for u in self.demo_users if u != user1])

            # 创建点赞记录
            like, created = Like.objects.get_or_create(
                user=user1,
                target_user=user2,
                defaults={
                    'is_mutual': random.choice([True, False])
                }
            )

            # 如果是互相喜欢，创建匹配记录
            if created and like.is_mutual:
                Match.objects.create(
                    user1=user1,
                    user2=user2,
                    status='matched',
                    match_score=random.uniform(70.0, 95.0),
                    match_reasons=['兴趣相投', '年龄合适', '地理位置接近'],
                    matched_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )

        # 创建推荐日志
        for i in range(20):
            user = random.choice(self.demo_users)
            recommended_user = random.choice([u for u in self.demo_users if u != user])

            RecommendationLog.objects.create(
                user=user,
                recommended_user=recommended_user,
                algorithm_version='v1.0',
                recommendation_score=random.uniform(60.0, 90.0),
                user_action=random.choice(['like', 'pass', 'view', '']),
                feedback_score=random.uniform(3.0, 5.0) if random.choice([True, False]) else None
            )

        # 创建用户匹配统计
        for user in self.demo_users:
            MatchingStatistics.objects.get_or_create(
                user=user,
                defaults={
                    'total_likes_sent': random.randint(5, 50),
                    'total_likes_received': random.randint(3, 40),
                    'total_super_likes_sent': random.randint(0, 10),
                    'total_super_likes_received': random.randint(0, 8),
                    'total_matches': random.randint(1, 15),
                    'total_profile_views': random.randint(10, 100),
                    'today_likes_sent': random.randint(0, 5),
                    'today_likes_received': random.randint(0, 3),
                    'today_super_likes_sent': random.randint(0, 2),
                    'today_matches': random.randint(0, 2),
                    'today_profile_views': random.randint(0, 10),
                    'like_success_rate': random.uniform(0.1, 0.8),
                    'super_like_success_rate': random.uniform(0.2, 0.9),
                    'last_like_time': timezone.now() - timedelta(days=random.randint(1, 7)),
                    'last_match_time': timezone.now() - timedelta(days=random.randint(1, 30))
                }
            )

        self.stdout.write(self.style.SUCCESS('✓ 创建了点赞记录、匹配记录、推荐日志和用户统计'))

    def create_orders(self):
        self.stdout.write('创建订单记录...')
        
        if not self.demo_users:
            return
        
        # 创建VIP订单
        vip_packages = VipPackage.objects.all()
        for i in range(5):
            user = random.choice(self.demo_users)
            package = random.choice(vip_packages)
            
            order = Order.objects.create(
                user=user,
                product_type='vip',
                product_id=package.id,
                product_name=package.name,
                original_amount=package.original_price,
                discount_amount=package.original_price - package.price,
                final_amount=package.price,
                payment_method=random.choice(['wechat', 'alipay']),
                status=random.choice(['paid', 'pending', 'cancelled']),
                transaction_id=f'TXN{timezone.now().timestamp():.0f}{i}'
            )
            
            if order.status == 'paid':
                order.paid_at = timezone.now() - timedelta(days=random.randint(1, 30))
                order.save()
                
                # 创建支付记录
                PaymentRecord.objects.create(
                    order=order,
                    payment_method=order.payment_method,
                    amount=order.final_amount,
                    status='success',
                    third_party_transaction_id=f'PAY{order.transaction_id}',
                    paid_at=order.paid_at
                )
        
        # 创建金币订单
        coin_packages = CoinPackage.objects.all()
        for i in range(8):
            user = random.choice(self.demo_users)
            package = random.choice(coin_packages)
            
            order = Order.objects.create(
                user=user,
                product_type='coin',
                product_id=package.id,
                product_name=package.name,
                original_amount=package.price,
                final_amount=package.price,
                payment_method=random.choice(['wechat', 'alipay']),
                status=random.choice(['paid', 'pending']),
                transaction_id=f'COIN{timezone.now().timestamp():.0f}{i}'
            )
            
            if order.status == 'paid':
                order.paid_at = timezone.now() - timedelta(days=random.randint(1, 15))
                order.save()
                
                # 创建金币交易记录
                CoinTransaction.objects.create(
                    user=user,
                    transaction_type='purchase',
                    amount=package.coin_amount + package.bonus_coins,
                    balance_before=random.randint(0, 500),
                    balance_after=random.randint(500, 1500),
                    description=f'购买{package.name}',
                    order=order
                )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 13 个订单记录'))

    def create_chat_records(self):
        self.stdout.write('创建聊天记录...')

        if len(self.demo_users) < 2:
            return

        # 获取已有的匹配记录来创建聊天会话
        matches = Match.objects.filter(status='matched')[:5]

        if not matches:
            self.stdout.write(self.style.WARNING('没有匹配记录，跳过聊天记录创建'))
            return

        # 为匹配记录创建聊天会话和消息
        for match in matches:
            session, created = ChatSession.objects.get_or_create(
                match=match,
                defaults={
                    'user1': match.user1,
                    'user2': match.user2,
                    'status': 'active',
                    'last_message_time': timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                    'unread_count_user1': random.randint(0, 5),
                    'unread_count_user2': random.randint(0, 3)
                }
            )

            if not created:
                continue

            # 创建聊天消息
            messages = [
                '你好！很高兴认识你',
                '我也很高兴认识你',
                '你平时喜欢做什么呢？',
                '我喜欢看电影、听音乐，你呢？',
                '我也喜欢看电影，有时间一起看吧',
                '好啊，期待！'
            ]

            for j, content in enumerate(messages[:random.randint(2, 6)]):
                ChatMessage.objects.create(
                    session=session,
                    sender=match.user1 if j % 2 == 0 else match.user2,
                    receiver=match.user2 if j % 2 == 0 else match.user1,
                    content=content,
                    message_type='text',
                    is_read=random.choice([True, False]),
                    created_at=timezone.now() - timedelta(minutes=random.randint(1, 60) * (len(messages) - j))
                )

        self.stdout.write(self.style.SUCCESS(f'✓ 创建了 {len(matches)} 个聊天会话和相关消息'))

    def create_gift_records(self):
        self.stdout.write('创建礼物记录...')
        
        if not self.demo_users:
            return
        
        gifts = Gift.objects.all()
        if not gifts:
            return
        
        # 创建礼物赠送记录
        for i in range(15):
            sender = random.choice(self.demo_users)
            receiver = random.choice([u for u in self.demo_users if u != sender])
            gift = random.choice(gifts)
            quantity = random.randint(1, 3)
            
            GiftRecord.objects.create(
                sender=sender,
                receiver=receiver,
                gift=gift,
                quantity=quantity,
                unit_price=gift.price,
                total_price=gift.price * quantity,
                message=random.choice([
                    '送你一份小心意',
                    '希望你喜欢',
                    '想你了',
                    '祝你开心每一天'
                ]),
                is_anonymous=random.choice([True, False]),
                is_read=random.choice([True, False])
            )
            
            # 更新礼物发送次数
            gift.send_count += quantity
            gift.save()
        
        # 创建用户礼物统计
        for user in self.demo_users:
            sent_records = GiftRecord.objects.filter(sender=user)
            received_records = GiftRecord.objects.filter(receiver=user)

            GiftStatistics.objects.get_or_create(
                user=user,
                defaults={
                    'total_gifts_sent': sent_records.count(),
                    'total_gifts_received': received_records.count(),
                    'total_coins_spent': sum(r.total_price for r in sent_records),
                    'total_coins_received': sum(r.total_price for r in received_records),
                    'today_gifts_sent': random.randint(0, 5),
                    'today_gifts_received': random.randint(0, 3),
                    'today_coins_spent': random.randint(0, 100),
                    'sender_rank': random.randint(1, 100),
                    'receiver_rank': random.randint(1, 100)
                }
            )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 15 个礼物记录和用户统计'))

    def create_gift_activities(self):
        self.stdout.write('创建礼物活动...')
        
        activities_data = [
            {
                'name': '情人节礼物大赏',
                'description': '情人节特别活动，送礼物赢大奖',
                'start_time': timezone.now() - timedelta(days=7),
                'end_time': timezone.now() + timedelta(days=7),
                'max_participants': 1000,
                'rules': ['活动期间送出礼物即可参与', '送礼越多排名越高', '前10名获得丰厚奖励'],
                'rewards': ['第1名：豪华游艇', '第2-3名：跑车', '第4-10名：钻石戒指'],
                'is_active': True
            },
            {
                'name': '春季送礼节',
                'description': '春暖花开，爱意满满',
                'start_time': timezone.now() + timedelta(days=30),
                'end_time': timezone.now() + timedelta(days=60),
                'max_participants': 500,
                'rules': ['活动期间送花类礼物双倍积分', '邀请好友参与额外奖励'],
                'rewards': ['积分兑换精美礼品', '幸运抽奖'],
                'is_active': True
            }
        ]
        
        for activity_data in activities_data:
            activity, created = GiftActivity.objects.get_or_create(
                name=activity_data['name'],
                defaults={
                    **activity_data,
                    'current_participants': random.randint(50, 200)
                }
            )
        
        self.stdout.write(self.style.SUCCESS('✓ 创建了 2 个礼物活动'))

    def create_moments_data(self):
        self.stdout.write('创建动态话题和内容...')

        if not self.demo_users:
            return

        # 创建话题
        topics_data = [
            {
                'name': '今日心情',
                'description': '分享你的每日心情和感悟',
                'is_official': True,
                'is_trending': True,
                'hot_score': 95.5
            },
            {
                'name': '美食分享',
                'description': '分享美食，分享快乐',
                'is_official': True,
                'is_trending': True,
                'hot_score': 88.2
            },
            {
                'name': '旅行日记',
                'description': '记录旅行中的美好时光',
                'is_official': True,
                'is_trending': False,
                'hot_score': 76.8
            },
            {
                'name': '健身打卡',
                'description': '健康生活，从运动开始',
                'is_official': False,
                'is_trending': True,
                'hot_score': 82.3
            },
            {
                'name': '读书笔记',
                'description': '分享读书心得和感悟',
                'is_official': False,
                'is_trending': False,
                'hot_score': 71.5
            }
        ]

        topics = []
        for topic_data in topics_data:
            topic, created = Topic.objects.get_or_create(
                name=topic_data['name'],
                defaults={
                    **topic_data,
                    'moment_count': random.randint(10, 100),
                    'participant_count': random.randint(50, 500),
                    'is_active': True
                }
            )
            topics.append(topic)

        # 创建动态内容
        moments_data = [
            {
                'content': '今天天气真好，心情也特别棒！阳光明媚的日子总是让人充满希望。',
                'location': '北京市朝阳区',
                'topics': ['今日心情']
            },
            {
                'content': '刚刚做了一道红烧肉，味道还不错呢！分享给大家看看～',
                'location': '上海市浦东新区',
                'topics': ['美食分享']
            },
            {
                'content': '周末去了趟西湖，真的太美了！人间天堂名不虚传。',
                'location': '杭州市西湖区',
                'topics': ['旅行日记']
            },
            {
                'content': '今天跑步5公里，虽然累但是很有成就感！坚持就是胜利！',
                'location': '深圳市南山区',
                'topics': ['健身打卡']
            },
            {
                'content': '最近在读《百年孤独》，马尔克斯的文字真的很有魅力。',
                'location': '广州市天河区',
                'topics': ['读书笔记']
            },
            {
                'content': '下午茶时光，一杯咖啡一本书，这就是我想要的慢生活。',
                'location': '成都市锦江区',
                'topics': ['今日心情']
            },
            {
                'content': '自制的蛋糕出炉啦！第一次做，卖相还可以吧？',
                'location': '武汉市武昌区',
                'topics': ['美食分享']
            },
            {
                'content': '爬山归来，虽然很累但是看到山顶的风景一切都值得了！',
                'location': '西安市雁塔区',
                'topics': ['旅行日记', '健身打卡']
            }
        ]

        created_moments = []
        for moment_data in moments_data:
            user = random.choice(self.demo_users)
            moment = Moment.objects.create(
                user=user,
                content=moment_data['content'],
                location=moment_data['location'],
                latitude=random.uniform(30.0, 40.0),
                longitude=random.uniform(110.0, 120.0),
                status='published',
                is_reviewed=True,
                like_count=random.randint(5, 50),
                comment_count=random.randint(0, 15),
                view_count=random.randint(20, 200),
                share_count=random.randint(0, 10),
                hot_score=random.uniform(60.0, 95.0),
                quality_score=random.uniform(70.0, 90.0),
                created_at=timezone.now() - timedelta(days=random.randint(1, 30))
            )

            # 关联话题
            for topic_name in moment_data['topics']:
                topic = next((t for t in topics if t.name == topic_name), None)
                if topic:
                    MomentTopic.objects.create(moment=moment, topic=topic)

            created_moments.append(moment)

        # 创建点赞记录
        for moment in created_moments:
            like_count = random.randint(3, min(15, len(self.demo_users)))
            liked_users = random.sample(self.demo_users, like_count)
            for user in liked_users:
                if user != moment.user:  # 不能给自己点赞
                    MomentLike.objects.create(
                        moment=moment,
                        user=user,
                        created_at=timezone.now() - timedelta(days=random.randint(1, 7))
                    )

        # 创建评论记录
        comments_data = [
            '太棒了！',
            '我也想去试试',
            '看起来很不错呢',
            '赞一个！',
            '好羡慕啊',
            '下次一起吧',
            '真的很美',
            '学到了',
            '继续加油！',
            '太有才了'
        ]

        for moment in created_moments:
            comment_count = random.randint(0, 8)
            for i in range(comment_count):
                user = random.choice([u for u in self.demo_users if u != moment.user])
                content = random.choice(comments_data)

                comment = MomentComment.objects.create(
                    moment=moment,
                    user=user,
                    content=content,
                    like_count=random.randint(0, 5),
                    created_at=timezone.now() - timedelta(days=random.randint(1, 5))
                )

                # 部分评论有回复
                if random.choice([True, False]) and i > 0:
                    reply_content = random.choice(['哈哈', '是的呢', '谢谢', '同感'])
                    MomentComment.objects.create(
                        moment=moment,
                        user=random.choice(self.demo_users),
                        content=reply_content,
                        reply_to=comment,
                        created_at=timezone.now() - timedelta(days=random.randint(1, 3))
                    )

        # 创建浏览记录
        for moment in created_moments:
            view_count = random.randint(5, 15)  # 减少数量避免重复
            viewed_users = random.sample(self.demo_users, min(view_count, len(self.demo_users)))
            for user in viewed_users:
                if user != moment.user:  # 不能浏览自己的动态
                    MomentView.objects.get_or_create(
                        moment=moment,
                        user=user,
                        defaults={
                            'view_duration': random.randint(5, 60),  # 浏览时长（秒）
                            'created_at': timezone.now() - timedelta(days=random.randint(1, 10))
                        }
                    )

        # 创建少量举报记录
        report_types = ['spam', 'inappropriate', 'harassment', 'fake', 'other']
        for i in range(3):
            moment = random.choice(created_moments)
            reporter = random.choice([u for u in self.demo_users if u != moment.user])

            MomentReport.objects.create(
                moment=moment,
                reporter=reporter,
                report_type=random.choice(report_types),
                reason=f'这条动态涉嫌{random.choice(["垃圾信息", "不当内容", "骚扰他人"])}',
                status=random.choice(['pending', 'processing', 'resolved']),
                created_at=timezone.now() - timedelta(days=random.randint(1, 15))
            )

        self.stdout.write(self.style.SUCCESS(f'✓ 创建了 {len(topics)} 个话题、{len(created_moments)} 条动态和相关互动记录'))
