#!/usr/bin/env python3
"""
为后台管理界面创建简单演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json

User = get_user_model()

class Command(BaseCommand):
    help = '为后台管理界面创建简单演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始创建后台管理演示数据...'))
        
        self.create_ai_matching_data()
        self.create_realtime_data()
        self.create_security_data()
        self.create_i18n_data()
        self.create_analytics_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 演示数据创建完成！'))

    def create_ai_matching_data(self):
        """创建AI智能匹配模块演示数据"""
        self.stdout.write('🧠 创建AI智能匹配模块演示数据...')
        
        try:
            from apps.ai_matching.models import (
                UserProfile, MatchingModel, UserSimilarity, MatchPrediction
            )
            
            users = list(User.objects.all()[:10])
            if not users:
                self.stdout.write(self.style.WARNING('没有找到用户，跳过AI匹配数据创建'))
                return
            
            # 创建用户画像
            for user in users:
                if not UserProfile.objects.filter(user=user).exists():
                    UserProfile.objects.create(
                        user=user,
                        age_group=random.choice(['18-25', '26-30', '31-35', '36-40', '40+']),
                        education_level=random.choice(['高中', '大专', '本科', '硕士', '博士']),
                        income_level=random.choice(['3k以下', '3k-5k', '5k-8k', '8k-12k', '12k-20k', '20k以上']),
                        occupation_category=random.choice(['IT互联网', '金融', '教育', '医疗', '销售', '设计', '其他']),
                        location_tier=random.choice(['一线城市', '二线城市', '三线城市', '其他']),
                        activity_score=round(random.uniform(0.1, 1.0), 2),
                        social_score=round(random.uniform(0.1, 1.0), 2),
                        response_rate=round(random.uniform(0.3, 0.9), 2),
                        online_frequency=round(random.uniform(0.1, 1.0), 2),
                        preferred_age_min=random.randint(18, 30),
                        preferred_age_max=random.randint(25, 45),
                        interests=random.sample(['音乐', '电影', '旅行', '美食', '运动', '阅读', '摄影', '游戏'], random.randint(2, 5)),
                        personality_traits=random.sample(['开朗', '内向', '幽默', '温柔', '理性', '感性'], random.randint(2, 4)),
                        lifestyle_tags=random.sample(['健身', '宅家', '社交', '旅行', '美食', '文艺'], random.randint(1, 3))
                    )
            
            # 创建匹配模型
            models_data = [
                {
                    'name': '协同过滤推荐模型',
                    'model_type': 'collaborative_filtering',
                    'description': '基于用户行为相似性的协同过滤算法',
                    'version': '1.2.0',
                    'accuracy': 0.78,
                    'is_active': True
                },
                {
                    'name': '深度学习匹配模型',
                    'model_type': 'deep_learning',
                    'description': '基于神经网络的深度学习匹配算法',
                    'version': '2.1.0',
                    'accuracy': 0.85,
                    'is_active': True
                }
            ]
            
            for model_data in models_data:
                if not MatchingModel.objects.filter(name=model_data['name']).exists():
                    MatchingModel.objects.create(**model_data)
            
            # 创建用户相似度数据
            for i in range(15):
                user1, user2 = random.sample(users, 2)
                if not UserSimilarity.objects.filter(user1=user1, user2=user2).exists():
                    UserSimilarity.objects.create(
                        user1=user1,
                        user2=user2,
                        overall_similarity=round(random.uniform(0.1, 0.95), 3),
                        demographic_similarity=round(random.uniform(0.0, 1.0), 3),
                        interest_similarity=round(random.uniform(0.0, 1.0), 3),
                        behavior_similarity=round(random.uniform(0.0, 1.0), 3),
                        preference_similarity=round(random.uniform(0.0, 1.0), 3)
                    )
            
            # 创建匹配预测数据
            models = list(MatchingModel.objects.all())
            for i in range(20):
                user, candidate = random.sample(users, 2)
                if not MatchPrediction.objects.filter(user=user, candidate=candidate).exists():
                    MatchPrediction.objects.create(
                        user=user,
                        candidate=candidate,
                        match_score=round(random.uniform(0.1, 0.95), 3),
                        compatibility_score=round(random.uniform(0.1, 0.9), 3),
                        predicted_by_model=random.choice(models) if models else None,
                        confidence=round(random.uniform(0.6, 0.95), 3)
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ AI智能匹配数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI智能匹配数据创建失败: {e}'))

    def create_realtime_data(self):
        """创建实时通讯模块演示数据"""
        self.stdout.write('⚡ 创建实时通讯模块演示数据...')
        
        try:
            from apps.realtime.models import WebSocketConnection
            
            users = list(User.objects.all()[:10])
            if not users:
                return
            
            # 创建WebSocket连接记录
            for i in range(15):
                user = random.choice(users)
                WebSocketConnection.objects.create(
                    user=user,
                    connection_type=random.choice(['chat', 'video_call', 'voice_call', 'notification']),
                    channel_name=f'user_{user.id}_{random.randint(1000, 9999)}',
                    client_ip=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    status=random.choice(['connected', 'disconnected', 'reconnecting']),
                    messages_sent=random.randint(0, 100),
                    messages_received=random.randint(0, 100),
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ])
                )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 实时通讯数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全防护模块演示数据"""
        self.stdout.write('🛡️ 创建安全防护模块演示数据...')
        
        try:
            from apps.security.models import SecurityEvent, IPBlacklist
            
            users = list(User.objects.all()[:10])
            
            # 创建安全事件
            event_types = [
                'login_attempt', 'failed_login', 'suspicious_activity',
                'rate_limit_exceeded', 'sql_injection_attempt', 'xss_attempt'
            ]
            
            for i in range(20):
                SecurityEvent.objects.create(
                    event_type=random.choice(event_types),
                    severity=random.choice(['low', 'medium', 'high', 'critical']),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ]),
                    referer='https://xiangqin.weixinjishu.top/',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    request_path=f'/api/{random.choice(["users", "matches", "chat", "profile"])}/',
                    request_data={'param': f'value_{i}'},
                    user=random.choice(users) if random.choice([True, False]) else None,
                    is_blocked=random.choice([True, False]),
                    response_action=random.choice(['allow', 'block', 'warn', 'log']),
                    risk_score=random.randint(0, 100)
                )
            
            # 创建IP黑名单
            for i in range(10):
                IPBlacklist.objects.create(
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    reason=random.choice([
                        '恶意攻击', '垃圾信息', '暴力破解', '异常访问', '违规行为'
                    ]),
                    is_active=random.choice([True, False]),
                    created_by=random.choice(users)
                )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 安全防护数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全防护数据创建失败: {e}'))

    def create_i18n_data(self):
        """创建国际化模块演示数据"""
        self.stdout.write('🌍 创建国际化模块演示数据...')
        
        try:
            from apps.i18n.models import Language, TranslationCategory, TranslationKey, Translation
            
            # 创建语言
            languages_data = [
                {'code': 'zh-CN', 'name': '简体中文', 'native_name': '简体中文', 'is_active': True, 'is_default': True},
                {'code': 'en-US', 'name': 'English', 'native_name': 'English', 'is_active': True},
                {'code': 'ja-JP', 'name': 'Japanese', 'native_name': '日本語', 'is_active': True},
            ]
            
            for lang_data in languages_data:
                if not Language.objects.filter(code=lang_data['code']).exists():
                    Language.objects.create(**lang_data)
            
            # 创建翻译分类
            if not TranslationCategory.objects.filter(name='应用界面').exists():
                category = TranslationCategory.objects.create(
                    name='应用界面',
                    description='应用程序界面相关翻译'
                )
                
                # 创建翻译键
                keys_data = [
                    {'key': 'app.title', 'default_value': '相亲交友'},
                    {'key': 'user.profile', 'default_value': '用户资料'},
                    {'key': 'chat.message', 'default_value': '聊天消息'},
                    {'key': 'match.found', 'default_value': '找到匹配'},
                ]
                
                for key_data in keys_data:
                    if not TranslationKey.objects.filter(key=key_data['key']).exists():
                        translation_key = TranslationKey.objects.create(
                            category=category,
                            **key_data
                        )
                        
                        # 为每种语言创建翻译
                        translations_data = {
                            'zh-CN': {'app.title': '相亲交友', 'user.profile': '用户资料', 'chat.message': '聊天消息', 'match.found': '找到匹配'},
                            'en-US': {'app.title': 'Dating App', 'user.profile': 'User Profile', 'chat.message': 'Chat Message', 'match.found': 'Match Found'},
                            'ja-JP': {'app.title': '出会いアプリ', 'user.profile': 'ユーザープロフィール', 'chat.message': 'チャットメッセージ', 'match.found': 'マッチが見つかりました'}
                        }
                        
                        for lang_code, translations in translations_data.items():
                            try:
                                language = Language.objects.get(code=lang_code)
                                if key_data['key'] in translations:
                                    Translation.objects.create(
                                        translation_key=translation_key,
                                        language=language,
                                        value=translations[key_data['key']],
                                        status='approved'
                                    )
                            except Language.DoesNotExist:
                                continue
            
            self.stdout.write(self.style.SUCCESS('  ✅ 国际化数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据创建失败: {e}'))

    def create_analytics_data(self):
        """创建高级数据分析模块演示数据"""
        self.stdout.write('📊 创建高级数据分析模块演示数据...')
        
        try:
            from apps.advanced_analytics.models import UserSegment, BehaviorPrediction
            
            users = list(User.objects.all()[:10])
            
            # 创建用户分群
            segments_data = [
                {
                    'name': '高活跃用户',
                    'description': '每日登录且互动频繁的用户群体',
                    'criteria': {'daily_login': True, 'messages_per_day': {'min': 10}}
                },
                {
                    'name': 'VIP潜在用户',
                    'description': '使用频率高但未购买VIP的用户',
                    'criteria': {'is_vip': False, 'app_usage_hours': {'min': 2}}
                }
            ]
            
            for segment_data in segments_data:
                if not UserSegment.objects.filter(name=segment_data['name']).exists():
                    segment = UserSegment.objects.create(
                        name=segment_data['name'],
                        description=segment_data['description'],
                        criteria=segment_data['criteria'],
                        is_active=True
                    )
                    # 随机分配一些用户到分群
                    segment.users.set(random.sample(users, random.randint(2, 5)))
            
            # 创建行为预测
            prediction_types = ['churn_risk', 'purchase_intent', 'engagement_level']
            
            for user in users[:5]:  # 只为前5个用户创建预测
                for pred_type in prediction_types:
                    if not BehaviorPrediction.objects.filter(user=user, prediction_type=pred_type).exists():
                        BehaviorPrediction.objects.create(
                            user=user,
                            prediction_type=pred_type,
                            predicted_value=round(random.uniform(0.1, 0.9), 3),
                            confidence_score=round(random.uniform(0.6, 0.95), 3),
                            prediction_data={
                                'factors': ['login_frequency', 'message_count'],
                                'model_version': 'v1.0'
                            }
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 高级数据分析数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级数据分析数据创建失败: {e}'))
