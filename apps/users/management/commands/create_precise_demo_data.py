#!/usr/bin/env python3
"""
基于实际模型字段创建精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '基于实际模型字段创建精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始创建精确的演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_advanced_analytics_data()
        self.create_analytics_data()
        self.create_chat_data()
        self.create_matching_data()
        self.create_ai_matching_data()
        self.create_realtime_data()
        self.create_gifts_data()
        self.create_payment_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 精确演示数据创建完成！'))

    def create_advanced_analytics_data(self):
        """创建高级分析模块精确数据"""
        self.stdout.write('📊 创建高级分析模块精确数据...')
        
        try:
            from apps.advanced_analytics.models import UserValueScore, BehaviorPrediction
            
            # 1. 用户价值评分 - 使用实际字段
            for user in self.users:
                if not UserValueScore.objects.filter(user=user).exists():
                    monetary_value = round(random.uniform(0, 1000), 2)
                    engagement_value = round(random.uniform(0, 100), 2)
                    social_value = round(random.uniform(0, 100), 2)
                    content_value = round(random.uniform(0, 100), 2)
                    referral_value = round(random.uniform(0, 100), 2)
                    total_value = monetary_value + engagement_value + social_value + content_value + referral_value
                    
                    UserValueScore.objects.create(
                        user=user,
                        monetary_value=monetary_value,
                        engagement_value=engagement_value,
                        social_value=social_value,
                        content_value=content_value,
                        referral_value=referral_value,
                        total_value_score=total_value,
                        value_tier=random.choice(['bronze', 'silver', 'gold', 'platinum', 'diamond']),
                        predicted_ltv=round(random.uniform(100, 5000), 2),
                        churn_risk_score=round(random.uniform(0, 1), 3),
                        calculation_version='1.0'
                    )
            
            # 2. 行为预测 - 使用实际字段
            prediction_types = ['churn', 'purchase', 'engagement', 'retention', 'referral']
            for user in self.users[:10]:
                for pred_type in random.sample(prediction_types, 2):
                    if not BehaviorPrediction.objects.filter(user=user, prediction_type=pred_type).exists():
                        BehaviorPrediction.objects.create(
                            user=user,
                            prediction_type=pred_type,
                            prediction_score=round(random.uniform(0.1, 0.9), 3),
                            confidence_level=round(random.uniform(0.6, 0.95), 3),
                            prediction_details={
                                'model_version': 'v1.0',
                                'calculation_date': timezone.now().isoformat()
                            },
                            contributing_factors=['login_frequency', 'message_count', 'profile_views'],
                            prediction_horizon=random.randint(7, 90),
                            predicted_date=timezone.now() + timedelta(days=random.randint(7, 90)),
                            model_name=f'{pred_type}_prediction_model',
                            model_version='1.0'
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 高级分析数据: UserValueScore {UserValueScore.objects.count()}条, BehaviorPrediction {BehaviorPrediction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级分析数据创建失败: {e}'))

    def create_analytics_data(self):
        """创建分析模块精确数据"""
        self.stdout.write('📈 创建分析模块精确数据...')
        
        try:
            from apps.analytics.models import UserBehaviorLog, RevenueRecord, PlatformStatistics, SystemAlert
            
            # 1. 用户行为日志 - 使用实际字段
            action_types = ['login', 'logout', 'view_profile', 'like', 'super_like', 'pass', 'match', 'send_message']
            for i in range(50):
                user = random.choice(self.users)
                target_user = random.choice([u for u in self.users if u != user]) if random.choice([True, False]) else None
                
                UserBehaviorLog.objects.create(
                    user=user,
                    action_type=random.choice(action_types),
                    target_user=target_user,
                    details={
                        'page': random.choice(['home', 'profile', 'chat', 'discover']),
                        'duration': random.randint(10, 300)
                    },
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    location=random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                    latitude=round(random.uniform(20, 50), 6),
                    longitude=round(random.uniform(100, 130), 6)
                )
            
            # 2. 收入记录 - 使用实际字段
            revenue_types = ['vip_subscription', 'coin_purchase', 'gift_purchase', 'premium_feature']
            for i in range(30):
                user = random.choice(self.users)
                revenue_type = random.choice(revenue_types)
                amount = Decimal(str(round(random.uniform(10, 500), 2)))
                
                RevenueRecord.objects.create(
                    user=user,
                    revenue_type=revenue_type,
                    product_name=f'{revenue_type}_product_{i}',
                    amount=amount,
                    currency='CNY',
                    payment_method=random.choice(['wechat_pay', 'alipay', 'bank_card']),
                    transaction_id=f'txn_{uuid.uuid4().hex[:12]}',
                    order_id=f'order_{uuid.uuid4().hex[:8]}',
                    payment_time=timezone.now() - timedelta(days=random.randint(1, 30)),
                    commission_rate=Decimal('0.05'),
                    commission_amount=amount * Decimal('0.05'),
                    refund_amount=Decimal('0.00'),
                    status='completed'
                )
            
            # 3. 平台统计 - 补充更多数据
            for i in range(7):  # 最近7天
                stat_date = date.today() - timedelta(days=i)
                if not PlatformStatistics.objects.filter(date=stat_date).exists():
                    PlatformStatistics.objects.create(
                        date=stat_date,
                        total_users=random.randint(1000, 5000),
                        new_users=random.randint(10, 100),
                        active_users=random.randint(500, 2000),
                        vip_users=random.randint(50, 500),
                        total_matches=random.randint(100, 1000),
                        new_matches=random.randint(10, 100),
                        successful_matches=random.randint(5, 50),
                        total_messages=random.randint(1000, 10000),
                        new_messages=random.randint(100, 1000),
                        active_conversations=random.randint(50, 500),
                        total_moments=random.randint(100, 1000),
                        new_moments=random.randint(10, 100),
                        moments_likes=random.randint(100, 1000),
                        daily_revenue=Decimal(str(round(random.uniform(1000, 10000), 2))),
                        vip_revenue=Decimal(str(round(random.uniform(500, 5000), 2))),
                        gift_revenue=Decimal(str(round(random.uniform(200, 2000), 2)))
                    )
            
            # 4. 系统告警 - 补充更多数据
            alert_types = ['user_behavior', 'system_error', 'payment_issue', 'security_threat', 'performance']
            alert_levels = ['info', 'warning', 'error', 'critical']
            
            for i in range(20):
                SystemAlert.objects.create(
                    alert_type=random.choice(alert_types),
                    alert_level=random.choice(alert_levels),
                    title=f'系统告警 #{i+1}',
                    message=f'告警消息内容 #{i+1}',
                    details={
                        'source': 'system_monitor',
                        'severity': random.choice(['low', 'medium', 'high']),
                        'affected_users': random.randint(0, 100)
                    },
                    is_resolved=random.choice([True, False]),
                    resolved_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                    resolved_by=random.choice(self.users) if random.choice([True, False]) else None
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 分析数据: UserBehaviorLog {UserBehaviorLog.objects.count()}条, RevenueRecord {RevenueRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 分析数据创建失败: {e}'))

    def create_chat_data(self):
        """创建聊天模块精确数据"""
        self.stdout.write('💬 创建聊天模块精确数据...')
        
        try:
            from apps.chat.models import ChatStatistics, OnlineStatus
            
            # 1. 聊天统计 - 使用实际字段
            for user in self.users:
                if not ChatStatistics.objects.filter(user=user).exists():
                    total_sent = random.randint(50, 500)
                    total_received = random.randint(40, 400)
                    
                    ChatStatistics.objects.create(
                        user=user,
                        total_messages_sent=total_sent,
                        total_messages_received=total_received,
                        total_sessions=random.randint(5, 50),
                        active_sessions=random.randint(1, 10),
                        today_messages_sent=random.randint(0, 20),
                        today_messages_received=random.randint(0, 15),
                        today_new_sessions=random.randint(0, 5),
                        avg_response_time=round(random.uniform(1, 60), 2),
                        response_rate=round(random.uniform(0.5, 0.9), 2),
                        last_message_time=timezone.now() - timedelta(hours=random.randint(1, 48)),
                        last_online_time=timezone.now() - timedelta(minutes=random.randint(1, 1440))
                    )
            
            # 2. 在线状态 - 使用实际字段
            for user in self.users:
                if not OnlineStatus.objects.filter(user=user).exists():
                    OnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'away', 'busy', 'offline']),
                        latitude=round(random.uniform(20, 50), 6),
                        longitude=round(random.uniform(100, 130), 6),
                        status_message=f'{user.nickname}的状态消息'
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 聊天数据: ChatStatistics {ChatStatistics.objects.count()}条, OnlineStatus {OnlineStatus.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 聊天数据创建失败: {e}'))

    def create_matching_data(self):
        """创建匹配模块精确数据"""
        self.stdout.write('💕 创建匹配模块精确数据...')
        
        try:
            from apps.matching.models import MatchingAlgorithmConfig, UserCompatibility, DailyRecommendation
            
            # 1. 匹配算法配置 - 使用实际字段
            configs = [
                {'name': '年龄权重', 'age_weight': 0.3, 'education_weight': 0.2, 'income_weight': 0.15},
                {'name': '教育权重', 'age_weight': 0.25, 'education_weight': 0.3, 'income_weight': 0.2},
                {'name': '收入权重', 'age_weight': 0.2, 'education_weight': 0.25, 'income_weight': 0.3}
            ]
            
            for i, config in enumerate(configs):
                if not MatchingAlgorithmConfig.objects.filter(name=config['name']).exists():
                    MatchingAlgorithmConfig.objects.create(
                        name=config['name'],
                        version=f'v1.{i}',
                        age_weight=config['age_weight'],
                        education_weight=config['education_weight'],
                        income_weight=config['income_weight'],
                        height_weight=0.1,
                        interest_weight=0.15,
                        location_weight=0.1,
                        max_distance=100,
                        min_match_score=0.6,
                        is_active=True
                    )
            
            # 2. 用户兼容性 - 使用实际字段
            for i in range(25):
                user1, user2 = random.sample(self.users, 2)
                if not UserCompatibility.objects.filter(user1=user1, user2=user2).exists():
                    UserCompatibility.objects.create(
                        user1=user1,
                        user2=user2,
                        age_score=round(random.uniform(0.0, 1.0), 3),
                        location_score=round(random.uniform(0.0, 1.0), 3),
                        education_score=round(random.uniform(0.0, 1.0), 3),
                        income_score=round(random.uniform(0.0, 1.0), 3),
                        height_score=round(random.uniform(0.0, 1.0), 3),
                        interest_score=round(random.uniform(0.0, 1.0), 3),
                        total_score=round(random.uniform(0.1, 0.95), 3),
                        analysis_details={
                            'calculation_method': 'weighted_average',
                            'factors_considered': ['age', 'education', 'location', 'interests']
                        }
                    )
            
            # 3. 每日推荐 - 使用实际字段
            for user in self.users:
                for days_ago in range(5):  # 最近5天
                    recommendation_date = date.today() - timedelta(days=days_ago)
                    if not DailyRecommendation.objects.filter(user=user, date=recommendation_date).exists():
                        recommended_users = random.sample([u for u in self.users if u != user], random.randint(3, 6))
                        
                        DailyRecommendation.objects.create(
                            user=user,
                            date=recommendation_date,
                            recommended_users=recommended_users,
                            algorithm_version='v1.0',
                            total_count=len(recommended_users),
                            viewed_count=random.randint(0, len(recommended_users)),
                            liked_count=random.randint(0, 2),
                            matched_count=random.randint(0, 1),
                            quality_score=round(random.uniform(0.6, 0.95), 3)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 匹配数据: MatchingAlgorithmConfig {MatchingAlgorithmConfig.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 匹配数据创建失败: {e}'))

    def create_ai_matching_data(self):
        """创建AI匹配模块精确数据"""
        self.stdout.write('🧠 创建AI匹配模块精确数据...')

        try:
            from apps.ai_matching.models import (
                UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
                ModelPerformanceMetric, MatchingModel
            )

            # 1. 用户行为模式 - 使用实际字段
            pattern_types = ['early_bird', 'night_owl', 'weekend_active', 'workday_focused']
            for user in self.users:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        pattern_name=random.choice(pattern_types),
                        pattern_data={
                            'active_hours': random.sample(range(24), random.randint(6, 12)),
                            'response_time_avg': random.randint(5, 120),
                            'message_frequency': random.randint(10, 100)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        pattern_strength=round(random.uniform(0.5, 1.0), 3),
                        last_analysis_date=timezone.now() - timedelta(days=random.randint(1, 7))
                    )

            # 2. 模型训练数据 - 使用实际字段
            data_types = ['user_interactions', 'match_outcomes', 'message_data', 'profile_data']
            for data_type in data_types:
                if not ModelTrainingData.objects.filter(data_type=data_type).exists():
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        data_source=f'production_db_{data_type}',
                        data_size=random.randint(10000, 100000),
                        quality_score=round(random.uniform(0.7, 0.95), 3),
                        feature_count=random.randint(50, 200),
                        training_accuracy=round(random.uniform(0.75, 0.95), 3),
                        validation_accuracy=round(random.uniform(0.70, 0.90), 3),
                        preprocessing_steps=['normalization', 'feature_extraction', 'validation']
                    )

            # 3. 推荐解释 - 使用实际字段
            for i in range(20):
                user, recommended_user = random.sample(self.users, 2)
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        explanation_text=f'基于共同兴趣推荐',
                        explanation_factors={
                            'common_interests': ['音乐', '电影'],
                            'similarity_score': round(random.uniform(0.6, 0.9), 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        algorithm_version='v2.1.0'
                    )

            # 4. 模型性能指标 - 使用实际字段
            models = list(MatchingModel.objects.all())
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc']

            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=round(random.uniform(0.6, 0.95), 4),
                            evaluation_dataset='test_set_2025',
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                            baseline_value=round(random.uniform(0.5, 0.8), 4),
                            improvement_percentage=round(random.uniform(5, 25), 2)
                        )

            self.stdout.write(self.style.SUCCESS(f'  ✅ AI匹配数据: UserBehaviorPattern {UserBehaviorPattern.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配数据创建失败: {e}'))

    def create_realtime_data(self):
        """创建实时通讯模块精确数据"""
        self.stdout.write('⚡ 创建实时通讯模块精确数据...')

        try:
            from apps.realtime.models import VoiceCall, VideoCall, VoiceMessage, RealtimeNotification

            # 1. 语音通话 - 使用实际字段
            for i in range(20):
                caller, callee = random.sample(self.users, 2)
                duration = random.randint(30, 1800)
                VoiceCall.objects.create(
                    caller=caller,
                    callee=callee,
                    call_id=uuid.uuid4(),  # 使用UUID对象
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    call_quality=round(random.uniform(3.0, 5.0), 1),
                    network_type=random.choice(['wifi', '4g', '5g', '3g']),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )

            # 2. 视频通话 - 使用实际字段
            for i in range(15):
                caller, callee = random.sample(self.users, 2)
                duration = random.randint(60, 3600)
                VideoCall.objects.create(
                    caller=caller,
                    callee=callee,
                    call_id=uuid.uuid4(),  # 使用UUID对象
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    call_quality=round(random.uniform(3.0, 5.0), 1),
                    video_quality=random.choice(['720p', '1080p', '480p']),
                    network_type=random.choice(['wifi', '4g', '5g']),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )

            # 3. 语音消息 - 使用实际字段
            for i in range(35):
                sender, receiver = random.sample(self.users, 2)
                VoiceMessage.objects.create(
                    sender=sender,
                    receiver=receiver,
                    message_id=uuid.uuid4(),  # 使用UUID对象
                    duration=random.randint(5, 60),
                    file_size=random.randint(50000, 500000),
                    file_url=f'/media/voice/{uuid.uuid4().hex[:8]}.mp3',
                    quality=random.choice(['high', 'medium', 'low']),
                    is_played=random.choice([True, False]),
                    played_at=timezone.now() - timedelta(hours=random.randint(1, 48)) if random.choice([True, False]) else None
                )

            # 4. 实时通知 - 使用实际字段
            notification_types = ['new_message', 'new_match', 'profile_view', 'gift_received', 'system_update']
            for i in range(40):
                RealtimeNotification.objects.create(
                    user=random.choice(self.users),
                    notification_type=random.choice(notification_types),
                    title=f'通知标题 #{i+1}',
                    content=f'通知内容 #{i+1}',
                    data={'extra_info': f'额外数据 #{i+1}'},
                    is_read=random.choice([True, False]),
                    priority=random.choice(['low', 'medium', 'high', 'urgent']),
                    read_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通讯数据: VoiceCall {VoiceCall.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

    def create_gifts_data(self):
        """创建礼物模块精确数据"""
        self.stdout.write('🎁 创建礼物模块精确数据...')

        try:
            from apps.gifts.models import UserGiftBox, GiftActivityParticipant, Gift, GiftActivity

            # 1. 用户礼物盒 - 使用实际字段
            gifts = list(Gift.objects.all())
            if gifts:
                for user in self.users:
                    user_gifts = random.sample(gifts, random.randint(1, min(5, len(gifts))))
                    for gift in user_gifts:
                        if not UserGiftBox.objects.filter(user=user, gift=gift).exists():
                            UserGiftBox.objects.create(
                                user=user,
                                gift=gift,
                                quantity=random.randint(1, 10),
                                source=random.choice(['purchase', 'gift_received', 'activity_reward', 'daily_check_in'])
                            )

            # 2. 活动参与者 - 使用实际字段
            activities = list(GiftActivity.objects.all())
            if activities:
                for activity in activities:
                    participants = random.sample(self.users, random.randint(5, 10))
                    for user in participants:
                        if not GiftActivityParticipant.objects.filter(activity=activity, user=user).exists():
                            GiftActivityParticipant.objects.create(
                                activity=activity,
                                user=user,
                                points_earned=random.randint(10, 100),
                                rewards_received=random.choice([True, False]),
                                completion_status=random.choice(['completed', 'in_progress', 'not_started'])
                            )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 礼物数据: UserGiftBox {UserGiftBox.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 礼物数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块精确数据"""
        self.stdout.write('💰 创建支付模块精确数据...')

        try:
            from apps.payment.models import UserVipRecord, VipPackage

            # 用户VIP记录 - 使用实际字段
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(6, 10)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            is_active=random.choice([True, False]),
                            expires_at=timezone.now() + timedelta(days=30),
                            auto_renewal=random.choice([True, False])
                        )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))
