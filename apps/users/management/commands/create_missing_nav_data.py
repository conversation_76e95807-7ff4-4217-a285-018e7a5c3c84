#!/usr/bin/env python3
"""
为缺失的后台导航页面创建演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为缺失的后台导航页面创建演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为缺失的导航页面创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各页面数据
        self.create_user_action_data()
        self.create_user_verification_data()
        self.create_matchmaker_client_data()
        self.create_service_record_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 缺失导航页面演示数据创建完成！'))

    def create_user_action_data(self):
        """创建用户行为数据"""
        self.stdout.write('📊 创建用户行为数据...')
        
        try:
            from apps.users.models import UserAction
            
            # 检查现有数据量
            existing_count = UserAction.objects.count()
            target_count = 50
            
            if existing_count < target_count:
                action_types = ['like', 'pass', 'super_like', 'visit', 'block', 'report']
                
                for i in range(target_count - existing_count):
                    user, target_user = random.sample(self.users, 2)
                    action_type = random.choice(action_types)
                    
                    # 避免重复的用户行为记录
                    if not UserAction.objects.filter(user=user, target_user=target_user, action_type=action_type).exists():
                        UserAction.objects.create(
                            user=user,
                            target_user=target_user,
                            action_type=action_type,
                            message=f'演示{action_type}行为 #{i+1}' if action_type in ['like', 'super_like'] else '',
                            created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户行为数据创建完成 - 总计: {UserAction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户行为数据创建失败: {e}'))

    def create_user_verification_data(self):
        """创建用户认证数据"""
        self.stdout.write('🔐 创建用户认证数据...')
        
        try:
            from apps.users.models import UserVerification
            
            verification_types = ['id_card', 'phone', 'education', 'profession', 'photo']
            statuses = ['pending', 'approved', 'rejected']
            
            # 为每个用户创建1-3个认证记录
            for user in self.users[:12]:  # 只为前12个用户创建认证记录
                # 随机选择1-3种认证类型
                selected_types = random.sample(verification_types, random.randint(1, 3))
                
                for verification_type in selected_types:
                    if not UserVerification.objects.filter(user=user, verification_type=verification_type).exists():
                        status = random.choice(statuses)
                        
                        # 根据认证类型创建不同的数据
                        verification_data = {}
                        if verification_type == 'id_card':
                            verification_data = {
                                'id_number': f'11010119{random.randint(80, 99)}0101{random.randint(1000, 9999)}',
                                'real_name': f'用户{user.id}'
                            }
                        elif verification_type == 'phone':
                            verification_data = {
                                'phone_number': f'138{random.randint(10000000, 99999999)}'
                            }
                        elif verification_type == 'education':
                            verification_data = {
                                'school': random.choice(['清华大学', '北京大学', '复旦大学', '上海交大', '浙江大学']),
                                'degree': random.choice(['本科', '硕士', '博士']),
                                'major': random.choice(['计算机科学', '金融学', '管理学', '医学', '法学'])
                            }
                        elif verification_type == 'profession':
                            verification_data = {
                                'company': random.choice(['阿里巴巴', '腾讯', '百度', '字节跳动', '美团']),
                                'position': random.choice(['软件工程师', '产品经理', '设计师', '运营专员', '销售经理']),
                                'salary_range': random.choice(['10k-15k', '15k-25k', '25k-35k', '35k以上'])
                            }
                        elif verification_type == 'photo':
                            verification_data = {
                                'photo_urls': [f'/media/verification/photo_{user.id}_{i}.jpg' for i in range(1, 4)]
                            }
                        
                        UserVerification.objects.create(
                            user=user,
                            verification_type=verification_type,
                            status=status,
                            data=verification_data,
                            reason='演示拒绝原因' if status == 'rejected' else '',
                            created_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                            updated_at=timezone.now() - timedelta(days=random.randint(0, 15))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户认证数据创建完成 - 总计: {UserVerification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户认证数据创建失败: {e}'))

    def create_matchmaker_client_data(self):
        """创建红娘客户数据"""
        self.stdout.write('👥 创建红娘客户数据...')
        
        try:
            from apps.users.models import MatchmakerProfile, MatchmakerClient
            
            # 获取红娘用户
            matchmakers = list(MatchmakerProfile.objects.all())
            if not matchmakers:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到红娘用户，跳过红娘客户数据创建'))
                return
            
            # 获取普通用户作为客户
            normal_users = [user for user in self.users if user.user_type == 'normal']
            if not normal_users:
                normal_users = self.users[:10]  # 如果没有普通用户，使用前10个用户
            
            service_packages = ['basic', 'standard', 'premium', 'vip']
            statuses = ['new', 'consulting', 'matching', 'matched', 'completed', 'cancelled']
            
            # 为每个红娘创建2-5个客户
            for matchmaker in matchmakers:
                client_count = random.randint(2, 5)
                selected_clients = random.sample(normal_users, min(client_count, len(normal_users)))
                
                for client_user in selected_clients:
                    if not MatchmakerClient.objects.filter(matchmaker=matchmaker, user=client_user).exists():
                        service_package = random.choice(service_packages)
                        
                        # 根据套餐设置费用
                        fee_mapping = {
                            'basic': Decimal(str(random.randint(1000, 3000))),
                            'standard': Decimal(str(random.randint(3000, 6000))),
                            'premium': Decimal(str(random.randint(6000, 10000))),
                            'vip': Decimal(str(random.randint(10000, 20000)))
                        }
                        
                        MatchmakerClient.objects.create(
                            matchmaker=matchmaker,
                            user=client_user,
                            service_package=service_package,
                            service_fee=fee_mapping[service_package],
                            status=random.choice(statuses),
                            start_date=timezone.now().date() - timedelta(days=random.randint(1, 90)),
                            end_date=timezone.now().date() + timedelta(days=random.randint(30, 180)),
                            notes=f'客户备注信息 - {service_package}套餐服务',
                            created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 红娘客户数据创建完成 - 总计: {MatchmakerClient.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 红娘客户数据创建失败: {e}'))

    def create_service_record_data(self):
        """创建服务记录数据"""
        self.stdout.write('📋 创建服务记录数据...')
        
        try:
            from apps.users.models import MatchmakerClient, ServiceRecord
            
            # 获取红娘客户
            clients = list(MatchmakerClient.objects.all())
            if not clients:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到红娘客户，跳过服务记录数据创建'))
                return
            
            service_types = ['consultation', 'profile_optimization', 'matching', 'date_arrangement', 'follow_up', 'feedback', 'other']
            
            # 为每个客户创建2-8条服务记录
            for client in clients:
                record_count = random.randint(2, 8)
                
                for i in range(record_count):
                    service_type = random.choice(service_types)
                    
                    # 根据服务类型生成不同的描述
                    descriptions = {
                        'consultation': '初次咨询，了解客户需求和期望',
                        'profile_optimization': '优化客户资料，提升匹配度',
                        'matching': '推荐匹配对象，分析匹配原因',
                        'date_arrangement': '安排约会，协调时间地点',
                        'follow_up': '跟进约会情况，收集反馈',
                        'feedback': '收集客户反馈，调整服务策略',
                        'other': '其他定制化服务'
                    }
                    
                    ServiceRecord.objects.create(
                        client=client,
                        service_type=service_type,
                        description=descriptions.get(service_type, '服务记录描述'),
                        duration_minutes=random.randint(30, 180),
                        notes=f'服务备注 #{i+1} - {service_type}',
                        service_date=timezone.now().date() - timedelta(days=random.randint(0, 30)),
                        created_at=timezone.now() - timedelta(days=random.randint(0, 30))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 服务记录数据创建完成 - 总计: {ServiceRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 服务记录数据创建失败: {e}'))
