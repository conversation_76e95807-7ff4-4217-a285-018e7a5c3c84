#!/usr/bin/env python3
"""
为最后13个缺少数据的模型创建精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最后13个缺少数据的模型创建精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最后13个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_i18n_data()
        self.create_operations_data()
        self.create_reports_data()
        self.create_security_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最后13个模型演示数据创建完成！'))

    def create_i18n_data(self):
        """创建国际化模块精确数据"""
        self.stdout.write('🌍 创建国际化模块精确数据...')
        
        try:
            from apps.i18n.models import (
                UserLanguagePreference, TranslationMemory, TranslationStatistics,
                LocalizationConfig, AutoTranslation, Language
            )
            
            # 获取语言列表
            languages = list(Language.objects.all())
            if not languages:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到语言数据，跳过国际化数据创建'))
                return
            
            # 1. 用户语言偏好 - 使用确切字段
            for user in self.users:
                if not UserLanguagePreference.objects.filter(user=user).exists():
                    primary_language = random.choice(languages)
                    secondary_languages = random.sample(languages, random.randint(0, 2))
                    
                    preference = UserLanguagePreference.objects.create(
                        user=user,
                        primary_language=primary_language,
                        auto_detect=random.choice([True, False]),
                        show_original=random.choice([True, False]),
                        auto_translate=random.choice([True, False])
                    )
                    
                    # 添加次要语言
                    if secondary_languages:
                        preference.secondary_languages.set(secondary_languages)
            
            # 2. 翻译记忆 - 使用确切字段
            for i in range(30):
                source_lang, target_lang = random.sample(languages, 2)
                
                # 生成不同类型的翻译内容
                source_texts = [
                    '欢迎使用相亲交友平台',
                    '您有新的匹配对象',
                    '消息发送成功',
                    '个人资料已更新',
                    '支付完成',
                    '系统维护通知',
                    '用户协议',
                    '隐私政策',
                    '联系客服',
                    '退出登录'
                ]
                
                target_texts = [
                    'Welcome to Dating Platform',
                    'You have a new match',
                    'Message sent successfully',
                    'Profile updated',
                    'Payment completed',
                    'System maintenance notice',
                    'User Agreement',
                    'Privacy Policy',
                    'Contact Support',
                    'Logout'
                ]
                
                if i < len(source_texts):
                    source_text = source_texts[i]
                    target_text = target_texts[i]
                else:
                    source_text = f'演示源文本 #{i+1}'
                    target_text = f'Demo target text #{i+1}'
                
                if not TranslationMemory.objects.filter(
                    source_text=source_text, 
                    source_language=source_lang, 
                    target_language=target_lang
                ).exists():
                    TranslationMemory.objects.create(
                        source_text=source_text,
                        target_text=target_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        similarity_score=round(random.uniform(0.8, 1.0), 2),
                        source_type=random.choice(['manual', 'auto', 'import', 'api']),
                        quality_rating=random.randint(3, 5),
                        is_verified=random.choice([True, False]),
                        usage_count=random.randint(0, 50),
                        last_used_at=timezone.now() - timedelta(days=random.randint(1, 30)) if random.choice([True, False]) else None
                    )
            
            # 3. 翻译统计 - 使用确切字段
            for language in languages:
                for days_ago in range(7):  # 最近7天的统计
                    stat_date = date.today() - timedelta(days=days_ago)
                    
                    if not TranslationStatistics.objects.filter(language=language, date=stat_date).exists():
                        total_keys = random.randint(100, 500)
                        translated_keys = random.randint(50, total_keys)
                        pending_keys = total_keys - translated_keys
                        completion_rate = round((translated_keys / total_keys) * 100, 2)
                        
                        TranslationStatistics.objects.create(
                            language=language,
                            date=stat_date,
                            total_keys=total_keys,
                            translated_keys=translated_keys,
                            pending_keys=pending_keys,
                            avg_quality_score=round(random.uniform(3.5, 5.0), 2),
                            approved_translations=random.randint(30, translated_keys),
                            completion_rate=completion_rate,
                            new_translations=random.randint(0, 20),
                            updated_translations=random.randint(0, 10)
                        )
            
            # 4. 本地化配置 - 使用确切字段
            for language in languages[:5]:  # 为前5种语言创建本地化配置
                if not LocalizationConfig.objects.filter(language=language).exists():
                    # 根据语言设置不同的格式
                    if language.code.startswith('zh'):
                        # 中文配置
                        date_format = 'Y年m月d日'
                        time_format = 'H:i:s'
                        currency_symbol = '¥'
                        name_format = 'last_first'
                    elif language.code.startswith('en'):
                        # 英文配置
                        date_format = 'm/d/Y'
                        time_format = 'g:i A'
                        currency_symbol = '$'
                        name_format = 'first_last'
                    elif language.code.startswith('ja'):
                        # 日文配置
                        date_format = 'Y年m月d日'
                        time_format = 'H:i'
                        currency_symbol = '¥'
                        name_format = 'last_first'
                    else:
                        # 默认配置
                        date_format = 'Y-m-d'
                        time_format = 'H:i:s'
                        currency_symbol = '$'
                        name_format = 'first_last'
                    
                    LocalizationConfig.objects.create(
                        language=language,
                        date_format=date_format,
                        time_format=time_format,
                        datetime_format=f'{date_format} {time_format}',
                        decimal_separator='.',
                        thousand_separator=',',
                        currency_symbol=currency_symbol,
                        currency_position='before',
                        address_format='{country} {province} {city} {district} {street}',
                        name_format=name_format,
                        config_data={
                            'week_start': 1 if language.code.startswith('zh') else 0,
                            'phone_format': '+86 {number}' if language.code.startswith('zh') else '+1 {number}'
                        }
                    )
            
            # 5. 自动翻译 - 使用确切字段
            for i in range(25):
                source_lang, target_lang = random.sample(languages, 2)
                
                # 生成翻译内容
                source_texts = [
                    '你好，很高兴认识你',
                    '今天天气真不错',
                    '你的照片很好看',
                    '我们可以聊聊吗',
                    '谢谢你的回复'
                ]
                
                translated_texts = [
                    'Hello, nice to meet you',
                    'The weather is really nice today',
                    'Your photos look great',
                    'Can we chat',
                    'Thank you for your reply'
                ]
                
                if i < len(source_texts):
                    source_text = source_texts[i]
                    translated_text = translated_texts[i]
                else:
                    source_text = f'自动翻译源文本 #{i+1}'
                    translated_text = f'Auto translated text #{i+1}'
                
                if not AutoTranslation.objects.filter(
                    source_text=source_text,
                    source_language=source_lang,
                    target_language=target_lang
                ).exists():
                    AutoTranslation.objects.create(
                        source_text=source_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        translated_text=translated_text,
                        engine=random.choice(['google', 'baidu', 'tencent', 'microsoft', 'deepl']),
                        confidence_score=round(random.uniform(0.6, 0.95), 2),
                        is_reviewed=random.choice([True, False]),
                        human_rating=random.randint(3, 5) if random.choice([True, False]) else None,
                        usage_count=random.randint(0, 20)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 国际化数据: UserLanguagePreference {UserLanguagePreference.objects.count()}条, TranslationMemory {TranslationMemory.objects.count()}条, TranslationStatistics {TranslationStatistics.objects.count()}条, LocalizationConfig {LocalizationConfig.objects.count()}条, AutoTranslation {AutoTranslation.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据创建失败: {e}'))

    def create_operations_data(self):
        """创建运营模块精确数据"""
        self.stdout.write('🎯 创建运营模块精确数据...')
        
        try:
            from apps.operations.models import CampaignParticipant, ABTest, MarketingCampaign
            
            # 1. 活动参与者 - 使用确切字段
            campaigns = list(MarketingCampaign.objects.all())
            if campaigns:
                for campaign in campaigns:
                    participants = random.sample(self.users, random.randint(8, 12))
                    for user in participants:
                        if not CampaignParticipant.objects.filter(campaign=campaign, user=user).exists():
                            CampaignParticipant.objects.create(
                                campaign=campaign,
                                user=user,
                                participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                                conversion_achieved=random.choice([True, False]),
                                conversion_value=Decimal(str(round(random.uniform(0, 100), 2))),
                                source=random.choice(['organic', 'paid', 'referral', 'social']),
                                utm_source=random.choice(['google', 'facebook', 'wechat', 'direct']),
                                utm_medium=random.choice(['cpc', 'social', 'email', 'organic']),
                                utm_campaign=f'campaign_{campaign.id}'
                            )
            
            # 2. A/B测试 - 使用确切字段
            tests = [
                {
                    'name': '首页布局A/B测试',
                    'description': '测试不同的首页布局对用户转化的影响',
                    'hypothesis': '新的首页布局将提高用户转化率15%'
                },
                {
                    'name': '注册流程优化测试',
                    'description': '测试简化注册流程的效果',
                    'hypothesis': '简化的注册流程将提高注册完成率20%'
                }
            ]
            
            for test_data in tests:
                if not ABTest.objects.filter(name=test_data['name']).exists():
                    ABTest.objects.create(
                        name=test_data['name'],
                        description=test_data['description'],
                        hypothesis=test_data['hypothesis'],
                        status=random.choice(['draft', 'running', 'completed', 'paused']),
                        control_group_size=random.randint(100, 500),
                        test_group_size=random.randint(100, 500),
                        conversion_rate_control=round(random.uniform(0.1, 0.3), 3),
                        conversion_rate_test=round(random.uniform(0.1, 0.3), 3),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        confidence_level=0.95,
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 30)),
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 运营数据: CampaignParticipant {CampaignParticipant.objects.count()}条, ABTest {ABTest.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 运营数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块精确数据"""
        self.stdout.write('🚨 创建举报模块精确数据...')

        try:
            from apps.reports.models import Blacklist, ContentModeration, SecurityLog

            # 1. 黑名单 - 使用确切字段
            for i in range(15):
                user, blocked_user = random.sample(self.users, 2)
                if not Blacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    Blacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'spam', 'inappropriate', 'fake_profile']),
                        notes=f'黑名单备注 #{i+1}'
                    )

            # 2. 内容审核 - 使用确切字段
            content_types = ['profile', 'moment', 'message', 'comment']
            for i in range(25):
                ContentModeration.objects.create(
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    user=random.choice(self.users),
                    status=random.choice(['pending', 'approved', 'rejected']),
                    moderation_reason=f'审核原因 #{i+1}',
                    moderator=random.choice(self.users),
                    flagged_keywords=['敏感词1', '敏感词2'] if random.choice([True, False]) else []
                )

            # 3. 安全日志 - 使用确切字段
            log_types = ['login_attempt', 'password_change', 'suspicious_activity', 'data_access']
            for i in range(30):
                SecurityLog.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    log_type=random.choice(log_types),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    details={'action': f'安全日志详情 #{i+1}', 'result': 'success'},
                    risk_level=random.choice(['low', 'medium', 'high', 'critical'])
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据: Blacklist {Blacklist.objects.count()}条, ContentModeration {ContentModeration.objects.count()}条, SecurityLog {SecurityLog.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全模块精确数据"""
        self.stdout.write('🛡️ 创建安全模块精确数据...')

        try:
            from apps.security.models import SecurityAuditLog, DataEncryption, RateLimitRecord

            # 1. 安全审计日志 - 使用确切字段
            action_types = ['login', 'logout', 'password_change', 'profile_update', 'admin_action', 'data_export']

            for i in range(40):
                SecurityAuditLog.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(action_types),
                    action_description=f'安全审计日志描述 #{i+1}',
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    resource_accessed=f'/api/resource_{random.randint(1, 10)}',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    response_status=random.choice([200, 201, 400, 401, 403, 404, 500]),
                    session_id=f'session_{uuid.uuid4().hex[:8]}',
                    success=random.choice([True, False]),
                    risk_level=random.choice(['low', 'medium', 'high', 'critical']),
                    additional_data={'extra_info': f'额外信息 #{i+1}'}
                )

            # 2. 数据加密记录 - 使用确切字段
            data_types = ['user_profile', 'chat_message', 'payment_info', 'personal_data', 'system_config']
            algorithms = ['AES-256-GCM', 'RSA-2048', 'ChaCha20-Poly1305']

            for i, data_type in enumerate(data_types):
                for j in range(5):
                    DataEncryption.objects.create(
                        data_type=data_type,
                        data_id=f'{data_type}_{i}_{j}',
                        algorithm=random.choice(algorithms),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        key_version=random.randint(1, 5),
                        encryption_status='encrypted',
                        data_size=random.randint(1024, 1048576),
                        checksum=f'sha256_{uuid.uuid4().hex[:16]}',
                        access_level=random.choice(['public', 'internal', 'confidential', 'top_secret']),
                        expires_at=timezone.now() + timedelta(days=random.randint(30, 365))
                    )

            # 3. 频率限制记录 - 使用确切字段
            limit_types = ['api_request', 'login_attempt', 'message_send', 'profile_view', 'search_query']

            for i in range(30):
                RateLimitRecord.objects.create(
                    identifier_type=random.choice(['ip', 'user', 'device']),
                    identifier_value=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    limit_type=random.choice(limit_types),
                    limit_count=random.randint(10, 100),
                    time_window=random.randint(60, 3600),
                    current_count=random.randint(0, 50),
                    is_exceeded=random.choice([True, False]),
                    exceeded_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                    reset_at=timezone.now() + timedelta(seconds=random.randint(60, 3600))
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全数据: SecurityAuditLog {SecurityAuditLog.objects.count()}条, DataEncryption {DataEncryption.objects.count()}条, RateLimitRecord {RateLimitRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))
