#!/usr/bin/env python3
"""
全面检查后台管理页面数据情况
"""

from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = '全面检查后台管理页面数据情况'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔍 开始全面检查后台管理页面数据情况...'))
        
        # 获取所有已注册的模型
        registered_models = []
        for model, model_admin in admin.site._registry.items():
            app_label = model._meta.app_label
            model_name = model._meta.model_name
            verbose_name = model._meta.verbose_name
            verbose_name_plural = model._meta.verbose_name_plural
            
            try:
                count = model.objects.count()
            except Exception as e:
                count = f'错误: {e}'
            
            registered_models.append({
                'app': app_label,
                'model': model_name,
                'verbose_name': verbose_name,
                'verbose_name_plural': verbose_name_plural,
                'count': count,
                'model_class': model
            })
        
        # 按应用分组显示
        apps_data = {}
        for item in registered_models:
            app = item['app']
            if app not in apps_data:
                apps_data[app] = []
            apps_data[app].append(item)
        
        # 显示结果
        empty_models = []
        total_models = 0
        
        for app_name, models in sorted(apps_data.items()):
            self.stdout.write(f'\n📱 {app_name.upper()} 应用:')
            for model in models:
                total_models += 1
                count = model['count']
                if isinstance(count, int):
                    if count == 0:
                        status = '❌'
                        empty_models.append(model)
                    elif count < 5:
                        status = '⚠️'
                    else:
                        status = '✅'
                    self.stdout.write(f'  {status} {model["verbose_name_plural"]} ({model["model"]}) - {count} 条')
                else:
                    self.stdout.write(f'  ❌ {model["verbose_name_plural"]} ({model["model"]}) - {count}')
                    empty_models.append(model)
        
        # 统计结果
        self.stdout.write(f'\n📊 统计结果:')
        self.stdout.write(f'  • 总模型数: {total_models}')
        self.stdout.write(f'  • 有数据模型: {total_models - len(empty_models)}')
        self.stdout.write(f'  • 缺少数据模型: {len(empty_models)}')
        
        if empty_models:
            self.stdout.write(f'\n❌ 缺少演示数据的模型:')
            for model in empty_models:
                self.stdout.write(f'  • {model["app"]}.{model["model"]} - {model["verbose_name_plural"]}')
        
        # 详细检查特定模块
        self.detailed_check()
        
        self.stdout.write(self.style.SUCCESS('\n✅ 全面检查完成！'))

    def detailed_check(self):
        """详细检查特定模块的数据情况"""
        self.stdout.write(f'\n🔍 详细检查特定模块数据情况:')
        
        # 检查聊天模块
        try:
            from apps.chat.models import ChatSession, ChatMessage, MessageTemplate, ChatStatistics, OnlineStatus
            self.stdout.write(f'\n💬 聊天模块:')
            self.stdout.write(f'  • 聊天会话: {ChatSession.objects.count()}')
            self.stdout.write(f'  • 聊天消息: {ChatMessage.objects.count()}')
            self.stdout.write(f'  • 消息模板: {MessageTemplate.objects.count()}')
            self.stdout.write(f'  • 聊天统计: {ChatStatistics.objects.count()}')
            self.stdout.write(f'  • 在线状态: {OnlineStatus.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 聊天模块检查失败: {e}')
        
        # 检查支付模块
        try:
            from apps.payment.models import VipPackage, CoinPackage, Order, PaymentRecord, UserVipRecord, CoinTransaction
            self.stdout.write(f'\n💰 支付模块:')
            self.stdout.write(f'  • VIP套餐: {VipPackage.objects.count()}')
            self.stdout.write(f'  • 金币套餐: {CoinPackage.objects.count()}')
            self.stdout.write(f'  • 订单记录: {Order.objects.count()}')
            self.stdout.write(f'  • 支付记录: {PaymentRecord.objects.count()}')
            self.stdout.write(f'  • VIP记录: {UserVipRecord.objects.count()}')
            self.stdout.write(f'  • 金币交易: {CoinTransaction.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 支付模块检查失败: {e}')
        
        # 检查礼物模块
        try:
            from apps.gifts.models import GiftCategory, Gift, GiftRecord, UserGiftBox, GiftStatistics, GiftActivity
            self.stdout.write(f'\n🎁 礼物模块:')
            self.stdout.write(f'  • 礼物分类: {GiftCategory.objects.count()}')
            self.stdout.write(f'  • 礼物列表: {Gift.objects.count()}')
            self.stdout.write(f'  • 赠送记录: {GiftRecord.objects.count()}')
            self.stdout.write(f'  • 用户礼物盒: {UserGiftBox.objects.count()}')
            self.stdout.write(f'  • 礼物统计: {GiftStatistics.objects.count()}')
            self.stdout.write(f'  • 礼物活动: {GiftActivity.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 礼物模块检查失败: {e}')
        
        # 检查动态模块
        try:
            from apps.moments.models import Moment, MomentLike, MomentComment, MomentView, MomentReport, Topic, MomentTopic
            self.stdout.write(f'\n📝 动态模块:')
            self.stdout.write(f'  • 话题管理: {Topic.objects.count()}')
            self.stdout.write(f'  • 动态列表: {Moment.objects.count()}')
            self.stdout.write(f'  • 动态点赞: {MomentLike.objects.count()}')
            self.stdout.write(f'  • 动态评论: {MomentComment.objects.count()}')
            self.stdout.write(f'  • 动态浏览: {MomentView.objects.count()}')
            self.stdout.write(f'  • 动态举报: {MomentReport.objects.count()}')
            self.stdout.write(f'  • 话题关联: {MomentTopic.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 动态模块检查失败: {e}')
        
        # 检查匹配模块
        try:
            from apps.matching.models import Like, Match, RecommendationLog, MatchingStatistics
            self.stdout.write(f'\n💕 匹配模块:')
            self.stdout.write(f'  • 点赞记录: {Like.objects.count()}')
            self.stdout.write(f'  • 匹配记录: {Match.objects.count()}')
            self.stdout.write(f'  • 推荐日志: {RecommendationLog.objects.count()}')
            self.stdout.write(f'  • 匹配统计: {MatchingStatistics.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 匹配模块检查失败: {e}')
        
        # 检查用户模块
        try:
            from apps.users.models import (
                ParentProfile, ChildProfile, MatchmakerProfile, UserPreference, 
                UserAction, UserVerification, UserBlacklist, MatchmakerClient, ServiceRecord
            )
            self.stdout.write(f'\n👥 用户模块:')
            self.stdout.write(f'  • 总用户数: {User.objects.count()}')
            self.stdout.write(f'  • 家长档案: {ParentProfile.objects.count()}')
            self.stdout.write(f'  • 子女档案: {ChildProfile.objects.count()}')
            self.stdout.write(f'  • 红娘档案: {MatchmakerProfile.objects.count()}')
            self.stdout.write(f'  • 用户偏好: {UserPreference.objects.count()}')
            self.stdout.write(f'  • 用户行为: {UserAction.objects.count()}')
            self.stdout.write(f'  • 用户认证: {UserVerification.objects.count()}')
            self.stdout.write(f'  • 用户黑名单: {UserBlacklist.objects.count()}')
            self.stdout.write(f'  • 红娘客户: {MatchmakerClient.objects.count()}')
            self.stdout.write(f'  • 服务记录: {ServiceRecord.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 用户模块检查失败: {e}')
        
        # 检查系统配置模块
        try:
            from apps.users.models import WechatConfig, AppConfig
            self.stdout.write(f'\n⚙️ 系统配置模块:')
            self.stdout.write(f'  • 微信配置: {WechatConfig.objects.count()}')
            self.stdout.write(f'  • 应用配置: {AppConfig.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 系统配置模块检查失败: {e}')
        
        # 检查AI匹配模块
        try:
            from apps.ai_matching.models import (
                UserProfile, MatchingModel, UserSimilarity, MatchPrediction,
                UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
                ModelPerformanceMetric
            )
            self.stdout.write(f'\n🧠 AI匹配模块:')
            self.stdout.write(f'  • 用户画像: {UserProfile.objects.count()}')
            self.stdout.write(f'  • 匹配模型: {MatchingModel.objects.count()}')
            self.stdout.write(f'  • 用户相似度: {UserSimilarity.objects.count()}')
            self.stdout.write(f'  • 匹配预测: {MatchPrediction.objects.count()}')
            self.stdout.write(f'  • 用户行为模式: {UserBehaviorPattern.objects.count()}')
            self.stdout.write(f'  • 模型训练数据: {ModelTrainingData.objects.count()}')
            self.stdout.write(f'  • 推荐解释: {RecommendationExplanation.objects.count()}')
            self.stdout.write(f'  • 模型性能指标: {ModelPerformanceMetric.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ AI匹配模块检查失败: {e}')
        
        # 检查实时通讯模块
        try:
            from apps.realtime.models import (
                WebSocketConnection, VoiceCall, VideoCall, VoiceMessage,
                RealtimeNotification, UserOnlineStatus
            )
            self.stdout.write(f'\n⚡ 实时通讯模块:')
            self.stdout.write(f'  • WebSocket连接: {WebSocketConnection.objects.count()}')
            self.stdout.write(f'  • 语音通话: {VoiceCall.objects.count()}')
            self.stdout.write(f'  • 视频通话: {VideoCall.objects.count()}')
            self.stdout.write(f'  • 语音消息: {VoiceMessage.objects.count()}')
            self.stdout.write(f'  • 实时通知: {RealtimeNotification.objects.count()}')
            self.stdout.write(f'  • 用户在线状态: {UserOnlineStatus.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 实时通讯模块检查失败: {e}')
        
        # 检查安全模块
        try:
            from apps.security.models import SecurityEvent, IPBlacklist, UserSecurityProfile, SecurityConfiguration
            self.stdout.write(f'\n🛡️ 安全模块:')
            self.stdout.write(f'  • 安全事件: {SecurityEvent.objects.count()}')
            self.stdout.write(f'  • IP黑名单: {IPBlacklist.objects.count()}')
            self.stdout.write(f'  • 用户安全档案: {UserSecurityProfile.objects.count()}')
            self.stdout.write(f'  • 安全配置: {SecurityConfiguration.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 安全模块检查失败: {e}')
        
        # 检查国际化模块
        try:
            from apps.i18n.models import Language, TranslationCategory, TranslationKey, Translation
            self.stdout.write(f'\n🌍 国际化模块:')
            self.stdout.write(f'  • 语言管理: {Language.objects.count()}')
            self.stdout.write(f'  • 翻译分类: {TranslationCategory.objects.count()}')
            self.stdout.write(f'  • 翻译键: {TranslationKey.objects.count()}')
            self.stdout.write(f'  • 翻译内容: {Translation.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 国际化模块检查失败: {e}')
        
        # 检查高级分析模块
        try:
            from apps.advanced_analytics.models import (
                UserSegment, UserSegmentMembership, UserValueScore, BehaviorPrediction,
                UserLifecycleStage, CohortAnalysis, FunnelAnalysis, ABTestExperiment,
                ABTestParticipant, BusinessIntelligenceReport, PredictiveModel,
                DataQualityMetric
            )
            self.stdout.write(f'\n📊 高级分析模块:')
            self.stdout.write(f'  • 用户分群: {UserSegment.objects.count()}')
            self.stdout.write(f'  • 分群成员: {UserSegmentMembership.objects.count()}')
            self.stdout.write(f'  • 用户价值评分: {UserValueScore.objects.count()}')
            self.stdout.write(f'  • 行为预测: {BehaviorPrediction.objects.count()}')
            self.stdout.write(f'  • 用户生命周期: {UserLifecycleStage.objects.count()}')
            self.stdout.write(f'  • 队列分析: {CohortAnalysis.objects.count()}')
            self.stdout.write(f'  • 漏斗分析: {FunnelAnalysis.objects.count()}')
            self.stdout.write(f'  • A/B测试实验: {ABTestExperiment.objects.count()}')
            self.stdout.write(f'  • A/B测试参与者: {ABTestParticipant.objects.count()}')
            self.stdout.write(f'  • 商业智能报告: {BusinessIntelligenceReport.objects.count()}')
            self.stdout.write(f'  • 预测模型: {PredictiveModel.objects.count()}')
            self.stdout.write(f'  • 数据质量指标: {DataQualityMetric.objects.count()}')
        except Exception as e:
            self.stdout.write(f'  ❌ 高级分析模块检查失败: {e}')
