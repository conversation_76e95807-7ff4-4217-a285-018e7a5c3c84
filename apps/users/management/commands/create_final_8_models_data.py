#!/usr/bin/env python3
"""
为最后8个缺少数据的模型创建精确的演示数据，实现100%数据覆盖
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最后8个缺少数据的模型创建精确的演示数据，实现100%数据覆盖'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最后8个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_operations_data()
        self.create_reports_data()
        self.create_security_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最后8个模型演示数据创建完成！实现100%数据覆盖！'))

    def create_operations_data(self):
        """创建运营模块精确数据"""
        self.stdout.write('🎯 创建运营模块精确数据...')
        
        try:
            from apps.operations.models import CampaignParticipant, ABTest, MarketingCampaign
            
            # 1. 活动参与者 - 使用确切字段
            campaigns = list(MarketingCampaign.objects.all())
            if campaigns:
                for campaign in campaigns:
                    participants = random.sample(self.users, random.randint(8, 12))
                    for user in participants:
                        if not CampaignParticipant.objects.filter(campaign=campaign, user=user).exists():
                            CampaignParticipant.objects.create(
                                campaign=campaign,
                                user=user,
                                participation_data={
                                    'source': random.choice(['app', 'web', 'wechat']),
                                    'device': random.choice(['ios', 'android', 'web']),
                                    'referrer': random.choice(['organic', 'social', 'paid'])
                                },
                                is_converted=random.choice([True, False]),
                                reward_received={
                                    'coins': random.randint(0, 100),
                                    'vip_days': random.randint(0, 7),
                                    'gifts': random.randint(0, 3)
                                }
                            )
            
            # 2. A/B测试 - 使用确切字段
            tests = [
                {
                    'name': '首页布局A/B测试',
                    'description': '测试不同的首页布局对用户转化的影响',
                    'test_type': 'ui',
                    'hypothesis': '新的首页布局将提高用户转化率15%',
                    'success_metric': 'registration_rate'
                },
                {
                    'name': '推荐算法A/B测试',
                    'description': '测试新的推荐算法对匹配成功率的影响',
                    'test_type': 'algorithm',
                    'hypothesis': '新的推荐算法将提高匹配成功率20%',
                    'success_metric': 'match_rate'
                },
                {
                    'name': '注册流程优化测试',
                    'description': '测试简化注册流程的效果',
                    'test_type': 'feature',
                    'hypothesis': '简化的注册流程将提高注册完成率25%',
                    'success_metric': 'completion_rate'
                }
            ]
            
            for test_data in tests:
                if not ABTest.objects.filter(name=test_data['name']).exists():
                    control_users = random.randint(100, 500)
                    test_users = random.randint(100, 500)
                    control_conversions = random.randint(10, control_users // 3)
                    test_conversions = random.randint(15, test_users // 2)
                    
                    ABTest.objects.create(
                        name=test_data['name'],
                        description=test_data['description'],
                        test_type=test_data['test_type'],
                        hypothesis=test_data['hypothesis'],
                        success_metric=test_data['success_metric'],
                        control_version={
                            'version': 'v1.0',
                            'config': {'layout': 'original', 'algorithm': 'v1'}
                        },
                        test_version={
                            'version': 'v2.0',
                            'config': {'layout': 'new', 'algorithm': 'v2'}
                        },
                        traffic_allocation=50.0,
                        start_time=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_time=timezone.now() + timedelta(days=random.randint(7, 30)),
                        status=random.choice(['running', 'completed']),
                        control_users=control_users,
                        test_users=test_users,
                        control_conversions=control_conversions,
                        test_conversions=test_conversions,
                        is_significant=test_conversions > control_conversions * 1.1,
                        confidence_level=round(random.uniform(0.85, 0.99), 2),
                        winner='test' if test_conversions > control_conversions else 'control',
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 运营数据: CampaignParticipant {CampaignParticipant.objects.count()}条, ABTest {ABTest.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 运营数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块精确数据"""
        self.stdout.write('🚨 创建举报模块精确数据...')
        
        try:
            from apps.reports.models import Blacklist, ContentModeration, SecurityLog
            
            # 1. 黑名单 - 使用确切字段
            for i in range(20):
                user, blocked_user = random.sample(self.users, 2)
                if not Blacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    Blacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice([
                            '发送骚扰信息',
                            '发布不当内容',
                            '虚假资料',
                            '恶意行为',
                            '垃圾信息',
                            '其他原因'
                        ])
                    )
            
            # 2. 内容审核 - 使用确切字段
            content_types = ['profile', 'moment', 'message', 'comment']
            statuses = ['pending', 'approved', 'rejected']
            
            for i in range(30):
                content_type = random.choice(content_types)
                status = random.choice(statuses)
                user = random.choice(self.users)
                moderator = random.choice(self.users) if status != 'pending' else None
                
                # 避免重复的content_type和content_id组合
                content_id = random.randint(1, 1000)
                while ContentModeration.objects.filter(content_type=content_type, content_id=content_id).exists():
                    content_id = random.randint(1, 1000)
                
                ContentModeration.objects.create(
                    content_type=content_type,
                    content_id=content_id,
                    user=user,
                    status=status,
                    moderator=moderator,
                    rejection_reason='内容不符合社区规范' if status == 'rejected' else '',
                    moderator_note=f'审核备注 #{i+1}' if moderator else '',
                    auto_score=round(random.uniform(0.1, 1.0), 2),
                    auto_tags=['sensitive_content', 'manual_review'] if random.choice([True, False]) else [],
                    reviewed_at=timezone.now() - timedelta(hours=random.randint(1, 48)) if status != 'pending' else None
                )
            
            # 3. 安全日志 - 使用确切字段
            log_types = ['login_attempt', 'password_change', 'suspicious_activity', 'data_access', 'profile_update']
            risk_levels = ['low', 'medium', 'high', 'critical']
            
            for i in range(40):
                user = random.choice(self.users) if random.choice([True, False]) else None
                log_type = random.choice(log_types)
                risk_level = random.choice(risk_levels)
                
                SecurityLog.objects.create(
                    user=user,
                    log_type=log_type,
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ]),
                    details={
                        'action': f'安全日志详情 #{i+1}',
                        'result': random.choice(['success', 'failure', 'blocked']),
                        'additional_info': f'额外信息 #{i+1}'
                    },
                    risk_level=risk_level
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据: Blacklist {Blacklist.objects.count()}条, ContentModeration {ContentModeration.objects.count()}条, SecurityLog {SecurityLog.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全模块精确数据"""
        self.stdout.write('🛡️ 创建安全模块精确数据...')
        
        try:
            from apps.security.models import SecurityAuditLog, DataEncryption, RateLimitRecord
            
            # 1. 安全审计日志 - 使用确切字段
            action_types = ['user_login', 'user_logout', 'password_change', 'profile_update', 'admin_action', 'data_export']
            
            for i in range(50):
                user = random.choice(self.users)
                action_type = random.choice(action_types)
                is_successful = random.choice([True, False])
                
                SecurityAuditLog.objects.create(
                    action_type=action_type,
                    user=user,
                    session_id=f'session_{uuid.uuid4().hex[:8]}',
                    action_description=f'安全审计日志描述 #{i+1} - {action_type}',
                    target_object_type=random.choice(['User', 'Profile', 'Message', 'System']),
                    target_object_id=str(random.randint(1, 100)),
                    is_successful=is_successful,
                    error_message='操作失败' if not is_successful else '',
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ]),
                    old_values={'old_field': 'old_value'} if action_type == 'profile_update' else {},
                    new_values={'new_field': 'new_value'} if action_type == 'profile_update' else {}
                )
            
            # 2. 数据加密记录 - 使用确切字段
            data_types = ['user_profile', 'chat_message', 'payment_info', 'personal_data', 'system_config']
            algorithms = ['AES-256-GCM', 'RSA-2048', 'ChaCha20-Poly1305']
            access_levels = ['public', 'internal', 'confidential', 'top_secret']
            
            for i, data_type in enumerate(data_types):
                for j in range(6):  # 每种数据类型创建6条记录
                    DataEncryption.objects.create(
                        data_type=data_type,
                        data_id=f'{data_type}_{i}_{j}',
                        algorithm=random.choice(algorithms),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        key_version=random.randint(1, 5),
                        encryption_status='encrypted',
                        data_size=random.randint(1024, 1048576),
                        checksum=f'sha256_{uuid.uuid4().hex[:16]}',
                        access_level=random.choice(access_levels),
                        expires_at=timezone.now() + timedelta(days=random.randint(30, 365))
                    )
            
            # 3. 频率限制记录 - 使用确切字段
            limit_types = ['api_request', 'login_attempt', 'message_send', 'like_action', 'search_query']
            identifier_types = ['ip', 'user', 'device']
            
            for i in range(40):
                identifier_type = random.choice(identifier_types)
                limit_type = random.choice(limit_types)
                limit_count = random.randint(10, 100)
                current_count = random.randint(0, limit_count + 10)
                is_exceeded = current_count > limit_count
                
                if identifier_type == 'ip':
                    identifier_value = f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}'
                elif identifier_type == 'user':
                    identifier_value = str(random.choice(self.users).id)
                else:  # device
                    identifier_value = f'device_{uuid.uuid4().hex[:8]}'
                
                RateLimitRecord.objects.create(
                    identifier_type=identifier_type,
                    identifier_value=identifier_value,
                    limit_type=limit_type,
                    limit_count=limit_count,
                    time_window=random.randint(60, 3600),
                    current_count=current_count,
                    is_exceeded=is_exceeded,
                    exceeded_at=timezone.now() - timedelta(minutes=random.randint(1, 60)) if is_exceeded else None,
                    reset_at=timezone.now() + timedelta(seconds=random.randint(60, 3600))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全数据: SecurityAuditLog {SecurityAuditLog.objects.count()}条, DataEncryption {DataEncryption.objects.count()}条, RateLimitRecord {RateLimitRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))
