#!/usr/bin/env python3
"""
为剩余缺少演示数据的模型创建完整的演示数据 - 第二部分
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为剩余缺少演示数据的模型创建完整的演示数据 - 第二部分'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为剩余缺少数据的模型创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_i18n_data()
        self.create_matching_data()
        self.create_moments_data()
        self.create_operations_data()
        self.create_payment_data()
        self.create_realtime_data()
        self.create_reports_data()
        self.create_security_data()
        self.create_system_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 剩余缺少数据的模型演示数据创建完成！'))

    def create_i18n_data(self):
        """创建国际化模块演示数据"""
        self.stdout.write('🌍 创建国际化模块演示数据...')
        
        try:
            from apps.i18n.models import (
                UserLanguagePreference, TranslationMemory, TranslationStatistics,
                LocalizationConfig, AutoTranslation, Language
            )
            
            # 1. 用户语言偏好
            languages = list(Language.objects.all())
            if languages:
                for user in self.users:
                    if not UserLanguagePreference.objects.filter(user=user).exists():
                        UserLanguagePreference.objects.create(
                            user=user,
                            primary_language=random.choice(languages),
                            secondary_language=random.choice(languages),
                            auto_translate=random.choice([True, False]),
                            preferred_timezone=random.choice([
                                'Asia/Shanghai', 'Asia/Tokyo', 'America/New_York'
                            ])
                        )
            
            # 2. 翻译记忆
            for i in range(20):
                TranslationMemory.objects.create(
                    source_text=f'源文本 #{i+1}',
                    target_text=f'目标文本 #{i+1}',
                    source_language=random.choice(languages) if languages else None,
                    target_language=random.choice(languages) if languages else None,
                    quality_score=round(random.uniform(0.7, 1.0), 2),
                    usage_count=random.randint(1, 50),
                    created_by=random.choice(self.users)
                )
            
            # 3. 翻译统计
            for language in languages:
                if not TranslationStatistics.objects.filter(language=language).exists():
                    total_keys = random.randint(100, 500)
                    translated_keys = random.randint(50, total_keys)
                    TranslationStatistics.objects.create(
                        language=language,
                        date=timezone.now().date(),
                        total_keys=total_keys,
                        translated_keys=translated_keys,
                        pending_keys=total_keys - translated_keys,
                        completion_rate=round((translated_keys / total_keys) * 100, 2)
                    )
            
            # 4. 本地化配置
            for language in languages[:3]:
                if not LocalizationConfig.objects.filter(language=language).exists():
                    LocalizationConfig.objects.create(
                        language=language,
                        date_format='%Y-%m-%d',
                        time_format='%H:%M:%S',
                        currency_symbol=random.choice(['¥', '$', '€']),
                        number_format='1,234.56',
                        first_day_of_week=random.choice([0, 1])
                    )
            
            # 5. 自动翻译
            for i in range(15):
                AutoTranslation.objects.create(
                    source_text=f'自动翻译源文本 #{i+1}',
                    translated_text=f'自动翻译结果 #{i+1}',
                    source_language=random.choice(languages) if languages else None,
                    target_language=random.choice(languages) if languages else None,
                    translation_service='google_translate',
                    confidence_score=round(random.uniform(0.6, 0.95), 2),
                    is_reviewed=random.choice([True, False])
                )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 国际化模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化模块数据创建失败: {e}'))

    def create_matching_data(self):
        """创建匹配模块演示数据"""
        self.stdout.write('💕 创建匹配模块演示数据...')
        
        try:
            from apps.matching.models import (
                MatchingAlgorithmConfig, UserCompatibility, DailyRecommendation
            )
            
            # 1. 匹配算法配置
            configs = [
                {'name': '年龄权重', 'key': 'age_weight', 'value': '0.3'},
                {'name': '教育权重', 'key': 'education_weight', 'value': '0.2'},
                {'name': '兴趣权重', 'key': 'interest_weight', 'value': '0.25'},
                {'name': '地理位置权重', 'key': 'location_weight', 'value': '0.15'},
                {'name': '活跃度权重', 'key': 'activity_weight', 'value': '0.1'}
            ]
            
            for config in configs:
                if not MatchingAlgorithmConfig.objects.filter(config_key=config['key']).exists():
                    MatchingAlgorithmConfig.objects.create(
                        name=config['name'],
                        config_key=config['key'],
                        config_value=config['value'],
                        description=f'{config["name"]}在匹配算法中的权重',
                        is_active=True
                    )
            
            # 2. 用户兼容性
            for i in range(25):
                user1, user2 = random.sample(self.users, 2)
                if not UserCompatibility.objects.filter(user1=user1, user2=user2).exists():
                    UserCompatibility.objects.create(
                        user1=user1,
                        user2=user2,
                        compatibility_score=round(random.uniform(0.1, 0.95), 3),
                        personality_match=round(random.uniform(0.0, 1.0), 3),
                        interest_match=round(random.uniform(0.0, 1.0), 3),
                        lifestyle_match=round(random.uniform(0.0, 1.0), 3),
                        calculated_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )
            
            # 3. 每日推荐
            for user in self.users:
                for days_ago in range(7):
                    recommendation_date = timezone.now().date() - timedelta(days=days_ago)
                    if not DailyRecommendation.objects.filter(user=user, recommendation_date=recommendation_date).exists():
                        recommended_users = random.sample([u for u in self.users if u != user], random.randint(3, 6))
                        DailyRecommendation.objects.create(
                            user=user,
                            recommendation_date=recommendation_date,
                            recommended_users=recommended_users,
                            algorithm_used=random.choice(['collaborative', 'content_based', 'hybrid']),
                            total_recommendations=len(recommended_users),
                            viewed_count=random.randint(0, len(recommended_users)),
                            liked_count=random.randint(0, 2)
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 匹配模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 匹配模块数据创建失败: {e}'))

    def create_moments_data(self):
        """创建动态模块演示数据"""
        self.stdout.write('📝 创建动态模块演示数据...')
        
        try:
            from apps.moments.models import MomentCommentLike, MomentComment
            
            # 评论点赞
            comments = list(MomentComment.objects.all())
            if comments:
                for i in range(30):
                    comment = random.choice(comments)
                    user = random.choice(self.users)
                    if not MomentCommentLike.objects.filter(comment=comment, user=user).exists():
                        MomentCommentLike.objects.create(
                            comment=comment,
                            user=user,
                            created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 动态模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 动态模块数据创建失败: {e}'))

    def create_operations_data(self):
        """创建运营模块演示数据"""
        self.stdout.write('🎯 创建运营模块演示数据...')
        
        try:
            from apps.operations.models import CampaignParticipant, ABTest, MarketingCampaign
            
            # 1. 活动参与者
            campaigns = list(MarketingCampaign.objects.all())
            if campaigns:
                for campaign in campaigns:
                    participants = random.sample(self.users, random.randint(5, 10))
                    for user in participants:
                        if not CampaignParticipant.objects.filter(campaign=campaign, user=user).exists():
                            CampaignParticipant.objects.create(
                                campaign=campaign,
                                user=user,
                                participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                                conversion_achieved=random.choice([True, False]),
                                conversion_value=Decimal(str(round(random.uniform(0, 100), 2)))
                            )
            
            # 2. A/B测试
            tests = [
                {'name': '首页布局测试', 'description': '测试不同的首页布局效果'},
                {'name': '注册流程测试', 'description': '测试简化的注册流程'}
            ]
            
            for test_data in tests:
                if not ABTest.objects.filter(name=test_data['name']).exists():
                    ABTest.objects.create(
                        name=test_data['name'],
                        description=test_data['description'],
                        status=random.choice(['active', 'completed', 'paused']),
                        control_group_size=random.randint(100, 500),
                        test_group_size=random.randint(100, 500),
                        conversion_rate_control=round(random.uniform(0.1, 0.3), 3),
                        conversion_rate_test=round(random.uniform(0.1, 0.3), 3),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 30))
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 运营模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 运营模块数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块演示数据"""
        self.stdout.write('💰 创建支付模块演示数据...')
        
        try:
            from apps.payment.models import UserVipRecord, VipPackage
            
            # 用户VIP记录
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(6, 10)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        start_date = timezone.now() - timedelta(days=random.randint(1, 90))
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            start_date=start_date,
                            end_date=start_date + timedelta(days=30),
                            is_active=random.choice([True, False]),
                            auto_renew=random.choice([True, False]),
                            purchase_price=package.price
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 支付模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付模块数据创建失败: {e}'))

    def create_realtime_data(self):
        """创建实时通讯模块演示数据"""
        self.stdout.write('⚡ 创建实时通讯模块演示数据...')
        
        try:
            from apps.realtime.models import VoiceCall, VideoCall, VoiceMessage, RealtimeNotification
            
            # 1. 语音通话
            for i in range(20):
                caller, callee = random.sample(self.users, 2)
                duration = random.randint(30, 1800)
                VoiceCall.objects.create(
                    caller=caller,
                    callee=callee,
                    call_id=f'voice_{uuid.uuid4().hex[:8]}',
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    call_quality=round(random.uniform(3.0, 5.0), 1),
                    started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )
            
            # 2. 视频通话
            for i in range(15):
                caller, callee = random.sample(self.users, 2)
                duration = random.randint(60, 3600)
                VideoCall.objects.create(
                    caller=caller,
                    callee=callee,
                    call_id=f'video_{uuid.uuid4().hex[:8]}',
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    call_quality=round(random.uniform(3.0, 5.0), 1),
                    video_quality=random.choice(['720p', '1080p', '480p']),
                    started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )
            
            # 3. 语音消息
            for i in range(35):
                sender, receiver = random.sample(self.users, 2)
                VoiceMessage.objects.create(
                    sender=sender,
                    receiver=receiver,
                    message_id=f'voice_msg_{uuid.uuid4().hex[:8]}',
                    duration=random.randint(5, 60),
                    file_size=random.randint(50000, 500000),
                    file_url=f'/media/voice/{uuid.uuid4().hex[:8]}.mp3',
                    quality=random.choice(['high', 'medium', 'low']),
                    is_played=random.choice([True, False])
                )
            
            # 4. 实时通知
            notification_types = ['new_message', 'new_match', 'profile_view', 'gift_received', 'system_update']
            for i in range(40):
                RealtimeNotification.objects.create(
                    user=random.choice(self.users),
                    notification_type=random.choice(notification_types),
                    title=f'通知标题 #{i+1}',
                    content=f'通知内容 #{i+1}',
                    is_read=random.choice([True, False]),
                    priority=random.choice(['low', 'medium', 'high'])
                )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 实时通讯模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯模块数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块演示数据"""
        self.stdout.write('🚨 创建举报模块演示数据...')

        try:
            from apps.reports.models import (
                Report, Blacklist, UserPunishment, AppealRecord,
                ContentModeration, SecurityLog
            )

            # 1. 举报记录
            report_types = ['inappropriate_content', 'harassment', 'fake_profile', 'spam']
            for i in range(25):
                reporter = random.choice(self.users)
                reported_user = random.choice([u for u in self.users if u != reporter])
                Report.objects.create(
                    reporter=reporter,
                    reported_user=reported_user,
                    report_type=random.choice(report_types),
                    description=f'举报描述 #{i+1}',
                    status=random.choice(['pending', 'investigating', 'resolved', 'rejected']),
                    evidence_urls=[f'/media/reports/evidence_{i}.jpg'],
                    admin_notes=f'管理员备注 #{i+1}' if random.choice([True, False]) else ''
                )

            # 2. 黑名单
            for i in range(15):
                user, blocked_user = random.sample(self.users, 2)
                if not Blacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    Blacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'spam', 'inappropriate']),
                        notes=f'黑名单备注 #{i+1}'
                    )

            # 3. 用户处罚
            punishment_types = ['warning', 'mute', 'temporary_ban', 'permanent_ban']
            for i in range(12):
                UserPunishment.objects.create(
                    user=random.choice(self.users),
                    punishment_type=random.choice(punishment_types),
                    reason=f'违规处罚 #{i+1}',
                    description=f'处罚描述 #{i+1}',
                    duration_hours=random.randint(24, 168) if random.choice([True, False]) else None,
                    is_active=random.choice([True, False]),
                    admin_user=random.choice(self.users)
                )

            # 4. 申诉记录
            punishments = list(UserPunishment.objects.all())
            if punishments:
                for punishment in random.sample(punishments, min(6, len(punishments))):
                    if not AppealRecord.objects.filter(punishment=punishment).exists():
                        AppealRecord.objects.create(
                            punishment=punishment,
                            user=punishment.user,
                            appeal_reason='申诉理由',
                            evidence_description='申诉证据描述',
                            status=random.choice(['pending', 'under_review', 'approved', 'rejected']),
                            admin_response='管理员回复' if random.choice([True, False]) else ''
                        )

            # 5. 内容审核
            content_types = ['profile', 'moment', 'message', 'comment']
            for i in range(30):
                ContentModeration.objects.create(
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    user=random.choice(self.users),
                    status=random.choice(['pending', 'approved', 'rejected']),
                    moderation_reason=f'审核原因 #{i+1}',
                    moderator=random.choice(self.users),
                    flagged_keywords=['敏感词1', '敏感词2'] if random.choice([True, False]) else []
                )

            # 6. 安全日志
            log_types = ['login_attempt', 'password_change', 'suspicious_activity']
            for i in range(40):
                SecurityLog.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    log_type=random.choice(log_types),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    details={'action': f'安全日志详情 #{i+1}'},
                    risk_level=random.choice(['low', 'medium', 'high'])
                )

            self.stdout.write(self.style.SUCCESS('  ✅ 举报模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报模块数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全模块演示数据"""
        self.stdout.write('🛡️ 创建安全模块演示数据...')

        try:
            from apps.security.models import SecurityAuditLog, DataEncryption, RateLimitRecord

            # 1. 安全审计日志
            audit_types = ['user_login', 'admin_action', 'data_access', 'system_change']
            for i in range(35):
                SecurityAuditLog.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(audit_types),
                    action_description=f'审计日志描述 #{i+1}',
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    resource_accessed=f'/api/resource_{i}',
                    success=random.choice([True, False]),
                    risk_level=random.choice(['low', 'medium', 'high'])
                )

            # 2. 数据加密记录
            data_types = ['user_profile', 'chat_message', 'payment_info', 'personal_data']
            for data_type in data_types:
                for i in range(5):
                    DataEncryption.objects.create(
                        data_type=data_type,
                        encryption_algorithm=random.choice(['AES-256', 'RSA-2048', 'ChaCha20']),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        encrypted_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                        encryption_status='active',
                        data_size=random.randint(1024, 1048576)
                    )

            # 3. 频率限制记录
            for i in range(25):
                RateLimitRecord.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    endpoint=f'/api/endpoint_{random.randint(1, 10)}',
                    request_count=random.randint(50, 200),
                    limit_exceeded=random.choice([True, False]),
                    time_window='1hour',
                    blocked_until=timezone.now() + timedelta(hours=1) if random.choice([True, False]) else None
                )

            self.stdout.write(self.style.SUCCESS('  ✅ 安全模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全模块数据创建失败: {e}'))

    def create_system_data(self):
        """创建系统模块演示数据"""
        self.stdout.write('⚙️ 创建系统模块演示数据...')

        try:
            from apps.system.models import AppVersion, UserFeedback

            # 1. 应用版本
            versions = ['1.0.0', '1.1.0', '1.2.0', '2.0.0', '2.1.0']
            for version in versions:
                if not AppVersion.objects.filter(version_number=version).exists():
                    AppVersion.objects.create(
                        version_number=version,
                        version_name=f'版本 {version}',
                        release_notes=f'版本 {version} 更新说明',
                        platform=random.choice(['ios', 'android', 'web']),
                        is_mandatory=random.choice([True, False]),
                        download_url=f'https://download.example.com/app_{version}.apk',
                        release_date=timezone.now() - timedelta(days=random.randint(1, 365))
                    )

            # 2. 用户反馈
            feedback_types = ['bug_report', 'feature_request', 'general_feedback', 'complaint']
            for i in range(20):
                UserFeedback.objects.create(
                    user=random.choice(self.users),
                    feedback_type=random.choice(feedback_types),
                    title=f'用户反馈 #{i+1}',
                    content=f'反馈内容详情 #{i+1}',
                    rating=random.randint(1, 5),
                    status=random.choice(['new', 'in_progress', 'resolved', 'closed']),
                    admin_response=f'管理员回复 #{i+1}' if random.choice([True, False]) else '',
                    contact_email=f'user{i}@example.com'
                )

            self.stdout.write(self.style.SUCCESS('  ✅ 系统模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 系统模块数据创建失败: {e}'))
