# apps/users/management/commands/init_config.py
from django.core.management.base import BaseCommand
from apps.users.models import WechatConfig, AppConfig
from apps.users.wechat_utils import ConfigManager


class Command(BaseCommand):
    help = '初始化系统配置'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新初始化配置',
        )

    def handle(self, *args, **options):
        self.stdout.write('开始初始化系统配置...')
        
        # 初始化默认应用配置
        ConfigManager.init_default_configs()
        self.stdout.write(self.style.SUCCESS('✓ 默认应用配置初始化完成'))
        
        # 创建默认微信配置（如果不存在）
        if not WechatConfig.objects.exists() or options['force']:
            wechat_config = WechatConfig.objects.create(
                name='默认微信配置',
                app_id='your_app_id_here',
                app_secret='your_app_secret_here',
                mch_id='your_mch_id_here',
                mch_key='your_mch_key_here',
                notify_url='https://your-domain.com/api/v1/payment/notify/',
                is_active=True
            )
            self.stdout.write(self.style.SUCCESS('✓ 默认微信配置创建完成'))
            self.stdout.write(self.style.WARNING('⚠️  请在后台管理中更新微信配置信息'))
        else:
            self.stdout.write(self.style.SUCCESS('✓ 微信配置已存在'))
        
        # 显示配置统计
        wechat_count = WechatConfig.objects.count()
        app_config_count = AppConfig.objects.count()
        
        self.stdout.write('\n配置统计:')
        self.stdout.write(f'  微信配置: {wechat_count} 个')
        self.stdout.write(f'  应用配置: {app_config_count} 个')
        
        self.stdout.write(self.style.SUCCESS('\n✅ 系统配置初始化完成！'))
        self.stdout.write('请访问后台管理页面完善配置信息：')
        self.stdout.write('  - 微信配置: /admin/users/wechatconfig/')
        self.stdout.write('  - 应用配置: /admin/users/appconfig/')
