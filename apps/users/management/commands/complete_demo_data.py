#!/usr/bin/env python3
"""
为所有后台管理页面补充完整的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid

User = get_user_model()

class Command(BaseCommand):
    help = '为所有后台管理页面补充完整的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始补充完整的后台管理演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all()[:15])
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 补充各模块数据
        self.complete_ai_matching_data()
        self.complete_realtime_data()
        self.complete_security_data()
        self.complete_i18n_data()
        self.complete_analytics_data()
        self.complete_other_modules_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 所有后台管理演示数据补充完成！'))

    def complete_ai_matching_data(self):
        """补充AI智能匹配模块演示数据"""
        self.stdout.write('🧠 补充AI智能匹配模块演示数据...')
        
        try:
            from apps.ai_matching.models import (
                UserProfile, MatchingModel, UserSimilarity, MatchPrediction,
                UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
                ModelPerformanceMetric
            )
            
            # 补充用户行为模式数据
            behavior_patterns = ['early_bird', 'night_owl', 'weekend_active', 'workday_focused']
            for user in self.users[:8]:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        pattern_type=random.choice(behavior_patterns),
                        pattern_data={
                            'active_hours': random.sample(range(24), random.randint(6, 12)),
                            'response_time_avg': random.randint(5, 120),
                            'message_frequency': random.randint(10, 100)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        last_updated=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            # 补充模型训练数据
            data_types = ['user_interactions', 'match_outcomes', 'message_data', 'profile_data']
            for i, data_type in enumerate(data_types):
                if not ModelTrainingData.objects.filter(data_type=data_type).exists():
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        data_source=f'production_db_{i+1}',
                        data_size=random.randint(10000, 100000),
                        quality_score=round(random.uniform(0.7, 0.95), 3),
                        preprocessing_steps=['normalization', 'feature_extraction', 'validation'],
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            # 补充推荐解释数据
            for i in range(12):
                user, recommended_user = random.sample(self.users, 2)
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        explanation_text=f'基于共同兴趣和相似的生活方式推荐',
                        explanation_factors={
                            'common_interests': random.sample(['音乐', '电影', '旅行', '美食'], 2),
                            'similarity_score': round(random.uniform(0.6, 0.9), 2),
                            'compatibility_reasons': ['年龄匹配', '教育背景相似', '地理位置接近']
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3)
                    )
            
            # 补充模型性能指标数据
            models = list(MatchingModel.objects.all())
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc']
            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=round(random.uniform(0.6, 0.95), 4),
                            evaluation_dataset='test_set_2025',
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30))
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ AI智能匹配数据补充完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI智能匹配数据补充失败: {e}'))

    def complete_realtime_data(self):
        """补充实时通讯模块演示数据"""
        self.stdout.write('⚡ 补充实时通讯模块演示数据...')
        
        try:
            from apps.realtime.models import (
                WebSocketConnection, VoiceCall, VideoCall, VoiceMessage,
                RealtimeNotification, UserOnlineStatus
            )
            
            # 补充语音通话数据
            for i in range(15):
                caller, receiver = random.sample(self.users, 2)
                duration = random.randint(30, 1800)
                VoiceCall.objects.create(
                    caller=caller,
                    receiver=receiver,
                    call_id=f'voice_{uuid.uuid4().hex[:8]}',
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    quality_score=round(random.uniform(3.0, 5.0), 1),
                    started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )
            
            # 补充视频通话数据
            for i in range(10):
                caller, receiver = random.sample(self.users, 2)
                duration = random.randint(60, 3600)
                VideoCall.objects.create(
                    caller=caller,
                    receiver=receiver,
                    call_id=f'video_{uuid.uuid4().hex[:8]}',
                    status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                    duration=duration,
                    quality_score=round(random.uniform(3.0, 5.0), 1),
                    video_quality=random.choice(['720p', '1080p', '480p']),
                    started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                    ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                )
            
            # 补充语音消息数据
            for i in range(25):
                sender, receiver = random.sample(self.users, 2)
                VoiceMessage.objects.create(
                    sender=sender,
                    receiver=receiver,
                    message_id=f'voice_msg_{uuid.uuid4().hex[:8]}',
                    duration=random.randint(5, 60),
                    file_size=random.randint(50000, 500000),
                    quality=random.choice(['high', 'medium', 'low']),
                    is_played=random.choice([True, False]),
                    played_at=timezone.now() - timedelta(hours=random.randint(1, 48)) if random.choice([True, False]) else None
                )
            
            # 补充实时通知数据
            notification_types = ['new_message', 'new_match', 'profile_view', 'gift_received', 'system_update']
            for i in range(30):
                RealtimeNotification.objects.create(
                    user=random.choice(self.users),
                    notification_type=random.choice(notification_types),
                    title=f'通知标题 #{i+1}',
                    content=f'这是一条演示通知内容 #{i+1}',
                    is_read=random.choice([True, False]),
                    priority=random.choice(['low', 'medium', 'high']),
                    read_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None
                )
            
            # 补充用户在线状态数据
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet']),
                        location_info={
                            'city': random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                            'country': '中国'
                        }
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 实时通讯数据补充完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据补充失败: {e}'))

    def complete_security_data(self):
        """补充安全防护模块演示数据"""
        self.stdout.write('🛡️ 补充安全防护模块演示数据...')
        
        try:
            from apps.security.models import (
                SecurityEvent, IPBlacklist, UserSecurityProfile,
                SecurityConfiguration, SecurityAuditLog, DataEncryption, RateLimitRecord
            )
            
            # 补充用户安全档案数据
            for user in self.users:
                if not UserSecurityProfile.objects.filter(user=user).exists():
                    UserSecurityProfile.objects.create(
                        user=user,
                        risk_score=random.randint(0, 100),
                        trust_level=random.choice(['low', 'medium', 'high']),
                        failed_login_count=random.randint(0, 5),
                        last_failed_login=timezone.now() - timedelta(hours=random.randint(1, 168)) if random.choice([True, False]) else None,
                        account_locked_until=timezone.now() + timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                        phone_verified=random.choice([True, False]),
                        email_verified=random.choice([True, False]),
                        identity_verified=random.choice([True, False]),
                        two_factor_enabled=random.choice([True, False]),
                        security_events_count=random.randint(0, 10)
                    )
            
            # 补充安全配置数据
            security_configs = [
                {'category': 'login', 'key': 'max_failed_attempts', 'value': '5'},
                {'category': 'login', 'key': 'lockout_duration', 'value': '30'},
                {'category': 'password', 'key': 'min_length', 'value': '8'},
                {'category': 'password', 'key': 'require_special_chars', 'value': 'true'},
                {'category': 'session', 'key': 'timeout_minutes', 'value': '120'},
                {'category': 'rate_limit', 'key': 'api_requests_per_minute', 'value': '100'}
            ]
            
            for config in security_configs:
                if not SecurityConfiguration.objects.filter(config_key=config['key']).exists():
                    SecurityConfiguration.objects.create(
                        category=config['category'],
                        config_key=config['key'],
                        config_value=config['value'],
                        description=f'{config["key"]}配置项',
                        is_active=True,
                        created_by=random.choice(self.users)
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 安全防护数据补充完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全防护数据补充失败: {e}'))

    def complete_i18n_data(self):
        """补充国际化模块演示数据"""
        self.stdout.write('🌍 补充国际化模块演示数据...')

        try:
            from apps.i18n.models import (
                Language, TranslationCategory, TranslationKey, Translation,
                UserLanguagePreference, TranslationStatistics, LocalizationConfig, AutoTranslation
            )

            # 补充用户语言偏好数据
            languages = list(Language.objects.all())
            for user in self.users[:10]:
                if not UserLanguagePreference.objects.filter(user=user).exists():
                    UserLanguagePreference.objects.create(
                        user=user,
                        primary_language=random.choice(languages),
                        secondary_language=random.choice(languages),
                        auto_translate=random.choice([True, False]),
                        preferred_timezone=random.choice([
                            'Asia/Shanghai', 'Asia/Tokyo', 'America/New_York',
                            'Europe/London', 'Australia/Sydney'
                        ])
                    )

            # 补充翻译统计数据
            for language in languages:
                if not TranslationStatistics.objects.filter(language=language, date=timezone.now().date()).exists():
                    total_keys = random.randint(100, 500)
                    translated_keys = random.randint(50, total_keys)
                    TranslationStatistics.objects.create(
                        language=language,
                        date=timezone.now().date(),
                        total_keys=total_keys,
                        translated_keys=translated_keys,
                        pending_keys=total_keys - translated_keys,
                        avg_quality_score=round(random.uniform(3.5, 5.0), 2),
                        approved_translations=random.randint(30, translated_keys),
                        completion_rate=round((translated_keys / total_keys) * 100, 2),
                        new_translations=random.randint(5, 20),
                        updated_translations=random.randint(2, 10)
                    )

            # 补充本地化配置数据
            for language in languages[:3]:  # 只为前3种语言创建配置
                if not LocalizationConfig.objects.filter(language=language).exists():
                    LocalizationConfig.objects.create(
                        language=language,
                        date_format='%Y-%m-%d',
                        time_format='%H:%M:%S',
                        currency_symbol=random.choice(['¥', '$', '€', '£']),
                        number_format='1,234.56',
                        first_day_of_week=random.choice([0, 1]),  # 0=Sunday, 1=Monday
                        rtl_support=language.is_rtl,
                        custom_settings={
                            'decimal_separator': '.',
                            'thousand_separator': ',',
                            'currency_position': 'before'
                        }
                    )

            self.stdout.write(self.style.SUCCESS('  ✅ 国际化数据补充完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据补充失败: {e}'))

    def complete_analytics_data(self):
        """补充高级数据分析模块演示数据"""
        self.stdout.write('📊 补充高级数据分析模块演示数据...')

        try:
            from apps.advanced_analytics.models import (
                UserSegment, UserSegmentMembership, UserValueScore, BehaviorPrediction,
                UserLifecycleStage, CohortAnalysis, FunnelAnalysis, ABTestExperiment,
                ABTestParticipant, BusinessIntelligenceReport, PredictiveModel, DataQualityMetric
            )

            # 补充用户价值评分数据
            for user in self.users:
                if not UserValueScore.objects.filter(user=user).exists():
                    UserValueScore.objects.create(
                        user=user,
                        monetary_value=round(random.uniform(0, 1000), 2),
                        engagement_score=round(random.uniform(0, 100), 2),
                        retention_score=round(random.uniform(0, 100), 2),
                        referral_score=round(random.uniform(0, 100), 2),
                        total_value_score=round(random.uniform(0, 400), 2),
                        value_tier=random.choice(['bronze', 'silver', 'gold', 'platinum']),
                        last_calculated_at=timezone.now() - timedelta(days=random.randint(1, 7))
                    )

            # 补充用户生命周期阶段数据
            lifecycle_stages = ['new', 'active', 'engaged', 'at_risk', 'churned']
            for user in self.users:
                if not UserLifecycleStage.objects.filter(user=user).exists():
                    UserLifecycleStage.objects.create(
                        user=user,
                        current_stage=random.choice(lifecycle_stages),
                        stage_duration_days=random.randint(1, 365),
                        stage_history=[
                            {'stage': 'new', 'date': '2025-01-01', 'duration': 7},
                            {'stage': 'active', 'date': '2025-01-08', 'duration': 30}
                        ],
                        predicted_next_stage=random.choice(lifecycle_stages),
                        transition_probability=round(random.uniform(0.1, 0.9), 3),
                        last_updated=timezone.now() - timedelta(days=random.randint(1, 30))
                    )

            # 补充A/B测试实验数据
            experiments = [
                {
                    'name': '新用户引导流程优化',
                    'description': '测试新的用户引导流程对注册转化率的影响',
                    'hypothesis': '新的引导流程将提高注册转化率15%'
                },
                {
                    'name': '匹配算法优化',
                    'description': '测试改进的匹配算法对用户满意度的影响',
                    'hypothesis': '新算法将提高匹配满意度20%'
                }
            ]

            for exp_data in experiments:
                if not ABTestExperiment.objects.filter(name=exp_data['name']).exists():
                    experiment = ABTestExperiment.objects.create(
                        name=exp_data['name'],
                        description=exp_data['description'],
                        hypothesis=exp_data['hypothesis'],
                        status=random.choice(['draft', 'running', 'completed', 'paused']),
                        primary_metric='conversion_rate',
                        secondary_metrics=['engagement_rate', 'retention_rate'],
                        target_sample_size=random.randint(1000, 10000),
                        current_sample_size=random.randint(500, 5000),
                        traffic_allocation=round(random.uniform(0.1, 0.5), 2),
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 60)),
                        expected_duration_days=random.randint(14, 90),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        confidence_interval=0.95,
                        results_summary={'control_conversion': 0.12, 'treatment_conversion': 0.15}
                    )

                    # 为实验添加参与者
                    for user in random.sample(self.users, random.randint(3, 8)):
                        ABTestParticipant.objects.create(
                            experiment=experiment,
                            user=user,
                            variant=random.choice(['control', 'treatment']),
                            assigned_at=timezone.now() - timedelta(days=random.randint(1, 15)),
                            converted=random.choice([True, False]),
                            conversion_value=round(random.uniform(0, 100), 2) if random.choice([True, False]) else 0
                        )

            self.stdout.write(self.style.SUCCESS('  ✅ 高级数据分析数据补充完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级数据分析数据补充失败: {e}'))

    def complete_other_modules_data(self):
        """补充其他模块演示数据"""
        self.stdout.write('🔧 补充其他模块演示数据...')

        try:
            # 补充用户偏好数据
            from apps.users.models import UserPreference, UserAction

            for user in self.users:
                if not UserPreference.objects.filter(user=user).exists():
                    UserPreference.objects.create(
                        user=user,
                        min_age=random.randint(18, 25),
                        max_age=random.randint(30, 45),
                        preferred_gender=random.choice([1, 2]),
                        max_distance=random.randint(10, 100),
                        education_preference=random.choice([1, 2, 3, 4, 5]),
                        income_preference=random.choice([1, 2, 3, 4, 5]),
                        height_preference_min=random.randint(150, 170),
                        height_preference_max=random.randint(170, 190)
                    )

            # 补充用户行为数据
            action_types = ['login', 'profile_view', 'like', 'message', 'search']
            for i in range(50):
                UserAction.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(action_types),
                    target_user=random.choice(self.users) if random.choice([True, False]) else None,
                    action_data={'page': 'home', 'duration': random.randint(10, 300)},
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )

            self.stdout.write(self.style.SUCCESS('  ✅ 其他模块数据补充完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 其他模块数据补充失败: {e}'))
