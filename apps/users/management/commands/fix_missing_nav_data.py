#!/usr/bin/env python3
"""
修复缺失的后台导航页面数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = '修复缺失的后台导航页面数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始修复缺失的导航页面数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 修复数据
        self.fix_user_verification_data()
        self.fix_service_record_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 缺失导航页面数据修复完成！'))

    def fix_user_verification_data(self):
        """修复用户认证数据"""
        self.stdout.write('🔐 修复用户认证数据...')
        
        try:
            from apps.users.models import UserVerification
            
            verification_types = ['id_card', 'phone', 'education', 'profession', 'photo']
            statuses = ['pending', 'approved', 'rejected']
            
            # 为每个用户创建1-3个认证记录
            for user in self.users[:12]:  # 只为前12个用户创建认证记录
                # 随机选择1-3种认证类型
                selected_types = random.sample(verification_types, random.randint(1, 3))
                
                for verification_type in selected_types:
                    if not UserVerification.objects.filter(user=user, verification_type=verification_type).exists():
                        status = random.choice(statuses)
                        
                        # 根据认证类型创建不同的数据
                        verification_data = {}
                        reason = ''
                        
                        if verification_type == 'id_card':
                            verification_data = {
                                'id_number': f'11010119{random.randint(80, 99)}0101{random.randint(1000, 9999)}',
                                'real_name': f'用户{user.id}'
                            }
                        elif verification_type == 'phone':
                            verification_data = {
                                'phone_number': f'138{random.randint(10000000, 99999999)}'
                            }
                        elif verification_type == 'education':
                            verification_data = {
                                'school': random.choice(['清华大学', '北京大学', '复旦大学', '上海交大', '浙江大学']),
                                'degree': random.choice(['本科', '硕士', '博士']),
                                'major': random.choice(['计算机科学', '金融学', '管理学', '医学', '法学'])
                            }
                        elif verification_type == 'profession':
                            verification_data = {
                                'company': random.choice(['阿里巴巴', '腾讯', '百度', '字节跳动', '美团']),
                                'position': random.choice(['软件工程师', '产品经理', '设计师', '运营专员', '销售经理']),
                                'salary_range': random.choice(['10k-15k', '15k-25k', '25k-35k', '35k以上'])
                            }
                        elif verification_type == 'photo':
                            verification_data = {
                                'photo_urls': [f'/media/verification/photo_{user.id}_{i}.jpg' for i in range(1, 4)]
                            }
                        
                        if status == 'rejected':
                            reason = random.choice([
                                '照片不清晰，请重新上传',
                                '信息不完整，请补充相关材料',
                                '证件信息与个人资料不符',
                                '需要提供更多证明材料'
                            ])
                        
                        UserVerification.objects.create(
                            user=user,
                            verification_type=verification_type,
                            status=status,
                            data=verification_data,
                            reason=reason
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户认证数据修复完成 - 总计: {UserVerification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户认证数据修复失败: {e}'))

    def fix_service_record_data(self):
        """修复服务记录数据"""
        self.stdout.write('📋 修复服务记录数据...')
        
        try:
            from apps.users.models import MatchmakerClient, ServiceRecord
            
            # 获取红娘客户
            clients = list(MatchmakerClient.objects.all())
            if not clients:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到红娘客户，跳过服务记录数据创建'))
                return
            
            service_types = ['consultation', 'profile_optimization', 'matching', 'date_arrangement', 'follow_up', 'feedback', 'other']
            
            # 为每个客户创建2-6条服务记录
            for client in clients:
                record_count = random.randint(2, 6)
                
                for i in range(record_count):
                    service_type = random.choice(service_types)
                    
                    # 根据服务类型生成不同的标题和内容
                    service_data = {
                        'consultation': {
                            'title': '初次咨询服务',
                            'content': '与客户进行深入沟通，了解其择偶需求、期望和个人情况，制定个性化服务方案。',
                            'result': '已了解客户基本需求，制定了初步匹配策略。'
                        },
                        'profile_optimization': {
                            'title': '个人资料优化',
                            'content': '帮助客户完善个人资料，优化照片选择，提升个人魅力展示。',
                            'result': '资料完善度提升30%，照片更换为专业形象照。'
                        },
                        'matching': {
                            'title': '匹配推荐服务',
                            'content': '根据客户条件和需求，精选优质匹配对象进行推荐。',
                            'result': f'推荐了{random.randint(3, 8)}位优质匹配对象。'
                        },
                        'date_arrangement': {
                            'title': '约会安排服务',
                            'content': '协调双方时间，安排合适的约会场所和活动。',
                            'result': '成功安排约会，双方反馈良好。'
                        },
                        'follow_up': {
                            'title': '跟进服务',
                            'content': '跟进客户约会情况，收集反馈，调整后续服务策略。',
                            'result': '收集到详细反馈，调整了匹配标准。'
                        },
                        'feedback': {
                            'title': '反馈收集',
                            'content': '收集客户对服务的意见和建议，持续改进服务质量。',
                            'result': '客户满意度评分4.5/5.0。'
                        },
                        'other': {
                            'title': '定制化服务',
                            'content': '根据客户特殊需求提供的个性化服务。',
                            'result': '完成定制化服务需求。'
                        }
                    }
                    
                    data = service_data.get(service_type, service_data['other'])
                    
                    ServiceRecord.objects.create(
                        client=client,
                        service_type=service_type,
                        title=data['title'],
                        content=data['content'],
                        service_date=timezone.now() - timedelta(days=random.randint(0, 30)),
                        duration=random.randint(30, 180),
                        result=data['result']
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 服务记录数据修复完成 - 总计: {ServiceRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 服务记录数据修复失败: {e}'))
