# apps/users/management/commands/init_system_config.py
from django.core.management.base import BaseCommand
from apps.users.models import WechatConfig, AppConfig


class Command(BaseCommand):
    help = '初始化系统配置数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化系统配置...')
        
        # 创建微信配置
        self.create_wechat_config()
        
        # 创建应用配置
        self.create_app_config()
        
        self.stdout.write(self.style.SUCCESS('✅ 系统配置初始化完成！'))

    def create_wechat_config(self):
        self.stdout.write('创建微信配置...')
        
        # 创建默认微信配置
        wechat_config, created = WechatConfig.objects.get_or_create(
            name='默认微信配置',
            defaults={
                'app_id': 'wx1234567890abcdef',  # 示例APPID
                'app_secret': 'your_app_secret_here',  # 示例AppSecret
                'mch_id': '1234567890',  # 示例商户号
                'mch_key': 'your_mch_key_here',  # 示例商户密钥
                'notify_url': 'https://your-domain.com/api/payment/notify/',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✓ 创建了默认微信配置'))
        else:
            self.stdout.write(self.style.WARNING('微信配置已存在'))

    def create_app_config(self):
        self.stdout.write('创建应用配置...')
        
        # 应用基础配置
        app_configs = [
            # 应用基础设置
            {
                'config_type': 'app',
                'key': 'app_name',
                'value': '缘分相亲',
                'description': '应用名称'
            },
            {
                'config_type': 'app',
                'key': 'app_version',
                'value': '1.0.0',
                'description': '应用版本号'
            },
            {
                'config_type': 'app',
                'key': 'app_description',
                'value': '专业的相亲交友平台，为您寻找真爱',
                'description': '应用描述'
            },
            {
                'config_type': 'app',
                'key': 'customer_service_phone',
                'value': '************',
                'description': '客服电话'
            },
            {
                'config_type': 'app',
                'key': 'customer_service_wechat',
                'value': 'xiangqin_service',
                'description': '客服微信号'
            },
            
            # 用户相关配置
            {
                'config_type': 'user',
                'key': 'max_daily_likes',
                'value': '20',
                'description': '普通用户每日最大点赞次数'
            },
            {
                'config_type': 'user',
                'key': 'vip_daily_likes',
                'value': '999',
                'description': 'VIP用户每日最大点赞次数'
            },
            {
                'config_type': 'user',
                'key': 'profile_complete_reward',
                'value': '100',
                'description': '完善资料奖励金币数'
            },
            {
                'config_type': 'user',
                'key': 'daily_signin_reward',
                'value': '10',
                'description': '每日签到奖励金币数'
            },
            {
                'config_type': 'user',
                'key': 'invite_friend_reward',
                'value': '50',
                'description': '邀请好友奖励金币数'
            },
            
            # 匹配相关配置
            {
                'config_type': 'matching',
                'key': 'match_distance_km',
                'value': '50',
                'description': '默认匹配距离范围（公里）'
            },
            {
                'config_type': 'matching',
                'key': 'match_age_range',
                'value': '5',
                'description': '默认匹配年龄范围（±年）'
            },
            {
                'config_type': 'matching',
                'key': 'super_like_cost',
                'value': '5',
                'description': '超级喜欢消耗金币数'
            },
            {
                'config_type': 'matching',
                'key': 'undo_action_cost',
                'value': '3',
                'description': '撤销操作消耗金币数'
            },
            
            # 聊天相关配置
            {
                'config_type': 'chat',
                'key': 'message_send_cost',
                'value': '1',
                'description': '普通用户发送消息消耗金币数'
            },
            {
                'config_type': 'chat',
                'key': 'voice_message_cost',
                'value': '2',
                'description': '发送语音消息消耗金币数'
            },
            {
                'config_type': 'chat',
                'key': 'image_message_cost',
                'value': '3',
                'description': '发送图片消息消耗金币数'
            },
            {
                'config_type': 'chat',
                'key': 'max_message_length',
                'value': '500',
                'description': '消息最大长度（字符）'
            },
            
            # 礼物相关配置
            {
                'config_type': 'gift',
                'key': 'gift_commission_rate',
                'value': '0.1',
                'description': '礼物平台抽成比例'
            },
            {
                'config_type': 'gift',
                'key': 'daily_gift_limit',
                'value': '10',
                'description': '每日最大送礼次数'
            },
            {
                'config_type': 'gift',
                'key': 'gift_return_rate',
                'value': '0.8',
                'description': '礼物回收比例'
            },
            
            # 动态相关配置
            {
                'config_type': 'moment',
                'key': 'daily_moment_limit',
                'value': '5',
                'description': '每日最大发布动态数'
            },
            {
                'config_type': 'moment',
                'key': 'moment_image_limit',
                'value': '9',
                'description': '动态最大图片数'
            },
            {
                'config_type': 'moment',
                'key': 'moment_content_max_length',
                'value': '500',
                'description': '动态内容最大长度'
            },
            
            # 支付相关配置
            {
                'config_type': 'payment',
                'key': 'min_recharge_amount',
                'value': '1.00',
                'description': '最小充值金额'
            },
            {
                'config_type': 'payment',
                'key': 'max_recharge_amount',
                'value': '10000.00',
                'description': '最大充值金额'
            },
            {
                'config_type': 'payment',
                'key': 'payment_timeout_minutes',
                'value': '30',
                'description': '支付超时时间（分钟）'
            },
            
            # 安全相关配置
            {
                'config_type': 'security',
                'key': 'max_login_attempts',
                'value': '5',
                'description': '最大登录尝试次数'
            },
            {
                'config_type': 'security',
                'key': 'account_lock_minutes',
                'value': '30',
                'description': '账户锁定时间（分钟）'
            },
            {
                'config_type': 'security',
                'key': 'sensitive_word_check',
                'value': 'true',
                'description': '是否开启敏感词检查'
            },
            
            # 推送相关配置
            {
                'config_type': 'notification',
                'key': 'push_new_match',
                'value': 'true',
                'description': '是否推送新匹配通知'
            },
            {
                'config_type': 'notification',
                'key': 'push_new_message',
                'value': 'true',
                'description': '是否推送新消息通知'
            },
            {
                'config_type': 'notification',
                'key': 'push_gift_received',
                'value': 'true',
                'description': '是否推送收到礼物通知'
            },
            
            # 审核相关配置
            {
                'config_type': 'review',
                'key': 'auto_review_enabled',
                'value': 'true',
                'description': '是否开启自动审核'
            },
            {
                'config_type': 'review',
                'key': 'manual_review_threshold',
                'value': '0.8',
                'description': '人工审核阈值'
            },
            {
                'config_type': 'review',
                'key': 'review_timeout_hours',
                'value': '24',
                'description': '审核超时时间（小时）'
            }
        ]
        
        created_count = 0
        for config_data in app_configs:
            config, created = AppConfig.objects.get_or_create(
                config_type=config_data['config_type'],
                key=config_data['key'],
                defaults={
                    'value': config_data['value'],
                    'description': config_data['description'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'✓ 创建了 {created_count} 个应用配置项'))
        
        if created_count < len(app_configs):
            self.stdout.write(self.style.WARNING(f'有 {len(app_configs) - created_count} 个配置项已存在'))
