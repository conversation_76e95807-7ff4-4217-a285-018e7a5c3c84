#!/usr/bin/env python3
"""
为最终缺少demo数据的后台导航菜单页面创建演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最终缺少demo数据的后台导航菜单页面创建演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最终缺少数据的导航页面创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_additional_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最终缺少数据的导航页面演示数据创建完成！'))

    def create_additional_data(self):
        """创建额外的演示数据"""
        self.stdout.write('📊 创建额外的演示数据...')
        
        # 1. 创建更多用户验证数据
        try:
            from apps.users.models import UserVerification
            
            verification_types = ['id_card', 'phone', 'education', 'profession', 'photo']
            statuses = ['pending', 'approved', 'rejected']
            
            for user in self.users[:10]:
                for verification_type in random.sample(verification_types, 2):
                    try:
                        if not UserVerification.objects.filter(user=user, verification_type=verification_type).exists():
                            UserVerification.objects.create(
                                user=user,
                                verification_type=verification_type,
                                status=random.choice(statuses)
                            )
                    except Exception:
                        continue
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户验证数据: {UserVerification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户验证数据创建失败: {e}'))

        # 2. 创建红娘客户数据
        try:
            from apps.users.models import MatchmakerProfile, MatchmakerClient
            
            matchmakers = list(MatchmakerProfile.objects.all())
            if matchmakers:
                normal_users = [user for user in self.users if user.user_type == 'normal'][:8]
                
                for matchmaker in matchmakers:
                    for client_user in random.sample(normal_users, min(3, len(normal_users))):
                        if not MatchmakerClient.objects.filter(matchmaker=matchmaker, user=client_user).exists():
                            MatchmakerClient.objects.create(
                                matchmaker=matchmaker,
                                user=client_user,
                                service_package=random.choice(['basic', 'standard', 'premium']),
                                service_fee=Decimal(str(random.randint(1000, 5000))),
                                status=random.choice(['new', 'consulting', 'matching', 'completed']),
                                start_date=timezone.now().date() - timedelta(days=random.randint(1, 60)),
                                end_date=timezone.now().date() + timedelta(days=random.randint(30, 180))
                            )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 红娘客户数据: {MatchmakerClient.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 红娘客户数据创建失败: {e}'))

        # 3. 创建服务记录数据
        try:
            from apps.users.models import ServiceRecord, MatchmakerClient
            
            clients = list(MatchmakerClient.objects.all())
            if clients:
                service_types = ['consultation', 'profile_optimization', 'matching', 'date_arrangement', 'follow_up']
                
                for client in clients:
                    for i in range(random.randint(2, 5)):
                        ServiceRecord.objects.create(
                            client=client,
                            service_type=random.choice(service_types),
                            title=f'服务记录 #{i+1}',
                            content=f'详细的服务内容描述 #{i+1}',
                            service_date=timezone.now() - timedelta(days=random.randint(0, 30)),
                            duration=random.randint(30, 180),
                            result=f'服务结果 #{i+1}'
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 服务记录数据: {ServiceRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 服务记录数据创建失败: {e}'))

        # 4. 创建AI匹配相关数据
        try:
            from apps.ai_matching.models import UserProfile, MatchingModel, UserSimilarity, MatchPrediction
            
            # 确保用户画像数据充足
            for user in self.users:
                if not UserProfile.objects.filter(user=user).exists():
                    UserProfile.objects.create(
                        user=user,
                        age_group=random.choice(['18-25', '26-30', '31-35', '36-40', '40+']),
                        education_level=random.choice(['高中', '大专', '本科', '硕士', '博士']),
                        income_level=random.choice(['3k以下', '3k-5k', '5k-8k', '8k-12k', '12k-20k', '20k以上']),
                        occupation_category=random.choice(['IT互联网', '金融', '教育', '医疗', '销售', '设计', '其他']),
                        location_tier=random.choice(['一线城市', '二线城市', '三线城市', '其他']),
                        activity_score=round(random.uniform(0.1, 1.0), 2),
                        social_score=round(random.uniform(0.1, 1.0), 2),
                        response_rate=round(random.uniform(0.3, 0.9), 2),
                        online_frequency=round(random.uniform(0.1, 1.0), 2),
                        preferred_age_min=random.randint(18, 30),
                        preferred_age_max=random.randint(25, 45),
                        interests=random.sample(['音乐', '电影', '旅行', '美食', '运动', '阅读'], random.randint(2, 4)),
                        personality_traits=random.sample(['开朗', '内向', '幽默', '温柔', '理性', '感性'], random.randint(2, 3)),
                        lifestyle_tags=random.sample(['健身', '宅家', '社交', '旅行', '美食'], random.randint(1, 3))
                    )
            
            # 创建更多用户相似度数据
            if UserSimilarity.objects.count() < 30:
                for i in range(20):
                    user1, user2 = random.sample(self.users, 2)
                    if not UserSimilarity.objects.filter(user1=user1, user2=user2).exists():
                        UserSimilarity.objects.create(
                            user1=user1,
                            user2=user2,
                            overall_similarity=round(random.uniform(0.1, 0.95), 3),
                            demographic_similarity=round(random.uniform(0.0, 1.0), 3),
                            interest_similarity=round(random.uniform(0.0, 1.0), 3),
                            behavior_similarity=round(random.uniform(0.0, 1.0), 3),
                            preference_similarity=round(random.uniform(0.0, 1.0), 3)
                        )
            
            # 创建更多匹配预测数据
            models = list(MatchingModel.objects.all())
            if models and MatchPrediction.objects.count() < 40:
                for i in range(25):
                    user, candidate = random.sample(self.users, 2)
                    if not MatchPrediction.objects.filter(user=user, candidate=candidate).exists():
                        MatchPrediction.objects.create(
                            user=user,
                            candidate=candidate,
                            match_score=round(random.uniform(0.1, 0.95), 3),
                            compatibility_score=round(random.uniform(0.1, 0.9), 3),
                            predicted_by_model=random.choice(models),
                            confidence=round(random.uniform(0.6, 0.95), 3)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ AI匹配数据更新完成'))
            self.stdout.write(self.style.SUCCESS(f'    • 用户画像: {UserProfile.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 用户相似度: {UserSimilarity.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 匹配预测: {MatchPrediction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配数据创建失败: {e}'))

        # 5. 创建实时通讯数据
        try:
            from apps.realtime.models import WebSocketConnection, UserOnlineStatus
            
            # 创建更多WebSocket连接记录
            if WebSocketConnection.objects.count() < 50:
                for i in range(20):
                    user = random.choice(self.users)
                    WebSocketConnection.objects.create(
                        user=user,
                        connection_type=random.choice(['chat', 'video_call', 'voice_call', 'notification']),
                        channel_name=f'user_{user.id}_{random.randint(1000, 9999)}',
                        client_ip=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                        status=random.choice(['connected', 'disconnected', 'reconnecting']),
                        messages_sent=random.randint(0, 100),
                        messages_received=random.randint(0, 100),
                        user_agent=random.choice([
                            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                            'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                        ])
                    )
            
            # 确保每个用户都有在线状态记录
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet'])
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通讯数据更新完成'))
            self.stdout.write(self.style.SUCCESS(f'    • WebSocket连接: {WebSocketConnection.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 在线状态: {UserOnlineStatus.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

        # 6. 创建高级分析数据
        try:
            from apps.advanced_analytics.models import UserSegment, UserSegmentMembership
            
            # 创建更多用户分群
            segments_data = [
                {'name': '高价值用户', 'description': '消费能力强的用户群体', 'segment_type': 'value_based'},
                {'name': '活跃社交用户', 'description': '社交活跃度高的用户', 'segment_type': 'behavioral'},
                {'name': '潜在流失用户', 'description': '可能流失的用户群体', 'segment_type': 'risk_based'}
            ]
            
            for segment_data in segments_data:
                if not UserSegment.objects.filter(name=segment_data['name']).exists():
                    segment = UserSegment.objects.create(
                        name=segment_data['name'],
                        description=segment_data['description'],
                        segment_type=segment_data['segment_type'],
                        conditions={'demo': True},
                        is_active=True,
                        user_count=random.randint(20, 100)
                    )
                    
                    # 为分群添加成员
                    selected_users = random.sample(self.users, random.randint(3, 6))
                    for user in selected_users:
                        UserSegmentMembership.objects.create(
                            user=user,
                            segment=segment,
                            confidence_score=round(random.uniform(0.7, 1.0), 2),
                            segment_features={'demo_feature': random.uniform(0.5, 1.0)}
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 高级分析数据更新完成'))
            self.stdout.write(self.style.SUCCESS(f'    • 用户分群: {UserSegment.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 分群成员: {UserSegmentMembership.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级分析数据创建失败: {e}'))
