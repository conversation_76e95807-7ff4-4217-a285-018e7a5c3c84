# apps/users/management/commands/check_admin_data.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.users.models import ParentProfile, ChildProfile, MatchmakerProfile, WechatConfig, AppConfig
from apps.matching.models import Like, Match, RecommendationLog, MatchingStatistics
from apps.chat.models import ChatSession, ChatMessage
from apps.payment.models import VipPackage, CoinPackage, Order, PaymentRecord, UserVipRecord, CoinTransaction
from apps.gifts.models import GiftCategory, Gift, GiftRecord, UserGiftBox, GiftStatistics, GiftActivity
from apps.moments.models import Moment, MomentLike, MomentComment, MomentView, MomentReport, Topic, MomentTopic

User = get_user_model()


class Command(BaseCommand):
    help = '检查后台管理页面的数据情况'

    def handle(self, *args, **options):
        self.stdout.write('🔍 检查后台管理页面数据情况...\n')
        
        # 用户相关数据
        self.check_user_data()
        
        # 匹配相关数据
        self.check_matching_data()
        
        # 聊天相关数据
        self.check_chat_data()
        
        # 支付相关数据
        self.check_payment_data()
        
        # 礼物相关数据
        self.check_gift_data()
        
        # 动态相关数据
        self.check_moment_data()
        
        # 系统配置数据
        self.check_config_data()
        
        self.stdout.write(self.style.SUCCESS('\n✅ 数据检查完成！'))

    def check_user_data(self):
        self.stdout.write(self.style.HTTP_INFO('👥 用户相关数据:'))
        
        user_count = User.objects.count()
        parent_count = ParentProfile.objects.count()
        child_count = ChildProfile.objects.count()
        matchmaker_count = MatchmakerProfile.objects.count()
        
        self.stdout.write(f'  • 总用户数: {user_count}')
        self.stdout.write(f'  • 家长档案: {parent_count}')
        self.stdout.write(f'  • 子女档案: {child_count}')
        self.stdout.write(f'  • 红娘档案: {matchmaker_count}')
        
        # 按用户类型统计
        normal_users = User.objects.filter(user_type='normal').count()
        parent_users = User.objects.filter(user_type='parent').count()
        matchmaker_users = User.objects.filter(user_type='matchmaker').count()
        
        self.stdout.write(f'  • 普通用户: {normal_users}')
        self.stdout.write(f'  • 家长用户: {parent_users}')
        self.stdout.write(f'  • 红娘用户: {matchmaker_users}')
        self.stdout.write('')

    def check_matching_data(self):
        self.stdout.write(self.style.HTTP_INFO('💕 匹配相关数据:'))
        
        like_count = Like.objects.count()
        match_count = Match.objects.count()
        recommendation_count = RecommendationLog.objects.count()
        statistics_count = MatchingStatistics.objects.count()
        
        self.stdout.write(f'  • 点赞记录: {like_count}')
        self.stdout.write(f'  • 匹配记录: {match_count}')
        self.stdout.write(f'  • 推荐日志: {recommendation_count}')
        self.stdout.write(f'  • 用户统计: {statistics_count}')
        
        # 匹配成功率
        mutual_likes = Like.objects.filter(is_mutual=True).count()
        if like_count > 0:
            success_rate = (mutual_likes / like_count) * 100
            self.stdout.write(f'  • 匹配成功率: {success_rate:.1f}%')
        
        self.stdout.write('')

    def check_chat_data(self):
        self.stdout.write(self.style.HTTP_INFO('💬 聊天相关数据:'))
        
        session_count = ChatSession.objects.count()
        message_count = ChatMessage.objects.count()
        
        self.stdout.write(f'  • 聊天会话: {session_count}')
        self.stdout.write(f'  • 聊天消息: {message_count}')
        
        # 活跃会话
        active_sessions = ChatSession.objects.filter(status='active').count()
        self.stdout.write(f'  • 活跃会话: {active_sessions}')
        
        if session_count > 0:
            avg_messages = message_count / session_count
            self.stdout.write(f'  • 平均消息数: {avg_messages:.1f}')
        
        self.stdout.write('')

    def check_payment_data(self):
        self.stdout.write(self.style.HTTP_INFO('💰 支付相关数据:'))
        
        vip_package_count = VipPackage.objects.count()
        coin_package_count = CoinPackage.objects.count()
        order_count = Order.objects.count()
        payment_count = PaymentRecord.objects.count()
        
        self.stdout.write(f'  • VIP套餐: {vip_package_count}')
        self.stdout.write(f'  • 金币套餐: {coin_package_count}')
        self.stdout.write(f'  • 订单记录: {order_count}')
        self.stdout.write(f'  • 支付记录: {payment_count}')
        
        # 订单状态统计
        paid_orders = Order.objects.filter(status='paid').count()
        pending_orders = Order.objects.filter(status='pending').count()
        
        self.stdout.write(f'  • 已支付订单: {paid_orders}')
        self.stdout.write(f'  • 待支付订单: {pending_orders}')
        
        self.stdout.write('')

    def check_gift_data(self):
        self.stdout.write(self.style.HTTP_INFO('🎁 礼物相关数据:'))
        
        category_count = GiftCategory.objects.count()
        gift_count = Gift.objects.count()
        gift_record_count = GiftRecord.objects.count()
        gift_statistics_count = GiftStatistics.objects.count()
        gift_activity_count = GiftActivity.objects.count()
        
        self.stdout.write(f'  • 礼物分类: {category_count}')
        self.stdout.write(f'  • 礼物种类: {gift_count}')
        self.stdout.write(f'  • 赠送记录: {gift_record_count}')
        self.stdout.write(f'  • 用户统计: {gift_statistics_count}')
        self.stdout.write(f'  • 礼物活动: {gift_activity_count}')
        
        # 热门礼物
        popular_gifts = Gift.objects.filter(is_popular=True).count()
        rare_gifts = Gift.objects.filter(is_rare=True).count()
        
        self.stdout.write(f'  • 热门礼物: {popular_gifts}')
        self.stdout.write(f'  • 稀有礼物: {rare_gifts}')
        
        self.stdout.write('')

    def check_moment_data(self):
        self.stdout.write(self.style.HTTP_INFO('📝 动态相关数据:'))
        
        topic_count = Topic.objects.count()
        moment_count = Moment.objects.count()
        like_count = MomentLike.objects.count()
        comment_count = MomentComment.objects.count()
        view_count = MomentView.objects.count()
        report_count = MomentReport.objects.count()
        
        self.stdout.write(f'  • 话题数量: {topic_count}')
        self.stdout.write(f'  • 动态数量: {moment_count}')
        self.stdout.write(f'  • 点赞记录: {like_count}')
        self.stdout.write(f'  • 评论记录: {comment_count}')
        self.stdout.write(f'  • 浏览记录: {view_count}')
        self.stdout.write(f'  • 举报记录: {report_count}')
        
        # 动态状态统计
        published_moments = Moment.objects.filter(status='published').count()
        trending_topics = Topic.objects.filter(is_trending=True).count()
        
        self.stdout.write(f'  • 已发布动态: {published_moments}')
        self.stdout.write(f'  • 热门话题: {trending_topics}')
        
        self.stdout.write('')

    def check_config_data(self):
        self.stdout.write(self.style.HTTP_INFO('⚙️ 系统配置数据:'))
        
        wechat_config_count = WechatConfig.objects.count()
        app_config_count = AppConfig.objects.count()
        
        self.stdout.write(f'  • 微信配置: {wechat_config_count}')
        self.stdout.write(f'  • 应用配置: {app_config_count}')
        
        # 配置类型统计
        if app_config_count > 0:
            config_types = AppConfig.objects.values('config_type').distinct().count()
            active_configs = AppConfig.objects.filter(is_active=True).count()
            
            self.stdout.write(f'  • 配置类型: {config_types}')
            self.stdout.write(f'  • 活跃配置: {active_configs}')
        
        self.stdout.write('')

    def get_admin_menu_summary(self):
        """获取后台管理菜单数据摘要"""
        summary = {
            '用户管理': {
                '用户列表': User.objects.count(),
                '家长档案': ParentProfile.objects.count(),
                '子女档案': ChildProfile.objects.count(),
                '红娘档案': MatchmakerProfile.objects.count(),
            },
            '匹配管理': {
                '点赞记录': Like.objects.count(),
                '匹配记录': Match.objects.count(),
                '推荐日志': RecommendationLog.objects.count(),
                '匹配统计': MatchingStatistics.objects.count(),
            },
            '聊天管理': {
                '聊天会话': ChatSession.objects.count(),
                '聊天消息': ChatMessage.objects.count(),
            },
            '支付管理': {
                'VIP套餐': VipPackage.objects.count(),
                '金币套餐': CoinPackage.objects.count(),
                '订单记录': Order.objects.count(),
                '支付记录': PaymentRecord.objects.count(),
            },
            '礼物管理': {
                '礼物分类': GiftCategory.objects.count(),
                '礼物列表': Gift.objects.count(),
                '赠送记录': GiftRecord.objects.count(),
                '礼物活动': GiftActivity.objects.count(),
            },
            '动态管理': {
                '话题管理': Topic.objects.count(),
                '动态列表': Moment.objects.count(),
                '点赞记录': MomentLike.objects.count(),
                '评论记录': MomentComment.objects.count(),
            },
            '系统配置': {
                '微信配置': WechatConfig.objects.count(),
                '应用配置': AppConfig.objects.count(),
            }
        }
        return summary
