#!/usr/bin/env python3
"""
为所有缺少演示数据的48个模型创建完整的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为所有缺少演示数据的48个模型创建完整的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为48个缺少数据的模型创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_advanced_analytics_data()
        self.create_ai_matching_data()
        self.create_analytics_data()
        self.create_auth_data()
        self.create_chat_data()
        self.create_gifts_data()
        self.create_i18n_data()
        self.create_matching_data()
        self.create_moments_data()
        self.create_operations_data()
        self.create_payment_data()
        self.create_realtime_data()
        self.create_reports_data()
        self.create_security_data()
        self.create_system_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 所有48个缺少数据的模型演示数据创建完成！'))

    def create_advanced_analytics_data(self):
        """创建高级分析模块演示数据"""
        self.stdout.write('📊 创建高级分析模块演示数据...')
        
        try:
            from apps.advanced_analytics.models import (
                UserValueScore, BehaviorPrediction, CohortAnalysis, FunnelAnalysis,
                ABTestExperiment, ABTestParticipant, BusinessIntelligenceReport,
                PredictiveModel, DataQualityMetric
            )
            
            # 1. 用户价值评分
            for user in self.users:
                if not UserValueScore.objects.filter(user=user).exists():
                    UserValueScore.objects.create(
                        user=user,
                        monetary_value=round(random.uniform(0, 1000), 2),
                        engagement_score=round(random.uniform(0, 100), 2),
                        retention_score=round(random.uniform(0, 100), 2),
                        referral_score=round(random.uniform(0, 100), 2),
                        total_value_score=round(random.uniform(0, 400), 2),
                        value_tier=random.choice(['bronze', 'silver', 'gold', 'platinum']),
                        last_calculated_at=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            # 2. 行为预测
            prediction_types = ['churn', 'purchase', 'engagement', 'retention', 'referral']
            for user in self.users[:10]:
                for pred_type in random.sample(prediction_types, 3):
                    if not BehaviorPrediction.objects.filter(user=user, prediction_type=pred_type).exists():
                        BehaviorPrediction.objects.create(
                            user=user,
                            prediction_type=pred_type,
                            prediction_score=round(random.uniform(0.1, 0.9), 3),
                            confidence_level=round(random.uniform(0.6, 0.95), 3),
                            prediction_details={
                                'model_version': 'v1.0',
                                'calculation_date': timezone.now().isoformat()
                            },
                            contributing_factors=['login_frequency', 'message_count', 'profile_views']
                        )
            
            # 3. 队列分析
            cohort_periods = ['weekly', 'monthly', 'quarterly']
            for i, period in enumerate(cohort_periods):
                if not CohortAnalysis.objects.filter(cohort_period=period).exists():
                    CohortAnalysis.objects.create(
                        cohort_name=f'{period.title()} Cohort {i+1}',
                        cohort_period=period,
                        cohort_date=timezone.now().date() - timedelta(days=30*i),
                        cohort_size=random.randint(50, 200),
                        retention_data={
                            'week_1': round(random.uniform(0.7, 0.9), 2),
                            'week_2': round(random.uniform(0.5, 0.7), 2),
                            'week_4': round(random.uniform(0.3, 0.5), 2),
                            'week_8': round(random.uniform(0.2, 0.4), 2)
                        },
                        calculated_at=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            # 4. 漏斗分析
            funnel_names = ['注册漏斗', '付费漏斗', '匹配漏斗']
            for funnel_name in funnel_names:
                if not FunnelAnalysis.objects.filter(funnel_name=funnel_name).exists():
                    FunnelAnalysis.objects.create(
                        funnel_name=funnel_name,
                        funnel_steps=['步骤1', '步骤2', '步骤3', '步骤4'],
                        conversion_data={
                            '步骤1': {'users': 1000, 'conversion_rate': 1.0},
                            '步骤2': {'users': 800, 'conversion_rate': 0.8},
                            '步骤3': {'users': 600, 'conversion_rate': 0.75},
                            '步骤4': {'users': 400, 'conversion_rate': 0.67}
                        },
                        analysis_period='monthly',
                        calculated_at=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            # 5. A/B测试实验
            experiments = [
                {'name': '新用户引导优化', 'description': '测试新的用户引导流程'},
                {'name': '匹配算法改进', 'description': '测试改进的匹配算法'}
            ]
            
            for exp_data in experiments:
                if not ABTestExperiment.objects.filter(name=exp_data['name']).exists():
                    experiment = ABTestExperiment.objects.create(
                        name=exp_data['name'],
                        description=exp_data['description'],
                        hypothesis='新版本将提高转化率',
                        status=random.choice(['draft', 'running', 'completed']),
                        primary_metric='conversion_rate',
                        secondary_metrics=['engagement_rate', 'retention_rate'],
                        target_sample_size=random.randint(1000, 5000),
                        current_sample_size=random.randint(500, 2500),
                        traffic_allocation=round(random.uniform(0.1, 0.5), 2),
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 60)),
                        expected_duration_days=random.randint(14, 90),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        confidence_interval=0.95,
                        results_summary={'control_conversion': 0.12, 'treatment_conversion': 0.15}
                    )
                    
                    # 为实验添加参与者
                    for user in random.sample(self.users, random.randint(5, 10)):
                        ABTestParticipant.objects.create(
                            experiment=experiment,
                            user=user,
                            variant=random.choice(['control', 'treatment']),
                            assigned_at=timezone.now() - timedelta(days=random.randint(1, 15)),
                            converted=random.choice([True, False]),
                            conversion_value=round(random.uniform(0, 100), 2) if random.choice([True, False]) else 0
                        )
            
            # 6. 商业智能报告
            report_types = ['用户增长报告', '收入分析报告', '用户行为报告']
            for report_type in report_types:
                if not BusinessIntelligenceReport.objects.filter(report_name=report_type).exists():
                    BusinessIntelligenceReport.objects.create(
                        report_name=report_type,
                        report_type='automated',
                        report_data={
                            'total_users': random.randint(1000, 5000),
                            'active_users': random.randint(500, 2000),
                            'revenue': round(random.uniform(10000, 50000), 2),
                            'conversion_rate': round(random.uniform(0.1, 0.3), 2)
                        },
                        generated_at=timezone.now() - timedelta(days=random.randint(1, 7)),
                        report_period='monthly',
                        created_by=random.choice(self.users)
                    )
            
            # 7. 预测模型
            model_types = ['churn_prediction', 'ltv_prediction', 'engagement_prediction']
            for model_type in model_types:
                if not PredictiveModel.objects.filter(model_type=model_type).exists():
                    PredictiveModel.objects.create(
                        name=f'{model_type.replace("_", " ").title()} Model',
                        model_type=model_type,
                        description=f'预测模型用于{model_type}',
                        algorithm='random_forest',
                        accuracy=round(random.uniform(0.7, 0.95), 3),
                        precision=round(random.uniform(0.7, 0.95), 3),
                        recall=round(random.uniform(0.7, 0.95), 3),
                        f1_score=round(random.uniform(0.7, 0.95), 3),
                        feature_importance={'feature1': 0.3, 'feature2': 0.25, 'feature3': 0.2},
                        training_data_size=random.randint(10000, 50000),
                        last_trained_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            # 8. 数据质量指标
            data_sources = ['user_data', 'transaction_data', 'behavior_data']
            metric_types = ['completeness', 'accuracy', 'consistency', 'timeliness']
            
            for data_source in data_sources:
                for metric_type in metric_types:
                    if not DataQualityMetric.objects.filter(data_source=data_source, metric_type=metric_type).exists():
                        DataQualityMetric.objects.create(
                            data_source=data_source,
                            metric_type=metric_type,
                            quality_score=round(random.uniform(0.7, 0.98), 3),
                            threshold=round(random.uniform(0.8, 0.95), 2),
                            issues_found=random.randint(0, 10),
                            total_records=random.randint(1000, 10000),
                            checked_at=timezone.now() - timedelta(hours=random.randint(1, 24))
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 高级分析模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级分析模块数据创建失败: {e}'))

    def create_ai_matching_data(self):
        """创建AI匹配模块演示数据"""
        self.stdout.write('🧠 创建AI匹配模块演示数据...')
        
        try:
            from apps.ai_matching.models import (
                UserBehaviorPattern, ModelTrainingData, RecommendationExplanation,
                ModelPerformanceMetric, MatchingModel
            )
            
            # 1. 用户行为模式
            pattern_types = ['early_bird', 'night_owl', 'weekend_active', 'workday_focused']
            for user in self.users:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        pattern_type=random.choice(pattern_types),
                        pattern_data={
                            'active_hours': random.sample(range(24), random.randint(6, 12)),
                            'response_time_avg': random.randint(5, 120),
                            'message_frequency': random.randint(10, 100)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        last_updated=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            # 2. 模型训练数据
            data_types = ['user_interactions', 'match_outcomes', 'message_data', 'profile_data']
            for data_type in data_types:
                if not ModelTrainingData.objects.filter(data_type=data_type).exists():
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        data_source=f'production_db_{data_type}',
                        data_size=random.randint(10000, 100000),
                        quality_score=round(random.uniform(0.7, 0.95), 3),
                        preprocessing_steps=['normalization', 'feature_extraction', 'validation']
                    )
            
            # 3. 推荐解释
            for i in range(20):
                user, recommended_user = random.sample(self.users, 2)
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        explanation_text=f'基于共同兴趣推荐',
                        explanation_factors={
                            'common_interests': ['音乐', '电影'],
                            'similarity_score': round(random.uniform(0.6, 0.9), 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3)
                    )
            
            # 4. 模型性能指标
            models = list(MatchingModel.objects.all())
            metrics = ['accuracy', 'precision', 'recall', 'f1_score']
            
            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=round(random.uniform(0.6, 0.95), 4),
                            evaluation_dataset='test_set_2025',
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30))
                        )
            
            self.stdout.write(self.style.SUCCESS('  ✅ AI匹配模块数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配模块数据创建失败: {e}'))

    def create_analytics_data(self):
        """创建分析模块演示数据"""
        self.stdout.write('📈 创建分析模块演示数据...')

        try:
            from apps.analytics.models import UserBehaviorLog, RevenueRecord

            # 1. 用户行为日志
            action_types = ['login', 'logout', 'profile_view', 'message_send', 'like', 'match']
            for i in range(50):
                UserBehaviorLog.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(action_types),
                    action_details={
                        'page': random.choice(['home', 'profile', 'chat', 'discover']),
                        'duration': random.randint(10, 300)
                    },
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    session_id=f'session_{uuid.uuid4().hex[:8]}',
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )

            # 2. 收入记录
            revenue_types = ['vip_subscription', 'coin_purchase', 'gift_purchase', 'premium_feature']
            for i in range(30):
                RevenueRecord.objects.create(
                    user=random.choice(self.users),
                    revenue_type=random.choice(revenue_types),
                    amount=Decimal(str(round(random.uniform(10, 500), 2))),
                    currency='CNY',
                    payment_method=random.choice(['wechat', 'alipay', 'card']),
                    transaction_id=f'txn_{uuid.uuid4().hex[:8]}',
                    recorded_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )

            self.stdout.write(self.style.SUCCESS('  ✅ 分析模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 分析模块数据创建失败: {e}'))

    def create_auth_data(self):
        """创建权限模块演示数据"""
        self.stdout.write('🔐 创建权限模块演示数据...')

        try:
            from django.contrib.auth.models import Group

            # 创建用户组
            groups = ['管理员', 'VIP用户', '普通用户', '红娘', '客服']
            for group_name in groups:
                if not Group.objects.filter(name=group_name).exists():
                    Group.objects.create(name=group_name)

            self.stdout.write(self.style.SUCCESS('  ✅ 权限模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 权限模块数据创建失败: {e}'))

    def create_chat_data(self):
        """创建聊天模块演示数据"""
        self.stdout.write('💬 创建聊天模块演示数据...')

        try:
            from apps.chat.models import MessageTemplate, ChatStatistics, OnlineStatus

            # 1. 消息模板
            templates = [
                {'title': '欢迎消息', 'category': 'welcome', 'content': '欢迎来到相亲交友平台！'},
                {'title': '匹配成功', 'category': 'match', 'content': '恭喜！您有新的匹配对象。'},
                {'title': '系统通知', 'category': 'system', 'content': '系统维护通知'},
                {'title': 'VIP到期', 'category': 'vip', 'content': '您的VIP即将到期'},
                {'title': '活动邀请', 'category': 'activity', 'content': '邀请您参加活动'}
            ]

            for template in templates:
                if not MessageTemplate.objects.filter(title=template['title']).exists():
                    MessageTemplate.objects.create(
                        title=template['title'],
                        category=template['category'],
                        content=template['content'],
                        usage_count=random.randint(10, 100),
                        success_rate=round(random.uniform(0.6, 0.9), 2),
                        is_active=True
                    )

            # 2. 聊天统计
            for user in self.users:
                if not ChatStatistics.objects.filter(user=user).exists():
                    ChatStatistics.objects.create(
                        user=user,
                        total_messages_sent=random.randint(50, 500),
                        total_messages_received=random.randint(40, 400),
                        active_sessions=random.randint(1, 10),
                        response_rate=round(random.uniform(0.5, 0.9), 2),
                        average_response_time=random.randint(30, 300)
                    )

            # 3. 在线状态
            for user in self.users:
                if not OnlineStatus.objects.filter(user=user).exists():
                    OnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'away', 'busy', 'offline']),
                        status_message=f'{user.nickname}的状态消息',
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440))
                    )

            self.stdout.write(self.style.SUCCESS('  ✅ 聊天模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 聊天模块数据创建失败: {e}'))

    def create_gifts_data(self):
        """创建礼物模块演示数据"""
        self.stdout.write('🎁 创建礼物模块演示数据...')

        try:
            from apps.gifts.models import UserGiftBox, GiftActivityParticipant, Gift, GiftActivity

            # 1. 用户礼物盒
            gifts = list(Gift.objects.all())
            if gifts:
                for user in self.users:
                    user_gifts = random.sample(gifts, random.randint(1, min(5, len(gifts))))
                    for gift in user_gifts:
                        if not UserGiftBox.objects.filter(user=user, gift=gift).exists():
                            UserGiftBox.objects.create(
                                user=user,
                                gift=gift,
                                quantity=random.randint(1, 10),
                                obtained_date=timezone.now() - timedelta(days=random.randint(1, 30))
                            )

            # 2. 活动参与者
            activities = list(GiftActivity.objects.all())
            if activities:
                for activity in activities:
                    participants = random.sample(self.users, random.randint(5, 10))
                    for user in participants:
                        if not GiftActivityParticipant.objects.filter(activity=activity, user=user).exists():
                            GiftActivityParticipant.objects.create(
                                activity=activity,
                                user=user,
                                participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                                points_earned=random.randint(10, 100),
                                rewards_received=random.choice([True, False])
                            )

            self.stdout.write(self.style.SUCCESS('  ✅ 礼物模块数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 礼物模块数据创建失败: {e}'))
