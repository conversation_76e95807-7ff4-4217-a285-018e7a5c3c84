#!/usr/bin/env python3
"""
为剩余缺少demo数据的后台导航菜单页面创建演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为剩余缺少demo数据的后台导航菜单页面创建演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为剩余缺少数据的导航页面创建演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建剩余模块数据
        self.create_content_audit_data()
        self.create_security_log_data()
        self.create_behavior_pattern_data()
        self.create_training_data()
        self.create_recommendation_explanation_data()
        self.create_performance_metric_data()
        self.create_voice_call_data()
        self.create_video_call_data()
        self.create_voice_message_data()
        self.create_realtime_notification_data()
        self.create_audit_log_data()
        self.create_data_encryption_data()
        self.create_rate_limit_data()
        self.create_user_value_score_data()
        self.create_behavior_prediction_data()
        self.create_cohort_analysis_data()
        self.create_funnel_analysis_data()
        self.create_ab_test_experiment_data()
        self.create_ab_test_participant_data()
        self.create_business_intelligence_report_data()
        self.create_predictive_model_data()
        self.create_data_quality_metric_data()
        self.create_platform_statistics_data()
        self.create_user_behavior_log_data()
        self.create_revenue_record_data()
        self.create_system_alert_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 剩余缺少数据的导航页面演示数据创建完成！'))

    def create_content_audit_data(self):
        """创建内容审核数据"""
        self.stdout.write('🔍 创建内容审核数据...')
        
        try:
            from apps.moderation.models import ContentAudit
            
            content_types = ['profile', 'moment', 'message', 'comment', 'photo']
            statuses = ['pending', 'approved', 'rejected', 'flagged']
            
            # 创建内容审核记录
            for i in range(40):
                ContentAudit.objects.create(
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    user=random.choice(self.users),
                    status=random.choice(statuses),
                    audit_reason=f'内容审核 #{i+1}',
                    audit_notes=f'审核备注 #{i+1}',
                    flagged_keywords=['敏感词1', '敏感词2'] if random.choice([True, False]) else [],
                    confidence_score=round(random.uniform(0.5, 1.0), 3),
                    auditor=random.choice(self.users),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                    reviewed_at=timezone.now() - timedelta(hours=random.randint(1, 72)) if random.choice([True, False]) else None
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 内容审核数据创建完成 - 总计: {ContentAudit.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 内容审核数据创建失败: {e}'))

    def create_security_log_data(self):
        """创建安全日志数据"""
        self.stdout.write('🛡️ 创建安全日志数据...')
        
        try:
            from apps.security.models import SecurityLog
            
            log_types = ['login_attempt', 'password_change', 'profile_update', 'suspicious_activity', 'api_access']
            
            # 创建安全日志记录
            for i in range(60):
                SecurityLog.objects.create(
                    user=random.choice(self.users) if random.choice([True, False]) else None,
                    log_type=random.choice(log_types),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ]),
                    request_path=f'/api/{random.choice(["users", "auth", "profile", "chat"])}/',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    response_status=random.choice([200, 201, 400, 401, 403, 404, 500]),
                    details={'action': f'安全日志详情 #{i+1}'},
                    risk_level=random.choice(['low', 'medium', 'high', 'critical']),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全日志数据创建完成 - 总计: {SecurityLog.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全日志数据创建失败: {e}'))

    def create_behavior_pattern_data(self):
        """创建行为模式数据"""
        self.stdout.write('📈 创建行为模式数据...')
        
        try:
            from apps.ai_matching.models import UserBehaviorPattern
            
            pattern_types = ['early_bird', 'night_owl', 'weekend_active', 'workday_focused', 'social_butterfly', 'selective_matcher']
            
            # 为用户创建行为模式数据
            for user in self.users:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        pattern_type=random.choice(pattern_types),
                        pattern_data={
                            'active_hours': random.sample(range(24), random.randint(6, 12)),
                            'response_time_avg': random.randint(5, 120),
                            'message_frequency': random.randint(10, 100),
                            'like_rate': round(random.uniform(0.1, 0.8), 2),
                            'match_rate': round(random.uniform(0.05, 0.3), 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        last_updated=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 行为模式数据创建完成 - 总计: {UserBehaviorPattern.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 行为模式数据创建失败: {e}'))

    def create_training_data(self):
        """创建训练数据"""
        self.stdout.write('🎯 创建训练数据...')
        
        try:
            from apps.ai_matching.models import ModelTrainingData
            
            data_types = ['user_interactions', 'match_outcomes', 'message_data', 'profile_data', 'behavior_data']
            
            # 创建训练数据记录
            for i, data_type in enumerate(data_types):
                if not ModelTrainingData.objects.filter(data_type=data_type).exists():
                    ModelTrainingData.objects.create(
                        data_type=data_type,
                        data_source=f'production_db_{i+1}',
                        data_size=random.randint(10000, 100000),
                        quality_score=round(random.uniform(0.7, 0.95), 3),
                        preprocessing_steps=['normalization', 'feature_extraction', 'validation', 'cleaning'],
                        feature_count=random.randint(50, 200),
                        training_accuracy=round(random.uniform(0.75, 0.95), 3),
                        validation_accuracy=round(random.uniform(0.70, 0.90), 3),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 训练数据创建完成 - 总计: {ModelTrainingData.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 训练数据创建失败: {e}'))

    def create_recommendation_explanation_data(self):
        """创建推荐解释数据"""
        self.stdout.write('💡 创建推荐解释数据...')
        
        try:
            from apps.ai_matching.models import RecommendationExplanation
            
            # 为用户对创建推荐解释
            for i in range(30):
                user, recommended_user = random.sample(self.users, 2)
                
                if not RecommendationExplanation.objects.filter(user=user, recommended_user=recommended_user).exists():
                    RecommendationExplanation.objects.create(
                        user=user,
                        recommended_user=recommended_user,
                        explanation_text=f'基于{random.choice(["共同兴趣", "相似背景", "互补性格", "地理位置"])}推荐',
                        explanation_factors={
                            'common_interests': random.sample(['音乐', '电影', '旅行', '美食', '运动'], 2),
                            'similarity_score': round(random.uniform(0.6, 0.9), 2),
                            'compatibility_reasons': random.sample(['年龄匹配', '教育背景', '职业相似', '价值观'], 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        algorithm_version='v2.1.0',
                        created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 推荐解释数据创建完成 - 总计: {RecommendationExplanation.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 推荐解释数据创建失败: {e}'))

    def create_performance_metric_data(self):
        """创建性能指标数据"""
        self.stdout.write('📊 创建性能指标数据...')
        
        try:
            from apps.ai_matching.models import ModelPerformanceMetric, MatchingModel
            
            # 获取匹配模型
            models = list(MatchingModel.objects.all())
            if not models:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到匹配模型，跳过性能指标数据创建'))
                return
            
            metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc', 'mse', 'mae']
            
            # 为每个模型创建性能指标
            for model in models:
                for metric in metrics:
                    if not ModelPerformanceMetric.objects.filter(model=model, metric_name=metric).exists():
                        ModelPerformanceMetric.objects.create(
                            model=model,
                            metric_name=metric,
                            metric_value=round(random.uniform(0.6, 0.95), 4),
                            evaluation_dataset='test_set_2025',
                            evaluation_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                            baseline_value=round(random.uniform(0.5, 0.8), 4),
                            improvement_percentage=round(random.uniform(5, 25), 2)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 性能指标数据创建完成 - 总计: {ModelPerformanceMetric.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 性能指标数据创建失败: {e}'))

    def create_voice_call_data(self):
        """创建语音通话数据"""
        self.stdout.write('📞 创建语音通话数据...')

        try:
            from apps.realtime.models import VoiceCall

            # 检查现有数据量
            existing_count = VoiceCall.objects.count()
            target_count = 25

            if existing_count < target_count:
                for i in range(target_count - existing_count):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(30, 1800)  # 30秒到30分钟

                    VoiceCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'voice_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy', 'failed']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        network_type=random.choice(['wifi', '4g', '5g', '3g']),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 语音通话数据创建完成 - 总计: {VoiceCall.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 语音通话数据创建失败: {e}'))

    def create_video_call_data(self):
        """创建视频通话数据"""
        self.stdout.write('📹 创建视频通话数据...')

        try:
            from apps.realtime.models import VideoCall

            # 检查现有数据量
            existing_count = VideoCall.objects.count()
            target_count = 20

            if existing_count < target_count:
                for i in range(target_count - existing_count):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(60, 3600)  # 1分钟到1小时

                    VideoCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'video_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy', 'failed']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        video_quality=random.choice(['720p', '1080p', '480p', '360p']),
                        network_type=random.choice(['wifi', '4g', '5g', '3g']),
                        started_at=timezone.now() - timedelta(hours=random.randint(1, 168)),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 视频通话数据创建完成 - 总计: {VideoCall.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 视频通话数据创建失败: {e}'))

    def create_voice_message_data(self):
        """创建语音消息数据"""
        self.stdout.write('🎤 创建语音消息数据...')

        try:
            from apps.realtime.models import VoiceMessage

            # 检查现有数据量
            existing_count = VoiceMessage.objects.count()
            target_count = 40

            if existing_count < target_count:
                for i in range(target_count - existing_count):
                    sender, receiver = random.sample(self.users, 2)

                    VoiceMessage.objects.create(
                        sender=sender,
                        receiver=receiver,
                        message_id=f'voice_msg_{uuid.uuid4().hex[:8]}',
                        duration=random.randint(5, 60),  # 5秒到1分钟
                        file_size=random.randint(50000, 500000),  # 50KB到500KB
                        file_url=f'/media/voice_messages/{uuid.uuid4().hex[:8]}.mp3',
                        quality=random.choice(['high', 'medium', 'low']),
                        is_played=random.choice([True, False]),
                        played_at=timezone.now() - timedelta(hours=random.randint(1, 48)) if random.choice([True, False]) else None,
                        created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 语音消息数据创建完成 - 总计: {VoiceMessage.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 语音消息数据创建失败: {e}'))

    def create_realtime_notification_data(self):
        """创建实时通知数据"""
        self.stdout.write('🔔 创建实时通知数据...')

        try:
            from apps.realtime.models import RealtimeNotification

            # 检查现有数据量
            existing_count = RealtimeNotification.objects.count()
            target_count = 50

            if existing_count < target_count:
                notification_types = ['new_message', 'new_match', 'profile_view', 'gift_received', 'system_update', 'activity_reminder']

                for i in range(target_count - existing_count):
                    notification_type = random.choice(notification_types)

                    RealtimeNotification.objects.create(
                        user=random.choice(self.users),
                        notification_type=notification_type,
                        title=f'{notification_type.replace("_", " ").title()} #{i+1}',
                        content=f'这是一条{notification_type}类型的实时通知内容',
                        data={'extra_info': f'额外数据 #{i+1}'},
                        is_read=random.choice([True, False]),
                        priority=random.choice(['low', 'medium', 'high', 'urgent']),
                        read_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                        created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通知数据创建完成 - 总计: {RealtimeNotification.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通知数据创建失败: {e}'))
