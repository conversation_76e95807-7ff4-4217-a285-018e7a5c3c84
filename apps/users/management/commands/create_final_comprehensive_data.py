#!/usr/bin/env python3
"""
为缺少演示数据的模型创建最终的演示数据 - 基于实际字段
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为缺少演示数据的模型创建最终的演示数据 - 基于实际字段'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为缺少数据的模型创建最终演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_safe_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最终演示数据创建完成！'))

    def create_safe_data(self):
        """创建安全的演示数据 - 只使用确实存在的字段"""
        self.stdout.write('📊 创建安全的演示数据...')
        
        # 1. 创建更多用户行为数据
        try:
            from apps.users.models import UserAction
            
            if UserAction.objects.count() < 100:
                action_types = ['like', 'pass', 'super_like', 'visit', 'block', 'report']
                for i in range(50):
                    user, target_user = random.sample(self.users, 2)
                    action_type = random.choice(action_types)
                    
                    if not UserAction.objects.filter(user=user, target_user=target_user, action_type=action_type).exists():
                        UserAction.objects.create(
                            user=user,
                            target_user=target_user,
                            action_type=action_type,
                            message=f'演示{action_type}行为',
                            created_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户行为数据: {UserAction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户行为数据创建失败: {e}'))

        # 2. 创建更多用户偏好数据
        try:
            from apps.users.models import UserPreference
            
            for user in self.users:
                if not UserPreference.objects.filter(user=user).exists():
                    UserPreference.objects.create(
                        user=user,
                        min_age=random.randint(18, 25),
                        max_age=random.randint(30, 45),
                        preferred_gender=random.choice([1, 2]),
                        preferred_location=random.choice(['北京', '上海', '广州', '深圳', '杭州']),
                        distance_range=random.randint(10, 100),
                        preferred_education=random.choice([1, 2, 3, 4, 5])
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户偏好数据: {UserPreference.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户偏好数据创建失败: {e}'))

        # 3. 创建更多用户验证数据
        try:
            from apps.users.models import UserVerification
            
            verification_types = ['id_card', 'phone', 'education', 'profession', 'photo']
            statuses = ['pending', 'approved', 'rejected']
            
            for user in self.users[:12]:
                for verification_type in random.sample(verification_types, 2):
                    try:
                        if not UserVerification.objects.filter(user=user, verification_type=verification_type).exists():
                            UserVerification.objects.create(
                                user=user,
                                verification_type=verification_type,
                                status=random.choice(statuses)
                            )
                    except Exception:
                        continue
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 用户验证数据: {UserVerification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 用户验证数据创建失败: {e}'))

        # 4. 创建更多黑名单数据
        try:
            from apps.users.models import UserBlacklist
            
            for i in range(20):
                user, blocked_user = random.sample(self.users, 2)
                
                if not UserBlacklist.objects.filter(user=user, blocked_user=blocked_user).exists():
                    UserBlacklist.objects.create(
                        user=user,
                        blocked_user=blocked_user,
                        reason=random.choice(['harassment', 'spam', 'inappropriate', 'other']),
                        created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 黑名单数据: {UserBlacklist.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 黑名单数据创建失败: {e}'))

        # 5. 创建更多红娘客户数据
        try:
            from apps.users.models import MatchmakerProfile, MatchmakerClient
            
            matchmakers = list(MatchmakerProfile.objects.all())
            if matchmakers:
                normal_users = [user for user in self.users if user.user_type == 'normal'][:10]
                
                for matchmaker in matchmakers:
                    for client_user in random.sample(normal_users, min(4, len(normal_users))):
                        if not MatchmakerClient.objects.filter(matchmaker=matchmaker, user=client_user).exists():
                            MatchmakerClient.objects.create(
                                matchmaker=matchmaker,
                                user=client_user,
                                service_package=random.choice(['basic', 'standard', 'premium']),
                                service_fee=Decimal(str(random.randint(1000, 5000))),
                                status=random.choice(['new', 'consulting', 'matching', 'completed']),
                                start_date=timezone.now().date() - timedelta(days=random.randint(1, 60)),
                                end_date=timezone.now().date() + timedelta(days=random.randint(30, 180))
                            )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 红娘客户数据: {MatchmakerClient.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 红娘客户数据创建失败: {e}'))

        # 6. 创建更多服务记录数据
        try:
            from apps.users.models import ServiceRecord, MatchmakerClient
            
            clients = list(MatchmakerClient.objects.all())
            if clients:
                service_types = ['consultation', 'profile_optimization', 'matching', 'date_arrangement', 'follow_up']
                
                for client in clients:
                    for i in range(random.randint(3, 6)):
                        ServiceRecord.objects.create(
                            client=client,
                            service_type=random.choice(service_types),
                            title=f'服务记录 #{i+1}',
                            content=f'详细的服务内容描述 #{i+1}',
                            service_date=timezone.now() - timedelta(days=random.randint(0, 30)),
                            duration=random.randint(30, 180),
                            result=f'服务结果 #{i+1}'
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 服务记录数据: {ServiceRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 服务记录数据创建失败: {e}'))

        # 7. 创建更多AI匹配数据
        try:
            from apps.ai_matching.models import UserProfile, UserSimilarity, MatchPrediction, MatchingModel
            
            # 确保用户画像数据充足
            for user in self.users:
                if not UserProfile.objects.filter(user=user).exists():
                    UserProfile.objects.create(
                        user=user,
                        age_group=random.choice(['18-25', '26-30', '31-35', '36-40', '40+']),
                        education_level=random.choice(['高中', '大专', '本科', '硕士', '博士']),
                        income_level=random.choice(['3k以下', '3k-5k', '5k-8k', '8k-12k', '12k-20k', '20k以上']),
                        occupation_category=random.choice(['IT互联网', '金融', '教育', '医疗', '销售', '设计', '其他']),
                        location_tier=random.choice(['一线城市', '二线城市', '三线城市', '其他']),
                        activity_score=round(random.uniform(0.1, 1.0), 2),
                        social_score=round(random.uniform(0.1, 1.0), 2),
                        response_rate=round(random.uniform(0.3, 0.9), 2),
                        online_frequency=round(random.uniform(0.1, 1.0), 2),
                        preferred_age_min=random.randint(18, 30),
                        preferred_age_max=random.randint(25, 45),
                        interests=random.sample(['音乐', '电影', '旅行', '美食', '运动', '阅读'], random.randint(2, 4)),
                        personality_traits=random.sample(['开朗', '内向', '幽默', '温柔', '理性', '感性'], random.randint(2, 3)),
                        lifestyle_tags=random.sample(['健身', '宅家', '社交', '旅行', '美食'], random.randint(1, 3))
                    )
            
            # 创建更多用户相似度数据
            if UserSimilarity.objects.count() < 50:
                for i in range(30):
                    user1, user2 = random.sample(self.users, 2)
                    if not UserSimilarity.objects.filter(user1=user1, user2=user2).exists():
                        UserSimilarity.objects.create(
                            user1=user1,
                            user2=user2,
                            overall_similarity=round(random.uniform(0.1, 0.95), 3),
                            demographic_similarity=round(random.uniform(0.0, 1.0), 3),
                            interest_similarity=round(random.uniform(0.0, 1.0), 3),
                            behavior_similarity=round(random.uniform(0.0, 1.0), 3),
                            preference_similarity=round(random.uniform(0.0, 1.0), 3)
                        )
            
            # 创建更多匹配预测数据
            models = list(MatchingModel.objects.all())
            if models and MatchPrediction.objects.count() < 60:
                for i in range(30):
                    user, candidate = random.sample(self.users, 2)
                    if not MatchPrediction.objects.filter(user=user, candidate=candidate).exists():
                        MatchPrediction.objects.create(
                            user=user,
                            candidate=candidate,
                            match_score=round(random.uniform(0.1, 0.95), 3),
                            compatibility_score=round(random.uniform(0.1, 0.9), 3),
                            predicted_by_model=random.choice(models),
                            confidence=round(random.uniform(0.6, 0.95), 3)
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ AI匹配数据更新完成'))
            self.stdout.write(self.style.SUCCESS(f'    • 用户画像: {UserProfile.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 用户相似度: {UserSimilarity.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 匹配预测: {MatchPrediction.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI匹配数据创建失败: {e}'))

        # 8. 创建更多实时通讯数据
        try:
            from apps.realtime.models import WebSocketConnection, UserOnlineStatus, VoiceCall, VideoCall
            
            # 创建更多WebSocket连接记录
            if WebSocketConnection.objects.count() < 80:
                for i in range(30):
                    user = random.choice(self.users)
                    WebSocketConnection.objects.create(
                        user=user,
                        connection_type=random.choice(['chat', 'video_call', 'voice_call', 'notification']),
                        channel_name=f'user_{user.id}_{random.randint(1000, 9999)}',
                        client_ip=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                        status=random.choice(['connected', 'disconnected', 'reconnecting']),
                        messages_sent=random.randint(0, 100),
                        messages_received=random.randint(0, 100),
                        user_agent=random.choice([
                            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                            'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                            'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                        ])
                    )
            
            # 确保每个用户都有在线状态记录
            for user in self.users:
                if not UserOnlineStatus.objects.filter(user=user).exists():
                    UserOnlineStatus.objects.create(
                        user=user,
                        status=random.choice(['online', 'offline', 'away', 'busy']),
                        last_seen=timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        device_type=random.choice(['mobile', 'desktop', 'tablet'])
                    )
            
            # 创建更多语音通话记录
            if VoiceCall.objects.count() < 40:
                for i in range(20):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(30, 1800)
                    VoiceCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'voice_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            # 创建更多视频通话记录
            if VideoCall.objects.count() < 30:
                for i in range(15):
                    caller, callee = random.sample(self.users, 2)
                    duration = random.randint(60, 3600)
                    VideoCall.objects.create(
                        caller=caller,
                        callee=callee,
                        call_id=f'video_{uuid.uuid4().hex[:8]}',
                        status=random.choice(['completed', 'missed', 'rejected', 'busy']),
                        duration=duration,
                        call_quality=round(random.uniform(3.0, 5.0), 1),
                        video_quality=random.choice(['720p', '1080p', '480p']),
                        ended_at=timezone.now() - timedelta(hours=random.randint(1, 168)) + timedelta(seconds=duration)
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通讯数据更新完成'))
            self.stdout.write(self.style.SUCCESS(f'    • WebSocket连接: {WebSocketConnection.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 在线状态: {UserOnlineStatus.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 语音通话: {VoiceCall.objects.count()}条'))
            self.stdout.write(self.style.SUCCESS(f'    • 视频通话: {VideoCall.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))
