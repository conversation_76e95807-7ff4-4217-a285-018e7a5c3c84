#!/usr/bin/env python3
"""
为高级功能模块创建演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import random
import json

User = get_user_model()

class Command(BaseCommand):
    help = '为高级功能模块创建演示数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--module',
            type=str,
            help='指定要创建数据的模块 (ai_matching, realtime, security, i18n, analytics)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='为所有高级模块创建演示数据',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始创建高级功能模块演示数据...'))
        
        if options['all']:
            self.create_ai_matching_data()
            self.create_realtime_data()
            self.create_security_data()
            self.create_i18n_data()
            self.create_analytics_data()
        elif options['module']:
            module = options['module']
            if module == 'ai_matching':
                self.create_ai_matching_data()
            elif module == 'realtime':
                self.create_realtime_data()
            elif module == 'security':
                self.create_security_data()
            elif module == 'i18n':
                self.create_i18n_data()
            elif module == 'analytics':
                self.create_analytics_data()
            else:
                self.stdout.write(self.style.ERROR(f'未知模块: {module}'))
                return
        else:
            self.stdout.write(self.style.WARNING('请指定 --module 或 --all 参数'))
            return
            
        self.stdout.write(self.style.SUCCESS('✅ 高级功能模块演示数据创建完成！'))

    def create_ai_matching_data(self):
        """创建AI智能匹配模块演示数据"""
        self.stdout.write('🧠 创建AI智能匹配模块演示数据...')
        
        try:
            from apps.ai_matching.models import (
                UserProfile, MatchingModel, UserSimilarity, 
                MatchPrediction, UserBehaviorPattern, ModelTrainingData,
                RecommendationExplanation, ModelPerformanceMetric
            )
            
            # 获取用户
            users = list(User.objects.all()[:10])
            if not users:
                self.stdout.write(self.style.WARNING('没有找到用户，跳过AI匹配数据创建'))
                return
            
            # 创建用户画像
            for user in users:
                if not UserProfile.objects.filter(user=user).exists():
                    UserProfile.objects.create(
                        user=user,
                        age_group=random.choice(['18-25', '26-30', '31-35', '36-40', '40+']),
                        education_level=random.choice(['高中', '大专', '本科', '硕士', '博士']),
                        income_level=random.choice(['3k以下', '3k-5k', '5k-8k', '8k-12k', '12k-20k', '20k以上']),
                        occupation_category=random.choice(['IT互联网', '金融', '教育', '医疗', '销售', '设计', '其他']),
                        location_tier=random.choice(['一线城市', '二线城市', '三线城市', '其他']),
                        activity_score=round(random.uniform(0.1, 1.0), 2),
                        social_score=round(random.uniform(0.1, 1.0), 2),
                        response_rate=round(random.uniform(0.3, 0.9), 2),
                        online_frequency=round(random.uniform(0.1, 1.0), 2),
                        preferred_age_min=random.randint(18, 30),
                        preferred_age_max=random.randint(25, 45),
                        preferred_education=['本科', '硕士'],
                        preferred_income=['8k-12k', '12k-20k', '20k以上'],
                        preferred_location=['一线城市', '二线城市'],
                        interests=random.sample(['音乐', '电影', '旅行', '美食', '运动', '阅读', '摄影', '游戏'], random.randint(2, 5)),
                        personality_traits=random.sample(['开朗', '内向', '幽默', '温柔', '理性', '感性'], random.randint(2, 4)),
                        lifestyle_tags=random.sample(['健身', '宅家', '社交', '旅行', '美食', '文艺'], random.randint(1, 3)),
                        feature_vector=[round(random.uniform(0, 1), 3) for _ in range(10)],
                        embedding_vector=[round(random.uniform(-1, 1), 3) for _ in range(50)]
                    )
            
            # 创建匹配模型
            models_data = [
                {
                    'name': '协同过滤推荐模型',
                    'model_type': 'collaborative_filtering',
                    'description': '基于用户行为相似性的协同过滤算法，通过分析用户的历史行为模式来预测匹配度',
                    'version': '1.2.0',
                    'accuracy': 0.78,
                    'precision': 0.82,
                    'recall': 0.75,
                    'f1_score': 0.78,
                    'is_active': True
                },
                {
                    'name': '深度学习匹配模型',
                    'model_type': 'deep_learning',
                    'description': '基于神经网络的深度学习匹配算法，能够学习复杂的用户特征关系',
                    'version': '2.1.0',
                    'accuracy': 0.85,
                    'precision': 0.87,
                    'recall': 0.83,
                    'f1_score': 0.85,
                    'is_active': True
                },
                {
                    'name': '混合推荐模型',
                    'model_type': 'hybrid',
                    'description': '结合多种算法的混合推荐模型，综合考虑内容和协同过滤',
                    'version': '1.0.0',
                    'accuracy': 0.82,
                    'precision': 0.84,
                    'recall': 0.80,
                    'f1_score': 0.82,
                    'is_active': False
                },
                {
                    'name': '基于内容的推荐模型',
                    'model_type': 'content_based',
                    'description': '基于用户资料和偏好的内容推荐算法',
                    'version': '1.5.0',
                    'accuracy': 0.75,
                    'precision': 0.79,
                    'recall': 0.72,
                    'f1_score': 0.75,
                    'is_active': False
                }
            ]
            
            for model_data in models_data:
                if not MatchingModel.objects.filter(name=model_data['name']).exists():
                    MatchingModel.objects.create(
                        **model_data,
                        parameters={
                            'learning_rate': 0.001,
                            'batch_size': 32,
                            'epochs': 100,
                            'hidden_layers': [128, 64, 32],
                            'dropout_rate': 0.2
                        },
                        weights={
                            'personality': 0.3,
                            'interests': 0.25,
                            'lifestyle': 0.2,
                            'communication': 0.15,
                            'values': 0.1
                        },
                        training_data_size=random.randint(5000, 50000),
                        last_trained_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
            
            # 创建用户相似度数据
            for i in range(25):
                user1, user2 = random.sample(users, 2)
                if not UserSimilarity.objects.filter(user1=user1, user2=user2).exists():
                    UserSimilarity.objects.create(
                        user1=user1,
                        user2=user2,
                        similarity_score=round(random.uniform(0.1, 0.95), 3),
                        feature_similarities={
                            'personality': round(random.uniform(0.0, 1.0), 3),
                            'interests': round(random.uniform(0.0, 1.0), 3),
                            'lifestyle': round(random.uniform(0.0, 1.0), 3),
                            'communication': round(random.uniform(0.0, 1.0), 3),
                            'values': round(random.uniform(0.0, 1.0), 3)
                        },
                        calculated_at=timezone.now() - timedelta(hours=random.randint(1, 72))
                    )
            
            # 创建匹配预测数据
            models = list(MatchingModel.objects.all())
            for i in range(40):
                user, candidate = random.sample(users, 2)
                if not MatchPrediction.objects.filter(user=user, candidate=candidate).exists():
                    MatchPrediction.objects.create(
                        user=user,
                        candidate=candidate,
                        match_score=round(random.uniform(0.1, 0.95), 3),
                        compatibility_score=round(random.uniform(0.1, 0.9), 3),
                        attraction_score=round(random.uniform(0.1, 0.9), 3),
                        conversation_potential=round(random.uniform(0.1, 0.9), 3),
                        score_breakdown={
                            'personality_match': round(random.uniform(0.0, 1.0), 3),
                            'interest_overlap': round(random.uniform(0.0, 1.0), 3),
                            'lifestyle_compatibility': round(random.uniform(0.0, 1.0), 3),
                            'communication_style': round(random.uniform(0.0, 1.0), 3),
                            'value_alignment': round(random.uniform(0.0, 1.0), 3)
                        },
                        predicted_by_model=random.choice(models) if models else None,
                        confidence=round(random.uniform(0.6, 0.95), 3),
                        actual_result=random.choice(['like', 'pass', 'match', 'chat', None])
                    )
            
            # 创建用户行为模式数据
            behavior_patterns = [
                'early_bird', 'night_owl', 'weekend_active', 'workday_focused',
                'quick_responder', 'thoughtful_responder', 'photo_focused', 'text_focused'
            ]
            
            for user in users:
                if not UserBehaviorPattern.objects.filter(user=user).exists():
                    UserBehaviorPattern.objects.create(
                        user=user,
                        pattern_type=random.choice(behavior_patterns),
                        pattern_data={
                            'active_hours': random.sample(range(24), random.randint(6, 12)),
                            'response_time_avg': random.randint(5, 120),  # 分钟
                            'message_frequency': random.randint(10, 100),  # 每天
                            'photo_view_rate': round(random.uniform(0.3, 0.9), 2),
                            'profile_completion': round(random.uniform(0.6, 1.0), 2)
                        },
                        confidence_score=round(random.uniform(0.7, 0.95), 3),
                        last_updated=timezone.now() - timedelta(days=random.randint(1, 7))
                    )
            
            self.stdout.write(self.style.SUCCESS('  ✅ AI智能匹配数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ AI智能匹配数据创建失败: {e}'))

    def create_realtime_data(self):
        """创建实时通讯模块演示数据"""
        self.stdout.write('⚡ 创建实时通讯模块演示数据...')
        
        try:
            from apps.realtime.models import (
                WebSocketConnection, VoiceCall, VideoCall, 
                VoiceMessage, RealtimeNotification, UserOnlineStatus
            )
            
            users = list(User.objects.all()[:10])
            if not users:
                return
            
            # 创建WebSocket连接记录
            for i in range(20):
                user = random.choice(users)
                WebSocketConnection.objects.create(
                    user=user,
                    connection_type=random.choice(['chat', 'video_call', 'voice_call', 'notification']),
                    channel_name=f'user_{user.id}_{random.randint(1000, 9999)}',
                    client_ip=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    status=random.choice(['connected', 'disconnected', 'reconnecting']),
                    messages_sent=random.randint(0, 100),
                    messages_received=random.randint(0, 100),
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ])
                )
            
            self.stdout.write(self.style.SUCCESS('  ✅ 实时通讯数据创建完成'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全防护模块演示数据"""
        self.stdout.write('🛡️ 创建安全防护模块演示数据...')

        try:
            from apps.security.models import (
                SecurityEvent, IPBlacklist, UserSecurityProfile,
                AuditLog, DataEncryption, RateLimitRule
            )

            users = list(User.objects.all()[:10])
            if not users:
                return

            # 创建安全事件
            event_types = [
                'login_attempt', 'failed_login', 'suspicious_activity',
                'rate_limit_exceeded', 'sql_injection_attempt', 'xss_attempt',
                'csrf_attack', 'brute_force_attack', 'unauthorized_access'
            ]

            for i in range(30):
                SecurityEvent.objects.create(
                    event_type=random.choice(event_types),
                    severity=random.choice(['low', 'medium', 'high', 'critical']),
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent=random.choice([
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X)',
                        'Mozilla/5.0 (Android 11; Mobile; rv:89.0)',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                    ]),
                    referer='https://xiangqin.weixinjishu.top/',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    request_path=f'/api/{random.choice(["users", "matches", "chat", "profile"])}/',
                    request_data={'param': f'value_{i}'},
                    user=random.choice(users) if random.choice([True, False]) else None,
                    is_blocked=random.choice([True, False]),
                    response_action=random.choice(['allow', 'block', 'warn', 'log']),
                    risk_score=random.randint(0, 100)
                )

            # 创建IP黑名单
            for i in range(15):
                IPBlacklist.objects.create(
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    reason=random.choice([
                        '恶意攻击', '垃圾信息', '暴力破解', '异常访问', '违规行为'
                    ]),
                    blocked_until=timezone.now() + timedelta(days=random.randint(1, 30)),
                    is_permanent=random.choice([True, False]),
                    created_by=random.choice(users),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 72))
                )

            # 创建用户安全档案
            for user in users:
                if not UserSecurityProfile.objects.filter(user=user).exists():
                    UserSecurityProfile.objects.create(
                        user=user,
                        risk_score=random.randint(0, 100),
                        security_level=random.choice(['low', 'medium', 'high']),
                        last_security_check=timezone.now() - timedelta(days=random.randint(1, 30)),
                        failed_login_attempts=random.randint(0, 5),
                        last_failed_login=timezone.now() - timedelta(hours=random.randint(1, 168)) if random.choice([True, False]) else None,
                        security_questions_set=random.choice([True, False]),
                        two_factor_enabled=random.choice([True, False]),
                        suspicious_activity_count=random.randint(0, 3)
                    )

            self.stdout.write(self.style.SUCCESS('  ✅ 安全防护数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全防护数据创建失败: {e}'))

    def create_i18n_data(self):
        """创建国际化模块演示数据"""
        self.stdout.write('🌍 创建国际化模块演示数据...')

        try:
            from apps.i18n.models import (
                Language, Translation, UserLanguagePreference,
                TranslationStatistics, LocalizationConfig, AutoTranslation
            )

            users = list(User.objects.all()[:10])

            # 创建语言
            languages_data = [
                {'code': 'zh-CN', 'name': '简体中文', 'native_name': '简体中文', 'is_active': True},
                {'code': 'zh-TW', 'name': '繁体中文', 'native_name': '繁體中文', 'is_active': True},
                {'code': 'en-US', 'name': 'English', 'native_name': 'English', 'is_active': True},
                {'code': 'ja-JP', 'name': 'Japanese', 'native_name': '日本語', 'is_active': True},
                {'code': 'ko-KR', 'name': 'Korean', 'native_name': '한국어', 'is_active': True},
                {'code': 'es-ES', 'name': 'Spanish', 'native_name': 'Español', 'is_active': False},
                {'code': 'fr-FR', 'name': 'French', 'native_name': 'Français', 'is_active': False},
            ]

            for lang_data in languages_data:
                if not Language.objects.filter(code=lang_data['code']).exists():
                    Language.objects.create(**lang_data)

            # 创建翻译
            languages = list(Language.objects.all())
            translation_keys = [
                'app.title', 'user.profile', 'chat.message', 'match.found',
                'payment.success', 'gift.sent', 'moment.liked', 'settings.language'
            ]

            translations_data = {
                'zh-CN': {
                    'app.title': '相亲交友',
                    'user.profile': '用户资料',
                    'chat.message': '聊天消息',
                    'match.found': '找到匹配',
                    'payment.success': '支付成功',
                    'gift.sent': '礼物已发送',
                    'moment.liked': '动态点赞',
                    'settings.language': '语言设置'
                },
                'en-US': {
                    'app.title': 'Dating App',
                    'user.profile': 'User Profile',
                    'chat.message': 'Chat Message',
                    'match.found': 'Match Found',
                    'payment.success': 'Payment Success',
                    'gift.sent': 'Gift Sent',
                    'moment.liked': 'Moment Liked',
                    'settings.language': 'Language Settings'
                },
                'ja-JP': {
                    'app.title': '出会いアプリ',
                    'user.profile': 'ユーザープロフィール',
                    'chat.message': 'チャットメッセージ',
                    'match.found': 'マッチが見つかりました',
                    'payment.success': '支払い成功',
                    'gift.sent': 'ギフトを送信しました',
                    'moment.liked': 'モーメントにいいね',
                    'settings.language': '言語設定'
                }
            }

            for lang_code, translations in translations_data.items():
                try:
                    language = Language.objects.get(code=lang_code)
                    for key, value in translations.items():
                        if not Translation.objects.filter(key=key, language=language).exists():
                            Translation.objects.create(
                                key=key,
                                language=language,
                                value=value,
                                is_approved=True,
                                created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                            )
                except Language.DoesNotExist:
                    continue

            # 创建用户语言偏好
            for user in users:
                if not UserLanguagePreference.objects.filter(user=user).exists():
                    UserLanguagePreference.objects.create(
                        user=user,
                        primary_language=random.choice(languages),
                        secondary_language=random.choice(languages),
                        auto_translate=random.choice([True, False]),
                        preferred_timezone=random.choice([
                            'Asia/Shanghai', 'Asia/Tokyo', 'America/New_York',
                            'Europe/London', 'Australia/Sydney'
                        ])
                    )

            self.stdout.write(self.style.SUCCESS('  ✅ 国际化数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据创建失败: {e}'))

    def create_analytics_data(self):
        """创建高级数据分析模块演示数据"""
        self.stdout.write('📊 创建高级数据分析模块演示数据...')

        try:
            from apps.advanced_analytics.models import (
                UserSegment, BehaviorPrediction, CohortAnalysis,
                ABTest, FunnelAnalysis, BusinessIntelligenceReport
            )

            users = list(User.objects.all()[:10])

            # 创建用户分群（已有1个，再创建几个）
            segments_data = [
                {
                    'name': '高活跃用户',
                    'description': '每日登录且互动频繁的用户群体',
                    'criteria': {
                        'daily_login': True,
                        'messages_per_day': {'min': 10},
                        'profile_views': {'min': 20}
                    }
                },
                {
                    'name': 'VIP潜在用户',
                    'description': '使用频率高但未购买VIP的用户',
                    'criteria': {
                        'is_vip': False,
                        'app_usage_hours': {'min': 2},
                        'feature_usage_rate': {'min': 0.7}
                    }
                },
                {
                    'name': '新用户群体',
                    'description': '注册时间少于30天的新用户',
                    'criteria': {
                        'registration_days': {'max': 30},
                        'profile_completion': {'min': 0.5}
                    }
                }
            ]

            for segment_data in segments_data:
                if not UserSegment.objects.filter(name=segment_data['name']).exists():
                    segment = UserSegment.objects.create(
                        name=segment_data['name'],
                        description=segment_data['description'],
                        criteria=segment_data['criteria'],
                        is_active=True,
                        created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                    )
                    # 随机分配一些用户到分群
                    segment.users.set(random.sample(users, random.randint(2, 5)))

            # 创建行为预测
            prediction_types = [
                'churn_risk', 'purchase_intent', 'engagement_level',
                'match_success_rate', 'feature_adoption'
            ]

            for user in users:
                for pred_type in random.sample(prediction_types, random.randint(2, 4)):
                    if not BehaviorPrediction.objects.filter(user=user, prediction_type=pred_type).exists():
                        BehaviorPrediction.objects.create(
                            user=user,
                            prediction_type=pred_type,
                            predicted_value=round(random.uniform(0.1, 0.9), 3),
                            confidence_score=round(random.uniform(0.6, 0.95), 3),
                            prediction_data={
                                'factors': random.sample([
                                    'login_frequency', 'message_count', 'profile_views',
                                    'match_rate', 'response_time', 'feature_usage'
                                ], random.randint(2, 4)),
                                'model_version': f'v{random.randint(1, 3)}.{random.randint(0, 9)}'
                            },
                            predicted_at=timezone.now() - timedelta(hours=random.randint(1, 168))
                        )

            self.stdout.write(self.style.SUCCESS('  ✅ 高级数据分析数据创建完成'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 高级数据分析数据创建失败: {e}'))
