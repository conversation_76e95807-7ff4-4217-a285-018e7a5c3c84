#!/usr/bin/env python3
"""
为剩余33个缺少数据的模型创建精确的演示数据
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal
from django.core.files.base import ContentFile

User = get_user_model()

class Command(BaseCommand):
    help = '为剩余33个缺少数据的模型创建精确的演示数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为剩余33个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建各模块数据
        self.create_realtime_data()
        self.create_reports_data()
        self.create_security_data()
        self.create_payment_data()
        self.create_system_data()
        self.create_operations_data()
        self.create_i18n_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 剩余33个模型演示数据创建完成！'))

    def create_realtime_data(self):
        """创建实时通讯模块精确数据"""
        self.stdout.write('⚡ 创建实时通讯模块精确数据...')
        
        try:
            from apps.realtime.models import VoiceCall, VideoCall, VoiceMessage, RealtimeNotification
            
            # 1. 语音通话 - 使用确切字段
            for i in range(25):
                caller, callee = random.sample(self.users, 2)
                duration_seconds = random.randint(30, 1800)
                duration = timedelta(seconds=duration_seconds)
                
                initiated_time = timezone.now() - timedelta(hours=random.randint(1, 168))
                answered_time = initiated_time + timedelta(seconds=random.randint(5, 30)) if random.choice([True, False]) else None
                ended_time = answered_time + duration if answered_time else initiated_time + timedelta(seconds=random.randint(5, 30))
                
                VoiceCall.objects.create(
                    caller=caller,
                    callee=callee,
                    status=random.choice(['ended', 'missed', 'rejected', 'failed']),
                    initiated_at=initiated_time,
                    answered_at=answered_time,
                    ended_at=ended_time,
                    duration=duration if answered_time else None,
                    call_quality=random.choice(['excellent', 'good', 'fair', 'poor']) if answered_time else None,
                    network_quality_caller=round(random.uniform(0.5, 1.0), 2),
                    network_quality_callee=round(random.uniform(0.5, 1.0), 2),
                    signaling_server='signal-server-01',
                    media_server='media-server-01',
                    codec_used='OPUS',
                    end_reason=random.choice(['normal', 'network_error', 'user_hangup', 'timeout'])
                )
            
            # 2. 视频通话 - 使用确切字段
            for i in range(20):
                caller, callee = random.sample(self.users, 2)
                duration_seconds = random.randint(60, 3600)
                duration = timedelta(seconds=duration_seconds)
                
                initiated_time = timezone.now() - timedelta(hours=random.randint(1, 168))
                answered_time = initiated_time + timedelta(seconds=random.randint(5, 30)) if random.choice([True, False]) else None
                ended_time = answered_time + duration if answered_time else initiated_time + timedelta(seconds=random.randint(5, 30))
                
                VideoCall.objects.create(
                    caller=caller,
                    callee=callee,
                    status=random.choice(['ended', 'missed', 'rejected', 'failed']),
                    initiated_at=initiated_time,
                    answered_at=answered_time,
                    ended_at=ended_time,
                    duration=duration if answered_time else None,
                    call_quality=random.choice(['excellent', 'good', 'fair', 'poor']) if answered_time else None,
                    video_resolution=random.choice(['720p', '1080p', '480p', '360p']),
                    frame_rate=random.choice([15, 24, 30, 60]),
                    bitrate=random.randint(500, 2000),
                    network_quality_caller=round(random.uniform(0.5, 1.0), 2),
                    network_quality_callee=round(random.uniform(0.5, 1.0), 2),
                    packet_loss_rate=round(random.uniform(0.0, 0.1), 3),
                    signaling_server='signal-server-01',
                    media_server='media-server-01',
                    video_codec='H264',
                    audio_codec='OPUS',
                    end_reason=random.choice(['normal', 'network_error', 'user_hangup', 'timeout']),
                    is_recorded=random.choice([True, False]),
                    recording_url=f'https://recordings.example.com/video_{uuid.uuid4().hex[:8]}.mp4' if random.choice([True, False]) else ''
                )
            
            # 3. 语音消息 - 使用确切字段
            for i in range(40):
                sender, receiver = random.sample(self.users, 2)
                duration = round(random.uniform(5, 60), 1)
                file_size = random.randint(50000, 500000)
                
                # 创建一个虚拟的音频文件
                audio_content = b'fake_audio_content_' + str(i).encode()
                audio_file = ContentFile(audio_content, name=f'voice_{uuid.uuid4().hex[:8]}.mp3')
                
                VoiceMessage.objects.create(
                    sender=sender,
                    receiver=receiver,
                    audio_file=audio_file,
                    duration=duration,
                    file_size=file_size,
                    sample_rate=random.choice([16000, 22050, 44100]),
                    bit_rate=random.choice([64, 128, 192]),
                    format=random.choice(['mp3', 'wav', 'aac']),
                    is_read=random.choice([True, False]),
                    read_at=timezone.now() - timedelta(hours=random.randint(1, 48)) if random.choice([True, False]) else None,
                    transcription=f'语音转文字内容 #{i+1}' if random.choice([True, False]) else '',
                    transcription_confidence=round(random.uniform(0.7, 0.95), 2) if random.choice([True, False]) else None
                )
            
            # 4. 实时通知 - 使用确切字段
            notification_types = ['message', 'like', 'match', 'voice_call', 'video_call', 'system']
            for i in range(50):
                RealtimeNotification.objects.create(
                    user=random.choice(self.users),
                    notification_type=random.choice(notification_types),
                    title=f'通知标题 #{i+1}',
                    content=f'通知内容详情 #{i+1}',
                    data={'extra_info': f'额外数据 #{i+1}', 'action_url': '/app/action'},
                    priority=random.choice(['low', 'normal', 'high', 'urgent']),
                    is_read=random.choice([True, False]),
                    read_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                    expires_at=timezone.now() + timedelta(days=random.randint(1, 30))
                )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 实时通讯数据: VoiceCall {VoiceCall.objects.count()}条, VideoCall {VideoCall.objects.count()}条, VoiceMessage {VoiceMessage.objects.count()}条, RealtimeNotification {RealtimeNotification.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 实时通讯数据创建失败: {e}'))

    def create_reports_data(self):
        """创建举报模块精确数据"""
        self.stdout.write('🚨 创建举报模块精确数据...')
        
        try:
            from apps.reports.models import Report, UserPunishment, AppealRecord
            
            # 1. 举报记录 - 使用确切字段
            report_types = ['inappropriate_content', 'harassment', 'fake_profile', 'spam', 'fraud']
            content_types = ['user', 'moment', 'message', 'comment']
            
            for i in range(30):
                reporter = random.choice(self.users)
                reported_user = random.choice([u for u in self.users if u != reporter])
                
                Report.objects.create(
                    reporter=reporter,
                    reported_user=reported_user,
                    content_type=random.choice(content_types),
                    content_id=random.randint(1, 100),
                    report_type=random.choice(report_types),
                    reason=f'举报原因详情 #{i+1}',
                    evidence=[f'/media/evidence/screenshot_{i}_{j}.jpg' for j in range(random.randint(1, 3))],
                    status=random.choice(['pending', 'processing', 'resolved', 'rejected']),
                    admin_user=random.choice(self.users) if random.choice([True, False]) else None,
                    admin_note=f'管理员处理备注 #{i+1}' if random.choice([True, False]) else '',
                    action_taken=random.choice(['warning_sent', 'content_removed', 'user_banned', 'no_action']) if random.choice([True, False]) else ''
                )
            
            # 2. 用户处罚 - 使用确切字段
            punishment_types = ['warning', 'mute', 'ban', 'permanent_ban', 'content_removal', 'feature_restriction']
            
            for i in range(20):
                user = random.choice(self.users)
                punishment_type = random.choice(punishment_types)
                
                start_time = timezone.now() - timedelta(days=random.randint(1, 30))
                end_time = start_time + timedelta(days=random.randint(1, 30)) if punishment_type != 'permanent_ban' else None
                
                punishment = UserPunishment.objects.create(
                    user=user,
                    punishment_type=punishment_type,
                    reason=f'处罚原因 #{i+1}',
                    description=f'详细处罚描述 #{i+1}',
                    related_report=random.choice(Report.objects.all()) if Report.objects.exists() and random.choice([True, False]) else None,
                    start_time=start_time,
                    end_time=end_time,
                    status=random.choice(['active', 'expired', 'revoked']),
                    admin_user=random.choice(self.users)
                )
            
            # 3. 申诉记录 - 使用确切字段
            punishments = list(UserPunishment.objects.all())
            if punishments:
                for punishment in random.sample(punishments, min(10, len(punishments))):
                    if not AppealRecord.objects.filter(punishment=punishment).exists():
                        AppealRecord.objects.create(
                            user=punishment.user,
                            punishment=punishment,
                            appeal_reason='我认为处罚不公正，请重新审核',
                            evidence_description='提供相关证据说明情况',
                            evidence_files=[f'/media/appeals/evidence_{punishment.id}_{i}.jpg' for i in range(random.randint(1, 3))],
                            status=random.choice(['pending', 'processing', 'approved', 'rejected']),
                            admin_user=random.choice(self.users) if random.choice([True, False]) else None,
                            admin_response='管理员回复内容' if random.choice([True, False]) else ''
                        )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 举报数据: Report {Report.objects.count()}条, UserPunishment {UserPunishment.objects.count()}条, AppealRecord {AppealRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 举报数据创建失败: {e}'))

    def create_security_data(self):
        """创建安全模块精确数据"""
        self.stdout.write('🛡️ 创建安全模块精确数据...')

        try:
            from apps.security.models import SecurityAuditLog, DataEncryption, RateLimitRecord

            # 1. 安全审计日志 - 使用确切字段
            action_types = ['login', 'logout', 'password_change', 'profile_update', 'admin_action']

            for i in range(40):
                SecurityAuditLog.objects.create(
                    user=random.choice(self.users),
                    action_type=random.choice(action_types),
                    action_description=f'安全审计日志描述 #{i+1}',
                    ip_address=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    user_agent='Mozilla/5.0 (Mobile; rv:89.0)',
                    resource_accessed=f'/api/resource_{random.randint(1, 10)}',
                    request_method=random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    response_status=random.choice([200, 201, 400, 401, 403, 404, 500]),
                    session_id=f'session_{uuid.uuid4().hex[:8]}',
                    success=random.choice([True, False]),
                    risk_level=random.choice(['low', 'medium', 'high', 'critical']),
                    additional_data={'extra_info': f'额外信息 #{i+1}'}
                )

            # 2. 数据加密记录 - 使用确切字段
            data_types = ['user_profile', 'chat_message', 'payment_info', 'personal_data', 'system_config']
            algorithms = ['AES-256-GCM', 'RSA-2048', 'ChaCha20-Poly1305']

            for i, data_type in enumerate(data_types):
                for j in range(5):
                    DataEncryption.objects.create(
                        data_type=data_type,
                        data_id=f'{data_type}_{i}_{j}',
                        algorithm=random.choice(algorithms),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        key_version=random.randint(1, 5),
                        encryption_status='encrypted',
                        data_size=random.randint(1024, 1048576),
                        checksum=f'sha256_{uuid.uuid4().hex[:16]}',
                        access_level=random.choice(['public', 'internal', 'confidential', 'top_secret']),
                        expires_at=timezone.now() + timedelta(days=random.randint(30, 365))
                    )

            # 3. 频率限制记录 - 使用确切字段
            limit_types = ['api_request', 'login_attempt', 'message_send', 'profile_view']

            for i in range(30):
                RateLimitRecord.objects.create(
                    identifier_type=random.choice(['ip', 'user', 'device']),
                    identifier_value=f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}',
                    limit_type=random.choice(limit_types),
                    limit_count=random.randint(10, 100),
                    time_window=random.randint(60, 3600),
                    current_count=random.randint(0, 50),
                    is_exceeded=random.choice([True, False]),
                    exceeded_at=timezone.now() - timedelta(hours=random.randint(1, 24)) if random.choice([True, False]) else None,
                    reset_at=timezone.now() + timedelta(seconds=random.randint(60, 3600))
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全数据: SecurityAuditLog {SecurityAuditLog.objects.count()}条, DataEncryption {DataEncryption.objects.count()}条, RateLimitRecord {RateLimitRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))

    def create_payment_data(self):
        """创建支付模块精确数据"""
        self.stdout.write('💰 创建支付模块精确数据...')

        try:
            from apps.payment.models import UserVipRecord, VipPackage

            # 用户VIP记录 - 使用确切字段
            vip_packages = list(VipPackage.objects.all())
            if vip_packages:
                for user in random.sample(self.users, random.randint(8, 12)):
                    if not UserVipRecord.objects.filter(user=user).exists():
                        package = random.choice(vip_packages)
                        UserVipRecord.objects.create(
                            user=user,
                            package=package,
                            is_active=random.choice([True, False])
                        )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 支付数据: UserVipRecord {UserVipRecord.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 支付数据创建失败: {e}'))

    def create_system_data(self):
        """创建系统模块精确数据"""
        self.stdout.write('⚙️ 创建系统模块精确数据...')

        try:
            from apps.system.models import UserFeedback

            # 用户反馈 - 使用确切字段
            feedback_types = ['bug_report', 'feature_request', 'general_feedback', 'complaint']

            for i in range(25):
                UserFeedback.objects.create(
                    user=random.choice(self.users),
                    feedback_type=random.choice(feedback_types),
                    title=f'用户反馈 #{i+1}',
                    content=f'反馈内容详情 #{i+1}',
                    status=random.choice(['new', 'in_progress', 'resolved', 'closed']),
                    priority=random.choice(['low', 'medium', 'high', 'urgent']),
                    category=random.choice(['ui_ux', 'performance', 'feature', 'bug', 'other']),
                    device_info={
                        'platform': random.choice(['iOS', 'Android', 'Web']),
                        'version': random.choice(['1.0.0', '1.1.0', '1.2.0']),
                        'device_model': random.choice(['iPhone 12', 'Samsung Galaxy S21', 'Chrome Browser'])
                    },
                    admin_user=random.choice(self.users) if random.choice([True, False]) else None
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 系统数据: UserFeedback {UserFeedback.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 系统数据创建失败: {e}'))

    def create_operations_data(self):
        """创建运营模块精确数据"""
        self.stdout.write('🎯 创建运营模块精确数据...')

        try:
            from apps.operations.models import CampaignParticipant, ABTest, MarketingCampaign

            # 1. 活动参与者 - 使用确切字段
            campaigns = list(MarketingCampaign.objects.all())
            if campaigns:
                for campaign in campaigns:
                    participants = random.sample(self.users, random.randint(8, 12))
                    for user in participants:
                        if not CampaignParticipant.objects.filter(campaign=campaign, user=user).exists():
                            CampaignParticipant.objects.create(
                                campaign=campaign,
                                user=user,
                                participation_date=timezone.now() - timedelta(days=random.randint(1, 15)),
                                conversion_achieved=random.choice([True, False]),
                                conversion_value=Decimal(str(round(random.uniform(0, 100), 2))),
                                source=random.choice(['organic', 'paid', 'referral', 'social']),
                                utm_source=random.choice(['google', 'facebook', 'wechat', 'direct']),
                                utm_medium=random.choice(['cpc', 'social', 'email', 'organic']),
                                utm_campaign=f'campaign_{campaign.id}'
                            )

            # 2. A/B测试 - 使用确切字段
            tests = [
                {'name': '首页布局A/B测试', 'description': '测试不同的首页布局对用户转化的影响'},
                {'name': '注册流程优化测试', 'description': '测试简化注册流程的效果'},
                {'name': '推荐算法对比测试', 'description': '对比新旧推荐算法的效果'}
            ]

            for test_data in tests:
                if not ABTest.objects.filter(name=test_data['name']).exists():
                    ABTest.objects.create(
                        name=test_data['name'],
                        description=test_data['description'],
                        hypothesis='新版本将提高用户转化率',
                        status=random.choice(['draft', 'running', 'completed', 'paused']),
                        control_group_size=random.randint(100, 500),
                        test_group_size=random.randint(100, 500),
                        conversion_rate_control=round(random.uniform(0.1, 0.3), 3),
                        conversion_rate_test=round(random.uniform(0.1, 0.3), 3),
                        statistical_significance=round(random.uniform(0.8, 0.99), 3),
                        confidence_level=0.95,
                        start_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                        end_date=timezone.now() + timedelta(days=random.randint(7, 30)),
                        created_by=random.choice(self.users)
                    )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 运营数据: CampaignParticipant {CampaignParticipant.objects.count()}条, ABTest {ABTest.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 运营数据创建失败: {e}'))

    def create_i18n_data(self):
        """创建国际化模块精确数据"""
        self.stdout.write('🌍 创建国际化模块精确数据...')

        try:
            from apps.i18n.models import (
                UserLanguagePreference, TranslationMemory, TranslationStatistics,
                LocalizationConfig, AutoTranslation, Language
            )

            # 获取语言列表
            languages = list(Language.objects.all())
            if not languages:
                self.stdout.write(self.style.WARNING('  ⚠️ 没有找到语言数据，跳过国际化数据创建'))
                return

            # 1. 用户语言偏好
            for user in self.users:
                if not UserLanguagePreference.objects.filter(user=user).exists():
                    UserLanguagePreference.objects.create(
                        user=user,
                        primary_language=random.choice(languages),
                        secondary_language=random.choice(languages),
                        auto_translate=random.choice([True, False]),
                        preferred_timezone=random.choice([
                            'Asia/Shanghai', 'Asia/Tokyo', 'America/New_York', 'Europe/London'
                        ]),
                        date_format=random.choice(['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY']),
                        time_format=random.choice(['24h', '12h'])
                    )

            # 2. 翻译记忆
            for i in range(30):
                source_lang, target_lang = random.sample(languages, 2)
                TranslationMemory.objects.create(
                    source_text=f'源文本内容 #{i+1}',
                    target_text=f'目标文本内容 #{i+1}',
                    source_language=source_lang,
                    target_language=target_lang,
                    quality_score=round(random.uniform(0.7, 1.0), 2),
                    usage_count=random.randint(1, 50),
                    context=f'翻译上下文 #{i+1}',
                    domain=random.choice(['general', 'technical', 'marketing', 'legal']),
                    created_by=random.choice(self.users)
                )

            # 3. 翻译统计
            for language in languages:
                if not TranslationStatistics.objects.filter(language=language).exists():
                    total_keys = random.randint(100, 500)
                    translated_keys = random.randint(50, total_keys)
                    TranslationStatistics.objects.create(
                        language=language,
                        date=timezone.now().date(),
                        total_keys=total_keys,
                        translated_keys=translated_keys,
                        pending_keys=total_keys - translated_keys,
                        completion_rate=round((translated_keys / total_keys) * 100, 2),
                        quality_score=round(random.uniform(0.7, 0.95), 2),
                        last_updated_by=random.choice(self.users)
                    )

            # 4. 本地化配置
            for language in languages[:5]:
                if not LocalizationConfig.objects.filter(language=language).exists():
                    LocalizationConfig.objects.create(
                        language=language,
                        date_format='%Y-%m-%d',
                        time_format='%H:%M:%S',
                        currency_symbol=random.choice(['¥', '$', '€', '£']),
                        currency_code=random.choice(['CNY', 'USD', 'EUR', 'GBP']),
                        number_format='1,234.56',
                        decimal_separator='.',
                        thousand_separator=',',
                        first_day_of_week=random.choice([0, 1]),
                        rtl_support=random.choice([True, False])
                    )

            # 5. 自动翻译
            for i in range(25):
                source_lang, target_lang = random.sample(languages, 2)
                AutoTranslation.objects.create(
                    source_text=f'自动翻译源文本 #{i+1}',
                    translated_text=f'自动翻译结果 #{i+1}',
                    source_language=source_lang,
                    target_language=target_lang,
                    translation_service=random.choice(['google_translate', 'baidu_translate', 'tencent_translate']),
                    confidence_score=round(random.uniform(0.6, 0.95), 2),
                    is_reviewed=random.choice([True, False]),
                    reviewed_by=random.choice(self.users) if random.choice([True, False]) else None,
                    cost=Decimal(str(round(random.uniform(0.01, 0.1), 3)))
                )

            self.stdout.write(self.style.SUCCESS(f'  ✅ 国际化数据: UserLanguagePreference {UserLanguagePreference.objects.count()}条, TranslationMemory {TranslationMemory.objects.count()}条'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 国际化数据创建失败: {e}'))
