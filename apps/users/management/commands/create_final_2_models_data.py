#!/usr/bin/env python3
"""
为最后2个缺少数据的模型创建精确的演示数据，实现真正的100%数据覆盖
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
import json
import uuid
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = '为最后2个缺少数据的模型创建精确的演示数据，实现真正的100%数据覆盖'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 开始为最后2个模型创建精确演示数据...'))
        
        # 获取用户列表
        self.users = list(User.objects.all())
        if not self.users:
            self.stdout.write(self.style.ERROR('❌ 没有找到用户，请先创建用户数据'))
            return
        
        # 创建安全模块数据
        self.create_security_data()
            
        self.stdout.write(self.style.SUCCESS('✅ 最后2个模型演示数据创建完成！实现真正的100%数据覆盖！'))

    def create_security_data(self):
        """创建安全模块精确数据"""
        self.stdout.write('🛡️ 创建安全模块精确数据...')
        
        try:
            from apps.security.models import DataEncryption, RateLimitRecord
            
            # 1. 数据加密记录 - 使用确切字段
            data_types = ['user_data', 'chat_message', 'file_upload', 'payment_info', 'sensitive_log']
            algorithms = ['AES-256', 'RSA-2048', 'ChaCha20']
            access_levels = ['public', 'internal', 'confidential', 'top_secret']
            
            for i, data_type in enumerate(data_types):
                for j in range(6):  # 每种数据类型创建6条记录
                    # 生成模拟的加密数据
                    original_data = f'原始数据内容 {data_type} #{i}_{j}'
                    encrypted_data = f'encrypted_{uuid.uuid4().hex}'.encode('utf-8')
                    
                    DataEncryption.objects.create(
                        data_type=data_type,
                        algorithm=random.choice(algorithms),
                        key_id=f'key_{uuid.uuid4().hex[:8]}',
                        encrypted_data=encrypted_data,
                        original_size=len(original_data.encode('utf-8')),
                        encrypted_size=len(encrypted_data),
                        checksum=f'sha256_{uuid.uuid4().hex[:16]}',
                        access_level=random.choice(access_levels),
                        expires_at=timezone.now() + timedelta(days=random.randint(30, 365)) if random.choice([True, False]) else None
                    )
            
            # 2. 频率限制记录 - 使用确切字段
            limit_types = ['api_request', 'login_attempt', 'message_send', 'like_action', 'search_query', 'file_upload', 'password_reset']
            identifier_types = ['ip', 'user', 'device']
            
            for i in range(50):
                identifier_type = random.choice(identifier_types)
                limit_type = random.choice(limit_types)
                limit_count = random.randint(10, 100)
                current_count = random.randint(0, limit_count + 10)
                is_exceeded = current_count > limit_count
                
                # 根据标识类型生成不同的标识值
                if identifier_type == 'ip':
                    identifier_value = f'{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}'
                elif identifier_type == 'user':
                    identifier_value = str(random.choice(self.users).id)
                else:  # device
                    identifier_value = f'device_{uuid.uuid4().hex[:8]}'
                
                # 检查是否已存在相同的记录（根据unique_together约束）
                if not RateLimitRecord.objects.filter(
                    identifier_type=identifier_type,
                    identifier_value=identifier_value,
                    limit_type=limit_type
                ).exists():
                    reset_time = timezone.now() + timedelta(seconds=random.randint(60, 3600))
                    
                    RateLimitRecord.objects.create(
                        identifier_type=identifier_type,
                        identifier_value=identifier_value,
                        limit_type=limit_type,
                        limit_count=limit_count,
                        time_window=random.randint(60, 3600),
                        current_count=current_count,
                        is_exceeded=is_exceeded,
                        exceeded_at=timezone.now() - timedelta(minutes=random.randint(1, 60)) if is_exceeded else None,
                        reset_at=reset_time
                    )
            
            self.stdout.write(self.style.SUCCESS(f'  ✅ 安全数据: DataEncryption {DataEncryption.objects.count()}条, RateLimitRecord {RateLimitRecord.objects.count()}条'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ 安全数据创建失败: {e}'))
            import traceback
            self.stdout.write(self.style.ERROR(f'  详细错误: {traceback.format_exc()}'))
