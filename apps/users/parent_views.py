# apps/users/parent_views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.utils import timezone
from .models import User, ParentProfile, ChildProfile
from .serializers import ParentProfileSerializer, ChildProfileSerializer


class ParentProfileViewSet(viewsets.ModelViewSet):
    """家长资料视图集"""
    serializer_class = ParentProfileSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return ParentProfile.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        # 设置用户类型为家长
        self.request.user.user_type = 'parent'
        self.request.user.save()
        serializer.save(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """获取家长中心统计数据"""
        try:
            parent_profile = ParentProfile.objects.get(user=request.user)
            children = ChildProfile.objects.filter(parent=parent_profile)
            
            stats = {
                'total_children': children.count(),
                'active_children': children.filter(status='active').count(),
                'total_views': sum(child.profile_views for child in children),
                'total_matches': sum(child.matches_count for child in children),
                'today_views': 0,  # 需要实现今日浏览统计
            }
            
            return Response(stats)
        except ParentProfile.DoesNotExist:
            return Response({'error': '家长资料不存在'}, status=status.HTTP_404_NOT_FOUND)


class ChildProfileViewSet(viewsets.ModelViewSet):
    """子女资料视图集"""
    serializer_class = ChildProfileSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        try:
            parent_profile = ParentProfile.objects.get(user=self.request.user)
            return ChildProfile.objects.filter(parent=parent_profile)
        except ParentProfile.DoesNotExist:
            return ChildProfile.objects.none()
    
    def perform_create(self, serializer):
        try:
            parent_profile = ParentProfile.objects.get(user=self.request.user)
            serializer.save(parent=parent_profile)
        except ParentProfile.DoesNotExist:
            from rest_framework.exceptions import ValidationError
            raise ValidationError('请先创建家长资料')
    
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换子女匹配状态"""
        child = self.get_object()
        new_status = 'active' if child.status == 'inactive' else 'inactive'
        child.status = new_status
        child.save()
        
        return Response({
            'status': new_status,
            'message': f'已{"开启" if new_status == "active" else "暂停"}匹配'
        })
    
    @action(detail=True, methods=['post'])
    def update_photos(self, request, pk=None):
        """更新子女相册"""
        child = self.get_object()
        photos = request.data.get('photos', [])
        
        if not isinstance(photos, list):
            return Response({'error': '相册格式错误'}, status=status.HTTP_400_BAD_REQUEST)
        
        child.photos = photos
        child.save()
        
        return Response({'message': '相册更新成功', 'photos': photos})
    
    @action(detail=False, methods=['get'])
    def browse_others(self, request):
        """浏览其他家长发布的子女信息"""
        # 获取筛选参数
        gender = request.query_params.get('gender')
        min_age = request.query_params.get('min_age')
        max_age = request.query_params.get('max_age')
        education = request.query_params.get('education')
        location = request.query_params.get('location')
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        
        # 排除自己的子女
        try:
            parent_profile = ParentProfile.objects.get(user=request.user)
            queryset = ChildProfile.objects.filter(status='active').exclude(parent=parent_profile)
        except ParentProfile.DoesNotExist:
            queryset = ChildProfile.objects.filter(status='active')
        
        # 应用筛选条件
        if gender:
            queryset = queryset.filter(gender=gender)
        if min_age:
            queryset = queryset.filter(age__gte=min_age)
        if max_age:
            queryset = queryset.filter(age__lte=max_age)
        if education:
            queryset = queryset.filter(education=education)
        if location:
            queryset = queryset.filter(location__icontains=location)
        
        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        children = queryset[start:end]
        
        # 序列化数据
        serializer = self.get_serializer(children, many=True)
        
        return Response({
            'results': serializer.data,
            'has_more': queryset.count() > end,
            'total': queryset.count()
        })
    
    @action(detail=True, methods=['post'])
    def record_view(self, request, pk=None):
        """记录浏览"""
        child = self.get_object()
        child.profile_views += 1
        child.save()
        
        return Response({'message': '浏览记录成功'})
    
    @action(detail=True, methods=['get'])
    def match_analysis(self, request, pk=None):
        """匹配分析"""
        child = self.get_object()
        
        # 这里可以实现匹配度计算逻辑
        analysis = {
            'completeness': self.calculate_completeness(child),
            'attractiveness_score': self.calculate_attractiveness(child),
            'match_suggestions': self.get_match_suggestions(child),
        }
        
        return Response(analysis)
    
    def calculate_completeness(self, child):
        """计算资料完整度"""
        total_fields = 10
        completed_fields = 0
        
        if child.name: completed_fields += 1
        if child.avatar: completed_fields += 1
        if child.bio: completed_fields += 1
        if child.profession: completed_fields += 1
        if child.education: completed_fields += 1
        if child.height: completed_fields += 1
        if child.weight: completed_fields += 1
        if child.income: completed_fields += 1
        if child.requirements: completed_fields += 1
        if child.photos: completed_fields += 1
        
        return int((completed_fields / total_fields) * 100)
    
    def calculate_attractiveness(self, child):
        """计算吸引力评分"""
        # 基础分数
        score = 60
        
        # 根据各种因素调整分数
        if child.photos and len(child.get_photos_list()) >= 3:
            score += 10
        if child.bio and len(child.bio) > 50:
            score += 5
        if child.education >= 3:  # 本科及以上
            score += 10
        if child.income and child.income >= 4:  # 中等收入及以上
            score += 10
        if child.profile_views > 50:
            score += 5
        
        return min(score, 100)
    
    def get_match_suggestions(self, child):
        """获取匹配建议"""
        suggestions = []
        
        if not child.photos or len(child.get_photos_list()) < 3:
            suggestions.append("建议上传更多照片，增加吸引力")
        
        if not child.bio or len(child.bio) < 50:
            suggestions.append("完善个人介绍，让别人更了解你")
        
        if not child.requirements:
            suggestions.append("明确择偶要求，提高匹配精准度")
        
        if child.profile_views < 10:
            suggestions.append("优化资料内容，提高曝光度")
        
        return suggestions
