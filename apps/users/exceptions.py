# apps/users/exceptions.py
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError


def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    # 调用默认的异常处理器
    response = exception_handler(exc, context)
    
    if response is not None:
        # 处理JWT认证相关异常
        if isinstance(exc, (InvalidToken, TokenError)):
            return Response({
                'detail': '身份认证信息无效',
                'code': 'invalid_token'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 处理认证失败异常
        if response.status_code == 403:
            error_detail = response.data.get('detail', '')
            if '身份认证信息未提供' in str(error_detail) or 'Authentication credentials were not provided' in str(error_detail):
                return Response({
                    'detail': '身份认证信息未提供',
                    'code': 'authentication_required'
                }, status=status.HTTP_401_UNAUTHORIZED)
            elif 'Given token not valid' in str(error_detail) or 'Token is invalid' in str(error_detail):
                return Response({
                    'detail': '身份认证信息无效',
                    'code': 'invalid_token'
                }, status=status.HTTP_401_UNAUTHORIZED)
    
    return response
