# apps/users/matchmaker_views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from .models import User, MatchmakerProfile, MatchmakerClient, ServiceRecord
from .serializers import MatchmakerProfileSerializer, MatchmakerClientSerializer, ServiceRecordSerializer


class MatchmakerProfileViewSet(viewsets.ModelViewSet):
    """红娘资料视图集"""
    serializer_class = MatchmakerProfileSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return MatchmakerProfile.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        # 设置用户类型为红娘
        self.request.user.user_type = 'matchmaker'
        self.request.user.save()
        serializer.save(user=self.request.user)
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """获取红娘工作台统计数据"""
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=request.user)
            clients = MatchmakerClient.objects.filter(matchmaker=matchmaker_profile)
            
            # 今日数据
            today = timezone.now().date()
            today_clients = clients.filter(start_date__date=today)
            today_records = ServiceRecord.objects.filter(
                client__matchmaker=matchmaker_profile,
                service_date__date=today
            )
            
            # 本月数据
            month_start = today.replace(day=1)
            month_clients = clients.filter(start_date__date__gte=month_start)
            month_records = ServiceRecord.objects.filter(
                client__matchmaker=matchmaker_profile,
                service_date__date__gte=month_start
            )
            
            stats = {
                'matchmaker_info': {
                    'name': matchmaker_profile.real_name,
                    'level': matchmaker_profile.get_level_display(),
                    'rating': matchmaker_profile.rating,
                    'experience': f"{matchmaker_profile.experience_years}年",
                    'certified': matchmaker_profile.is_certified,
                    'specialties': matchmaker_profile.get_specialties_list(),
                    'success_rate': matchmaker_profile.success_rate,
                    'total_clients': matchmaker_profile.total_clients,
                    'active_clients': matchmaker_profile.active_clients,
                    'monthly_income': float(matchmaker_profile.monthly_income)
                },
                'today_stats': {
                    'new_clients': today_clients.count(),
                    'appointments': today_records.filter(service_type='consultation').count(),
                    'matches': today_records.filter(service_type='matching').count(),
                    'income': float(sum(record.client.service_fee for record in today_records if record.client.service_fee))
                },
                'month_stats': {
                    'new_clients': month_clients.count(),
                    'total_appointments': month_records.filter(service_type='consultation').count(),
                    'successful_matches': month_records.filter(service_type='matching').count(),
                    'income': float(matchmaker_profile.monthly_income),
                    'client_satisfaction': 96  # 可以从实际评分计算
                }
            }
            
            return Response(stats)
        except MatchmakerProfile.DoesNotExist:
            return Response({'error': '红娘资料不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['get'])
    def todo_list(self, request):
        """获取待办事项"""
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=request.user)
            
            # 获取今日的服务记录作为待办事项
            today = timezone.now().date()
            upcoming_services = ServiceRecord.objects.filter(
                client__matchmaker=matchmaker_profile,
                service_date__date=today,
                service_date__gt=timezone.now()
            ).order_by('service_date')
            
            todo_items = []
            for service in upcoming_services:
                priority = 'high' if service.service_type in ['consultation', 'date_arrangement'] else 'medium'
                todo_items.append({
                    'id': service.id,
                    'type': service.service_type,
                    'title': service.title,
                    'time': service.service_date.strftime('%H:%M'),
                    'priority': priority,
                    'status': 'pending'
                })
            
            return Response(todo_items)
        except MatchmakerProfile.DoesNotExist:
            return Response({'error': '红娘资料不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['get'])
    def recent_clients(self, request):
        """获取最新客户"""
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=request.user)
            recent_clients = MatchmakerClient.objects.filter(
                matchmaker=matchmaker_profile
            ).order_by('-start_date')[:5]
            
            serializer = MatchmakerClientSerializer(recent_clients, many=True)
            return Response(serializer.data)
        except MatchmakerProfile.DoesNotExist:
            return Response({'error': '红娘资料不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['get'])
    def match_recommendations(self, request):
        """获取匹配推荐"""
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=request.user)
            
            # 获取活跃客户
            active_clients = MatchmakerClient.objects.filter(
                matchmaker=matchmaker_profile,
                status='matching'
            )
            
            recommendations = []
            # 这里可以实现智能匹配算法
            # 暂时返回模拟数据
            if active_clients.count() >= 2:
                client1 = active_clients.first()
                client2 = active_clients.last()
                
                recommendations.append({
                    'id': f"{client1.id}_{client2.id}",
                    'client1': {
                        'name': client1.user.nickname,
                        'avatar': client1.user.avatar.url if client1.user.avatar else None,
                        'age': self.calculate_age(client1.user.birth_date) if client1.user.birth_date else None
                    },
                    'client2': {
                        'name': client2.user.nickname,
                        'avatar': client2.user.avatar.url if client2.user.avatar else None,
                        'age': self.calculate_age(client2.user.birth_date) if client2.user.birth_date else None
                    },
                    'match_rate': 92,
                    'reason': '兴趣爱好相似，价值观匹配'
                })
            
            return Response(recommendations)
        except MatchmakerProfile.DoesNotExist:
            return Response({'error': '红娘资料不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    def calculate_age(self, birth_date):
        """计算年龄"""
        if not birth_date:
            return None
        today = timezone.now().date()
        return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))


class MatchmakerClientViewSet(viewsets.ModelViewSet):
    """红娘客户管理视图集"""
    serializer_class = MatchmakerClientSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=self.request.user)
            return MatchmakerClient.objects.filter(matchmaker=matchmaker_profile)
        except MatchmakerProfile.DoesNotExist:
            return MatchmakerClient.objects.none()
    
    def perform_create(self, serializer):
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=self.request.user)

            # 确保user字段被正确设置
            user_id = self.request.data.get('user')
            if not user_id:
                raise ValueError('用户ID不能为空')

            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                raise ValueError('用户不存在')

            # 检查是否已经是该红娘的客户
            existing_client = MatchmakerClient.objects.filter(
                matchmaker=matchmaker_profile,
                user=user
            ).first()

            if existing_client:
                raise ValueError('该用户已经是您的客户')

            # 保存客户关系
            client = serializer.save(matchmaker=matchmaker_profile, user=user)

            # 更新红娘统计
            matchmaker_profile.total_clients += 1
            matchmaker_profile.active_clients += 1
            matchmaker_profile.save()

        except MatchmakerProfile.DoesNotExist:
            raise ValueError('请先创建红娘资料')
        except ValueError as e:
            from rest_framework.exceptions import ValidationError
            raise ValidationError(str(e))
    
    @action(detail=False, methods=['get'])
    def client_stats(self, request):
        """获取客户统计"""
        queryset = self.get_queryset()
        
        stats = {
            'total': queryset.count(),
            'new': queryset.filter(status='new').count(),
            'matching': queryset.filter(status='matching').count(),
            'matched': queryset.filter(status='matched').count(),
            'inactive': queryset.filter(status='cancelled').count()
        }
        
        return Response(stats)
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """更新客户状态"""
        client = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in dict(MatchmakerClient.STATUS_CHOICES):
            return Response({'error': '无效的状态'}, status=status.HTTP_400_BAD_REQUEST)
        
        old_status = client.status
        client.status = new_status
        client.save()
        
        # 更新红娘统计
        matchmaker_profile = client.matchmaker
        if old_status in ['new', 'consulting', 'matching'] and new_status in ['completed', 'cancelled']:
            matchmaker_profile.active_clients = max(0, matchmaker_profile.active_clients - 1)
        elif old_status in ['completed', 'cancelled'] and new_status in ['new', 'consulting', 'matching']:
            matchmaker_profile.active_clients += 1
        
        if new_status == 'matched':
            matchmaker_profile.successful_matches += 1
            matchmaker_profile.success_rate = (matchmaker_profile.successful_matches / matchmaker_profile.total_clients) * 100
        
        matchmaker_profile.save()
        
        return Response({'message': '状态更新成功', 'status': new_status})
    
    @action(detail=True, methods=['post'])
    def set_priority(self, request, pk=None):
        """设置客户优先级"""
        client = self.get_object()
        priority = request.data.get('priority')
        
        if priority not in dict(MatchmakerClient.PRIORITY_CHOICES):
            return Response({'error': '无效的优先级'}, status=status.HTTP_400_BAD_REQUEST)
        
        client.priority = priority
        client.save()
        
        return Response({'message': '优先级设置成功', 'priority': priority})
    
    @action(detail=True, methods=['post'])
    def add_note(self, request, pk=None):
        """添加客户备注"""
        client = self.get_object()
        note = request.data.get('note', '')
        
        if client.notes:
            client.notes += f"\n[{timezone.now().strftime('%Y-%m-%d %H:%M')}] {note}"
        else:
            client.notes = f"[{timezone.now().strftime('%Y-%m-%d %H:%M')}] {note}"
        
        client.save()
        
        return Response({'message': '备注添加成功'})


class ServiceRecordViewSet(viewsets.ModelViewSet):
    """服务记录视图集"""
    serializer_class = ServiceRecordSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=self.request.user)
            return ServiceRecord.objects.filter(client__matchmaker=matchmaker_profile)
        except MatchmakerProfile.DoesNotExist:
            return ServiceRecord.objects.none()

    def perform_create(self, serializer):
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=self.request.user)

            # 获取客户ID
            client_id = self.request.data.get('client')
            if not client_id:
                raise ValueError('客户ID不能为空')

            try:
                # 确保客户属于当前红娘
                client = MatchmakerClient.objects.get(
                    id=client_id,
                    matchmaker=matchmaker_profile
                )
            except MatchmakerClient.DoesNotExist:
                raise ValueError('客户不存在或不属于您')

            # 保存服务记录
            serializer.save(client=client)

        except MatchmakerProfile.DoesNotExist:
            raise ValueError('请先创建红娘资料')
        except ValueError as e:
            from rest_framework.exceptions import ValidationError
            raise ValidationError(str(e))
    
    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """服务记录分析"""
        queryset = self.get_queryset()
        
        # 按服务类型统计
        service_type_stats = queryset.values('service_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 按月份统计
        month_stats = queryset.filter(
            service_date__gte=timezone.now() - timedelta(days=180)
        ).extra(
            select={'month': "DATE_FORMAT(service_date, '%%Y-%%m')"}
        ).values('month').annotate(
            count=Count('id')
        ).order_by('month')
        
        # 服务时长统计
        avg_duration = queryset.aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
        
        analytics = {
            'service_type_stats': list(service_type_stats),
            'month_stats': list(month_stats),
            'avg_duration': round(avg_duration, 2),
            'total_records': queryset.count()
        }

        return Response(analytics)

    @action(detail=False, methods=['get'])
    def dashboard_analytics(self, request):
        """获取仪表板分析数据"""
        try:
            matchmaker_profile = MatchmakerProfile.objects.get(user=request.user)
            queryset = self.get_queryset()

            # 今日数据
            today = timezone.now().date()
            today_records = queryset.filter(service_date__date=today)

            # 本周数据
            week_start = today - timedelta(days=today.weekday())
            week_records = queryset.filter(service_date__date__gte=week_start)

            # 本月数据
            month_start = today.replace(day=1)
            month_records = queryset.filter(service_date__date__gte=month_start)

            # 核心指标
            core_metrics = {
                'total_revenue': float(matchmaker_profile.monthly_income),
                'new_clients': MatchmakerClient.objects.filter(
                    matchmaker=matchmaker_profile,
                    start_date__date__gte=month_start
                ).count(),
                'successful_matches': matchmaker_profile.successful_matches,
                'client_satisfaction': 96.5,  # 可以从实际评分计算
                'avg_service_time': queryset.aggregate(avg=Avg('duration'))['avg'] or 0,
                'conversion_rate': 44.4  # 可以从实际数据计算
            }

            # 收入分析
            current_income = float(matchmaker_profile.monthly_income)
            revenue_analysis = {
                'current_period': current_income,
                'previous_period': current_income * 0.85,  # 模拟上期数据
                'growth_rate': 19.9,
                'monthly_data': self.get_monthly_revenue_data(matchmaker_profile),
                'service_type_revenue': self.get_service_type_revenue(matchmaker_profile)
            }

            # 客户分析
            client_analysis = {
                'total_clients': matchmaker_profile.total_clients,
                'active_clients': matchmaker_profile.active_clients,
                'new_clients_data': self.get_new_clients_trend(matchmaker_profile),
                'client_status_distribution': self.get_client_status_distribution(matchmaker_profile),
                'client_source_data': self.get_client_source_data(matchmaker_profile)
            }

            # 服务分析
            service_analysis = {
                'total_services': queryset.count(),
                'avg_duration': round(queryset.aggregate(avg=Avg('duration'))['avg'] or 0, 1),
                'service_type_data': self.get_service_type_analysis(queryset),
                'service_efficiency': self.get_service_efficiency_trend(queryset),
                'satisfaction_trend': self.get_satisfaction_trend(matchmaker_profile)
            }

            # 匹配分析
            match_analysis = {
                'total_matches': matchmaker_profile.successful_matches,
                'success_rate': matchmaker_profile.success_rate,
                'match_trend': self.get_match_trend(matchmaker_profile),
                'age_group_matches': self.get_age_group_matches(matchmaker_profile),
                'education_matches': self.get_education_matches(matchmaker_profile)
            }

            analytics_data = {
                'core_metrics': core_metrics,
                'revenue_analysis': revenue_analysis,
                'client_analysis': client_analysis,
                'service_analysis': service_analysis,
                'match_analysis': match_analysis
            }

            return Response(analytics_data)

        except MatchmakerProfile.DoesNotExist:
            return Response({'error': '红娘资料不存在'}, status=status.HTTP_404_NOT_FOUND)

    def get_monthly_revenue_data(self, matchmaker_profile):
        """获取月度收入数据"""
        # 模拟数据，实际应该从数据库查询
        return [
            {'month': '1月', 'revenue': 32000},
            {'month': '2月', 'revenue': 28000},
            {'month': '3月', 'revenue': 35000},
            {'month': '4月', 'revenue': 42000},
            {'month': '5月', 'revenue': 38000},
            {'month': '6月', 'revenue': 45000}
        ]

    def get_service_type_revenue(self, matchmaker_profile):
        """获取服务类型收入分布"""
        return [
            {'type': '咨询服务', 'revenue': 15000, 'percentage': 35},
            {'type': '匹配服务', 'revenue': 20000, 'percentage': 45},
            {'type': '跟进服务', 'revenue': 8000, 'percentage': 20}
        ]

    def get_new_clients_trend(self, matchmaker_profile):
        """获取新客户趋势"""
        return [
            {'date': '2024-01', 'count': 5},
            {'date': '2024-02', 'count': 8},
            {'date': '2024-03', 'count': 12},
            {'date': '2024-04', 'count': 15},
            {'date': '2024-05', 'count': 18},
            {'date': '2024-06', 'count': 22}
        ]

    def get_client_status_distribution(self, matchmaker_profile):
        """获取客户状态分布"""
        return [
            {'status': '新客户', 'count': 15, 'percentage': 30},
            {'status': '服务中', 'count': 25, 'percentage': 50},
            {'status': '已完成', 'count': 8, 'percentage': 16},
            {'status': '暂停', 'count': 2, 'percentage': 4}
        ]

    def get_client_source_data(self, matchmaker_profile):
        """获取客户来源数据"""
        return [
            {'source': '推荐', 'count': 20, 'percentage': 40},
            {'source': '线上', 'count': 18, 'percentage': 36},
            {'source': '线下', 'count': 12, 'percentage': 24}
        ]

    def get_service_type_analysis(self, queryset):
        """获取服务类型分析"""
        return [
            {'type': '咨询', 'count': 25, 'avg_duration': 45},
            {'type': '匹配', 'count': 18, 'avg_duration': 90},
            {'type': '跟进', 'count': 32, 'avg_duration': 30}
        ]

    def get_service_efficiency_trend(self, queryset):
        """获取服务效率趋势"""
        return [
            {'date': '2024-01', 'efficiency': 85},
            {'date': '2024-02', 'efficiency': 88},
            {'date': '2024-03', 'efficiency': 92},
            {'date': '2024-04', 'efficiency': 89},
            {'date': '2024-05', 'efficiency': 94},
            {'date': '2024-06', 'efficiency': 96}
        ]

    def get_satisfaction_trend(self, matchmaker_profile):
        """获取满意度趋势"""
        return [
            {'date': '2024-01', 'satisfaction': 92},
            {'date': '2024-02', 'satisfaction': 94},
            {'date': '2024-03', 'satisfaction': 96},
            {'date': '2024-04', 'satisfaction': 95},
            {'date': '2024-05', 'satisfaction': 97},
            {'date': '2024-06', 'satisfaction': 96}
        ]

    def get_match_trend(self, matchmaker_profile):
        """获取匹配趋势"""
        return [
            {'date': '2024-01', 'matches': 3},
            {'date': '2024-02', 'matches': 5},
            {'date': '2024-03', 'matches': 8},
            {'date': '2024-04', 'matches': 6},
            {'date': '2024-05', 'matches': 9},
            {'date': '2024-06', 'matches': 12}
        ]

    def get_age_group_matches(self, matchmaker_profile):
        """获取年龄组匹配数据"""
        return [
            {'age_group': '20-25', 'matches': 8, 'percentage': 20},
            {'age_group': '26-30', 'matches': 15, 'percentage': 37.5},
            {'age_group': '31-35', 'matches': 12, 'percentage': 30},
            {'age_group': '36-40', 'matches': 5, 'percentage': 12.5}
        ]

    def get_education_matches(self, matchmaker_profile):
        """获取学历匹配数据"""
        return [
            {'education': '本科', 'matches': 18, 'percentage': 45},
            {'education': '硕士', 'matches': 15, 'percentage': 37.5},
            {'education': '博士', 'matches': 5, 'percentage': 12.5},
            {'education': '其他', 'matches': 2, 'percentage': 5}
        ]



    def get_service_type_analysis(self, queryset):
        """获取服务类型分析"""
        total_count = queryset.count()
        if total_count == 0:
            return []

        service_counts = queryset.values('service_type').annotate(count=Count('id'))
        service_type_map = dict(ServiceRecord.SERVICE_TYPE_CHOICES)

        result = []
        for item in service_counts:
            count = item['count']
            percentage = round((count / total_count) * 100, 1)
            result.append({
                'type': service_type_map.get(item['service_type'], item['service_type']),
                'count': count,
                'percentage': percentage
            })

        return result

    def get_service_efficiency_trend(self, queryset):
        """获取服务效率趋势"""
        # 模拟数据，实际应该计算真实效率
        return [
            {'week': '第1周', 'efficiency': 85},
            {'week': '第2周', 'efficiency': 88},
            {'week': '第3周', 'efficiency': 92},
            {'week': '第4周', 'efficiency': 89}
        ]

    def get_satisfaction_trend(self, matchmaker_profile):
        """获取满意度趋势"""
        return [
            {'month': '1月', 'satisfaction': 94.2},
            {'month': '2月', 'satisfaction': 95.1},
            {'month': '3月', 'satisfaction': 96.3},
            {'month': '4月', 'satisfaction': 95.8},
            {'month': '5月', 'satisfaction': 96.5},
            {'month': '6月', 'satisfaction': 96.8}
        ]

    def get_match_trend(self, matchmaker_profile):
        """获取匹配趋势"""
        return [
            {'month': '1月', 'matches': 12},
            {'month': '2月', 'matches': 10},
            {'month': '3月', 'matches': 15},
            {'month': '4月', 'matches': 13},
            {'month': '5月', 'matches': 14},
            {'month': '6月', 'matches': 14}
        ]

    def get_age_group_matches(self, matchmaker_profile):
        """获取年龄段匹配分布"""
        total_matches = matchmaker_profile.successful_matches
        return [
            {'age_group': '20-25岁', 'matches': int(total_matches * 0.231), 'percentage': 23.1},
            {'age_group': '26-30岁', 'matches': int(total_matches * 0.410), 'percentage': 41.0},
            {'age_group': '31-35岁', 'matches': int(total_matches * 0.256), 'percentage': 25.6},
            {'age_group': '36-40岁', 'matches': int(total_matches * 0.103), 'percentage': 10.3}
        ]

    def get_education_matches(self, matchmaker_profile):
        """获取学历匹配分布"""
        total_matches = matchmaker_profile.successful_matches
        return [
            {'education': '本科', 'matches': int(total_matches * 0.449), 'percentage': 44.9},
            {'education': '硕士', 'matches': int(total_matches * 0.359), 'percentage': 35.9},
            {'education': '博士', 'matches': int(total_matches * 0.154), 'percentage': 15.4},
            {'education': '其他', 'matches': int(total_matches * 0.038), 'percentage': 3.8}
        ]
