from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import (User, UserPreference, UserAction, UserVerification,
                    ParentProfile, ChildProfile, MatchmakerProfile,
                    MatchmakerClient, ServiceRecord, UserPrivacySettings,
                    UserNotificationSettings, UserRecommendationSettings, UserBlacklist,
                    VisitorRecord, UserAnalytics, DailyAnalytics, Notification, SystemMessage)


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    age = serializers.SerializerMethodField()
    is_vip = serializers.SerializerMethodField()
    photos_list = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'nickname', 'avatar', 'gender', 'age', 'birthday',
            'location', 'profession', 'education', 'height', 'weight',
            'income', 'marriage_status', 'bio', 'photos_list', 'is_verified',
            'vip_level', 'is_vip', 'points', 'coins', 'profile_views',
            'likes_received', 'likes_sent', 'matches_count', 'created_at'
        ]
        read_only_fields = [
            'id', 'is_verified', 'vip_level', 'is_vip', 'points', 'coins',
            'profile_views', 'likes_received', 'likes_sent', 'matches_count', 'created_at'
        ]
    
    def get_age(self, obj):
        if obj.birthday:
            from datetime import date
            today = date.today()
            return today.year - obj.birthday.year - ((today.month, today.day) < (obj.birthday.month, obj.birthday.day))
        return obj.age
    
    def get_is_vip(self, obj):
        return obj.is_vip
    
    def get_photos_list(self, obj):
        return obj.get_photos_list()


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器（详细信息）"""
    age = serializers.SerializerMethodField()
    is_vip = serializers.SerializerMethodField()
    photos_list = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'nickname', 'avatar', 'gender', 'age', 'birthday',
            'location', 'profession', 'education', 'height', 'weight',
            'income', 'marriage_status', 'bio', 'photos_list', 'is_verified',
            'vip_level', 'is_vip', 'distance', 'last_active'
        ]
    
    def get_age(self, obj):
        if obj.birthday:
            from datetime import date
            today = date.today()
            return today.year - obj.birthday.year - ((today.month, today.day) < (obj.birthday.month, obj.birthday.day))
        return obj.age
    
    def get_is_vip(self, obj):
        return obj.is_vip
    
    def get_photos_list(self, obj):
        return obj.get_photos_list()
    
    def get_distance(self, obj):
        # 计算距离逻辑，需要当前用户的位置信息
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            current_user = request.user
            if (current_user.latitude and current_user.longitude and 
                obj.latitude and obj.longitude):
                # 使用haversine公式计算距离
                from math import radians, cos, sin, asin, sqrt
                
                lat1, lon1 = radians(current_user.latitude), radians(current_user.longitude)
                lat2, lon2 = radians(obj.latitude), radians(obj.longitude)
                
                dlon = lon2 - lon1
                dlat = lat2 - lat1
                a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371  # 地球半径（公里）
                
                return round(c * r, 1)
        return None


class UserPreferenceSerializer(serializers.ModelSerializer):
    """用户偏好序列化器"""
    
    class Meta:
        model = UserPreference
        fields = [
            'min_age', 'max_age', 'preferred_gender', 'preferred_location',
            'distance_range', 'preferred_education', 'preferred_income',
            'min_height', 'max_height', 'only_verified', 'only_with_photos'
        ]


class UserActionSerializer(serializers.ModelSerializer):
    """用户行为序列化器"""
    target_user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = UserAction
        fields = ['id', 'target_user', 'action_type', 'message', 'created_at']
        read_only_fields = ['id', 'created_at']


class UserVerificationSerializer(serializers.ModelSerializer):
    """用户认证序列化器"""
    
    class Meta:
        model = UserVerification
        fields = [
            'id', 'verification_type', 'status', 'data', 'reason', 'created_at'
        ]
        read_only_fields = ['id', 'status', 'reason', 'created_at']


class WeChatLoginSerializer(serializers.Serializer):
    """微信登录序列化器"""
    code = serializers.CharField(max_length=100)
    encrypted_data = serializers.CharField(required=False, allow_blank=True)
    iv = serializers.CharField(required=False, allow_blank=True)


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器"""
    
    class Meta:
        model = User
        fields = [
            'nickname', 'avatar', 'gender', 'birthday', 'location',
            'profession', 'education', 'height', 'weight', 'income',
            'marriage_status', 'bio'
        ]
    
    def validate_nickname(self, value):
        if len(value) < 2:
            raise serializers.ValidationError("昵称至少需要2个字符")
        if len(value) > 20:
            raise serializers.ValidationError("昵称不能超过20个字符")
        return value
    
    def validate_height(self, value):
        if value and (value < 100 or value > 250):
            raise serializers.ValidationError("身高必须在100-250cm之间")
        return value
    
    def validate_weight(self, value):
        if value and (value < 30 or value > 200):
            raise serializers.ValidationError("体重必须在30-200kg之间")
        return value


class PhotoUploadSerializer(serializers.Serializer):
    """照片上传序列化器"""
    photo = serializers.ImageField()
    
    def validate_photo(self, value):
        # 验证文件大小（5MB）
        if value.size > 5 * 1024 * 1024:
            raise serializers.ValidationError("图片大小不能超过5MB")
        
        # 验证文件格式
        allowed_formats = ['JPEG', 'JPG', 'PNG', 'WEBP']
        if value.image.format not in allowed_formats:
            raise serializers.ValidationError("只支持JPEG、PNG、WEBP格式的图片")
        
        return value


class UserStatsSerializer(serializers.Serializer):
    """用户统计序列化器"""
    profile_views = serializers.IntegerField()
    likes_received = serializers.IntegerField()
    likes_sent = serializers.IntegerField()
    matches_count = serializers.IntegerField()
    today_views = serializers.IntegerField()
    today_likes = serializers.IntegerField()
    today_matches = serializers.IntegerField()
    completion_rate = serializers.FloatField()


class VisitorRecordSerializer(serializers.ModelSerializer):
    """访客记录序列化器"""
    visitor = UserProfileSerializer(read_only=True)

    class Meta:
        model = UserAction
        fields = ['id', 'visitor', 'created_at']
        read_only_fields = ['id', 'created_at']


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    password = serializers.CharField(write_only=True, min_length=6)
    verification_code = serializers.CharField(write_only=True, max_length=6)
    username = serializers.CharField(max_length=150)

    class Meta:
        model = User
        fields = [
            'username', 'password', 'verification_code', 'nickname',
            'gender', 'location', 'openid'
        ]

    def validate_username(self, value):
        """验证用户名"""
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("该用户名已被注册")
        return value

    def validate_openid(self, value):
        """验证OpenID"""
        if User.objects.filter(openid=value).exists():
            raise serializers.ValidationError("该微信账号已被注册")
        return value

    def create(self, validated_data):
        """创建用户"""
        verification_code = validated_data.pop('verification_code')
        password = validated_data.pop('password')

        # 创建用户
        user = User.objects.create_user(
            password=password,
            **validated_data
        )

        return user


class ParentProfileSerializer(serializers.ModelSerializer):
    """家长资料序列化器"""
    user_info = UserSerializer(source='user', read_only=True)
    children_count = serializers.SerializerMethodField()

    class Meta:
        model = ParentProfile
        fields = [
            'id', 'user_info', 'real_name', 'relationship', 'description',
            'success_count', 'is_verified', 'verified_at', 'children_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'verified_at', 'created_at', 'updated_at']

    def get_children_count(self, obj):
        return obj.children.count()


class ChildProfileSerializer(serializers.ModelSerializer):
    """子女资料序列化器"""
    parent_info = serializers.SerializerMethodField()
    photos_list = serializers.SerializerMethodField()
    age_display = serializers.SerializerMethodField()

    class Meta:
        model = ChildProfile
        fields = [
            'id', 'parent_info', 'name', 'avatar', 'gender', 'age', 'age_display',
            'birthday', 'location', 'profession', 'education', 'height', 'weight',
            'income', 'marriage_status', 'bio', 'photos_list', 'requirements',
            'status', 'profile_views', 'likes_received', 'matches_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'profile_views', 'likes_received', 'matches_count',
            'created_at', 'updated_at'
        ]

    def get_parent_info(self, obj):
        return {
            'name': obj.parent.real_name,
            'avatar': obj.parent.user.avatar.url if obj.parent.user.avatar else None,
            'verified': obj.parent.is_verified,
            'success_count': obj.parent.success_count,
            'description': obj.parent.description
        }

    def get_photos_list(self, obj):
        return obj.get_photos_list()

    def get_age_display(self, obj):
        if obj.birthday:
            from datetime import date
            today = date.today()
            return today.year - obj.birthday.year - ((today.month, today.day) < (obj.birthday.month, obj.birthday.day))
        return obj.age


class MatchmakerProfileSerializer(serializers.ModelSerializer):
    """红娘资料序列化器"""
    user_info = UserSerializer(source='user', read_only=True)
    specialties_list = serializers.SerializerMethodField()
    service_areas_list = serializers.SerializerMethodField()
    level_display = serializers.CharField(source='get_level_display', read_only=True)

    class Meta:
        model = MatchmakerProfile
        fields = [
            'id', 'user_info', 'real_name', 'company', 'experience_years',
            'level', 'level_display', 'rating', 'specialties_list', 'total_clients',
            'active_clients', 'successful_matches', 'success_rate', 'monthly_income',
            'total_income', 'is_certified', 'certified_at', 'certification_number',
            'service_areas_list', 'min_fee', 'max_fee', 'status',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_clients', 'active_clients', 'successful_matches',
            'success_rate', 'monthly_income', 'total_income', 'is_certified',
            'certified_at', 'created_at', 'updated_at'
        ]

    def get_specialties_list(self, obj):
        return obj.get_specialties_list()

    def get_service_areas_list(self, obj):
        return obj.get_service_areas_list()


class MatchmakerClientSerializer(serializers.ModelSerializer):
    """红娘客户序列化器"""
    user_info = UserSerializer(source='user', read_only=True)
    matchmaker_info = serializers.SerializerMethodField()
    service_package_display = serializers.CharField(source='get_service_package_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = MatchmakerClient
        fields = [
            'id', 'user', 'user_info', 'matchmaker_info', 'service_package',
            'service_package_display', 'service_fee', 'status', 'status_display',
            'priority', 'priority_display', 'start_date', 'end_date',
            'last_contact', 'requirements', 'budget', 'notes',
            'satisfaction_rating', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'start_date', 'end_date', 'last_contact',
            'created_at', 'updated_at'
        ]

    def get_matchmaker_info(self, obj):
        return {
            'name': obj.matchmaker.real_name,
            'level': obj.matchmaker.get_level_display(),
            'rating': obj.matchmaker.rating,
            'certified': obj.matchmaker.is_certified
        }


class ServiceRecordSerializer(serializers.ModelSerializer):
    """服务记录序列化器"""
    client_info = serializers.SerializerMethodField(read_only=True)
    service_type_display = serializers.CharField(source='get_service_type_display', read_only=True)

    class Meta:
        model = ServiceRecord
        fields = [
            'id', 'client', 'client_info', 'service_type', 'service_type_display',
            'title', 'content', 'service_date', 'duration', 'result',
            'next_action', 'attachments', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_client_info(self, obj):
        if obj.client:
            return {
                'name': obj.client.user.nickname,
                'avatar': obj.client.user.avatar.url if obj.client.user.avatar else None,
                'status': obj.client.get_status_display()
            }
        return None


class UserPrivacySettingsSerializer(serializers.ModelSerializer):
    """用户隐私设置序列化器"""

    class Meta:
        model = UserPrivacySettings
        fields = [
            'profile_visibility', 'photo_visibility', 'message_permission',
            'voice_call_permission', 'video_call_permission', 'show_distance',
            'show_last_active', 'allow_search_by_phone', 'allow_search_by_wechat',
            'appear_in_recommendations', 'updated_at'
        ]
        read_only_fields = ['updated_at']


class UserNotificationSettingsSerializer(serializers.ModelSerializer):
    """用户通知设置序列化器"""

    class Meta:
        model = UserNotificationSettings
        fields = [
            'new_match_notification', 'like_notification', 'super_like_notification',
            'new_message_notification', 'voice_message_notification',
            'moment_like_notification', 'moment_comment_notification',
            'gift_received_notification', 'activity_notification',
            'promotion_notification', 'system_notification', 'security_notification',
            'quiet_hours_enabled', 'quiet_start_time', 'quiet_end_time', 'updated_at'
        ]
        read_only_fields = ['updated_at']


class UserRecommendationSettingsSerializer(serializers.ModelSerializer):
    """用户推荐偏好设置序列化器"""

    class Meta:
        model = UserRecommendationSettings
        fields = [
            'preferred_gender', 'min_age', 'max_age', 'max_distance',
            'preferred_cities', 'preferred_education', 'preferred_income',
            'preferred_occupations', 'min_height', 'max_height',
            'preferred_body_types', 'smoking_preference', 'drinking_preference',
            'preferred_interests', 'has_children_preference', 'marriage_intention',
            'enable_smart_recommendation', 'recommendation_frequency', 'updated_at'
        ]
        read_only_fields = ['updated_at']


class UserBlacklistSerializer(serializers.ModelSerializer):
    """用户黑名单序列化器"""
    blocked_user = UserProfileSerializer(read_only=True)
    blocked_user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = UserBlacklist
        fields = [
            'id', 'blocked_user', 'blocked_user_id', 'reason', 'note', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class BlockUserSerializer(serializers.Serializer):
    """拉黑用户序列化器"""
    user_id = serializers.IntegerField()
    reason = serializers.ChoiceField(choices=UserBlacklist.BLOCK_REASONS)
    note = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            if user == self.context['request'].user:
                raise serializers.ValidationError("不能拉黑自己")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")


class UserSettingsSerializer(serializers.Serializer):
    """用户设置汇总序列化器"""
    privacy = UserPrivacySettingsSerializer(read_only=True)
    notifications = UserNotificationSettingsSerializer(read_only=True)
    recommendations = UserRecommendationSettingsSerializer(read_only=True)


class VisitorRecordSerializer(serializers.ModelSerializer):
    """访客记录序列化器"""
    visitor = UserProfileSerializer(read_only=True)
    visited_user = UserProfileSerializer(read_only=True)

    class Meta:
        model = VisitorRecord
        fields = [
            'id', 'visitor', 'visited_user', 'visit_count', 'first_visit_at',
            'last_visit_at', 'source', 'duration', 'is_anonymous'
        ]
        read_only_fields = ['id', 'first_visit_at', 'last_visit_at']


class UserAnalyticsSerializer(serializers.ModelSerializer):
    """用户分析数据序列化器"""

    class Meta:
        model = UserAnalytics
        fields = [
            'profile_views', 'profile_likes', 'profile_super_likes',
            'total_matches', 'successful_matches', 'login_days', 'active_hours',
            'last_active_at', 'sent_messages', 'received_messages',
            'published_moments', 'gifts_sent', 'gifts_received',
            'new_friends', 'chat_sessions', 'updated_at'
        ]
        read_only_fields = ['updated_at']


class DailyAnalyticsSerializer(serializers.ModelSerializer):
    """每日分析数据序列化器"""

    class Meta:
        model = DailyAnalytics
        fields = [
            'date', 'profile_views', 'received_likes', 'sent_likes',
            'new_matches', 'messages_sent', 'messages_received',
            'login_count', 'active_minutes', 'created_at'
        ]
        read_only_fields = ['created_at']


class AnalyticsOverviewSerializer(serializers.Serializer):
    """分析概览序列化器"""
    # 基础统计
    profile_views = serializers.IntegerField()
    profile_likes = serializers.IntegerField()
    total_matches = serializers.IntegerField()

    # 今日数据
    today_views = serializers.IntegerField()
    today_likes = serializers.IntegerField()
    today_matches = serializers.IntegerField()

    # 趋势数据（7天）
    views_trend = serializers.ListField(child=serializers.IntegerField())
    likes_trend = serializers.ListField(child=serializers.IntegerField())
    matches_trend = serializers.ListField(child=serializers.IntegerField())

    # 排名信息
    popularity_rank = serializers.IntegerField()
    activity_rank = serializers.IntegerField()


class RecordVisitSerializer(serializers.Serializer):
    """记录访问序列化器"""
    visited_user_id = serializers.IntegerField()
    source = serializers.CharField(max_length=20, default='recommendation')
    duration = serializers.IntegerField(default=0)
    is_anonymous = serializers.BooleanField(default=False)

    def validate_visited_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            if user == self.context['request'].user:
                raise serializers.ValidationError("不能访问自己")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")


class NotificationSerializer(serializers.ModelSerializer):
    """通知消息序列化器"""
    related_user = UserProfileSerializer(read_only=True)

    class Meta:
        model = Notification
        fields = [
            'id', 'type', 'title', 'content', 'related_user',
            'related_object_id', 'related_object_type', 'is_read',
            'extra_data', 'created_at', 'read_at'
        ]
        read_only_fields = ['id', 'created_at', 'read_at']


class SystemMessageSerializer(serializers.ModelSerializer):
    """系统消息序列化器"""

    class Meta:
        model = SystemMessage
        fields = [
            'id', 'title', 'content', 'message_type', 'send_to_all',
            'is_active', 'is_sent', 'send_at', 'created_at'
        ]
        read_only_fields = ['id', 'is_sent', 'created_at']


class NotificationStatsSerializer(serializers.Serializer):
    """通知统计序列化器"""
    total_count = serializers.IntegerField()
    unread_count = serializers.IntegerField()
    today_count = serializers.IntegerField()
    types_count = serializers.DictField()


class MarkNotificationReadSerializer(serializers.Serializer):
    """标记通知已读序列化器"""
    notification_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="通知ID列表，为空则标记所有未读通知"
    )
