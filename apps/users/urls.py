from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from . import views
from .parent_views import ParentProfileViewSet, ChildProfileViewSet
from .matchmaker_views import MatchmakerProfileViewSet, MatchmakerClientViewSet, ServiceRecordViewSet

router = DefaultRouter()
router.register(r'users', views.UserViewSet)
router.register(r'preferences', views.UserPreferenceViewSet, basename='userpreference')

# 家长相关路由
router.register(r'parent-profiles', ParentProfileViewSet, basename='parent-profile')
router.register(r'child-profiles', ChildProfileViewSet, basename='child-profile')

# 红娘相关路由
router.register(r'matchmaker-profiles', MatchmakerProfileViewSet, basename='matchmaker-profile')
router.register(r'matchmaker-clients', MatchmakerClientViewSet, basename='matchmaker-client')
router.register(r'service-records', ServiceRecordViewSet, basename='service-record')

urlpatterns = [
    # 认证相关
    path('auth/login/', views.CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/refresh/', views.CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('auth/register/', views.register, name='register'),
    path('auth/wechat-login/', views.wechat_login, name='wechat_login'),
    path('auth/send-code/', views.send_verification_code, name='send_code'),

    # 用户设置相关
    path('settings/', views.user_settings, name='user_settings'),
    path('settings/privacy/', views.privacy_settings, name='privacy_settings'),
    path('settings/notifications/', views.notification_settings, name='notification_settings'),
    path('settings/recommendations/', views.recommendation_settings, name='recommendation_settings'),
    path('settings/blacklist/', views.blacklist_management, name='blacklist_management'),
    path('settings/blacklist/<int:user_id>/', views.remove_from_blacklist, name='remove_from_blacklist'),
    path('settings/reset/', views.reset_settings, name='reset_settings'),

    # 数据分析相关
    path('analytics/overview/', views.analytics_overview, name='analytics_overview'),
    path('analytics/detail/', views.user_analytics_detail, name='user_analytics_detail'),
    path('analytics/daily/', views.daily_analytics_list, name='daily_analytics_list'),
    path('visitors/', views.visitor_records, name='visitor_records'),
    path('visitors/record/', views.record_visit, name='record_visit'),

    # 通知系统相关
    path('notifications/', views.notification_list, name='notification_list'),
    path('notifications/stats/', views.notification_stats, name='notification_stats'),
    path('notifications/mark-read/', views.mark_notifications_read, name='mark_notifications_read'),
    path('notifications/<int:notification_id>/', views.delete_notification, name='delete_notification'),
    path('notifications/clear/', views.clear_all_notifications, name='clear_all_notifications'),
    path('system-messages/', views.system_messages, name='system_messages'),

    # 新增统计功能API
    path('stats/', views.UserStatsView.as_view(), name='user_stats'),
    path('activity-chart/', views.UserActivityChartView.as_view(), name='user_activity_chart'),
    path('detailed-stats/', views.UserDetailedStatsView.as_view(), name='user_detailed_stats'),

    # 其他接口
    path('', include(router.urls)),
]
