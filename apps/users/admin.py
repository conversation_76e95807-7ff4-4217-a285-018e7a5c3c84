from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import (User, UserPreference, UserAction, UserVerification,
                    ParentProfile, ChildProfile, MatchmakerProfile,
                    MatchmakerClient, ServiceRecord, WechatConfig, AppConfig)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ['id', 'nickname', 'username', 'gender_display', 'age', 'location',
                   'vip_level_display', 'is_verified', 'status_display', 'created_at']
    list_filter = ['gender', 'vip_level', 'is_verified', 'status', 'created_at']
    search_fields = ['nickname', 'username', 'openid', 'location']
    readonly_fields = ['openid', 'unionid', 'created_at', 'updated_at', 'last_active']
    list_per_page = 20  # 分页优化，减少单页加载数据量
    list_max_show_all = 100  # 限制最大显示数量

    def get_queryset(self, request):
        """优化查询性能，减少数据库查询次数"""
        return super().get_queryset(request).select_related().prefetch_related()

    fieldsets = (
        ('基本信息', {
            'fields': ('username', 'nickname', 'openid', 'unionid', 'avatar_display')
        }),
        ('个人资料', {
            'fields': ('gender', 'age', 'birthday', 'location', 'profession', 
                      'education', 'height', 'weight', 'income', 'marriage_status', 'bio')
        }),
        ('认证信息', {
            'fields': ('is_verified', 'verified_at')
        }),
        ('VIP信息', {
            'fields': ('vip_level', 'vip_expire_time')
        }),
        ('积分金币', {
            'fields': ('points', 'coins')
        }),
        ('统计信息', {
            'fields': ('profile_views', 'likes_received', 'likes_sent', 'matches_count')
        }),
        ('状态信息', {
            'fields': ('status', 'is_active', 'is_staff', 'is_superuser')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'last_active', 'last_login')
        }),
    )
    
    def gender_display(self, obj):
        return obj.get_gender_display()
    gender_display.short_description = '性别'
    
    def vip_level_display(self, obj):
        if obj.is_vip:
            return format_html('<span style="color: gold;">⭐ {}</span>', obj.get_vip_level_display())
        return obj.get_vip_level_display()
    vip_level_display.short_description = 'VIP等级'
    
    def status_display(self, obj):
        colors = {1: 'green', 2: 'red', 3: 'gray'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def avatar_display(self, obj):
        if obj.avatar:
            return format_html('<img src="{}" width="50" height="50" style="border-radius: 50%;" />', obj.avatar)
        return '无头像'
    avatar_display.short_description = '头像'


@admin.register(UserPreference)
class UserPreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'min_age', 'max_age', 'preferred_gender_display', 
                   'preferred_location', 'distance_range', 'only_verified']
    list_filter = ['preferred_gender', 'only_verified', 'only_with_photos']
    search_fields = ['user__nickname', 'preferred_location']
    
    def preferred_gender_display(self, obj):
        return obj.get_preferred_gender_display() if obj.preferred_gender else '不限'
    preferred_gender_display.short_description = '偏好性别'


@admin.register(UserAction)
class UserActionAdmin(admin.ModelAdmin):
    list_display = ['user', 'target_user', 'action_type_display', 'created_at']
    list_filter = ['action_type', 'created_at']
    search_fields = ['user__nickname', 'target_user__nickname']
    readonly_fields = ['created_at']
    
    def action_type_display(self, obj):
        colors = {
            'like': 'red',
            'super_like': 'purple',
            'pass': 'gray',
            'visit': 'blue',
            'block': 'black',
            'report': 'orange'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.action_type, 'black'),
            obj.get_action_type_display()
        )
    action_type_display.short_description = '行为类型'


@admin.register(UserVerification)
class UserVerificationAdmin(admin.ModelAdmin):
    list_display = ['user', 'verification_type_display', 'status_display', 'created_at']
    list_filter = ['verification_type', 'status', 'created_at']
    search_fields = ['user__nickname']
    readonly_fields = ['created_at', 'updated_at']
    
    def verification_type_display(self, obj):
        return obj.get_verification_type_display()
    verification_type_display.short_description = '认证类型'
    
    def status_display(self, obj):
        colors = {'pending': 'orange', 'approved': 'green', 'rejected': 'red'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(ParentProfile)
class ParentProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'real_name', 'relationship', 'success_count',
                   'is_verified', 'children_count', 'created_at']
    list_filter = ['relationship', 'is_verified', 'created_at']
    search_fields = ['real_name', 'user__nickname', 'description']
    readonly_fields = ['created_at', 'updated_at', 'verified_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'real_name', 'relationship', 'description')
        }),
        ('统计信息', {
            'fields': ('success_count',)
        }),
        ('认证信息', {
            'fields': ('is_verified', 'verified_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def children_count(self, obj):
        return obj.children.count()
    children_count.short_description = '子女数量'


@admin.register(ChildProfile)
class ChildProfileAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'gender_display', 'age', 'profession',
                   'status_display', 'profile_views', 'matches_count', 'created_at']
    list_filter = ['gender', 'status', 'education', 'marriage_status', 'created_at']
    search_fields = ['name', 'parent__real_name', 'profession', 'location']
    readonly_fields = ['profile_views', 'likes_received', 'matches_count',
                      'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('parent', 'name', 'avatar', 'gender', 'age', 'birthday')
        }),
        ('详细信息', {
            'fields': ('location', 'profession', 'education', 'height', 'weight',
                      'income', 'marriage_status')
        }),
        ('个人介绍', {
            'fields': ('bio', 'photos', 'requirements')
        }),
        ('状态信息', {
            'fields': ('status',)
        }),
        ('统计信息', {
            'fields': ('profile_views', 'likes_received', 'matches_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def gender_display(self, obj):
        return obj.get_gender_display()
    gender_display.short_description = '性别'

    def status_display(self, obj):
        colors = {'active': 'green', 'inactive': 'orange', 'matched': 'blue'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(MatchmakerProfile)
class MatchmakerProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'real_name', 'level_display', 'rating', 'total_clients',
                   'success_rate', 'monthly_income', 'is_certified', 'status_display']
    list_filter = ['level', 'is_certified', 'status', 'created_at']
    search_fields = ['real_name', 'user__nickname', 'company', 'certification_number']
    readonly_fields = ['total_clients', 'active_clients', 'successful_matches',
                      'success_rate', 'monthly_income', 'total_income',
                      'certified_at', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'real_name', 'company', 'experience_years')
        }),
        ('等级评级', {
            'fields': ('level', 'rating', 'specialties')
        }),
        ('统计信息', {
            'fields': ('total_clients', 'active_clients', 'successful_matches',
                      'success_rate', 'monthly_income', 'total_income')
        }),
        ('认证信息', {
            'fields': ('is_certified', 'certified_at', 'certification_number')
        }),
        ('服务设置', {
            'fields': ('service_areas', 'min_fee', 'max_fee', 'status')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def level_display(self, obj):
        colors = {'bronze': 'brown', 'silver': 'gray', 'gold': 'gold', 'diamond': 'blue'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.level, 'black'),
            obj.get_level_display()
        )
    level_display.short_description = '等级'

    def status_display(self, obj):
        colors = {'active': 'green', 'busy': 'orange', 'inactive': 'red'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(MatchmakerClient)
class MatchmakerClientAdmin(admin.ModelAdmin):
    list_display = ['user', 'matchmaker', 'service_package_display', 'service_fee',
                   'status_display', 'priority_display', 'satisfaction_rating', 'start_date']
    list_filter = ['service_package', 'status', 'priority', 'start_date']
    search_fields = ['user__nickname', 'matchmaker__real_name', 'requirements']
    readonly_fields = ['start_date', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('matchmaker', 'user', 'service_package', 'service_fee')
        }),
        ('状态信息', {
            'fields': ('status', 'priority', 'start_date', 'end_date', 'last_contact')
        }),
        ('客户需求', {
            'fields': ('requirements', 'budget')
        }),
        ('服务记录', {
            'fields': ('notes', 'satisfaction_rating')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def service_package_display(self, obj):
        return obj.get_service_package_display()
    service_package_display.short_description = '服务套餐'

    def status_display(self, obj):
        colors = {
            'new': 'blue', 'consulting': 'orange', 'matching': 'purple',
            'matched': 'green', 'completed': 'gray', 'cancelled': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'

    def priority_display(self, obj):
        colors = {'low': 'gray', 'medium': 'orange', 'high': 'red'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.priority, 'black'),
            obj.get_priority_display()
        )
    priority_display.short_description = '优先级'


@admin.register(ServiceRecord)
class ServiceRecordAdmin(admin.ModelAdmin):
    list_display = ['client', 'service_type_display', 'title', 'service_date',
                   'duration', 'created_at']
    list_filter = ['service_type', 'service_date', 'created_at']
    search_fields = ['client__user__nickname', 'title', 'content']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('client', 'service_type', 'title', 'content')
        }),
        ('服务时间', {
            'fields': ('service_date', 'duration')
        }),
        ('服务结果', {
            'fields': ('result', 'next_action')
        }),
        ('附件', {
            'fields': ('attachments',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def service_type_display(self, obj):
        return obj.get_service_type_display()
    service_type_display.short_description = '服务类型'


@admin.register(WechatConfig)
class WechatConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'app_id', 'mch_id', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'app_id', 'mch_id']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'is_active')
        }),
        ('小程序配置', {
            'fields': ('app_id', 'app_secret'),
            'classes': ('collapse',)
        }),
        ('微信支付配置', {
            'fields': ('mch_id', 'mch_key', 'notify_url'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # 如果设置为激活，则将其他配置设为非激活
        if obj.is_active:
            WechatConfig.objects.filter(is_active=True).update(is_active=False)
        super().save_model(request, obj, form, change)


@admin.register(AppConfig)
class AppConfigAdmin(admin.ModelAdmin):
    list_display = ['config_type', 'key', 'value_preview', 'description', 'is_active', 'updated_at']
    list_filter = ['config_type', 'is_active', 'created_at']
    search_fields = ['key', 'value', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('配置信息', {
            'fields': ('config_type', 'key', 'value', 'description', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def value_preview(self, obj):
        """显示配置值预览"""
        if len(obj.value) > 50:
            return obj.value[:50] + '...'
        return obj.value
    value_preview.short_description = '配置值'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # 为value字段添加textarea widget
        form.base_fields['value'].widget.attrs.update({'rows': 4, 'cols': 80})
        return form
