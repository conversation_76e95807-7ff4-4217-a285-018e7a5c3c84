# apps/users/wechat_utils.py - 微信工具类
import requests
import json
import hashlib
import time
from django.conf import settings
from .models import WechatConfig, AppConfig
import logging

logger = logging.getLogger(__name__)


class WechatAPI:
    """微信API工具类"""
    
    def __init__(self):
        self.config = WechatConfig.get_active_config()
        if not self.config:
            raise ValueError("未找到激活的微信配置")
    
    @property
    def app_id(self):
        return self.config.app_id
    
    @property
    def app_secret(self):
        return self.config.app_secret
    
    def get_access_token(self):
        """获取access_token"""
        url = f"https://api.weixin.qq.com/cgi-bin/token"
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'access_token' in data:
                return data['access_token']
            else:
                logger.error(f"获取access_token失败: {data}")
                return None
                
        except Exception as e:
            logger.error(f"获取access_token异常: {e}")
            return None
    
    def code2session(self, js_code):
        """小程序登录凭证校验"""
        url = "https://api.weixin.qq.com/sns/jscode2session"
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'js_code': js_code,
            'grant_type': 'authorization_code'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'openid' in data:
                return {
                    'openid': data.get('openid'),
                    'session_key': data.get('session_key'),
                    'unionid': data.get('unionid')
                }
            else:
                logger.error(f"code2session失败: {data}")
                return None
                
        except Exception as e:
            logger.error(f"code2session异常: {e}")
            return None
    
    def decrypt_data(self, encrypted_data, iv, session_key):
        """解密微信数据"""
        try:
            from Crypto.Cipher import AES
            import base64
            
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            # AES解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            decrypted = decrypted[:-decrypted[-1]]
            
            # 解析JSON
            result = json.loads(decrypted.decode('utf-8'))
            return result
            
        except Exception as e:
            logger.error(f"解密数据失败: {e}")
            return None
    
    def get_phone_number(self, encrypted_data, iv, session_key):
        """获取手机号"""
        decrypted_data = self.decrypt_data(encrypted_data, iv, session_key)
        if decrypted_data:
            return decrypted_data.get('phoneNumber')
        return None


class WechatPayAPI:
    """微信支付API工具类"""
    
    def __init__(self):
        self.config = WechatConfig.get_active_config()
        if not self.config:
            raise ValueError("未找到激活的微信配置")
    
    @property
    def mch_id(self):
        return self.config.mch_id
    
    @property
    def mch_key(self):
        return self.config.mch_key
    
    @property
    def notify_url(self):
        return self.config.notify_url
    
    def generate_sign(self, params):
        """生成微信支付签名"""
        # 排序参数
        sorted_params = sorted(params.items())
        
        # 拼接字符串
        string_a = '&'.join([f"{k}={v}" for k, v in sorted_params if v])
        string_sign_temp = f"{string_a}&key={self.mch_key}"
        
        # MD5加密
        sign = hashlib.md5(string_sign_temp.encode('utf-8')).hexdigest().upper()
        return sign
    
    def create_order(self, order_data):
        """创建支付订单"""
        url = "https://api.mch.weixin.qq.com/pay/unifiedorder"
        
        params = {
            'appid': self.config.app_id,
            'mch_id': self.mch_id,
            'nonce_str': self.generate_nonce_str(),
            'body': order_data.get('body', '商品购买'),
            'out_trade_no': order_data['out_trade_no'],
            'total_fee': order_data['total_fee'],
            'spbill_create_ip': order_data.get('spbill_create_ip', '127.0.0.1'),
            'notify_url': self.notify_url,
            'trade_type': 'JSAPI',
            'openid': order_data['openid']
        }
        
        # 生成签名
        params['sign'] = self.generate_sign(params)
        
        # 转换为XML
        xml_data = self.dict_to_xml(params)
        
        try:
            response = requests.post(url, data=xml_data, timeout=10)
            result = self.xml_to_dict(response.text)
            
            if result.get('return_code') == 'SUCCESS' and result.get('result_code') == 'SUCCESS':
                return {
                    'prepay_id': result['prepay_id'],
                    'code_url': result.get('code_url')
                }
            else:
                logger.error(f"创建支付订单失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"创建支付订单异常: {e}")
            return None
    
    def generate_nonce_str(self):
        """生成随机字符串"""
        import random
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits, k=32))
    
    def dict_to_xml(self, data):
        """字典转XML"""
        xml = ['<xml>']
        for k, v in data.items():
            xml.append(f'<{k}>{v}</{k}>')
        xml.append('</xml>')
        return ''.join(xml)
    
    def xml_to_dict(self, xml_str):
        """XML转字典"""
        import xml.etree.ElementTree as ET
        root = ET.fromstring(xml_str)
        result = {}
        for child in root:
            result[child.tag] = child.text
        return result


class ConfigManager:
    """配置管理器"""

    @staticmethod
    def get_wechat_config():
        """获取微信配置"""
        # 首先尝试从SystemConfig获取
        try:
            from apps.system.models import SystemConfig

            # 获取微信配置项
            wechat_configs = SystemConfig.objects.filter(
                config_type='wechat',
                is_active=True
            ).values('key', 'value')

            if wechat_configs:
                config_dict = {}
                for config in wechat_configs:
                    # 移除前缀 'wechat_'
                    key = config['key'].replace('wechat_', '')
                    config_dict[key] = config['value']

                return {
                    'app_id': config_dict.get('app_id', ''),
                    'app_secret': config_dict.get('app_secret', ''),
                    'mch_id': config_dict.get('mch_id', ''),
                    'mch_key': config_dict.get('mch_key', ''),
                    'notify_url': config_dict.get('notify_url', '')
                }
        except ImportError:
            pass

        # 回退到原始的WechatConfig
        config = WechatConfig.get_active_config()
        if config:
            return {
                'app_id': config.app_id,
                'app_secret': config.app_secret,
                'mch_id': config.mch_id,
                'mch_key': config.mch_key,
                'notify_url': config.notify_url
            }
        return None

    @staticmethod
    def get_app_config(config_type, key, default=None):
        """获取应用配置"""
        # 首先尝试从SystemConfig获取
        try:
            from apps.system.models import SystemConfig

            system_key = f'{config_type}_{key}'
            try:
                config = SystemConfig.objects.get(
                    key=system_key,
                    is_active=True
                )
                return config.value
            except SystemConfig.DoesNotExist:
                pass
        except ImportError:
            pass

        # 回退到原始的AppConfig
        return AppConfig.get_config(config_type, key, default)

    @staticmethod
    def set_app_config(config_type, key, value, description=''):
        """设置应用配置"""
        # 优先设置到SystemConfig
        try:
            from apps.system.models import SystemConfig

            system_key = f'{config_type}_{key}'
            config, created = SystemConfig.objects.get_or_create(
                key=system_key,
                defaults={
                    'value': value,
                    'config_type': config_type,
                    'description': description,
                    'is_active': True,
                    'is_public': False,
                }
            )
            if not created:
                config.value = value
                config.description = description
                config.save()
            return config
        except ImportError:
            pass

        # 回退到原始的AppConfig
        return AppConfig.set_config(config_type, key, value, description)
    
    @staticmethod
    def init_default_configs():
        """初始化默认配置"""
        default_configs = [
            ('system', 'app_name', '相亲交友', '应用名称'),
            ('system', 'app_version', '1.0.0', '应用版本'),
            ('system', 'max_upload_size', '10485760', '最大上传文件大小(字节)'),
            ('wechat', 'login_required', 'false', '是否强制登录'),
            ('payment', 'enable_payment', 'true', '是否启用支付功能'),
            ('sms', 'enable_sms', 'false', '是否启用短信功能'),
        ]
        
        for config_type, key, value, description in default_configs:
            AppConfig.set_config(config_type, key, value, description)
        
        logger.info("默认配置初始化完成")


# 全局实例
def get_wechat_api():
    """获取微信API实例"""
    try:
        return WechatAPI()
    except ValueError as e:
        logger.warning(f"获取微信API失败: {e}")
        return None


def get_wechat_pay_api():
    """获取微信支付API实例"""
    try:
        return WechatPayAPI()
    except ValueError as e:
        logger.warning(f"获取微信支付API失败: {e}")
        return None
