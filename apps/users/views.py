from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.serializers import TokenRefreshSerializer
from django.contrib.auth import authenticate
from django.db.models import Q
from .models import User, UserPreference, UserAction, UserVerification
from .serializers import (
    UserSerializer, UserProfileSerializer, UserPreferenceSerializer,
    UserActionSerializer, UserVerificationSerializer, WeChatLoginSerializer,
    UserUpdateSerializer, PhotoUploadSerializer, UserStatsSerializer,
    VisitorRecordSerializer, UserRegistrationSerializer
)
import requests
import json


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def wechat_login(request):
    """微信小程序登录"""
    serializer = WeChatLoginSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    code = serializer.validated_data['code']
    
    # 调用微信API获取openid
    from django.conf import settings
    wechat_config = settings.WECHAT_MINI_PROGRAM
    
    url = 'https://api.weixin.qq.com/sns/jscode2session'
    params = {
        'appid': wechat_config['APP_ID'],
        'secret': wechat_config['APP_SECRET'],
        'js_code': code,
        'grant_type': 'authorization_code'
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            return Response({
                'code': 400,
                'message': '微信登录失败',
                'error': data.get('errmsg', '未知错误')
            }, status=status.HTTP_400_BAD_REQUEST)
        
        openid = data['openid']
        session_key = data.get('session_key')
        unionid = data.get('unionid')
        
        # 查找或创建用户
        user, created = User.objects.get_or_create(
            openid=openid,
            defaults={
                'username': openid,
                'unionid': unionid,
                'nickname': f'用户{openid[-6:]}'
            }
        )
        
        # 生成JWT token
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        return Response({
            'code': 200,
            'message': '登录成功',
            'data': {
                'token': str(access_token),
                'refresh_token': str(refresh),
                'user_info': UserSerializer(user).data,
                'is_new_user': created
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': '服务器错误',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserViewSet(ModelViewSet):
    """用户视图集"""
    queryset = User.objects.filter(status=1)
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return UserProfileSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        if self.action == 'list':
            # 排除当前用户和已拉黑的用户
            queryset = queryset.exclude(id=self.request.user.id)
            # 可以添加更多筛选条件
        return queryset
    
    @drf_action(detail=False, methods=['get'])
    def profile(self, request):
        """获取当前用户资料"""
        serializer = UserSerializer(request.user)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    @drf_action(detail=False, methods=['put'])
    def update_profile(self, request):
        """更新用户资料"""
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'code': 200,
                'message': '更新成功',
                'data': UserSerializer(request.user).data
            })
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @drf_action(detail=False, methods=['post'])
    def upload_photo(self, request):
        """上传照片"""
        serializer = PhotoUploadSerializer(data=request.data)
        if serializer.is_valid():
            photo = serializer.validated_data['photo']
            
            # 这里应该上传到云存储，暂时返回模拟URL
            photo_url = f"https://example.com/photos/{photo.name}"
            
            # 添加到用户相册
            request.user.add_photo(photo_url)
            
            return Response({
                'code': 200,
                'message': '上传成功',
                'data': {'photo_url': photo_url}
            })
        
        return Response({
            'code': 400,
            'message': '上传失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @drf_action(detail=False, methods=['delete'])
    def delete_photo(self, request):
        """删除照片"""
        photo_url = request.data.get('photo_url')
        if not photo_url:
            return Response({
                'code': 400,
                'message': '缺少photo_url参数'
            }, status=status.HTTP_400_BAD_REQUEST)

        request.user.remove_photo(photo_url)

        return Response({
            'code': 200,
            'message': '删除成功'
        })

    @drf_action(detail=False, methods=['get'])
    def stats(self, request):
        """获取用户统计信息"""
        user = request.user
        
        # 计算资料完整度
        completion_fields = [
            'nickname', 'avatar', 'gender', 'age', 'location',
            'profession', 'education', 'bio'
        ]
        completed_fields = sum(1 for field in completion_fields if getattr(user, field))
        completion_rate = (completed_fields / len(completion_fields)) * 100
        
        # 今日统计（这里需要实现具体的统计逻辑）
        today_views = 0  # 从统计表获取
        today_likes = 0  # 从统计表获取
        today_matches = 0  # 从统计表获取
        
        stats_data = {
            'profile_views': user.profile_views,
            'likes_received': user.likes_received,
            'likes_sent': user.likes_sent,
            'matches_count': user.matches_count,
            'today_views': today_views,
            'today_likes': today_likes,
            'today_matches': today_matches,
            'completion_rate': round(completion_rate, 1)
        }
        
        serializer = UserStatsSerializer(stats_data)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    @drf_action(detail=False, methods=['get'])
    def visitors(self, request):
        """获取访客记录"""
        visitors = UserAction.objects.filter(
            target_user=request.user,
            action_type='visit'
        ).select_related('user').order_by('-created_at')[:50]

        serializer = VisitorRecordSerializer(visitors, many=True)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    @drf_action(detail=True, methods=['post'])
    def visit(self, request, pk=None):
        """访问用户资料"""
        target_user = self.get_object()
        
        # 记录访问行为
        UserAction.objects.get_or_create(
            user=request.user,
            target_user=target_user,
            action_type='visit',
            defaults={'message': ''}
        )
        
        # 增加访问次数
        target_user.profile_views += 1
        target_user.save()
        
        return Response({
            'code': 200,
            'message': '访问记录成功'
        })


class UserPreferenceViewSet(ModelViewSet):
    """用户偏好视图集"""
    serializer_class = UserPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserPreference.objects.filter(user=self.request.user)
    
    def get_object(self):
        preference, created = UserPreference.objects.get_or_create(
            user=self.request.user
        )
        return preference
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
    
    @drf_action(detail=False, methods=['get'])
    def my_preference(self, request):
        """获取我的偏好设置"""
        preference = self.get_object()
        serializer = self.get_serializer(preference)
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })

    @drf_action(detail=False, methods=['put'])
    def update_preference(self, request):
        """更新偏好设置"""
        preference = self.get_object()
        serializer = self.get_serializer(preference, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'code': 200,
                'message': '更新成功',
                'data': serializer.data
            })
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([])
def register(request):
    """用户注册"""
    serializer = UserRegistrationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    # 验证验证码（这里简化处理，实际应该验证真实的验证码）
    verification_code = serializer.validated_data.get('verification_code')
    if verification_code != '123456':  # 测试验证码
        return Response({
            'code': 400,
            'message': '验证码错误'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 创建用户
    user = serializer.save()

    # 生成JWT token
    refresh = RefreshToken.for_user(user)

    return Response({
        'code': 200,
        'message': '注册成功',
        'data': {
            'user': UserSerializer(user).data,
            'access': str(refresh.access_token),
            'refresh': str(refresh)
        }
    })


@api_view(['POST'])
@permission_classes([])
def send_verification_code(request):
    """发送验证码"""
    phone = request.data.get('phone')

    if not phone:
        return Response({
            'code': 400,
            'message': '手机号不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 这里应该调用短信服务发送验证码
    # 暂时返回成功

    return Response({
        'code': 200,
        'message': '验证码已发送'
    })


class CustomTokenObtainPairView(TokenObtainPairView):
    """自定义JWT登录视图"""

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # 获取用户信息
            username = request.data.get('username')
            user = User.objects.filter(username=username).first()

            if user:
                return Response({
                    'code': 200,
                    'message': '登录成功',
                    'data': {
                        'user': UserSerializer(user).data,
                        'access': response.data['access'],
                        'refresh': response.data['refresh']
                    }
                })

        return Response({
            'code': 400,
            'message': '用户名或密码错误'
        }, status=status.HTTP_400_BAD_REQUEST)


class CustomTokenRefreshView(TokenRefreshView):
    """自定义令牌刷新视图"""

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # 重新格式化响应以保持一致性
            return Response({
                'code': 200,
                'message': '令牌刷新成功',
                'data': {
                    'access': response.data['access'],
                    'refresh': response.data.get('refresh')  # 可能没有新的refresh token
                }
            })

        return Response({
            'code': 400,
            'message': '令牌刷新失败'
        }, status=status.HTTP_400_BAD_REQUEST)


# 用户设置相关视图
from .models import (UserPrivacySettings, UserNotificationSettings, UserRecommendationSettings,
                    UserBlacklist, VisitorRecord, UserAnalytics, DailyAnalytics, Notification, SystemMessage)
from .serializers import (
    UserPrivacySettingsSerializer, UserNotificationSettingsSerializer,
    UserRecommendationSettingsSerializer, UserBlacklistSerializer,
    BlockUserSerializer, UserSettingsSerializer, VisitorRecordSerializer,
    UserAnalyticsSerializer, DailyAnalyticsSerializer, AnalyticsOverviewSerializer,
    RecordVisitSerializer, NotificationSerializer, SystemMessageSerializer,
    NotificationStatsSerializer, MarkNotificationReadSerializer
)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_settings(request):
    """获取用户所有设置"""
    user = request.user

    # 获取或创建各种设置
    privacy_settings, _ = UserPrivacySettings.objects.get_or_create(user=user)
    notification_settings, _ = UserNotificationSettings.objects.get_or_create(user=user)
    recommendation_settings, _ = UserRecommendationSettings.objects.get_or_create(user=user)

    data = {
        'privacy': UserPrivacySettingsSerializer(privacy_settings).data,
        'notifications': UserNotificationSettingsSerializer(notification_settings).data,
        'recommendations': UserRecommendationSettingsSerializer(recommendation_settings).data
    }

    return Response(data)


@api_view(['GET', 'PUT'])
@permission_classes([permissions.IsAuthenticated])
def privacy_settings(request):
    """隐私设置"""
    settings, created = UserPrivacySettings.objects.get_or_create(user=request.user)

    if request.method == 'GET':
        serializer = UserPrivacySettingsSerializer(settings)
        return Response(serializer.data)

    elif request.method == 'PUT':
        serializer = UserPrivacySettingsSerializer(settings, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'PUT'])
@permission_classes([permissions.IsAuthenticated])
def notification_settings(request):
    """通知设置"""
    settings, created = UserNotificationSettings.objects.get_or_create(user=request.user)

    if request.method == 'GET':
        serializer = UserNotificationSettingsSerializer(settings)
        return Response(serializer.data)

    elif request.method == 'PUT':
        serializer = UserNotificationSettingsSerializer(settings, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'PUT'])
@permission_classes([permissions.IsAuthenticated])
def recommendation_settings(request):
    """推荐偏好设置"""
    settings, created = UserRecommendationSettings.objects.get_or_create(user=request.user)

    if request.method == 'GET':
        serializer = UserRecommendationSettingsSerializer(settings)
        return Response(serializer.data)

    elif request.method == 'PUT':
        serializer = UserRecommendationSettingsSerializer(settings, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAuthenticated])
def blacklist_management(request):
    """黑名单管理"""
    if request.method == 'GET':
        # 获取黑名单列表
        blacklist = UserBlacklist.objects.filter(user=request.user).order_by('-created_at')

        # 分页
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        start = (page - 1) * page_size
        end = start + page_size

        paginated_blacklist = blacklist[start:end]
        serializer = UserBlacklistSerializer(paginated_blacklist, many=True)

        return Response({
            'results': serializer.data,
            'has_more': len(blacklist) > end
        })

    elif request.method == 'POST':
        # 添加到黑名单
        serializer = BlockUserSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            try:
                # 检查是否已经在黑名单中
                if UserBlacklist.objects.filter(
                    user=request.user,
                    blocked_user_id=serializer.validated_data['user_id']
                ).exists():
                    return Response({'error': '用户已在黑名单中'}, status=status.HTTP_400_BAD_REQUEST)

                # 创建黑名单记录
                blacklist_record = UserBlacklist.objects.create(
                    user=request.user,
                    blocked_user_id=serializer.validated_data['user_id'],
                    reason=serializer.validated_data['reason'],
                    note=serializer.validated_data.get('note', '')
                )

                return Response(
                    UserBlacklistSerializer(blacklist_record).data,
                    status=status.HTTP_201_CREATED
                )

            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_from_blacklist(request, user_id):
    """从黑名单中移除用户"""
    try:
        blacklist_record = UserBlacklist.objects.get(
            user=request.user,
            blocked_user_id=user_id
        )
        blacklist_record.delete()
        return Response({'message': '已从黑名单中移除'})

    except UserBlacklist.DoesNotExist:
        return Response({'error': '用户不在黑名单中'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reset_settings(request):
    """重置设置到默认值"""
    setting_type = request.data.get('type', 'all')

    try:
        if setting_type in ['all', 'privacy']:
            UserPrivacySettings.objects.filter(user=request.user).delete()

        if setting_type in ['all', 'notifications']:
            UserNotificationSettings.objects.filter(user=request.user).delete()

        if setting_type in ['all', 'recommendations']:
            UserRecommendationSettings.objects.filter(user=request.user).delete()

        return Response({'message': '设置已重置为默认值'})

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


# 数据分析相关视图
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models import Sum, Count, Q


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def visitor_records(request):
    """获取访客记录"""
    user = request.user

    # 获取访客记录
    visitors = VisitorRecord.objects.filter(visited_user=user).order_by('-last_visit_at')

    # 分页
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))
    start = (page - 1) * page_size
    end = start + page_size

    paginated_visitors = visitors[start:end]

    # 检查用户是否有VIP权限查看访客
    can_view_visitors = hasattr(user, 'vip_records') and user.vip_records.filter(
        end_time__gt=timezone.now()
    ).exists()

    if can_view_visitors:
        serializer = VisitorRecordSerializer(paginated_visitors, many=True)
        visitor_data = serializer.data
    else:
        # 非VIP用户只能看到模糊信息
        visitor_data = []
        for visitor in paginated_visitors:
            visitor_data.append({
                'id': visitor.id,
                'visitor': {
                    'id': 0,
                    'nickname': '神秘访客',
                    'avatar': '/images/avatar/mystery.png'
                },
                'last_visit_at': visitor.last_visit_at,
                'visit_count': visitor.visit_count
            })

    return Response({
        'results': visitor_data,
        'has_more': len(visitors) > end,
        'can_view_details': can_view_visitors,
        'total_visitors': visitors.count()
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def record_visit(request):
    """记录访问"""
    serializer = RecordVisitSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        try:
            visited_user_id = serializer.validated_data['visited_user_id']
            source = serializer.validated_data['source']
            duration = serializer.validated_data['duration']
            is_anonymous = serializer.validated_data['is_anonymous']

            # 获取或创建访客记录
            visitor_record, created = VisitorRecord.objects.get_or_create(
                visitor=request.user,
                visited_user_id=visited_user_id,
                defaults={
                    'source': source,
                    'duration': duration,
                    'is_anonymous': is_anonymous
                }
            )

            if not created:
                # 更新访问记录
                visitor_record.visit_count += 1
                visitor_record.last_visit_at = timezone.now()
                visitor_record.duration = max(visitor_record.duration, duration)
                visitor_record.save()

            # 更新被访问用户的分析数据
            analytics, created = UserAnalytics.objects.get_or_create(
                user_id=visited_user_id,
                defaults={'profile_views': 0}
            )
            analytics.profile_views += 1
            analytics.save()

            # 更新每日分析数据
            today = timezone.now().date()
            daily_analytics, created = DailyAnalytics.objects.get_or_create(
                user_id=visited_user_id,
                date=today,
                defaults={'profile_views': 0}
            )
            daily_analytics.profile_views += 1
            daily_analytics.save()

            return Response({'message': '访问记录已保存'})

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def analytics_overview(request):
    """分析概览"""
    user = request.user

    try:
        # 获取或创建用户分析数据
        analytics, created = UserAnalytics.objects.get_or_create(
            user=user,
            defaults={
                'profile_views': 0, 'profile_likes': 0, 'total_matches': 0,
                'login_days': 0, 'active_hours': 0, 'sent_messages': 0,
                'received_messages': 0, 'published_moments': 0,
                'gifts_sent': 0, 'gifts_received': 0, 'new_friends': 0,
                'chat_sessions': 0
            }
        )

        # 获取今日数据
        today = timezone.now().date()
        today_analytics = DailyAnalytics.objects.filter(user=user, date=today).first()

        today_views = today_analytics.profile_views if today_analytics else 0
        today_likes = today_analytics.received_likes if today_analytics else 0
        today_matches = today_analytics.new_matches if today_analytics else 0

        # 获取7天趋势数据
        seven_days_ago = today - timedelta(days=6)
        daily_data = DailyAnalytics.objects.filter(
            user=user,
            date__gte=seven_days_ago,
            date__lte=today
        ).order_by('date')

        # 构建趋势数据
        views_trend = []
        likes_trend = []
        matches_trend = []

        for i in range(7):
            date = seven_days_ago + timedelta(days=i)
            day_data = daily_data.filter(date=date).first()

            views_trend.append(day_data.profile_views if day_data else 0)
            likes_trend.append(day_data.received_likes if day_data else 0)
            matches_trend.append(day_data.new_matches if day_data else 0)

        # 计算排名（模拟）
        popularity_rank = min(100, max(1, 100 - (analytics.profile_views + analytics.profile_likes) // 10))
        activity_rank = min(100, max(1, 100 - (analytics.login_days + analytics.sent_messages // 10)))

        data = {
            'profile_views': analytics.profile_views,
            'profile_likes': analytics.profile_likes,
            'total_matches': analytics.total_matches,
            'today_views': today_views,
            'today_likes': today_likes,
            'today_matches': today_matches,
            'views_trend': views_trend,
            'likes_trend': likes_trend,
            'matches_trend': matches_trend,
            'popularity_rank': popularity_rank,
            'activity_rank': activity_rank
        }

        return Response(data)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_analytics_detail(request):
    """用户详细分析数据"""
    user = request.user

    try:
        analytics, created = UserAnalytics.objects.get_or_create(user=user)
        serializer = UserAnalyticsSerializer(analytics)
        return Response(serializer.data)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def daily_analytics_list(request):
    """每日分析数据列表"""
    user = request.user

    try:
        # 获取最近30天的数据
        thirty_days_ago = timezone.now().date() - timedelta(days=29)
        daily_data = DailyAnalytics.objects.filter(
            user=user,
            date__gte=thirty_days_ago
        ).order_by('-date')

        serializer = DailyAnalyticsSerializer(daily_data, many=True)
        return Response(serializer.data)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


# 通知系统相关视图
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def notification_list(request):
    """获取通知列表"""
    user = request.user

    try:
        # 获取查询参数
        notification_type = request.GET.get('type')
        is_read = request.GET.get('is_read')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 构建查询
        notifications = Notification.objects.filter(
            user=user,
            is_deleted=False
        )

        if notification_type:
            notifications = notifications.filter(type=notification_type)

        if is_read is not None:
            notifications = notifications.filter(is_read=is_read.lower() == 'true')

        notifications = notifications.order_by('-created_at')

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        paginated_notifications = notifications[start:end]

        serializer = NotificationSerializer(paginated_notifications, many=True)

        return Response({
            'results': serializer.data,
            'has_more': len(notifications) > end,
            'total': notifications.count()
        })

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def notification_stats(request):
    """获取通知统计"""
    user = request.user

    try:
        notifications = Notification.objects.filter(user=user, is_deleted=False)

        total_count = notifications.count()
        unread_count = notifications.filter(is_read=False).count()

        # 今日通知数
        today = timezone.now().date()
        today_count = notifications.filter(created_at__date=today).count()

        # 按类型统计
        types_count = {}
        for notification_type, _ in Notification.NOTIFICATION_TYPES:
            count = notifications.filter(type=notification_type).count()
            if count > 0:
                types_count[notification_type] = count

        data = {
            'total_count': total_count,
            'unread_count': unread_count,
            'today_count': today_count,
            'types_count': types_count
        }

        return Response(data)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notifications_read(request):
    """标记通知为已读"""
    serializer = MarkNotificationReadSerializer(data=request.data)
    if serializer.is_valid():
        try:
            notification_ids = serializer.validated_data.get('notification_ids')

            if notification_ids:
                # 标记指定通知为已读
                notifications = Notification.objects.filter(
                    id__in=notification_ids,
                    user=request.user,
                    is_read=False
                )
            else:
                # 标记所有未读通知为已读
                notifications = Notification.objects.filter(
                    user=request.user,
                    is_read=False
                )

            updated_count = 0
            for notification in notifications:
                notification.mark_as_read()
                updated_count += 1

            return Response({
                'message': f'已标记 {updated_count} 条通知为已读',
                'updated_count': updated_count
            })

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def delete_notification(request, notification_id):
    """删除通知"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.is_deleted = True
        notification.save(update_fields=['is_deleted'])

        return Response({'message': '通知已删除'})

    except Notification.DoesNotExist:
        return Response({'error': '通知不存在'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def clear_all_notifications(request):
    """清空所有通知"""
    try:
        updated_count = Notification.objects.filter(
            user=request.user,
            is_deleted=False
        ).update(is_deleted=True)

        return Response({
            'message': f'已清空 {updated_count} 条通知',
            'cleared_count': updated_count
        })

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_messages(request):
    """获取系统消息"""
    try:
        messages = SystemMessage.objects.filter(
            is_active=True,
            is_sent=True
        ).filter(
            Q(send_to_all=True) | Q(target_users=request.user)
        ).order_by('-send_at')

        # 分页
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        start = (page - 1) * page_size
        end = start + page_size

        paginated_messages = messages[start:end]
        serializer = SystemMessageSerializer(paginated_messages, many=True)

        return Response({
            'results': serializer.data,
            'has_more': len(messages) > end
        })

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


# ==================== 新增统计功能API ====================

class UserStatsView(APIView):
    """用户统计数据API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户统计数据"""
        user = request.user

        try:
            # 获取或创建用户统计记录
            stats, created = UserStatistics.objects.get_or_create(
                user=user,
                defaults={
                    'today_recommendations': 156,
                    'today_likes_received': 23,
                    'today_matches': 12,
                    'today_messages': 8,
                    'today_profile_views': 45,
                    'monthly_likes_received': 245,
                    'monthly_matches': 89,
                    'monthly_visitors': 156,
                    'monthly_messages': 234,
                    'total_likes_received': 1234,
                    'total_matches': 456,
                    'total_profile_views': 2345,
                    'activity_score': Decimal('4.8'),
                    'profile_completeness': 85
                }
            )

            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'today': {
                        'recommendations': stats.today_recommendations,
                        'likes_received': stats.today_likes_received,
                        'matches': stats.today_matches,
                        'messages': stats.today_messages,
                        'profile_views': stats.today_profile_views
                    },
                    'monthly': {
                        'likes_received': stats.monthly_likes_received,
                        'matches': stats.monthly_matches,
                        'visitors': stats.monthly_visitors,
                        'messages': stats.monthly_messages
                    },
                    'total': {
                        'likes_received': stats.total_likes_received,
                        'matches': stats.total_matches,
                        'profile_views': stats.total_profile_views
                    },
                    'activity_score': float(stats.activity_score),
                    'profile_completeness': stats.profile_completeness
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取统计数据失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserActivityChartView(APIView):
    """用户活跃度图表数据API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户活跃度图表数据"""
        user = request.user
        period = request.GET.get('period', 'week')

        try:
            if period == 'week':
                # 获取最近7天的活跃度数据
                end_date = date.today()
                start_date = end_date - timedelta(days=6)

                # 模拟数据 - 实际应该从UserActivityLog获取
                weekly_activity = [
                    {"day": "周一", "count": 12, "percentage": 60},
                    {"day": "周二", "count": 8, "percentage": 40},
                    {"day": "周三", "count": 15, "percentage": 75},
                    {"day": "周四", "count": 10, "percentage": 50},
                    {"day": "周五", "count": 18, "percentage": 90},
                    {"day": "周六", "count": 20, "percentage": 100},
                    {"day": "周日", "count": 16, "percentage": 80}
                ]

                return Response({
                    'code': 200,
                    'data': {
                        'weekly_activity': weekly_activity
                    }
                })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取活跃度数据失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserDetailedStatsView(APIView):
    """用户详细统计数据API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户详细统计数据"""
        user = request.user

        try:
            return Response({
                'code': 200,
                'data': {
                    'monthly_likes': 45,
                    'monthly_matches': 12,
                    'monthly_visitors': 89,
                    'profile_completeness': 95,
                    'activity_score': 4.8,
                    'ranking': {
                        'likes_rank': 156,
                        'activity_rank': 89,
                        'total_users': 10000
                    }
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取详细统计失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
