from django.contrib import admin
from django.utils.html import format_html
from .models import (Report, Blacklist, UserPunishment, AppealRecord, 
                    ContentModeration, SecurityLog)


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = ['id', 'reporter', 'reported_user', 'content_type_display', 
                   'report_type_display', 'status_display', 'admin_user', 'created_at']
    list_filter = ['content_type', 'report_type', 'status', 'created_at']
    search_fields = ['reporter__nickname', 'reported_user__nickname', 'reason']
    readonly_fields = ['created_at', 'updated_at', 'resolved_at']
    
    fieldsets = (
        ('举报信息', {
            'fields': ('reporter', 'reported_user', 'content_type', 'content_id')
        }),
        ('举报详情', {
            'fields': ('report_type', 'reason', 'evidence')
        }),
        ('处理信息', {
            'fields': ('status', 'admin_user', 'admin_note', 'action_taken')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'resolved_at')
        }),
    )
    
    def content_type_display(self, obj):
        return obj.get_content_type_display()
    content_type_display.short_description = '内容类型'
    
    def report_type_display(self, obj):
        return obj.get_report_type_display()
    report_type_display.short_description = '举报类型'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'processing': 'blue',
            'resolved': 'green',
            'rejected': 'red',
            'closed': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '处理状态'


@admin.register(Blacklist)
class BlacklistAdmin(admin.ModelAdmin):
    list_display = ['user', 'blocked_user', 'reason', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__nickname', 'blocked_user__nickname', 'reason']
    readonly_fields = ['created_at']


@admin.register(UserPunishment)
class UserPunishmentAdmin(admin.ModelAdmin):
    list_display = ['user', 'punishment_type_display', 'status_display', 
                   'start_time', 'end_time', 'admin_user', 'is_active_display']
    list_filter = ['punishment_type', 'status', 'start_time']
    search_fields = ['user__nickname', 'reason', 'description']
    readonly_fields = ['start_time', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'punishment_type', 'reason', 'description')
        }),
        ('关联信息', {
            'fields': ('related_report', 'admin_user')
        }),
        ('时间信息', {
            'fields': ('start_time', 'end_time', 'status')
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def punishment_type_display(self, obj):
        colors = {
            'warning': 'orange',
            'mute': 'blue',
            'ban': 'red',
            'permanent_ban': 'darkred',
            'content_removal': 'purple',
            'feature_restriction': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.punishment_type, 'black'),
            obj.get_punishment_type_display()
        )
    punishment_type_display.short_description = '处罚类型'
    
    def status_display(self, obj):
        colors = {'active': 'green', 'expired': 'gray', 'revoked': 'blue'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def is_active_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓</span>')
        else:
            return format_html('<span style="color: red;">✗</span>')
    is_active_display.short_description = '是否生效'


@admin.register(AppealRecord)
class AppealRecordAdmin(admin.ModelAdmin):
    list_display = ['user', 'punishment', 'status_display', 'admin_user', 
                   'created_at', 'resolved_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__nickname', 'reason', 'admin_response']
    readonly_fields = ['created_at', 'updated_at', 'resolved_at']
    
    fieldsets = (
        ('申诉信息', {
            'fields': ('user', 'punishment', 'reason', 'evidence')
        }),
        ('处理信息', {
            'fields': ('status', 'admin_user', 'admin_response')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'resolved_at')
        }),
    )
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'processing': 'blue',
            'approved': 'green',
            'rejected': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '申诉状态'


@admin.register(ContentModeration)
class ContentModerationAdmin(admin.ModelAdmin):
    list_display = ['content_type_display', 'user', 'status_display', 
                   'moderator', 'auto_score', 'created_at', 'reviewed_at']
    list_filter = ['content_type', 'status', 'created_at']
    search_fields = ['user__nickname', 'moderator__nickname', 'rejection_reason']
    readonly_fields = ['auto_score', 'auto_tags', 'created_at', 'reviewed_at']
    
    fieldsets = (
        ('内容信息', {
            'fields': ('content_type', 'content_id', 'user')
        }),
        ('审核信息', {
            'fields': ('status', 'moderator', 'rejection_reason', 'moderator_note')
        }),
        ('自动审核', {
            'fields': ('auto_score', 'auto_tags')
        }),
        ('时间信息', {
            'fields': ('created_at', 'reviewed_at')
        }),
    )
    
    def content_type_display(self, obj):
        return obj.get_content_type_display()
    content_type_display.short_description = '内容类型'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'approved': 'green',
            'rejected': 'red',
            'flagged': 'purple'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '审核状态'


@admin.register(SecurityLog)
class SecurityLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'log_type_display', 'risk_level_display', 
                   'ip_address', 'created_at']
    list_filter = ['log_type', 'risk_level', 'created_at']
    search_fields = ['user__nickname', 'description', 'ip_address']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'log_type', 'description', 'risk_level')
        }),
        ('详细信息', {
            'fields': ('details', 'ip_address', 'user_agent')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def log_type_display(self, obj):
        return obj.get_log_type_display()
    log_type_display.short_description = '日志类型'
    
    def risk_level_display(self, obj):
        colors = {
            'low': 'green',
            'medium': 'orange',
            'high': 'red',
            'critical': 'darkred'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.risk_level, 'black'),
            obj.get_risk_level_display()
        )
    risk_level_display.short_description = '风险等级'
