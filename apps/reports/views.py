from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>, date
import json

from .models import (
    Report, SecurityLog, UserPunishment, AppealRecord,
    Blacklist, ContentModeration
)
from .serializers import (
    ReportSerializer, CreateReportSerializer, SecurityLogSerializer,
    UserPunishmentSerializer, AppealRecordSerializer, CreateAppealSerializer,
    BlacklistSerializer, AddToBlacklistSerializer, ContentModerationSerializer,
    SecuritySettingsSerializer, VerificationRequestSerializer,
    RiskAssessmentSerializer, SafetyTipSerializer, SecurityReportSerializer,
    UserSafetyScoreSerializer
)


class ReportViewSet(ModelViewSet):
    """举报视图集"""
    serializer_class = ReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的举报记录"""
        return Report.objects.filter(reporter=self.request.user).order_by('-created_at')
    
    @drf_action(detail=False, methods=['post'])
    def create_report(self, request):
        """创建举报"""
        serializer = CreateReportSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查是否重复举报
        existing_report = Report.objects.filter(
            reporter=request.user,
            report_type=serializer.validated_data['report_type'],
            target_id=serializer.validated_data['target_id']
        ).first()
        
        if existing_report:
            return Response({
                'code': 400,
                'message': '您已经举报过该内容'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建举报记录
        from apps.users.models import User
        reported_user = User.objects.get(id=serializer.validated_data['reported_user_id'])
        
        report = Report.objects.create(
            reporter=request.user,
            reported_user=reported_user,
            report_type=serializer.validated_data['report_type'],
            target_id=serializer.validated_data['target_id'],
            reason=serializer.validated_data['reason'],
            description=serializer.validated_data['description'],
            evidence_images=serializer.validated_data.get('evidence_images', [])
        )
        
        # 记录安全日志
        SecurityLog.objects.create(
            user=request.user,
            action_type='report',
            description=f'举报{serializer.validated_data["report_type"]}',
            extra_data={
                'report_id': report.id,
                'target_id': serializer.validated_data['target_id'],
                'reason': serializer.validated_data['reason']
            }
        )
        
        report_serializer = ReportSerializer(report)
        
        return Response({
            'code': 200,
            'message': '举报成功，我们会尽快处理',
            'data': report_serializer.data
        })
    
    @drf_action(detail=False, methods=['get'])
    def my_reports(self, request):
        """获取我的举报记录"""
        reports = self.get_queryset()
        serializer = ReportSerializer(reports, many=True)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })


class BlacklistViewSet(ModelViewSet):
    """黑名单视图集"""
    serializer_class = BlacklistSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的黑名单"""
        return Blacklist.objects.filter(user=self.request.user).order_by('-created_at')
    
    @drf_action(detail=False, methods=['post'])
    def add_to_blacklist(self, request):
        """添加到黑名单"""
        serializer = AddToBlacklistSerializer(data=request.data, context={'request': request})
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        blocked_user_id = serializer.validated_data['blocked_user_id']
        reason = serializer.validated_data.get('reason', '')
        
        # 检查是否已经拉黑
        existing_blacklist = Blacklist.objects.filter(
            user=request.user,
            blocked_user_id=blocked_user_id
        ).first()
        
        if existing_blacklist:
            return Response({
                'code': 400,
                'message': '该用户已在黑名单中'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 添加到黑名单
        from apps.users.models import User
        blocked_user = User.objects.get(id=blocked_user_id)
        
        blacklist = Blacklist.objects.create(
            user=request.user,
            blocked_user=blocked_user,
            reason=reason
        )
        
        # 记录安全日志
        SecurityLog.objects.create(
            user=request.user,
            action_type='blacklist_add',
            description=f'拉黑用户{blocked_user.nickname}',
            extra_data={
                'blocked_user_id': blocked_user_id,
                'reason': reason
            }
        )
        
        blacklist_serializer = BlacklistSerializer(blacklist)
        
        return Response({
            'code': 200,
            'message': '已添加到黑名单',
            'data': blacklist_serializer.data
        })
    
    @drf_action(detail=True, methods=['post'])
    def remove_from_blacklist(self, request, pk=None):
        """从黑名单移除"""
        blacklist = self.get_object()
        
        # 记录安全日志
        SecurityLog.objects.create(
            user=request.user,
            action_type='blacklist_remove',
            description=f'移除黑名单用户{blacklist.blocked_user.nickname}',
            extra_data={
                'blocked_user_id': blacklist.blocked_user.id
            }
        )
        
        blacklist.delete()
        
        return Response({
            'code': 200,
            'message': '已从黑名单移除'
        })


class SecurityLogViewSet(ReadOnlyModelViewSet):
    """安全日志视图集"""
    serializer_class = SecurityLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的安全日志"""
        return SecurityLog.objects.filter(user=self.request.user).order_by('-created_at')


class UserPunishmentViewSet(ReadOnlyModelViewSet):
    """用户处罚视图集"""
    serializer_class = UserPunishmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的处罚记录"""
        return UserPunishment.objects.filter(user=self.request.user).order_by('-created_at')


class AppealRecordViewSet(ModelViewSet):
    """申诉记录视图集"""
    serializer_class = AppealRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的申诉记录"""
        return AppealRecord.objects.filter(user=self.request.user).order_by('-created_at')
    
    @drf_action(detail=False, methods=['post'])
    def create_appeal(self, request):
        """创建申诉"""
        serializer = CreateAppealSerializer(data=request.data, context={'request': request})
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        punishment_id = serializer.validated_data['punishment_id']
        appeal_reason = serializer.validated_data['appeal_reason']
        evidence_images = serializer.validated_data.get('evidence_images', [])
        
        # 检查是否已经申诉过
        existing_appeal = AppealRecord.objects.filter(
            user=request.user,
            punishment_id=punishment_id
        ).first()
        
        if existing_appeal:
            return Response({
                'code': 400,
                'message': '您已经对该处罚提交过申诉'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建申诉记录
        punishment = UserPunishment.objects.get(id=punishment_id)
        
        appeal = AppealRecord.objects.create(
            user=request.user,
            punishment=punishment,
            appeal_reason=appeal_reason,
            evidence_images=evidence_images
        )
        
        # 记录安全日志
        SecurityLog.objects.create(
            user=request.user,
            action_type='appeal',
            description=f'申诉处罚记录',
            extra_data={
                'appeal_id': appeal.id,
                'punishment_id': punishment_id
            }
        )
        
        appeal_serializer = AppealRecordSerializer(appeal)
        
        return Response({
            'code': 200,
            'message': '申诉已提交，我们会尽快处理',
            'data': appeal_serializer.data
        })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def security_settings(request):
    """获取安全设置"""
    user = request.user
    
    # 从用户扩展信息中获取安全设置
    security_settings_data = user.extra_data.get('security_settings', {})
    
    # 设置默认值
    default_settings = {
        'login_notification': True,
        'unusual_activity_alert': True,
        'two_factor_auth': False,
        'privacy_mode': False,
        'block_stranger_messages': False
    }
    
    # 合并设置
    settings = {**default_settings, **security_settings_data}
    
    serializer = SecuritySettingsSerializer(settings)
    
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_security_settings(request):
    """更新安全设置"""
    serializer = SecuritySettingsSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    user = request.user
    
    # 更新用户安全设置
    if not user.extra_data:
        user.extra_data = {}
    
    user.extra_data['security_settings'] = serializer.validated_data
    user.save()
    
    # 记录安全日志
    SecurityLog.objects.create(
        user=user,
        action_type='settings_update',
        description='更新安全设置',
        extra_data=serializer.validated_data
    )
    
    return Response({
        'code': 200,
        'message': '安全设置已更新'
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_verification(request):
    """申请认证"""
    serializer = VerificationRequestSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    user = request.user
    verification_type = serializer.validated_data['verification_type']

    # 检查是否已经认证过
    if user.extra_data and user.extra_data.get(f'{verification_type}_verified'):
        return Response({
            'code': 400,
            'message': '您已经通过该类型认证'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 保存认证申请信息
    if not user.extra_data:
        user.extra_data = {}

    user.extra_data[f'{verification_type}_verification'] = {
        'status': 'pending',
        'data': serializer.validated_data,
        'submitted_at': timezone.now().isoformat()
    }
    user.save()

    # 记录安全日志
    SecurityLog.objects.create(
        user=user,
        action_type='verification_request',
        description=f'申请{verification_type}认证',
        extra_data={
            'verification_type': verification_type
        }
    )

    return Response({
        'code': 200,
        'message': '认证申请已提交，我们会在3-5个工作日内完成审核'
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_safety_score(request):
    """获取用户安全评分"""
    user = request.user

    # 计算安全评分
    score = 100
    factors = []
    suggestions = []

    # 检查实名认证
    if not user.is_verified:
        score -= 20
        factors.append('未完成实名认证')
        suggestions.append('完成实名认证可提高安全评分')

    # 检查头像
    if not user.avatar:
        score -= 10
        factors.append('未上传头像')
        suggestions.append('上传真实头像可提高可信度')

    # 检查资料完整度
    profile_fields = [user.nickname, user.age, user.location, user.profession]
    empty_fields = sum(1 for field in profile_fields if not field)
    if empty_fields > 0:
        score -= empty_fields * 5
        factors.append(f'资料不完整（缺少{empty_fields}项）')
        suggestions.append('完善个人资料可提高安全评分')

    # 检查最近的举报记录
    recent_reports = Report.objects.filter(
        reported_user=user,
        created_at__gte=timezone.now() - timedelta(days=30)
    ).count()

    if recent_reports > 0:
        score -= recent_reports * 10
        factors.append(f'最近30天被举报{recent_reports}次')
        suggestions.append('注意言行举止，避免被举报')

    # 检查处罚记录
    active_punishments = UserPunishment.objects.filter(
        user=user,
        is_active=True
    ).count()

    if active_punishments > 0:
        score -= active_punishments * 15
        factors.append(f'当前有{active_punishments}项处罚')
        suggestions.append('遵守平台规则，避免违规行为')

    # 确保评分不低于0
    score = max(0, score)

    # 确定评分等级
    if score >= 90:
        score_level = '优秀'
    elif score >= 80:
        score_level = '良好'
    elif score >= 70:
        score_level = '一般'
    elif score >= 60:
        score_level = '较差'
    else:
        score_level = '很差'

    safety_data = {
        'safety_score': score,
        'score_level': score_level,
        'factors': factors,
        'suggestions': suggestions,
        'last_updated': timezone.now()
    }

    serializer = UserSafetyScoreSerializer(safety_data)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def safety_tips(request):
    """获取安全提示"""
    tips = [
        {
            'tip_type': 'privacy',
            'title': '保护个人隐私',
            'content': '不要在聊天中透露身份证号、银行卡号等敏感信息',
            'importance': 'high',
            'read_count': 1250
        },
        {
            'tip_type': 'meeting',
            'title': '线下见面安全',
            'content': '首次见面选择公共场所，告知朋友行程安排',
            'importance': 'high',
            'read_count': 980
        },
        {
            'tip_type': 'fraud',
            'title': '防范诈骗',
            'content': '警惕以各种理由要求转账汇款的行为',
            'importance': 'high',
            'read_count': 1580
        },
        {
            'tip_type': 'photo',
            'title': '照片安全',
            'content': '不要发送包含个人信息的照片，如身份证、工作证等',
            'importance': 'medium',
            'read_count': 750
        },
        {
            'tip_type': 'report',
            'title': '及时举报',
            'content': '遇到不当行为及时举报，维护平台环境',
            'importance': 'medium',
            'read_count': 650
        }
    ]

    serializer = SafetyTipSerializer(tips, many=True)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def risk_assessment(request):
    """风险评估"""
    user = request.user

    # 计算风险评分
    risk_score = 0
    risk_factors = []
    recommendations = []

    # 检查账号年龄
    account_age = (timezone.now() - user.date_joined).days
    if account_age < 7:
        risk_score += 20
        risk_factors.append('新注册账号')
        recommendations.append('完善个人资料，提高账号可信度')

    # 检查登录频率
    recent_logins = SecurityLog.objects.filter(
        user=user,
        action_type='login',
        created_at__gte=timezone.now() - timedelta(days=7)
    ).count()

    if recent_logins > 20:
        risk_score += 15
        risk_factors.append('登录频率异常')
        recommendations.append('注意账号安全，避免频繁登录')

    # 检查举报记录
    report_count = Report.objects.filter(
        reported_user=user,
        created_at__gte=timezone.now() - timedelta(days=30)
    ).count()

    if report_count > 3:
        risk_score += 30
        risk_factors.append('被举报次数较多')
        recommendations.append('注意言行举止，遵守平台规则')

    # 检查设备信息
    device_count = SecurityLog.objects.filter(
        user=user,
        created_at__gte=timezone.now() - timedelta(days=7)
    ).values('device_info').distinct().count()

    if device_count > 3:
        risk_score += 10
        risk_factors.append('多设备登录')
        recommendations.append('注意账号安全，避免在不安全设备上登录')

    # 确定风险等级
    if risk_score >= 50:
        risk_level = 'high'
    elif risk_score >= 30:
        risk_level = 'medium'
    elif risk_score >= 10:
        risk_level = 'low'
    else:
        risk_level = 'very_low'

    assessment_data = {
        'user_id': user.id,
        'risk_score': risk_score,
        'risk_level': risk_level,
        'risk_factors': risk_factors,
        'recommendations': recommendations,
        'assessment_time': timezone.now()
    }

    serializer = RiskAssessmentSerializer(assessment_data)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def security_report(request):
    """安全报告"""
    today = date.today()

    # 统计数据
    total_reports = Report.objects.filter(
        created_at__date=today
    ).count()

    processed_reports = Report.objects.filter(
        created_at__date=today,
        status__in=['resolved', 'rejected']
    ).count()

    pending_reports = total_reports - processed_reports

    high_risk_users = SecurityLog.objects.filter(
        created_at__date=today,
        risk_level='high'
    ).values('user').distinct().count()

    blocked_users = UserPunishment.objects.filter(
        created_at__date=today,
        punishment_type='ban'
    ).count()

    security_incidents = SecurityLog.objects.filter(
        created_at__date=today,
        action_type__in=['report', 'punishment', 'appeal']
    ).count()

    report_data = {
        'report_date': today,
        'total_reports': total_reports,
        'processed_reports': processed_reports,
        'pending_reports': pending_reports,
        'high_risk_users': high_risk_users,
        'blocked_users': blocked_users,
        'security_incidents': security_incidents
    }

    serializer = SecurityReportSerializer(report_data)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })
