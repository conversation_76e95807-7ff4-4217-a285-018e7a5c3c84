from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'reports', views.ReportViewSet, basename='report')
router.register(r'blacklist', views.BlacklistViewSet, basename='blacklist')
router.register(r'security-logs', views.SecurityLogViewSet, basename='security-log')
router.register(r'punishments', views.UserPunishmentViewSet, basename='punishment')
router.register(r'appeals', views.AppealRecordViewSet, basename='appeal')

urlpatterns = [
    path('', include(router.urls)),
    
    # 安全设置
    path('security/settings/', views.security_settings, name='security-settings'),
    path('security/update-settings/', views.update_security_settings, name='update-security-settings'),
    
    # 认证相关
    path('security/request-verification/', views.request_verification, name='request-verification'),
    
    # 安全评估
    path('security/safety-score/', views.user_safety_score, name='user-safety-score'),
    path('security/risk-assessment/', views.risk_assessment, name='risk-assessment'),
    
    # 安全提示和报告
    path('security/safety-tips/', views.safety_tips, name='safety-tips'),
    path('security/report/', views.security_report, name='security-report'),
]
