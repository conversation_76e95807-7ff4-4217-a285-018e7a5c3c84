from django.db import models
from apps.users.models import User


class Report(models.Model):
    """举报记录"""
    REPORT_TYPES = [
        ('inappropriate_content', '内容不当'),
        ('harassment', '骚扰行为'),
        ('fake_profile', '虚假资料'),
        ('spam', '垃圾信息'),
        ('fraud', '诈骗行为'),
        ('underage', '未成年人'),
        ('violence', '暴力内容'),
        ('hate_speech', '仇恨言论'),
        ('copyright', '版权问题'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已处理'),
        ('rejected', '已拒绝'),
        ('closed', '已关闭'),
    ]
    
    CONTENT_TYPES = [
        ('user', '用户'),
        ('moment', '动态'),
        ('message', '消息'),
        ('comment', '评论'),
    ]
    
    # 举报基本信息
    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports_made', 
                                verbose_name='举报者')
    reported_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports_received', 
                                     verbose_name='被举报用户')
    
    # 举报内容
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES, verbose_name='内容类型')
    content_id = models.IntegerField(verbose_name='内容ID')
    
    # 举报详情
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES, verbose_name='举报类型')
    reason = models.TextField(verbose_name='举报原因')
    evidence = models.JSONField(default=list, verbose_name='证据列表')
    
    # 处理信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='处理状态')
    admin_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='handled_reports', verbose_name='处理管理员')
    admin_note = models.TextField(blank=True, verbose_name='管理员备注')
    action_taken = models.CharField(max_length=100, blank=True, verbose_name='采取的行动')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='举报时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    
    class Meta:
        db_table = 'reports'
        verbose_name = '举报记录'
        verbose_name_plural = '举报记录'
        ordering = ['-created_at']
        unique_together = ['reporter', 'reported_user', 'content_type', 'content_id']
    
    def __str__(self):
        return f'{self.reporter.nickname} 举报 {self.reported_user.nickname}'


class Blacklist(models.Model):
    """黑名单"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blacklist', 
                            verbose_name='用户')
    blocked_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blocked_by', 
                                    verbose_name='被拉黑用户')
    reason = models.CharField(max_length=200, blank=True, verbose_name='拉黑原因')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='拉黑时间')
    
    class Meta:
        db_table = 'blacklist'
        verbose_name = '黑名单'
        verbose_name_plural = '黑名单'
        unique_together = ['user', 'blocked_user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 拉黑了 {self.blocked_user.nickname}'


class UserPunishment(models.Model):
    """用户处罚记录"""
    PUNISHMENT_TYPES = [
        ('warning', '警告'),
        ('mute', '禁言'),
        ('ban', '封禁'),
        ('permanent_ban', '永久封禁'),
        ('content_removal', '内容删除'),
        ('feature_restriction', '功能限制'),
    ]
    
    STATUS_CHOICES = [
        ('active', '生效中'),
        ('expired', '已过期'),
        ('revoked', '已撤销'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='punishments', 
                            verbose_name='用户')
    punishment_type = models.CharField(max_length=20, choices=PUNISHMENT_TYPES, 
                                      verbose_name='处罚类型')
    
    # 处罚详情
    reason = models.TextField(verbose_name='处罚原因')
    description = models.TextField(blank=True, verbose_name='处罚描述')
    
    # 关联举报
    related_report = models.ForeignKey(Report, on_delete=models.SET_NULL, null=True, blank=True,
                                      related_name='punishments', verbose_name='关联举报')
    
    # 处罚时间
    start_time = models.DateTimeField(auto_now_add=True, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', 
                             verbose_name='状态')
    
    # 执行人
    admin_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='punishments_given', verbose_name='执行管理员')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_punishments'
        verbose_name = '用户处罚'
        verbose_name_plural = '用户处罚'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.get_punishment_type_display()}'
    
    @property
    def is_active(self):
        """是否生效中"""
        if self.status != 'active':
            return False
        if self.end_time:
            from django.utils import timezone
            return timezone.now() < self.end_time
        return True


class AppealRecord(models.Model):
    """申诉记录"""
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('approved', '申诉成功'),
        ('rejected', '申诉失败'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='appeals', 
                            verbose_name='申诉用户')
    punishment = models.ForeignKey(UserPunishment, on_delete=models.CASCADE, 
                                  related_name='appeals', verbose_name='处罚记录')
    
    # 申诉内容
    reason = models.TextField(verbose_name='申诉理由')
    evidence = models.JSONField(default=list, verbose_name='申诉证据')
    
    # 处理信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='申诉状态')
    admin_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='handled_appeals', verbose_name='处理管理员')
    admin_response = models.TextField(blank=True, verbose_name='管理员回复')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='申诉时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    
    class Meta:
        db_table = 'appeal_records'
        verbose_name = '申诉记录'
        verbose_name_plural = '申诉记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} 申诉处罚'


class ContentModeration(models.Model):
    """内容审核"""
    CONTENT_TYPES = [
        ('user_profile', '用户资料'),
        ('moment', '动态'),
        ('comment', '评论'),
        ('message', '消息'),
        ('photo', '照片'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '审核通过'),
        ('rejected', '审核拒绝'),
        ('flagged', '标记问题'),
    ]
    
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES, verbose_name='内容类型')
    content_id = models.IntegerField(verbose_name='内容ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='moderated_content', 
                            verbose_name='内容作者')
    
    # 审核信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='审核状态')
    moderator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='moderated_by', verbose_name='审核员')
    
    # 审核结果
    rejection_reason = models.TextField(blank=True, verbose_name='拒绝原因')
    moderator_note = models.TextField(blank=True, verbose_name='审核备注')
    
    # 自动审核信息
    auto_score = models.FloatField(null=True, blank=True, verbose_name='自动审核得分')
    auto_tags = models.JSONField(default=list, verbose_name='自动标签')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='提交时间')
    reviewed_at = models.DateTimeField(null=True, blank=True, verbose_name='审核时间')
    
    class Meta:
        db_table = 'content_moderation'
        verbose_name = '内容审核'
        verbose_name_plural = '内容审核'
        ordering = ['-created_at']
        unique_together = ['content_type', 'content_id']
    
    def __str__(self):
        return f'{self.get_content_type_display()} 审核 - {self.user.nickname}'


class SecurityLog(models.Model):
    """安全日志"""
    LOG_TYPES = [
        ('login_attempt', '登录尝试'),
        ('suspicious_activity', '可疑活动'),
        ('report_submitted', '提交举报'),
        ('punishment_applied', '执行处罚'),
        ('appeal_submitted', '提交申诉'),
        ('content_blocked', '内容屏蔽'),
        ('account_locked', '账户锁定'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='security_logs', 
                            verbose_name='用户')
    log_type = models.CharField(max_length=30, choices=LOG_TYPES, verbose_name='日志类型')
    description = models.TextField(verbose_name='描述')
    
    # 详细信息
    details = models.JSONField(default=dict, verbose_name='详细信息')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')
    
    # 风险等级
    risk_level = models.CharField(max_length=10, choices=[
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ], default='low', verbose_name='风险等级')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'security_logs'
        verbose_name = '安全日志'
        verbose_name_plural = '安全日志'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.get_log_type_display()}'
