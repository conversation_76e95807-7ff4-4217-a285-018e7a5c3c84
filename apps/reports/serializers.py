from rest_framework import serializers
from .models import (
    Report, SecurityLog, UserPunishment, AppealRecord,
    Blacklist, ContentModeration
)
from apps.users.serializers import UserProfileSerializer


class ReportSerializer(serializers.ModelSerializer):
    """举报序列化器"""
    reporter = UserProfileSerializer(read_only=True)
    reported_user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = Report
        fields = [
            'id', 'reporter', 'reported_user', 'report_type', 'target_id',
            'reason', 'description', 'evidence_images', 'status',
            'admin_note', 'created_at', 'processed_at'
        ]
        read_only_fields = [
            'id', 'reporter', 'status', 'admin_note', 'created_at', 'processed_at'
        ]


class CreateReportSerializer(serializers.Serializer):
    """创建举报序列化器"""
    REPORT_TYPES = [
        ('user', '用户'),
        ('moment', '动态'),
        ('comment', '评论'),
        ('message', '消息'),
    ]
    
    REASONS = [
        ('spam', '垃圾信息'),
        ('inappropriate', '不当内容'),
        ('harassment', '骚扰'),
        ('fake', '虚假信息'),
        ('violence', '暴力内容'),
        ('copyright', '版权侵犯'),
        ('fraud', '诈骗'),
        ('other', '其他'),
    ]
    
    report_type = serializers.ChoiceField(choices=REPORT_TYPES)
    target_id = serializers.IntegerField()
    reported_user_id = serializers.IntegerField()
    reason = serializers.ChoiceField(choices=REASONS)
    description = serializers.CharField(max_length=1000)
    evidence_images = serializers.ListField(
        child=serializers.URLField(),
        required=False,
        allow_empty=True
    )
    
    def validate_reported_user_id(self, value):
        """验证被举报用户ID"""
        from apps.users.models import User
        try:
            User.objects.get(id=value, status=1)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("被举报用户不存在")
    
    def validate(self, data):
        """验证举报数据"""
        report_type = data.get('report_type')
        target_id = data.get('target_id')
        
        # 验证目标对象是否存在
        if report_type == 'user':
            from apps.users.models import User
            try:
                User.objects.get(id=target_id, status=1)
            except User.DoesNotExist:
                raise serializers.ValidationError("被举报用户不存在")
        
        elif report_type == 'moment':
            from apps.moments.models import Moment
            try:
                Moment.objects.get(id=target_id, status='published')
            except Moment.DoesNotExist:
                raise serializers.ValidationError("被举报动态不存在")
        
        elif report_type == 'comment':
            from apps.moments.models import MomentComment
            try:
                MomentComment.objects.get(id=target_id)
            except MomentComment.DoesNotExist:
                raise serializers.ValidationError("被举报评论不存在")
        
        elif report_type == 'message':
            from apps.chat.models import ChatMessage
            try:
                ChatMessage.objects.get(id=target_id)
            except ChatMessage.DoesNotExist:
                raise serializers.ValidationError("被举报消息不存在")
        
        return data


class SecurityLogSerializer(serializers.ModelSerializer):
    """安全日志序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = SecurityLog
        fields = [
            'id', 'user', 'action_type', 'ip_address', 'user_agent',
            'location', 'device_info', 'risk_level', 'description',
            'extra_data', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserPunishmentSerializer(serializers.ModelSerializer):
    """用户处罚序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = UserPunishment
        fields = [
            'id', 'user', 'punishment_type', 'reason', 'description',
            'start_time', 'end_time', 'is_active', 'created_by',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class AppealRecordSerializer(serializers.ModelSerializer):
    """申诉记录序列化器"""
    user = UserProfileSerializer(read_only=True)
    punishment = UserPunishmentSerializer(read_only=True)
    
    class Meta:
        model = AppealRecord
        fields = [
            'id', 'user', 'punishment', 'appeal_reason', 'evidence_images',
            'status', 'admin_response', 'processed_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'user', 'status', 'admin_response', 'processed_at', 'created_at'
        ]


class CreateAppealSerializer(serializers.Serializer):
    """创建申诉序列化器"""
    punishment_id = serializers.IntegerField()
    appeal_reason = serializers.CharField(max_length=1000)
    evidence_images = serializers.ListField(
        child=serializers.URLField(),
        required=False,
        allow_empty=True
    )
    
    def validate_punishment_id(self, value):
        """验证处罚ID"""
        request = self.context.get('request')
        try:
            punishment = UserPunishment.objects.get(
                id=value,
                user=request.user,
                is_active=True
            )
            return value
        except UserPunishment.DoesNotExist:
            raise serializers.ValidationError("处罚记录不存在或已失效")


class BlacklistSerializer(serializers.ModelSerializer):
    """黑名单序列化器"""
    user = UserProfileSerializer(read_only=True)
    blocked_user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = Blacklist
        fields = [
            'id', 'user', 'blocked_user', 'reason', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']


class AddToBlacklistSerializer(serializers.Serializer):
    """添加黑名单序列化器"""
    blocked_user_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=200, required=False, allow_blank=True)
    
    def validate_blocked_user_id(self, value):
        """验证被拉黑用户ID"""
        from apps.users.models import User
        request = self.context.get('request')
        
        if value == request.user.id:
            raise serializers.ValidationError("不能拉黑自己")
        
        try:
            User.objects.get(id=value, status=1)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")


class ContentModerationSerializer(serializers.ModelSerializer):
    """内容审核序列化器"""
    
    class Meta:
        model = ContentModeration
        fields = [
            'id', 'content_type', 'content_id', 'content_text',
            'moderation_result', 'risk_level', 'keywords',
            'auto_action', 'manual_review', 'reviewer_note',
            'created_at', 'reviewed_at'
        ]
        read_only_fields = ['id', 'created_at', 'reviewed_at']


class SecuritySettingsSerializer(serializers.Serializer):
    """安全设置序列化器"""
    login_notification = serializers.BooleanField(default=True)
    unusual_activity_alert = serializers.BooleanField(default=True)
    two_factor_auth = serializers.BooleanField(default=False)
    privacy_mode = serializers.BooleanField(default=False)
    block_stranger_messages = serializers.BooleanField(default=False)


class VerificationRequestSerializer(serializers.Serializer):
    """认证申请序列化器"""
    VERIFICATION_TYPES = [
        ('identity', '身份认证'),
        ('education', '学历认证'),
        ('profession', '职业认证'),
        ('income', '收入认证'),
    ]
    
    verification_type = serializers.ChoiceField(choices=VERIFICATION_TYPES)
    real_name = serializers.CharField(max_length=50)
    id_number = serializers.CharField(max_length=20)
    id_front_image = serializers.URLField()
    id_back_image = serializers.URLField()
    additional_info = serializers.JSONField(required=False, default=dict)
    
    def validate_id_number(self, value):
        """验证身份证号"""
        import re
        
        # 简单的身份证号格式验证
        pattern = r'^\d{17}[\dXx]$'
        if not re.match(pattern, value):
            raise serializers.ValidationError("身份证号格式不正确")
        
        return value


class RiskAssessmentSerializer(serializers.Serializer):
    """风险评估序列化器"""
    user_id = serializers.IntegerField()
    risk_score = serializers.FloatField()
    risk_level = serializers.CharField()
    risk_factors = serializers.ListField()
    recommendations = serializers.ListField()
    assessment_time = serializers.DateTimeField()


class SafetyTipSerializer(serializers.Serializer):
    """安全提示序列化器"""
    tip_type = serializers.CharField()
    title = serializers.CharField()
    content = serializers.CharField()
    importance = serializers.CharField()
    read_count = serializers.IntegerField()


class SecurityReportSerializer(serializers.Serializer):
    """安全报告序列化器"""
    report_date = serializers.DateField()
    total_reports = serializers.IntegerField()
    processed_reports = serializers.IntegerField()
    pending_reports = serializers.IntegerField()
    high_risk_users = serializers.IntegerField()
    blocked_users = serializers.IntegerField()
    security_incidents = serializers.IntegerField()
    
    
class UserSafetyScoreSerializer(serializers.Serializer):
    """用户安全评分序列化器"""
    safety_score = serializers.IntegerField()
    score_level = serializers.CharField()
    factors = serializers.ListField()
    suggestions = serializers.ListField()
    last_updated = serializers.DateTimeField()
