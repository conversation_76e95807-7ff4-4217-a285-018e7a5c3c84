"""
用户反馈系统视图
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from django.http import JsonResponse
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework import status

from .models import (
    UserFeedback, FeedbackCategory, FeedbackReply, 
    FeedbackTemplate, FeedbackTag, FeedbackStatistics
)


class FeedbackCategoriesAPIView(APIView):
    """反馈分类API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取反馈分类列表"""
        categories = FeedbackCategory.objects.filter(is_active=True).order_by('sort_order')
        
        category_data = []
        for category in categories:
            category_data.append({
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'icon': category.icon
            })
        
        return Response({
            'code': 200,
            'data': category_data
        })


class UserFeedbackAPIView(APIView):
    """用户反馈API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户反馈列表"""
        user = request.user
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 10))
        status_filter = request.GET.get('status', 'all')
        
        # 构建查询条件
        queryset = UserFeedback.objects.filter(user=user)
        
        if status_filter != 'all':
            queryset = queryset.filter(status=status_filter)
        
        # 分页
        total = queryset.count()
        offset = (page - 1) * limit
        feedbacks = queryset.order_by('-created_at')[offset:offset + limit]
        
        # 格式化数据
        feedback_data = []
        for feedback in feedbacks:
            feedback_data.append({
                'id': feedback.id,
                'title': feedback.title,
                'content': feedback.content[:100] + '...' if len(feedback.content) > 100 else feedback.content,
                'feedback_type': feedback.get_feedback_type_display(),
                'status': feedback.get_status_display(),
                'status_code': feedback.status,
                'priority': feedback.get_priority_display(),
                'created_at': feedback.created_at.strftime('%Y-%m-%d %H:%M'),
                'reply_count': feedback.replies.count(),
                'has_unread_replies': self.has_unread_replies(feedback, user)
            })
        
        return Response({
            'code': 200,
            'data': {
                'feedbacks': feedback_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': (total + limit - 1) // limit
                }
            }
        })
    
    def post(self, request):
        """提交用户反馈"""
        user = request.user
        
        try:
            # 获取请求数据
            title = request.data.get('title', '').strip()
            content = request.data.get('content', '').strip()
            feedback_type = request.data.get('feedback_type', 'other')
            category_id = request.data.get('category_id')
            contact_phone = request.data.get('contact_phone', '').strip()
            contact_email = request.data.get('contact_email', '').strip()
            attachments = request.data.get('attachments', [])
            device_info = request.data.get('device_info', {})
            
            # 验证必填字段
            if not title or not content:
                return Response({
                    'code': 400,
                    'message': '标题和内容不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取分类
            category = None
            if category_id:
                try:
                    category = FeedbackCategory.objects.get(id=category_id, is_active=True)
                except FeedbackCategory.DoesNotExist:
                    pass
            
            # 创建反馈
            feedback = UserFeedback.objects.create(
                user=user,
                category=category,
                feedback_type=feedback_type,
                title=title,
                content=content,
                contact_phone=contact_phone,
                contact_email=contact_email,
                attachments=attachments,
                device_info=device_info,
                app_version=request.data.get('app_version', ''),
                system_version=request.data.get('system_version', '')
            )
            
            # 自动分配优先级
            feedback.priority = self.auto_assign_priority(feedback)
            feedback.save()
            
            # 发送通知给管理员
            self.notify_admins_new_feedback(feedback)
            
            return Response({
                'code': 200,
                'message': '反馈提交成功，我们会尽快处理',
                'data': {
                    'feedback_id': feedback.id,
                    'status': feedback.get_status_display()
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'提交失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def has_unread_replies(self, feedback, user):
        """检查是否有未读回复"""
        # 简化实现，实际应该记录用户最后查看时间
        return feedback.replies.filter(
            is_internal=False,
            created_at__gt=feedback.updated_at
        ).exists()
    
    def auto_assign_priority(self, feedback):
        """自动分配优先级"""
        # 根据反馈类型和关键词自动分配优先级
        urgent_keywords = ['崩溃', '无法登录', '支付失败', '数据丢失']
        high_keywords = ['卡顿', '闪退', '功能异常', '无法使用']
        
        content_lower = feedback.content.lower()
        title_lower = feedback.title.lower()
        
        # 检查紧急关键词
        for keyword in urgent_keywords:
            if keyword in content_lower or keyword in title_lower:
                return 'urgent'
        
        # 检查高优先级关键词
        for keyword in high_keywords:
            if keyword in content_lower or keyword in title_lower:
                return 'high'
        
        # 根据反馈类型分配
        if feedback.feedback_type == 'bug':
            return 'high'
        elif feedback.feedback_type == 'complaint':
            return 'medium'
        else:
            return 'low'
    
    def notify_admins_new_feedback(self, feedback):
        """通知管理员新反馈"""
        # 这里应该发送通知给管理员
        # 可以通过邮件、短信、系统通知等方式
        pass


class FeedbackDetailAPIView(APIView):
    """反馈详情API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, feedback_id):
        """获取反馈详情"""
        user = request.user
        
        try:
            feedback = UserFeedback.objects.get(id=feedback_id, user=user)
        except UserFeedback.DoesNotExist:
            return Response({
                'code': 404,
                'message': '反馈不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 获取回复列表
        replies = feedback.replies.filter(is_internal=False).order_by('created_at')
        reply_data = []
        for reply in replies:
            reply_data.append({
                'id': reply.id,
                'content': reply.content,
                'attachments': reply.attachments,
                'is_solution': reply.is_solution,
                'created_at': reply.created_at.strftime('%Y-%m-%d %H:%M'),
                'user': {
                    'username': reply.user.username,
                    'is_staff': reply.user.is_staff
                }
            })
        
        # 获取标签
        tags = [tag.name for tag in FeedbackTag.objects.filter(
            feedback_relations__feedback=feedback
        )]
        
        feedback_data = {
            'id': feedback.id,
            'title': feedback.title,
            'content': feedback.content,
            'feedback_type': feedback.get_feedback_type_display(),
            'status': feedback.get_status_display(),
            'status_code': feedback.status,
            'priority': feedback.get_priority_display(),
            'category': feedback.category.name if feedback.category else None,
            'attachments': feedback.attachments,
            'device_info': feedback.device_info,
            'contact_phone': feedback.contact_phone,
            'contact_email': feedback.contact_email,
            'resolution': feedback.resolution,
            'resolution_time': feedback.resolution_time.strftime('%Y-%m-%d %H:%M') if feedback.resolution_time else None,
            'created_at': feedback.created_at.strftime('%Y-%m-%d %H:%M'),
            'updated_at': feedback.updated_at.strftime('%Y-%m-%d %H:%M'),
            'replies': reply_data,
            'tags': tags,
            'can_rate': feedback.status == 'resolved' and not feedback.user_rating
        }
        
        return Response({
            'code': 200,
            'data': feedback_data
        })


class FeedbackRatingAPIView(APIView):
    """反馈评价API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, feedback_id):
        """提交反馈评价"""
        user = request.user
        
        try:
            feedback = UserFeedback.objects.get(id=feedback_id, user=user)
        except UserFeedback.DoesNotExist:
            return Response({
                'code': 404,
                'message': '反馈不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        if feedback.status != 'resolved':
            return Response({
                'code': 400,
                'message': '只能对已解决的反馈进行评价'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if feedback.user_rating:
            return Response({
                'code': 400,
                'message': '该反馈已经评价过了'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        rating = request.data.get('rating')
        comment = request.data.get('comment', '').strip()
        
        if not rating or not (1 <= int(rating) <= 5):
            return Response({
                'code': 400,
                'message': '评分必须在1-5之间'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 保存评价
        feedback.user_rating = int(rating)
        feedback.user_comment = comment
        feedback.save()
        
        return Response({
            'code': 200,
            'message': '评价提交成功，感谢您的反馈'
        })


class AdminFeedbackAPIView(APIView):
    """管理员反馈管理API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取反馈管理列表"""
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        status_filter = request.GET.get('status', 'all')
        priority_filter = request.GET.get('priority', 'all')
        type_filter = request.GET.get('type', 'all')
        search = request.GET.get('search', '').strip()
        
        # 构建查询条件
        queryset = UserFeedback.objects.all()
        
        if status_filter != 'all':
            queryset = queryset.filter(status=status_filter)
        
        if priority_filter != 'all':
            queryset = queryset.filter(priority=priority_filter)
        
        if type_filter != 'all':
            queryset = queryset.filter(feedback_type=type_filter)
        
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(user__username__icontains=search)
            )
        
        # 分页
        total = queryset.count()
        offset = (page - 1) * limit
        feedbacks = queryset.order_by('-created_at')[offset:offset + limit]
        
        # 格式化数据
        feedback_data = []
        for feedback in feedbacks:
            feedback_data.append({
                'id': feedback.id,
                'title': feedback.title,
                'user': {
                    'id': feedback.user.id,
                    'username': feedback.user.username,
                    'nickname': feedback.user.nickname
                },
                'feedback_type': feedback.get_feedback_type_display(),
                'status': feedback.get_status_display(),
                'status_code': feedback.status,
                'priority': feedback.get_priority_display(),
                'priority_code': feedback.priority,
                'category': feedback.category.name if feedback.category else None,
                'assigned_to': feedback.assigned_to.username if feedback.assigned_to else None,
                'created_at': feedback.created_at.strftime('%Y-%m-%d %H:%M'),
                'is_overdue': feedback.is_overdue,
                'reply_count': feedback.replies.count()
            })
        
        return Response({
            'code': 200,
            'data': {
                'feedbacks': feedback_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': (total + limit - 1) // limit
                }
            }
        })
    
    def post(self, request):
        """批量操作反馈"""
        action = request.data.get('action')
        feedback_ids = request.data.get('feedback_ids', [])
        
        if not action or not feedback_ids:
            return Response({
                'code': 400,
                'message': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        feedbacks = UserFeedback.objects.filter(id__in=feedback_ids)
        
        if action == 'assign':
            assigned_to_id = request.data.get('assigned_to_id')
            if assigned_to_id:
                feedbacks.update(assigned_to_id=assigned_to_id)
                message = f'成功分配 {feedbacks.count()} 个反馈'
            else:
                return Response({
                    'code': 400,
                    'message': '请选择分配人员'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        elif action == 'change_status':
            new_status = request.data.get('new_status')
            if new_status:
                feedbacks.update(status=new_status)
                message = f'成功更新 {feedbacks.count()} 个反馈状态'
            else:
                return Response({
                    'code': 400,
                    'message': '请选择新状态'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        elif action == 'change_priority':
            new_priority = request.data.get('new_priority')
            if new_priority:
                feedbacks.update(priority=new_priority)
                message = f'成功更新 {feedbacks.count()} 个反馈优先级'
            else:
                return Response({
                    'code': 400,
                    'message': '请选择新优先级'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        else:
            return Response({
                'code': 400,
                'message': '无效的操作'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'code': 200,
            'message': message
        })


class FeedbackStatsAPIView(APIView):
    """反馈统计API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取反馈统计数据"""
        date_range = int(request.GET.get('range', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=date_range)
        
        # 基础统计
        total_feedbacks = UserFeedback.objects.count()
        pending_feedbacks = UserFeedback.objects.filter(status='pending').count()
        processing_feedbacks = UserFeedback.objects.filter(status='processing').count()
        resolved_feedbacks = UserFeedback.objects.filter(status='resolved').count()
        
        # 按类型统计
        type_stats = UserFeedback.objects.values('feedback_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 按优先级统计
        priority_stats = UserFeedback.objects.values('priority').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 处理效率统计
        resolved_with_time = UserFeedback.objects.filter(
            status='resolved',
            resolution_time__isnull=False,
            created_at__date__gte=start_date
        )
        
        avg_resolution_time = 0
        if resolved_with_time.exists():
            total_time = sum([
                (feedback.resolution_time - feedback.created_at).total_seconds() / 3600
                for feedback in resolved_with_time
            ])
            avg_resolution_time = total_time / resolved_with_time.count()
        
        # 用户满意度
        rated_feedbacks = UserFeedback.objects.filter(
            user_rating__isnull=False,
            created_at__date__gte=start_date
        )
        avg_rating = rated_feedbacks.aggregate(avg_rating=Avg('user_rating'))['avg_rating'] or 0
        
        # 每日趋势
        daily_stats = []
        for i in range(date_range):
            date = start_date + timedelta(days=i)
            daily_count = UserFeedback.objects.filter(created_at__date=date).count()
            daily_stats.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': daily_count
            })
        
        return Response({
            'code': 200,
            'data': {
                'overview': {
                    'total_feedbacks': total_feedbacks,
                    'pending_feedbacks': pending_feedbacks,
                    'processing_feedbacks': processing_feedbacks,
                    'resolved_feedbacks': resolved_feedbacks,
                    'resolution_rate': (resolved_feedbacks / total_feedbacks * 100) if total_feedbacks > 0 else 0
                },
                'type_stats': list(type_stats),
                'priority_stats': list(priority_stats),
                'efficiency': {
                    'avg_resolution_time_hours': round(avg_resolution_time, 2),
                    'avg_rating': round(avg_rating, 2),
                    'total_rated': rated_feedbacks.count()
                },
                'daily_trend': daily_stats
            }
        })
