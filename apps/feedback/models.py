"""
用户反馈系统模型
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class FeedbackCategory(models.Model):
    """反馈分类"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    icon = models.CharField(max_length=50, blank=True, verbose_name='图标')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈分类'
        verbose_name_plural = '反馈分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class UserFeedback(models.Model):
    """用户反馈"""
    FEEDBACK_TYPES = [
        ('bug', '问题反馈'),
        ('feature', '功能建议'),
        ('complaint', '投诉建议'),
        ('praise', '表扬建议'),
        ('other', '其他'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
        ('rejected', '已拒绝'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    category = models.ForeignKey(
        FeedbackCategory, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        verbose_name='反馈分类'
    )
    
    # 反馈内容
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPES, verbose_name='反馈类型')
    title = models.CharField(max_length=200, verbose_name='反馈标题')
    content = models.TextField(verbose_name='反馈内容')
    
    # 附件信息
    attachments = models.JSONField(default=list, verbose_name='附件列表')
    
    # 设备和环境信息
    device_info = models.JSONField(default=dict, verbose_name='设备信息')
    app_version = models.CharField(max_length=20, blank=True, verbose_name='应用版本')
    system_version = models.CharField(max_length=50, blank=True, verbose_name='系统版本')
    
    # 联系方式
    contact_phone = models.CharField(max_length=20, blank=True, verbose_name='联系电话')
    contact_email = models.EmailField(blank=True, verbose_name='联系邮箱')
    
    # 处理信息
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium', verbose_name='优先级')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    assigned_to = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='assigned_feedbacks',
        verbose_name='处理人'
    )
    
    # 处理结果
    resolution = models.TextField(blank=True, verbose_name='处理结果')
    resolution_time = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    
    # 用户评价
    user_rating = models.IntegerField(null=True, blank=True, verbose_name='用户评分')
    user_comment = models.TextField(blank=True, verbose_name='用户评价')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户反馈'
        verbose_name_plural = '用户反馈'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['feedback_type', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.title}"
    
    @property
    def is_overdue(self):
        """是否超期"""
        if self.status in ['resolved', 'closed', 'rejected']:
            return False
        
        # 根据优先级设置处理时限
        time_limits = {
            'urgent': 4,    # 4小时
            'high': 24,     # 24小时
            'medium': 72,   # 72小时
            'low': 168      # 168小时(7天)
        }
        
        limit_hours = time_limits.get(self.priority, 72)
        deadline = self.created_at + timezone.timedelta(hours=limit_hours)
        
        return timezone.now() > deadline


class FeedbackReply(models.Model):
    """反馈回复"""
    feedback = models.ForeignKey(
        UserFeedback, 
        on_delete=models.CASCADE, 
        related_name='replies',
        verbose_name='反馈'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='回复人')
    content = models.TextField(verbose_name='回复内容')
    attachments = models.JSONField(default=list, verbose_name='附件列表')
    
    is_internal = models.BooleanField(default=False, verbose_name='是否内部回复')
    is_solution = models.BooleanField(default=False, verbose_name='是否解决方案')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈回复'
        verbose_name_plural = '反馈回复'
        ordering = ['created_at']
    
    def __str__(self):
        return f"{self.feedback.title} - {self.user.username}"


class FeedbackTemplate(models.Model):
    """反馈模板"""
    TEMPLATE_TYPES = [
        ('quick_reply', '快速回复'),
        ('resolution', '解决方案'),
        ('rejection', '拒绝理由'),
        ('follow_up', '跟进模板'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='模板名称')
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES, verbose_name='模板类型')
    content = models.TextField(verbose_name='模板内容')
    
    category = models.ForeignKey(
        FeedbackCategory, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='适用分类'
    )
    
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '反馈模板'
        verbose_name_plural = '反馈模板'
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name


class FeedbackStatistics(models.Model):
    """反馈统计"""
    date = models.DateField(verbose_name='统计日期')
    
    # 反馈数量统计
    total_feedbacks = models.IntegerField(default=0, verbose_name='总反馈数')
    new_feedbacks = models.IntegerField(default=0, verbose_name='新增反馈')
    resolved_feedbacks = models.IntegerField(default=0, verbose_name='已解决反馈')
    pending_feedbacks = models.IntegerField(default=0, verbose_name='待处理反馈')
    
    # 按类型统计
    bug_count = models.IntegerField(default=0, verbose_name='问题反馈数')
    feature_count = models.IntegerField(default=0, verbose_name='功能建议数')
    complaint_count = models.IntegerField(default=0, verbose_name='投诉建议数')
    praise_count = models.IntegerField(default=0, verbose_name='表扬建议数')
    
    # 处理效率统计
    avg_resolution_time = models.FloatField(default=0, verbose_name='平均处理时间(小时)')
    resolution_rate = models.FloatField(default=0, verbose_name='解决率(%)')
    user_satisfaction = models.FloatField(default=0, verbose_name='用户满意度')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈统计'
        verbose_name_plural = '反馈统计'
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.date} - 反馈数: {self.total_feedbacks}"


class FeedbackTag(models.Model):
    """反馈标签"""
    name = models.CharField(max_length=50, unique=True, verbose_name='标签名称')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='标签颜色')
    description = models.TextField(blank=True, verbose_name='标签描述')
    
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈标签'
        verbose_name_plural = '反馈标签'
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name


class FeedbackTagRelation(models.Model):
    """反馈标签关联"""
    feedback = models.ForeignKey(
        UserFeedback, 
        on_delete=models.CASCADE, 
        related_name='tag_relations',
        verbose_name='反馈'
    )
    tag = models.ForeignKey(
        FeedbackTag, 
        on_delete=models.CASCADE, 
        related_name='feedback_relations',
        verbose_name='标签'
    )
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈标签关联'
        verbose_name_plural = '反馈标签关联'
        unique_together = ['feedback', 'tag']
    
    def __str__(self):
        return f"{self.feedback.title} - {self.tag.name}"


class FeedbackNotification(models.Model):
    """反馈通知"""
    NOTIFICATION_TYPES = [
        ('new_feedback', '新反馈'),
        ('status_change', '状态变更'),
        ('new_reply', '新回复'),
        ('overdue', '超期提醒'),
        ('user_rating', '用户评价'),
    ]
    
    feedback = models.ForeignKey(UserFeedback, on_delete=models.CASCADE, verbose_name='反馈')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name='通知类型')
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='接收人')
    
    title = models.CharField(max_length=200, verbose_name='通知标题')
    content = models.TextField(verbose_name='通知内容')
    
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '反馈通知'
        verbose_name_plural = '反馈通知'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.recipient.username} - {self.title}"
