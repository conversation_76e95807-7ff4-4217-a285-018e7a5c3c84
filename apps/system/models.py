from django.db import models
from django.utils import timezone
from django.conf import settings
from apps.users.models import User


class SystemConfig(models.Model):
    """系统配置"""
    CONFIG_TYPES = [
        ('basic', '基础配置'),
        ('wechat', '微信配置'),
        ('payment', '支付配置'),
        ('notification', '通知配置'),
        ('security', '安全配置'),
        ('feature', '功能配置'),
        ('ui', '界面配置'),
        ('system', '系统配置'),
        ('sms', '短信配置'),
        ('email', '邮件配置'),
    ]
    
    key = models.CharField(max_length=100, unique=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    config_type = models.CharField(max_length=20, choices=CONFIG_TYPES, verbose_name='配置类型')
    description = models.TextField(blank=True, verbose_name='配置说明')
    
    # 配置属性
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'system_configs'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        ordering = ['config_type', 'key']
    
    def __str__(self):
        return f'{self.key}: {self.value[:50]}'


class Announcement(models.Model):
    """系统公告"""
    ANNOUNCEMENT_TYPES = [
        ('system', '系统公告'),
        ('maintenance', '维护公告'),
        ('feature', '功能更新'),
        ('activity', '活动公告'),
        ('notice', '重要通知'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='公告标题')
    content = models.TextField(verbose_name='公告内容')
    announcement_type = models.CharField(max_length=20, choices=ANNOUNCEMENT_TYPES, verbose_name='公告类型')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal', verbose_name='优先级')
    
    # 显示设置
    is_popup = models.BooleanField(default=False, verbose_name='是否弹窗显示')
    is_top = models.BooleanField(default=False, verbose_name='是否置顶')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 时间设置
    start_time = models.DateTimeField(default=timezone.now, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 统计信息
    view_count = models.IntegerField(default=0, verbose_name='查看次数')
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                  related_name='created_announcements', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'announcements'
        verbose_name = '系统公告'
        verbose_name_plural = '系统公告'
        ordering = ['-is_top', '-priority', '-created_at']
    
    def __str__(self):
        return self.title


class BannerAd(models.Model):
    """轮播广告"""
    AD_TYPES = [
        ('banner', '轮播图'),
        ('popup', '弹窗广告'),
        ('floating', '浮动广告'),
        ('native', '原生广告'),
    ]
    
    POSITIONS = [
        ('home_top', '首页顶部'),
        ('home_middle', '首页中部'),
        ('discover_top', '发现页顶部'),
        ('profile_top', '个人页顶部'),
        ('chat_bottom', '聊天页底部'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='广告标题')
    image = models.URLField(verbose_name='广告图片')
    link_url = models.URLField(blank=True, verbose_name='跳转链接')
    
    ad_type = models.CharField(max_length=20, choices=AD_TYPES, verbose_name='广告类型')
    position = models.CharField(max_length=20, choices=POSITIONS, verbose_name='显示位置')
    
    # 显示设置
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 时间设置
    start_time = models.DateTimeField(default=timezone.now, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 统计信息
    view_count = models.IntegerField(default=0, verbose_name='展示次数')
    click_count = models.IntegerField(default=0, verbose_name='点击次数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'banner_ads'
        verbose_name = '轮播广告'
        verbose_name_plural = '轮播广告'
        ordering = ['position', 'sort_order']
    
    def __str__(self):
        return f'{self.title} - {self.get_position_display()}'
    
    @property
    def click_rate(self):
        """点击率"""
        if self.view_count == 0:
            return 0
        return round(self.click_count / self.view_count * 100, 2)


class AppVersion(models.Model):
    """应用版本管理"""
    PLATFORMS = [
        ('miniprogram', '微信小程序'),
        ('android', 'Android'),
        ('ios', 'iOS'),
        ('web', 'Web'),
    ]
    
    platform = models.CharField(max_length=20, choices=PLATFORMS, verbose_name='平台')
    version_name = models.CharField(max_length=50, verbose_name='版本名称')
    version_code = models.IntegerField(verbose_name='版本号')
    
    # 更新信息
    update_content = models.TextField(verbose_name='更新内容')
    download_url = models.URLField(blank=True, verbose_name='下载链接')
    
    # 更新设置
    is_force_update = models.BooleanField(default=False, verbose_name='是否强制更新')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 统计信息
    download_count = models.IntegerField(default=0, verbose_name='下载次数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'app_versions'
        verbose_name = '应用版本'
        verbose_name_plural = '应用版本'
        ordering = ['-version_code']
        unique_together = ['platform', 'version_code']
    
    def __str__(self):
        return f'{self.get_platform_display()} v{self.version_name}'


class FeedbackCategory(models.Model):
    """反馈分类"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'feedback_categories'
        verbose_name = '反馈分类'
        verbose_name_plural = '反馈分类'
        ordering = ['sort_order']
    
    def __str__(self):
        return self.name


class UserFeedback(models.Model):
    """用户反馈"""
    FEEDBACK_TYPES = [
        ('bug', 'Bug反馈'),
        ('suggestion', '功能建议'),
        ('complaint', '投诉举报'),
        ('praise', '表扬建议'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    category = models.ForeignKey(FeedbackCategory, on_delete=models.CASCADE, verbose_name='反馈分类')
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPES, verbose_name='反馈类型')
    
    title = models.CharField(max_length=200, verbose_name='反馈标题')
    content = models.TextField(verbose_name='反馈内容')
    images = models.JSONField(default=list, verbose_name='反馈图片')
    
    # 联系信息
    contact_info = models.CharField(max_length=200, blank=True, verbose_name='联系方式')
    
    # 处理状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='处理状态')
    admin_reply = models.TextField(blank=True, verbose_name='管理员回复')
    processed_by = models.CharField(max_length=100, blank=True, verbose_name='处理人')
    processed_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_feedbacks'
        verbose_name = '用户反馈'
        verbose_name_plural = '用户反馈'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.title}'
