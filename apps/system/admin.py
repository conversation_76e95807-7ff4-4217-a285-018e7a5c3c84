from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from django.db import models
from .models import (SystemConfig, Announcement, BannerAd, AppVersion,
                    FeedbackCategory, UserFeedback)


@admin.register(SystemConfig)
class SystemConfigAdmin(admin.ModelAdmin):
    list_display = ['key', 'value_preview', 'config_type_display', 'is_active', 'is_public', 'updated_at']
    list_filter = ['config_type', 'is_active', 'is_public']
    search_fields = ['key', 'value', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('key', 'value', 'config_type', 'description')
        }),
        ('设置', {
            'fields': ('is_active', 'is_public')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """自定义查询集，按配置类型排序"""
        return super().get_queryset(request).order_by('config_type', 'key')

    def value_preview(self, obj):
        """显示配置值预览"""
        if len(obj.value) > 50:
            return obj.value[:50] + '...'
        return obj.value
    value_preview.short_description = '配置值'

    def config_type_display(self, obj):
        """显示配置类型"""
        colors = {
            'basic': '#007cba',
            'wechat': '#1aad19',
            'payment': '#ff9500',
            'notification': '#ff3b30',
            'security': '#5856d6',
            'feature': '#34c759',
            'ui': '#af52de',
            'system': '#007aff',
            'sms': '#ff2d92',
            'email': '#5ac8fa',
        }
        color = colors.get(obj.config_type, '#8e8e93')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_config_type_display()
        )
    config_type_display.short_description = '配置类型'

    def changelist_view(self, request, extra_context=None):
        """自定义列表视图，添加配置统计"""
        extra_context = extra_context or {}

        # 统计各类型配置数量
        from django.db.models import Count
        stats = SystemConfig.objects.values('config_type').annotate(
            count=Count('id'),
            active_count=Count('id', filter=models.Q(is_active=True))
        ).order_by('config_type')

        extra_context['config_stats'] = stats
        return super().changelist_view(request, extra_context)
    
    def config_type_display(self, obj):
        colors = {
            'basic': 'blue', 'payment': 'green', 'notification': 'orange',
            'security': 'red', 'feature': 'purple', 'ui': 'brown'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.config_type, 'black'),
            obj.get_config_type_display()
        )
    config_type_display.short_description = '配置类型'


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ['title', 'announcement_type_display', 'priority_display', 
                   'is_popup', 'is_top', 'is_active', 'view_count', 'created_at']
    list_filter = ['announcement_type', 'priority', 'is_popup', 'is_top', 'is_active', 'created_at']
    search_fields = ['title', 'content']
    readonly_fields = ['view_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'announcement_type', 'priority')
        }),
        ('显示设置', {
            'fields': ('is_popup', 'is_top', 'is_active')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('统计信息', {
            'fields': ('view_count',)
        }),
        ('管理信息', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )
    
    def announcement_type_display(self, obj):
        colors = {
            'system': 'blue', 'maintenance': 'orange', 'feature': 'green',
            'activity': 'purple', 'notice': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.announcement_type, 'black'),
            obj.get_announcement_type_display()
        )
    announcement_type_display.short_description = '公告类型'
    
    def priority_display(self, obj):
        colors = {'low': 'gray', 'normal': 'blue', 'high': 'orange', 'urgent': 'red'}
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.priority, 'black'),
            obj.get_priority_display()
        )
    priority_display.short_description = '优先级'
    
    actions = ['activate_announcements', 'deactivate_announcements', 'set_top']
    
    def activate_announcements(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'已激活 {updated} 条公告')
    activate_announcements.short_description = '激活公告'
    
    def deactivate_announcements(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'已停用 {updated} 条公告')
    deactivate_announcements.short_description = '停用公告'
    
    def set_top(self, request, queryset):
        updated = queryset.update(is_top=True)
        self.message_user(request, f'已置顶 {updated} 条公告')
    set_top.short_description = '置顶公告'


@admin.register(BannerAd)
class BannerAdAdmin(admin.ModelAdmin):
    list_display = ['title', 'ad_type_display', 'position_display', 'sort_order',
                   'view_count', 'click_count', 'click_rate_display', 'is_active']
    list_filter = ['ad_type', 'position', 'is_active', 'created_at']
    search_fields = ['title', 'link_url']
    readonly_fields = ['view_count', 'click_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'image', 'link_url')
        }),
        ('广告设置', {
            'fields': ('ad_type', 'position', 'sort_order', 'is_active')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('统计信息', {
            'fields': ('view_count', 'click_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def ad_type_display(self, obj):
        return obj.get_ad_type_display()
    ad_type_display.short_description = '广告类型'
    
    def position_display(self, obj):
        return obj.get_position_display()
    position_display.short_description = '显示位置'
    
    def click_rate_display(self, obj):
        rate = obj.click_rate
        color = 'green' if rate > 5 else 'orange' if rate > 2 else 'red'
        return format_html('<span style="color: {};">{:.2f}%</span>', color, rate)
    click_rate_display.short_description = '点击率'


@admin.register(AppVersion)
class AppVersionAdmin(admin.ModelAdmin):
    list_display = ['platform_display', 'version_name', 'version_code', 
                   'is_force_update', 'is_active', 'download_count', 'created_at']
    list_filter = ['platform', 'is_force_update', 'is_active', 'created_at']
    search_fields = ['version_name', 'update_content']
    readonly_fields = ['download_count', 'created_at']
    
    fieldsets = (
        ('版本信息', {
            'fields': ('platform', 'version_name', 'version_code')
        }),
        ('更新信息', {
            'fields': ('update_content', 'download_url')
        }),
        ('更新设置', {
            'fields': ('is_force_update', 'is_active')
        }),
        ('统计信息', {
            'fields': ('download_count',)
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def platform_display(self, obj):
        colors = {
            'miniprogram': 'green', 'android': 'blue', 
            'ios': 'gray', 'web': 'purple'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.platform, 'black'),
            obj.get_platform_display()
        )
    platform_display.short_description = '平台'


@admin.register(FeedbackCategory)
class FeedbackCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description_preview', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']
    
    def description_preview(self, obj):
        return obj.description[:50] + '...' if len(obj.description) > 50 else obj.description
    description_preview.short_description = '描述'


@admin.register(UserFeedback)
class UserFeedbackAdmin(admin.ModelAdmin):
    list_display = ['user', 'title', 'category', 'feedback_type_display', 
                   'status_display', 'processed_by', 'created_at']
    list_filter = ['category', 'feedback_type', 'status', 'created_at']
    search_fields = ['user__nickname', 'title', 'content']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('反馈信息', {
            'fields': ('user', 'category', 'feedback_type', 'title', 'content', 'images')
        }),
        ('联系信息', {
            'fields': ('contact_info',)
        }),
        ('处理信息', {
            'fields': ('status', 'admin_reply', 'processed_by', 'processed_at')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def feedback_type_display(self, obj):
        colors = {
            'bug': 'red', 'suggestion': 'blue', 'complaint': 'orange',
            'praise': 'green', 'other': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.feedback_type, 'black'),
            obj.get_feedback_type_display()
        )
    feedback_type_display.short_description = '反馈类型'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange', 'processing': 'blue', 
            'resolved': 'green', 'closed': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '处理状态'
    
    actions = ['mark_as_processing', 'mark_as_resolved']
    
    def mark_as_processing(self, request, queryset):
        updated = queryset.update(
            status='processing', 
            processed_by=request.user.username,
            processed_at=timezone.now()
        )
        self.message_user(request, f'已标记 {updated} 条反馈为处理中')
    mark_as_processing.short_description = '标记为处理中'
    
    def mark_as_resolved(self, request, queryset):
        updated = queryset.update(
            status='resolved',
            processed_by=request.user.username,
            processed_at=timezone.now()
        )
        self.message_user(request, f'已标记 {updated} 条反馈为已解决')
    mark_as_resolved.short_description = '标记为已解决'
