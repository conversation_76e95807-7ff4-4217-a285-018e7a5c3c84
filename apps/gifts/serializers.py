from rest_framework import serializers
from .models import Gift, GiftCategory, GiftRecord, UserGiftBox
from apps.users.serializers import UserProfileSerializer


class GiftCategorySerializer(serializers.ModelSerializer):
    """礼品分类序列化器"""
    
    class Meta:
        model = GiftCategory
        fields = ['id', 'name', 'icon', 'sort_order', 'is_active']


class GiftSerializer(serializers.ModelSerializer):
    """礼品序列化器"""
    category = GiftCategorySerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True, required=False)
    is_available = serializers.ReadOnlyField()
    discount_rate = serializers.ReadOnlyField()

    class Meta:
        model = Gift
        fields = [
            'id', 'name', 'description', 'icon', 'animation_url', 'category', 'category_id',
            'price', 'original_price', 'is_rare', 'is_limited', 'limited_quantity',
            'sold_quantity', 'effects', 'sort_order', 'is_active', 'is_popular',
            'send_count', 'is_available', 'discount_rate', 'created_at'
        ]
        read_only_fields = ['id', 'sold_quantity', 'send_count', 'created_at']


class GiftRecordSerializer(serializers.ModelSerializer):
    """礼品记录序列化器"""
    sender = UserProfileSerializer(read_only=True)
    receiver = UserProfileSerializer(read_only=True)
    gift = GiftSerializer(read_only=True)
    gift_id = serializers.IntegerField(write_only=True)
    receiver_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = GiftRecord
        fields = [
            'id', 'sender', 'receiver', 'gift', 'gift_id', 'receiver_id',
            'quantity', 'total_cost', 'payment_type', 'message',
            'is_anonymous', 'created_at'
        ]
        read_only_fields = ['id', 'sender', 'total_cost', 'created_at']
    
    def create(self, validated_data):
        # 自动设置发送者
        validated_data['sender'] = self.context['request'].user

        # 计算总费用
        gift = Gift.objects.get(id=validated_data['gift_id'])
        quantity = validated_data['quantity']

        validated_data['unit_price'] = gift.price
        validated_data['total_price'] = gift.price * quantity

        return super().create(validated_data)


class UserGiftBoxSerializer(serializers.ModelSerializer):
    """用户礼品盒序列化器"""
    gift = GiftSerializer(read_only=True)

    class Meta:
        model = UserGiftBox
        fields = [
            'id', 'gift', 'quantity', 'received_at', 'updated_at'
        ]
        read_only_fields = ['id', 'received_at', 'updated_at']


class SendGiftSerializer(serializers.Serializer):
    """发送礼品序列化器"""
    gift_id = serializers.IntegerField()
    receiver_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1, max_value=99, default=1)
    message = serializers.CharField(max_length=200, required=False, allow_blank=True)
    is_anonymous = serializers.BooleanField(default=False)
    
    def validate_gift_id(self, value):
        try:
            gift = Gift.objects.get(id=value, is_active=True)
            if gift.is_limited and gift.sold_quantity >= gift.limited_quantity:
                raise serializers.ValidationError("礼品库存不足")
            return value
        except Gift.DoesNotExist:
            raise serializers.ValidationError("礼品不存在")
    
    def validate_receiver_id(self, value):
        from apps.users.models import User
        try:
            User.objects.get(id=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("接收者不存在")


class GiftStoreSerializer(serializers.ModelSerializer):
    """礼品商店序列化器（用于商店展示）"""
    category = GiftCategorySerializer(read_only=True)
    is_available = serializers.ReadOnlyField()
    discount_rate = serializers.ReadOnlyField()

    class Meta:
        model = Gift
        fields = [
            'id', 'name', 'description', 'icon', 'animation_url', 'category',
            'price', 'original_price', 'is_rare', 'is_limited', 'limited_quantity',
            'sold_quantity', 'effects', 'is_popular', 'send_count',
            'is_available', 'discount_rate'
        ]


class GiftStatisticsSerializer(serializers.Serializer):
    """礼品统计序列化器"""
    total_sent = serializers.IntegerField()
    total_received = serializers.IntegerField()
    total_cost = serializers.DecimalField(max_digits=10, decimal_places=2)
    favorite_gift = GiftSerializer()
    recent_records = GiftRecordSerializer(many=True)
