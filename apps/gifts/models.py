from django.db import models
from apps.users.models import User


class GiftCategory(models.Model):
    """礼物分类"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    icon = models.URLField(blank=True, verbose_name='分类图标')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'gift_categories'
        verbose_name = '礼物分类'
        verbose_name_plural = '礼物分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class Gift(models.Model):
    """礼物"""
    category = models.ForeignKey(GiftCategory, on_delete=models.CASCADE, related_name='gifts', 
                                verbose_name='分类')
    name = models.CharField(max_length=100, verbose_name='礼物名称')
    description = models.TextField(blank=True, verbose_name='礼物描述')
    icon = models.URLField(verbose_name='礼物图标')
    animation_url = models.URLField(blank=True, verbose_name='动画URL')
    
    # 价格信息
    price = models.IntegerField(verbose_name='价格(金币)')
    original_price = models.IntegerField(null=True, blank=True, verbose_name='原价(金币)')
    
    # 特殊属性
    is_rare = models.BooleanField(default=False, verbose_name='是否稀有')
    is_limited = models.BooleanField(default=False, verbose_name='是否限量')
    limited_quantity = models.IntegerField(null=True, blank=True, verbose_name='限量数量')
    sold_quantity = models.IntegerField(default=0, verbose_name='已售数量')
    
    # 效果配置
    effects = models.JSONField(default=dict, verbose_name='特效配置')
    
    # 排序和状态
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_popular = models.BooleanField(default=False, verbose_name='是否热门')
    
    # 统计信息
    send_count = models.IntegerField(default=0, verbose_name='赠送次数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'gifts'
        verbose_name = '礼物'
        verbose_name_plural = '礼物'
        ordering = ['sort_order', 'price']
    
    def __str__(self):
        return f'{self.name} - {self.price}金币'
    
    @property
    def is_available(self):
        """是否可购买"""
        if not self.is_active:
            return False
        if self.is_limited and self.sold_quantity >= self.limited_quantity:
            return False
        return True
    
    @property
    def discount_rate(self):
        """折扣率"""
        if self.original_price and self.original_price > self.price:
            return (self.original_price - self.price) / self.original_price
        return 0


class GiftRecord(models.Model):
    """礼物赠送记录"""
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_gifts', 
                              verbose_name='发送者')
    receiver = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_gifts', 
                                verbose_name='接收者')
    gift = models.ForeignKey(Gift, on_delete=models.CASCADE, related_name='gift_records', 
                            verbose_name='礼物')
    
    # 赠送信息
    quantity = models.IntegerField(default=1, verbose_name='数量')
    unit_price = models.IntegerField(verbose_name='单价(金币)')
    total_price = models.IntegerField(verbose_name='总价(金币)')
    message = models.TextField(max_length=200, blank=True, verbose_name='附言')
    
    # 状态信息
    is_anonymous = models.BooleanField(default=False, verbose_name='是否匿名')
    is_read = models.BooleanField(default=False, verbose_name='是否已读')
    read_at = models.DateTimeField(null=True, blank=True, verbose_name='阅读时间')
    
    # 关联信息
    chat_message = models.OneToOneField('chat.ChatMessage', on_delete=models.SET_NULL, 
                                       null=True, blank=True, verbose_name='聊天消息')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'gift_records'
        verbose_name = '礼物记录'
        verbose_name_plural = '礼物记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.sender.nickname} 送给 {self.receiver.nickname} {self.gift.name}'
    
    def mark_as_read(self):
        """标记为已读"""
        if not self.is_read:
            self.is_read = True
            from django.utils import timezone
            self.read_at = timezone.now()
            self.save()


class UserGiftBox(models.Model):
    """用户礼物盒"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='gift_box', 
                            verbose_name='用户')
    gift = models.ForeignKey(Gift, on_delete=models.CASCADE, verbose_name='礼物')
    quantity = models.IntegerField(default=0, verbose_name='数量')
    
    # 获得方式
    source = models.CharField(max_length=50, default='received', verbose_name='来源')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_gift_box'
        verbose_name = '用户礼物盒'
        verbose_name_plural = '用户礼物盒'
        unique_together = ['user', 'gift']
        ordering = ['-updated_at']
    
    def __str__(self):
        return f'{self.user.nickname} 拥有 {self.gift.name} x{self.quantity}'


class GiftStatistics(models.Model):
    """礼物统计"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='gift_stats', 
                               verbose_name='用户')
    
    # 发送统计
    total_gifts_sent = models.IntegerField(default=0, verbose_name='发送礼物总数')
    total_coins_spent = models.IntegerField(default=0, verbose_name='花费金币总数')
    favorite_gift = models.ForeignKey(Gift, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='favorite_by_users', verbose_name='最爱礼物')
    
    # 接收统计
    total_gifts_received = models.IntegerField(default=0, verbose_name='接收礼物总数')
    total_coins_received = models.IntegerField(default=0, verbose_name='收到金币总数')
    most_received_gift = models.ForeignKey(Gift, on_delete=models.SET_NULL, null=True, blank=True,
                                          related_name='most_received_by_users', 
                                          verbose_name='最常收到礼物')
    
    # 今日统计
    today_gifts_sent = models.IntegerField(default=0, verbose_name='今日发送礼物')
    today_gifts_received = models.IntegerField(default=0, verbose_name='今日接收礼物')
    today_coins_spent = models.IntegerField(default=0, verbose_name='今日花费金币')
    
    # 排名信息
    sender_rank = models.IntegerField(default=0, verbose_name='发送者排名')
    receiver_rank = models.IntegerField(default=0, verbose_name='接收者排名')
    
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'gift_statistics'
        verbose_name = '礼物统计'
        verbose_name_plural = '礼物统计'
    
    def __str__(self):
        return f'{self.user.nickname} 礼物统计'
    
    def reset_daily_stats(self):
        """重置每日统计"""
        self.today_gifts_sent = 0
        self.today_gifts_received = 0
        self.today_coins_spent = 0
        self.save()


class GiftActivity(models.Model):
    """礼物活动"""
    name = models.CharField(max_length=100, verbose_name='活动名称')
    description = models.TextField(verbose_name='活动描述')
    banner_image = models.URLField(blank=True, verbose_name='横幅图片')
    
    # 活动时间
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    
    # 活动规则
    rules = models.JSONField(default=dict, verbose_name='活动规则')
    rewards = models.JSONField(default=list, verbose_name='奖励配置')
    
    # 参与限制
    max_participants = models.IntegerField(null=True, blank=True, verbose_name='最大参与人数')
    current_participants = models.IntegerField(default=0, verbose_name='当前参与人数')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'gift_activities'
        verbose_name = '礼物活动'
        verbose_name_plural = '礼物活动'
        ordering = ['-start_time']
    
    def __str__(self):
        return self.name
    
    @property
    def is_ongoing(self):
        """是否进行中"""
        from django.utils import timezone
        now = timezone.now()
        return self.start_time <= now <= self.end_time and self.is_active


class GiftActivityParticipant(models.Model):
    """礼物活动参与者"""
    activity = models.ForeignKey(GiftActivity, on_delete=models.CASCADE, 
                                related_name='participants', verbose_name='活动')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='gift_activities', 
                            verbose_name='用户')
    
    # 参与数据
    participation_data = models.JSONField(default=dict, verbose_name='参与数据')
    score = models.IntegerField(default=0, verbose_name='得分')
    rank = models.IntegerField(default=0, verbose_name='排名')
    
    # 奖励信息
    rewards_received = models.JSONField(default=list, verbose_name='已获得奖励')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='参与时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'gift_activity_participants'
        verbose_name = '活动参与者'
        verbose_name_plural = '活动参与者'
        unique_together = ['activity', 'user']
        ordering = ['-score']
    
    def __str__(self):
        return f'{self.user.nickname} 参与 {self.activity.name}'
