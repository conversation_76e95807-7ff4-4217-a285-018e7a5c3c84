from django.contrib import admin
from django.utils.html import format_html
from .models import (GiftCategory, Gift, GiftRecord, UserGiftBox, 
                    GiftStatistics, GiftActivity, GiftActivityParticipant)


@admin.register(GiftCategory)
class GiftCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'icon')
        }),
        ('管理信息', {
            'fields': ('sort_order', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(Gift)
class GiftAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'price_display', 'availability_display', 
                   'send_count', 'is_popular', 'is_active', 'created_at']
    list_filter = ['category', 'is_rare', 'is_limited', 'is_popular', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['send_count', 'sold_quantity', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('category', 'name', 'description', 'icon', 'animation_url')
        }),
        ('价格信息', {
            'fields': ('price', 'original_price')
        }),
        ('特殊属性', {
            'fields': ('is_rare', 'is_limited', 'limited_quantity', 'sold_quantity')
        }),
        ('效果配置', {
            'fields': ('effects',)
        }),
        ('管理信息', {
            'fields': ('sort_order', 'is_active', 'is_popular')
        }),
        ('统计信息', {
            'fields': ('send_count',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def price_display(self, obj):
        if obj.original_price and obj.original_price > obj.price:
            return format_html(
                '<span style="text-decoration: line-through; color: gray;">{}</span> '
                '<span style="color: red; font-weight: bold;">{}</span> 金币',
                obj.original_price, obj.price
            )
        return f'{obj.price} 金币'
    price_display.short_description = '价格'
    
    def availability_display(self, obj):
        if not obj.is_active:
            return format_html('<span style="color: red;">已下架</span>')
        elif obj.is_limited:
            remaining = obj.limited_quantity - obj.sold_quantity
            if remaining <= 0:
                return format_html('<span style="color: red;">已售罄</span>')
            else:
                return format_html('<span style="color: orange;">限量 (剩余{})</span>', remaining)
        else:
            return format_html('<span style="color: green;">正常销售</span>')
    availability_display.short_description = '可用性'


@admin.register(GiftRecord)
class GiftRecordAdmin(admin.ModelAdmin):
    list_display = ['sender', 'receiver', 'gift', 'quantity', 'total_price', 
                   'is_anonymous', 'is_read', 'created_at']
    list_filter = ['is_anonymous', 'is_read', 'created_at']
    search_fields = ['sender__nickname', 'receiver__nickname', 'gift__name', 'message']
    readonly_fields = ['unit_price', 'total_price', 'read_at', 'created_at']
    
    fieldsets = (
        ('赠送信息', {
            'fields': ('sender', 'receiver', 'gift', 'quantity', 'unit_price', 'total_price')
        }),
        ('附加信息', {
            'fields': ('message', 'is_anonymous')
        }),
        ('状态信息', {
            'fields': ('is_read', 'read_at', 'chat_message')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )


@admin.register(UserGiftBox)
class UserGiftBoxAdmin(admin.ModelAdmin):
    list_display = ['user', 'gift', 'quantity', 'source', 'updated_at']
    list_filter = ['source', 'created_at']
    search_fields = ['user__nickname', 'gift__name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GiftStatistics)
class GiftStatisticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_gifts_sent', 'total_gifts_received', 
                   'total_coins_spent', 'total_coins_received', 'sender_rank', 'receiver_rank']
    search_fields = ['user__nickname']
    readonly_fields = ['updated_at']
    
    fieldsets = (
        ('发送统计', {
            'fields': ('total_gifts_sent', 'total_coins_spent', 'favorite_gift', 'sender_rank')
        }),
        ('接收统计', {
            'fields': ('total_gifts_received', 'total_coins_received', 'most_received_gift', 'receiver_rank')
        }),
        ('今日统计', {
            'fields': ('today_gifts_sent', 'today_gifts_received', 'today_coins_spent')
        }),
        ('时间信息', {
            'fields': ('updated_at',)
        }),
    )


@admin.register(GiftActivity)
class GiftActivityAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_time', 'end_time', 'current_participants', 
                   'max_participants', 'status_display', 'is_active']
    list_filter = ['is_active', 'start_time', 'end_time']
    search_fields = ['name', 'description']
    readonly_fields = ['current_participants', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'banner_image')
        }),
        ('活动时间', {
            'fields': ('start_time', 'end_time')
        }),
        ('活动规则', {
            'fields': ('rules', 'rewards')
        }),
        ('参与限制', {
            'fields': ('max_participants', 'current_participants')
        }),
        ('状态信息', {
            'fields': ('is_active',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def status_display(self, obj):
        if obj.is_ongoing:
            return format_html('<span style="color: green;">进行中</span>')
        else:
            from django.utils import timezone
            now = timezone.now()
            if now < obj.start_time:
                return format_html('<span style="color: orange;">未开始</span>')
            else:
                return format_html('<span style="color: gray;">已结束</span>')
    status_display.short_description = '活动状态'


@admin.register(GiftActivityParticipant)
class GiftActivityParticipantAdmin(admin.ModelAdmin):
    list_display = ['activity', 'user', 'score', 'rank', 'created_at']
    list_filter = ['activity', 'created_at']
    search_fields = ['activity__name', 'user__nickname']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('activity', 'user')
        }),
        ('参与数据', {
            'fields': ('participation_data', 'score', 'rank')
        }),
        ('奖励信息', {
            'fields': ('rewards_received',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
