from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q, Sum, Count
from django.db import transaction
from django.utils import timezone
from .models import Gift, GiftCategory, GiftRecord, UserGiftBox, GiftStatistics
from .serializers import (
    GiftSerializer, GiftCategorySerializer, GiftRecordSerializer,
    UserGiftBoxSerializer, SendGiftSerializer, GiftStoreSerializer
)
from apps.payment.models import CoinBalance


class GiftCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """礼品分类视图集"""
    queryset = GiftCategory.objects.filter(is_active=True).order_by('sort_order')
    serializer_class = GiftCategorySerializer
    permission_classes = [permissions.IsAuthenticated]


class GiftViewSet(viewsets.ReadOnlyModelViewSet):
    """礼品视图集"""
    serializer_class = GiftSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = Gift.objects.filter(is_active=True).order_by('sort_order')
        
        # 按分类筛选
        category_id = self.request.query_params.get('category_id')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # 按价格类型筛选
        price_type = self.request.query_params.get('price_type')
        if price_type in ['coin', 'cash']:
            queryset = queryset.filter(price_type=price_type)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def store(self, request):
        """礼品商店 - 按分类展示礼品"""
        categories = GiftCategory.objects.filter(is_active=True).order_by('sort_order')
        result = []
        
        for category in categories:
            gifts = Gift.objects.filter(
                category=category, 
                is_active=True
            ).order_by('sort_order')
            
            category_data = GiftCategorySerializer(category).data
            category_data['gifts'] = GiftStoreSerializer(gifts, many=True).data
            result.append(category_data)
        
        return Response(result)
    
    @action(detail=False, methods=['get'])
    def popular(self, request):
        """热门礼品"""
        # 根据发送次数统计热门礼品
        popular_gifts = Gift.objects.filter(is_active=True).annotate(
            send_count=Count('giftrecord')
        ).order_by('-send_count')[:10]
        
        serializer = GiftStoreSerializer(popular_gifts, many=True)
        return Response(serializer.data)


class GiftRecordViewSet(viewsets.ModelViewSet):
    """礼品记录视图集"""
    serializer_class = GiftRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        
        # 根据查询参数决定返回发送的还是接收的记录
        record_type = self.request.query_params.get('type', 'all')
        
        if record_type == 'sent':
            return GiftRecord.objects.filter(sender=user).order_by('-created_at')
        elif record_type == 'received':
            return GiftRecord.objects.filter(receiver=user).order_by('-created_at')
        else:
            return GiftRecord.objects.filter(
                Q(sender=user) | Q(receiver=user)
            ).order_by('-created_at')
    
    @action(detail=False, methods=['post'])
    def send_gift(self, request):
        """发送礼品"""
        serializer = SendGiftSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # 获取礼品和接收者
                gift = Gift.objects.get(id=serializer.validated_data['gift_id'])
                receiver_id = serializer.validated_data['receiver_id']
                quantity = serializer.validated_data['quantity']
                message = serializer.validated_data.get('message', '')
                is_anonymous = serializer.validated_data.get('is_anonymous', False)

                # 检查库存
                if gift.is_limited and gift.sold_quantity + quantity > gift.limited_quantity:
                    return Response({
                        'code': 400,
                        'message': '礼品库存不足'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 计算费用
                total_cost = gift.price * quantity

                # 检查用户金币余额
                coin_balance, created = CoinBalance.objects.get_or_create(
                    user=request.user,
                    defaults={'balance': 0}
                )

                if coin_balance.balance < total_cost:
                    return Response({
                        'code': 400,
                        'message': '金币余额不足'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 扣除金币
                coin_balance.balance -= total_cost
                coin_balance.total_spent += total_cost
                coin_balance.save()

                # 获取接收者
                from apps.users.models import User
                receiver = User.objects.get(id=receiver_id)

                # 创建礼品记录
                gift_record = GiftRecord.objects.create(
                    sender=request.user,
                    receiver=receiver,
                    gift=gift,
                    quantity=quantity,
                    unit_price=gift.price,
                    total_price=total_cost,
                    message=message,
                    is_anonymous=is_anonymous
                )

                # 更新库存
                if gift.is_limited:
                    gift.sold_quantity += quantity
                    gift.save()

                # 更新礼物发送统计
                gift.send_count += quantity
                gift.save()

                # 更新接收者礼物盒
                user_gift_box, created = UserGiftBox.objects.get_or_create(
                    user=receiver,
                    gift=gift,
                    defaults={'quantity': 0}
                )
                user_gift_box.quantity += quantity
                user_gift_box.save()

                # 更新用户统计
                sender_stats, created = GiftStatistics.objects.get_or_create(
                    user=request.user,
                    defaults={
                        'total_gifts_sent': 0,
                        'total_coins_spent': 0,
                        'today_gifts_sent': 0,
                        'today_coins_spent': 0
                    }
                )
                sender_stats.total_gifts_sent += quantity
                sender_stats.total_coins_spent += total_cost
                sender_stats.today_gifts_sent += quantity
                sender_stats.today_coins_spent += total_cost
                sender_stats.save()

                receiver_stats, created = GiftStatistics.objects.get_or_create(
                    user=receiver,
                    defaults={
                        'total_gifts_received': 0,
                        'total_coins_received': 0,
                        'today_gifts_received': 0
                    }
                )
                receiver_stats.total_gifts_received += quantity
                receiver_stats.total_coins_received += total_cost
                receiver_stats.today_gifts_received += quantity
                receiver_stats.save()

                return Response({
                    'code': 200,
                    'message': '礼物赠送成功',
                    'data': GiftRecordSerializer(gift_record).data
                }, status=status.HTTP_201_CREATED)

        except Gift.DoesNotExist:
            return Response({
                'code': 404,
                'message': '礼物不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except User.DoesNotExist:
            return Response({
                'code': 404,
                'message': '接收者不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'系统错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """礼品统计"""
        user = request.user

        # 发送统计
        sent_stats = GiftRecord.objects.filter(sender=user).aggregate(
            total_sent=Count('id'),
            total_cost=Sum('total_price')
        )

        # 接收统计
        received_stats = GiftRecord.objects.filter(receiver=user).aggregate(
            total_received=Count('id'),
            total_value=Sum('total_price')
        )

        # 最喜欢的礼品（发送最多的）
        favorite_gift_record = GiftRecord.objects.filter(sender=user).values('gift').annotate(
            count=Count('gift')
        ).order_by('-count').first()

        favorite_gift = None
        if favorite_gift_record:
            favorite_gift = Gift.objects.get(id=favorite_gift_record['gift'])

        # 最近记录
        recent_records = GiftRecord.objects.filter(
            Q(sender=user) | Q(receiver=user)
        ).select_related('gift', 'sender', 'receiver').order_by('-created_at')[:5]

        return Response({
            'code': 200,
            'data': {
                'sent': {
                    'total_count': sent_stats['total_sent'] or 0,
                    'total_cost': sent_stats['total_cost'] or 0
                },
                'received': {
                    'total_count': received_stats['total_received'] or 0,
                    'total_value': received_stats['total_value'] or 0
                },
                'favorite_gift': GiftSerializer(favorite_gift).data if favorite_gift else None,
                'recent_records': GiftRecordSerializer(recent_records, many=True).data
            }
        })


class UserGiftViewSet(viewsets.ReadOnlyModelViewSet):
    """用户礼品盒视图集"""
    serializer_class = UserGiftBoxSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return UserGiftBox.objects.filter(
            user=self.request.user,
            quantity__gt=0
        ).select_related('gift').order_by('-updated_at')

    def list(self, request):
        """礼品盒列表"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        # 计算总价值
        total_value = sum(item.gift.price * item.quantity for item in queryset)

        return Response({
            'code': 200,
            'data': {
                'gifts': serializer.data,
                'total_value': total_value,
                'total_count': queryset.count()
            }
        })


# 添加独立的API函数
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def gift_categories(request):
    """获取礼物分类列表"""
    categories = GiftCategory.objects.filter(is_active=True).order_by('sort_order')

    # 为每个分类添加礼物数量
    result = []
    for category in categories:
        category_data = GiftCategorySerializer(category).data
        category_data['gift_count'] = Gift.objects.filter(
            category=category,
            is_active=True
        ).count()
        result.append(category_data)

    return Response({
        'code': 200,
        'data': result
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def popular_gifts(request):
    """获取热门礼物"""
    popular_gifts = Gift.objects.filter(
        is_active=True
    ).order_by('-send_count', '-is_popular')[:20]

    serializer = GiftSerializer(popular_gifts, many=True)
    return Response({
        'code': 200,
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_coin_balance(request):
    """获取用户金币余额"""
    coin_balance, created = CoinBalance.objects.get_or_create(
        user=request.user,
        defaults={'balance': 0}
    )

    return Response({
        'code': 200,
        'data': {
            'balance': coin_balance.balance,
            'total_earned': coin_balance.total_earned,
            'total_spent': coin_balance.total_spent
        }
    })
