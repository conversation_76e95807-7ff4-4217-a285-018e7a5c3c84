from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'categories', views.GiftCategoryViewSet)
router.register(r'gifts', views.GiftViewSet, basename='gift')
router.register(r'records', views.GiftRecordViewSet, basename='giftrecord')
router.register(r'user-gifts', views.UserGiftViewSet, basename='usergiftbox')

urlpatterns = [
    path('', include(router.urls)),

    # 独立API端点
    path('categories/list/', views.gift_categories, name='gift-categories'),
    path('popular/', views.popular_gifts, name='popular-gifts'),
    path('coin-balance/', views.user_coin_balance, name='user-coin-balance'),
]
