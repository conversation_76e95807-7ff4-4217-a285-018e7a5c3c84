from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.db.models import Q, Sum
from django.utils import timezone
from django.http import HttpResponse
from datetime import timedelta, date
import uuid
import logging

logger = logging.getLogger(__name__)
import hashlib

from .models import (
    Order, PaymentRecord, CoinPackage, VipPackage,
    CoinTransaction, UserVipRecord, CoinBalance, CoinRechargeRecord
)
from utils.wechat_pay import wechat_pay
from .serializers import (
    OrderSerializer, PaymentRecordSerializer, CoinPackageSerializer,
    VipPackageSerializer, CoinTransactionSerializer, UserVipRecordSerializer,
    CreateOrderSerializer, PaymentCallbackSerializer, CoinUseSerializer,
    VipStatusSerializer, WalletSerializer, RechargeHistorySerializer,
    ConsumptionHistorySerializer, RefundRequestSerializer, InvoiceRequestSerializer
)


class CoinPackageViewSet(ReadOnlyModelViewSet):
    """金币套餐视图集"""
    queryset = CoinPackage.objects.filter(is_active=True).order_by('price')
    serializer_class = CoinPackageSerializer
    permission_classes = [permissions.IsAuthenticated]


class VipPackageViewSet(ReadOnlyModelViewSet):
    """VIP套餐视图集"""
    queryset = VipPackage.objects.filter(is_active=True).order_by('sort_order')
    serializer_class = VipPackageSerializer
    permission_classes = [permissions.IsAuthenticated]


class OrderViewSet(ModelViewSet):
    """订单视图集"""
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的订单"""
        return Order.objects.filter(user=self.request.user).order_by('-created_at')
    
    @drf_action(detail=False, methods=['post'])
    def create_order(self, request):
        """创建订单"""
        serializer = CreateOrderSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        order_type = serializer.validated_data['order_type']
        package = serializer.validated_data['package']
        payment_method = serializer.validated_data['payment_method']
        
        # 生成订单号
        order_no = self.generate_order_no()
        
        # 创建订单
        order_data = {
            'user': request.user,
            'order_no': order_no,
            'order_type': order_type,
            'amount': package.price,
            'payment_method': payment_method,
            'status': 'pending',
            'expire_time': timezone.now() + timedelta(minutes=30)  # 30分钟过期
        }
        
        if order_type == 'coin':
            order_data['coin_package'] = package
        else:
            order_data['vip_package'] = package
        
        order = Order.objects.create(**order_data)
        
        # 生成支付参数
        payment_params = self.generate_payment_params(order, payment_method)
        
        serializer = OrderSerializer(order)
        
        return Response({
            'code': 200,
            'message': '订单创建成功',
            'data': {
                'order': serializer.data,
                'payment_params': payment_params
            }
        })
    
    @drf_action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消订单"""
        order = self.get_object()
        
        if order.status != 'pending':
            return Response({
                'code': 400,
                'message': '订单状态不允许取消'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        order.status = 'cancelled'
        order.save()
        
        return Response({
            'code': 200,
            'message': '订单已取消'
        })
    
    @drf_action(detail=True, methods=['get'])
    def payment_status(self, request, pk=None):
        """查询支付状态"""
        order = self.get_object()
        
        # 这里可以调用第三方支付接口查询真实状态
        # 暂时返回订单当前状态
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'order_no': order.order_no,
                'status': order.status,
                'amount': order.amount,
                'payment_time': order.payment_time
            }
        })
    
    def generate_order_no(self):
        """生成订单号"""
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f"XQ{timestamp}{random_str}".upper()
    
    def generate_payment_params(self, order, payment_method):
        """生成支付参数"""
        # 这里应该调用真实的支付接口生成支付参数
        # 暂时返回模拟数据
        
        if payment_method == 'wechat':
            return {
                'appId': 'wx1234567890',
                'timeStamp': str(int(timezone.now().timestamp())),
                'nonceStr': str(uuid.uuid4()).replace('-', ''),
                'package': f'prepay_id=wx{order.order_no}',
                'signType': 'MD5',
                'paySign': 'mock_sign'
            }
        else:  # alipay
            return {
                'orderString': f'alipay_sdk=mock&order_no={order.order_no}&amount={order.amount}'
            }


class PaymentCallbackView:
    """支付回调处理"""
    
    @api_view(['POST'])
    @permission_classes([])
    def wechat_callback(request):
        """微信支付回调"""
        # 验证签名
        if not PaymentCallbackView.verify_wechat_sign(request.data):
            return Response({'code': 'FAIL', 'message': '签名验证失败'})
        
        order_no = request.data.get('out_trade_no')
        transaction_id = request.data.get('transaction_id')
        total_fee = int(request.data.get('total_fee', 0)) / 100  # 分转元
        
        try:
            order = Order.objects.get(order_no=order_no)
            
            if order.status == 'paid':
                return Response({'code': 'SUCCESS', 'message': 'OK'})
            
            # 处理支付成功
            PaymentCallbackView.handle_payment_success(order, transaction_id, total_fee)
            
            return Response({'code': 'SUCCESS', 'message': 'OK'})
            
        except Order.DoesNotExist:
            return Response({'code': 'FAIL', 'message': '订单不存在'})
        except Exception as e:
            return Response({'code': 'FAIL', 'message': str(e)})
    
    @api_view(['POST'])
    @permission_classes([])
    def alipay_callback(request):
        """支付宝支付回调"""
        # 验证签名
        if not PaymentCallbackView.verify_alipay_sign(request.data):
            return Response('fail')
        
        order_no = request.data.get('out_trade_no')
        trade_no = request.data.get('trade_no')
        total_amount = float(request.data.get('total_amount', 0))
        trade_status = request.data.get('trade_status')
        
        if trade_status != 'TRADE_SUCCESS':
            return Response('success')
        
        try:
            order = Order.objects.get(order_no=order_no)
            
            if order.status == 'paid':
                return Response('success')
            
            # 处理支付成功
            PaymentCallbackView.handle_payment_success(order, trade_no, total_amount)
            
            return Response('success')
            
        except Order.DoesNotExist:
            return Response('fail')
        except Exception as e:
            return Response('fail')
    
    @staticmethod
    def verify_wechat_sign(data):
        """验证微信签名"""
        # 这里应该实现真实的微信签名验证逻辑
        return True
    
    @staticmethod
    def verify_alipay_sign(data):
        """验证支付宝签名"""
        # 这里应该实现真实的支付宝签名验证逻辑
        return True
    
    @staticmethod
    def handle_payment_success(order, transaction_id, amount):
        """处理支付成功"""
        # 更新订单状态
        order.status = 'paid'
        order.payment_time = timezone.now()
        order.save()
        
        # 创建支付记录
        PaymentRecord.objects.create(
            user=order.user,
            order=order,
            transaction_id=transaction_id,
            payment_method=order.payment_method,
            amount=amount,
            status='success',
            paid_at=timezone.now()
        )
        
        # 处理业务逻辑
        if order.order_type == 'coin':
            # 增加金币
            coin_package = order.coin_package
            total_coins = coin_package.coin_amount + coin_package.bonus_coins
            
            order.user.coin_balance += total_coins
            order.user.save()
            
            # 记录金币交易
            CoinTransaction.objects.create(
                user=order.user,
                transaction_type='recharge',
                amount=total_coins,
                balance_after=order.user.coin_balance,
                description=f'充值{coin_package.name}',
                related_order_id=order.id
            )
            
        elif order.order_type == 'vip':
            # 开通VIP
            vip_package = order.vip_package

            # 检查是否已有VIP记录
            existing_vip = UserVipRecord.objects.filter(
                user=order.user,
                is_active=True
            ).first()

            if existing_vip:
                # 延长VIP时间
                existing_vip.end_time += timedelta(days=vip_package.duration)
                existing_vip.save()
            else:
                # 创建新VIP记录
                UserVipRecord.objects.create(
                    user=order.user,
                    order=order,
                    package=vip_package,
                    start_time=timezone.now(),
                    end_time=timezone.now() + timedelta(days=vip_package.duration),
                    duration=vip_package.duration,
                    is_active=True
                )

            # 更新用户VIP状态
            order.user.is_vip = True
            order.user.vip_level = vip_package.sort_order  # 使用sort_order作为等级
            order.user.vip_expire_time = UserVipRecord.objects.filter(
                user=order.user,
                is_active=True
            ).first().end_time
            order.user.save()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def wallet_info(request):
    """获取钱包信息"""
    user = request.user
    
    # VIP状态
    vip_record = UserVipRecord.objects.filter(
        user=user,
        is_active=True,
        end_time__gt=timezone.now()
    ).first()
    
    if vip_record:
        vip_status = {
            'is_vip': True,
            'vip_level': vip_record.package.sort_order,
            'vip_name': vip_record.package.name,
            'expire_time': vip_record.end_time,
            'days_left': (vip_record.end_time.date() - date.today()).days,
            'features': vip_record.package.features
        }
    else:
        vip_status = {
            'is_vip': False,
            'vip_level': 0,
            'vip_name': '普通用户',
            'expire_time': None,
            'days_left': 0,
            'features': []
        }
    
    # 统计数据
    total_recharged = Order.objects.filter(
        user=user,
        status='paid'
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    total_consumed_coins = CoinTransaction.objects.filter(
        user=user,
        transaction_type__in=['consume', 'gift']
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    wallet_data = {
        'coin_balance': user.coin_balance,
        'total_recharged': total_recharged,
        'total_consumed_coins': total_consumed_coins,
        'vip_status': vip_status
    }
    
    serializer = WalletSerializer(wallet_data)
    
    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def use_coins(request):
    """使用金币"""
    serializer = CoinUseSerializer(data=request.data, context={'request': request})
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    amount = serializer.validated_data['amount']
    description = serializer.validated_data['description']
    related_id = serializer.validated_data.get('related_id')
    
    user = request.user
    
    # 扣除金币
    user.coin_balance -= amount
    user.save()
    
    # 记录交易
    CoinTransaction.objects.create(
        user=user,
        transaction_type='consume',
        amount=-amount,
        balance_after=user.coin_balance,
        description=description,
        related_order_id=related_id
    )
    
    return Response({
        'code': 200,
        'message': '金币使用成功',
        'data': {
            'remaining_balance': user.coin_balance
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def recharge_history(request):
    """充值历史"""
    user = request.user

    # 获取充值订单
    orders = Order.objects.filter(
        user=user,
        status='paid'
    ).order_by('-payment_time')

    # 按日期分组
    history_data = {}
    for order in orders:
        date_key = order.payment_time.date()
        if date_key not in history_data:
            history_data[date_key] = {
                'date': date_key,
                'orders': [],
                'total_amount': 0,
                'total_coins': 0
            }

        history_data[date_key]['orders'].append(order)
        history_data[date_key]['total_amount'] += order.amount

        if order.order_type == 'coin':
            history_data[date_key]['total_coins'] += (
                order.coin_package.coin_amount + order.coin_package.bonus_coins
            )

    # 转换为列表并排序
    history_list = list(history_data.values())
    history_list.sort(key=lambda x: x['date'], reverse=True)

    serializer = RechargeHistorySerializer(history_list, many=True)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def consumption_history(request):
    """消费历史"""
    user = request.user

    # 获取消费记录
    transactions = CoinTransaction.objects.filter(
        user=user,
        transaction_type__in=['consume', 'gift']
    ).order_by('-created_at')

    # 按日期分组
    history_data = {}
    for transaction in transactions:
        date_key = transaction.created_at.date()
        if date_key not in history_data:
            history_data[date_key] = {
                'date': date_key,
                'transactions': [],
                'total_coins': 0
            }

        history_data[date_key]['transactions'].append(transaction)
        history_data[date_key]['total_coins'] += abs(transaction.amount)

    # 转换为列表并排序
    history_list = list(history_data.values())
    history_list.sort(key=lambda x: x['date'], reverse=True)

    serializer = ConsumptionHistorySerializer(history_list, many=True)

    return Response({
        'code': 200,
        'message': 'success',
        'data': serializer.data
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_refund(request):
    """申请退款"""
    serializer = RefundRequestSerializer(data=request.data, context={'request': request})
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    order_id = serializer.validated_data['order_id']
    reason = serializer.validated_data['reason']

    try:
        order = Order.objects.get(id=order_id, user=request.user)

        # 更新订单状态
        order.status = 'refund_pending'
        order.save()

        # 这里可以调用第三方支付接口申请退款
        # 暂时直接返回成功

        return Response({
            'code': 200,
            'message': '退款申请已提交，请等待处理'
        })

    except Order.DoesNotExist:
        return Response({
            'code': 404,
            'message': '订单不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_invoice(request):
    """申请发票"""
    serializer = InvoiceRequestSerializer(data=request.data, context={'request': request})
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    order_ids = serializer.validated_data['order_ids']
    invoice_type = serializer.validated_data['invoice_type']
    invoice_title = serializer.validated_data['invoice_title']
    tax_number = serializer.validated_data.get('tax_number', '')
    email = serializer.validated_data['email']

    # 这里可以调用发票服务接口
    # 暂时直接返回成功

    return Response({
        'code': 200,
        'message': '发票申请已提交，将在3-5个工作日内发送到您的邮箱'
    })


class CoinTransactionViewSet(ReadOnlyModelViewSet):
    """金币交易记录视图集"""
    serializer_class = CoinTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的金币交易记录"""
        return CoinTransaction.objects.filter(
            user=self.request.user
        ).order_by('-created_at')


class UserVipRecordViewSet(ReadOnlyModelViewSet):
    """用户VIP记录视图集"""
    serializer_class = UserVipRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的VIP记录"""
        return UserVipRecord.objects.filter(
            user=self.request.user
        ).order_by('-created_at')


# 积分系统相关视图
from .models import PointsTask, UserPointsAccount, PointsTransaction, UserTaskProgress
from .serializers import (
    PointsTaskSerializer, UserPointsAccountSerializer,
    PointsTransactionSerializer, UserTaskProgressSerializer, CompleteTaskSerializer
)
from django.utils import timezone
from datetime import datetime, timedelta


class PointsTaskViewSet(ReadOnlyModelViewSet):
    """积分任务视图集"""
    queryset = PointsTask.objects.filter(is_active=True).order_by('sort_order')
    serializer_class = PointsTaskSerializer
    permission_classes = [permissions.IsAuthenticated]


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def points_balance(request):
    """获取用户积分余额"""
    account, created = UserPointsAccount.objects.get_or_create(
        user=request.user,
        defaults={'total_points': 0, 'available_points': 0, 'used_points': 0}
    )

    serializer = UserPointsAccountSerializer(account)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def points_transactions(request):
    """获取积分交易记录"""
    transactions = PointsTransaction.objects.filter(
        user=request.user
    ).order_by('-created_at')

    # 分页
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))
    start = (page - 1) * page_size
    end = start + page_size

    paginated_transactions = transactions[start:end]
    serializer = PointsTransactionSerializer(paginated_transactions, many=True)

    return Response({
        'results': serializer.data,
        'has_more': len(transactions) > end
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def daily_tasks(request):
    """获取每日任务列表"""
    today = timezone.now().date().strftime('%Y-%m-%d')

    # 获取所有每日任务
    daily_tasks = PointsTask.objects.filter(
        frequency='daily',
        is_active=True
    ).order_by('sort_order')

    # 获取用户今日任务进度
    user_progress = UserTaskProgress.objects.filter(
        user=request.user,
        date_key=today
    ).select_related('task')

    progress_dict = {p.task_id: p for p in user_progress}

    # 组装任务数据
    task_data = []
    for task in daily_tasks:
        progress = progress_dict.get(task.id)
        task_info = PointsTaskSerializer(task).data
        task_info['progress'] = {
            'current_count': progress.current_count if progress else 0,
            'is_completed': progress.is_completed if progress else False,
            'completed_at': progress.completed_at if progress else None
        }
        task_data.append(task_info)

    return Response(task_data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def complete_task(request):
    """完成任务"""
    serializer = CompleteTaskSerializer(data=request.data)
    if serializer.is_valid():
        task_id = serializer.validated_data['task_id']

        try:
            with transaction.atomic():
                task = PointsTask.objects.get(id=task_id, is_active=True)
                today = timezone.now().date().strftime('%Y-%m-%d')

                # 获取或创建任务进度
                progress, created = UserTaskProgress.objects.get_or_create(
                    user=request.user,
                    task=task,
                    date_key=today,
                    defaults={'current_count': 0, 'is_completed': False}
                )

                # 检查是否已完成
                if progress.is_completed:
                    return Response({'error': '任务已完成'}, status=400)

                # 更新进度
                progress.current_count += 1

                # 检查是否达到目标
                if progress.current_count >= task.target_count:
                    progress.is_completed = True
                    progress.completed_at = timezone.now()

                    # 发放积分奖励
                    account, created = UserPointsAccount.objects.get_or_create(
                        user=request.user,
                        defaults={'total_points': 0, 'available_points': 0, 'used_points': 0}
                    )

                    # 记录积分交易
                    PointsTransaction.objects.create(
                        user=request.user,
                        transaction_type='earn',
                        points=task.points_reward,
                        earn_source='task',
                        task=task,
                        description=f'完成任务：{task.name}',
                        balance_before=account.available_points,
                        balance_after=account.available_points + task.points_reward
                    )

                    # 更新账户余额
                    account.total_points += task.points_reward
                    account.available_points += task.points_reward
                    account.save()

                progress.save()

                return Response({
                    'message': '任务完成' if progress.is_completed else '进度更新',
                    'progress': UserTaskProgressSerializer(progress).data,
                    'points_earned': task.points_reward if progress.is_completed else 0
                })

        except PointsTask.DoesNotExist:
            return Response({'error': '任务不存在'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    return Response(serializer.errors, status=400)


# 金币系统相关API
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def coin_balance(request):
    """获取用户金币余额"""
    coin_balance, created = CoinBalance.objects.get_or_create(
        user=request.user,
        defaults={'balance': 0}
    )

    # 重置每日统计
    coin_balance.reset_daily_stats()

    return Response({
        'code': 200,
        'data': {
            'balance': coin_balance.balance,
            'total_earned': coin_balance.total_earned,
            'total_spent': coin_balance.total_spent,
            'today_earned': coin_balance.today_earned,
            'today_spent': coin_balance.today_spent,
            'frozen_balance': coin_balance.frozen_balance
        }
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def recharge_coins(request):
    """充值金币"""
    package_id = request.data.get('package_id')
    payment_method = request.data.get('payment_method', 'wechat')

    try:
        package = CoinPackage.objects.get(id=package_id, is_active=True)
    except CoinPackage.DoesNotExist:
        return Response({
            'code': 404,
            'message': '金币套餐不存在'
        }, status=status.HTTP_404_NOT_FOUND)

    # 创建订单
    order = Order.objects.create(
        user=request.user,
        product_type='coins',
        product_id=package.id,
        product_name=package.name,
        product_data={
            'coin_amount': package.coin_amount,
            'bonus_coins': package.bonus_coins,
            'total_coins': package.total_coins
        },
        original_amount=package.price,
        final_amount=package.price,
        payment_method=payment_method,
        expired_at=timezone.now() + timedelta(minutes=30)
    )

    # 创建充值记录
    recharge_record = CoinRechargeRecord.objects.create(
        user=request.user,
        order=order,
        package=package,
        coin_amount=package.coin_amount,
        bonus_coins=package.bonus_coins,
        total_coins=package.total_coins,
        amount_paid=package.price
    )

    # 这里应该调用支付接口
    # 暂时直接处理充值（测试用）
    if payment_method == 'test':
        order.status = 'paid'
        order.paid_at = timezone.now()
        order.save()

        recharge_record.process_recharge()

        return Response({
            'code': 200,
            'message': '充值成功',
            'data': {
                'order_id': order.id,
                'coins_added': package.total_coins
            }
        })

    return Response({
        'code': 200,
        'message': '订单创建成功',
        'data': {
            'order_id': order.id,
            'order_no': order.order_no,
            'amount': order.final_amount,
            'expired_at': order.expired_at,
            'pay_params': {}  # 支付参数
        }
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def use_coins(request):
    """使用金币"""
    amount = request.data.get('amount', 0)
    description = request.data.get('description', '消费金币')

    if amount <= 0:
        return Response({
            'code': 400,
            'message': '金币数量必须大于0'
        }, status=status.HTTP_400_BAD_REQUEST)

    coin_balance, created = CoinBalance.objects.get_or_create(
        user=request.user,
        defaults={'balance': 0}
    )

    if coin_balance.balance < amount:
        return Response({
            'code': 400,
            'message': '金币余额不足'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 消费金币
    success = coin_balance.spend_coins(amount, description)

    if success:
        return Response({
            'code': 200,
            'message': '消费成功',
            'data': {
                'remaining_balance': coin_balance.balance,
                'amount_spent': amount
            }
        })
    else:
        return Response({
            'code': 500,
            'message': '消费失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def coin_transactions(request):
    """获取金币交易记录"""
    page = int(request.GET.get('page', 1))
    page_size = min(int(request.GET.get('page_size', 20)), 50)
    transaction_type = request.GET.get('type', 'all')

    queryset = CoinTransaction.objects.filter(user=request.user)

    if transaction_type != 'all':
        queryset = queryset.filter(transaction_type=transaction_type)

    # 分页
    start = (page - 1) * page_size
    end = start + page_size
    transactions = queryset.order_by('-created_at')[start:end]

    # 序列化数据
    transaction_data = []
    for transaction in transactions:
        transaction_data.append({
            'id': transaction.id,
            'transaction_type': transaction.transaction_type,
            'transaction_type_display': transaction.get_transaction_type_display(),
            'amount': transaction.amount,
            'balance_before': transaction.balance_before,
            'balance_after': transaction.balance_after,
            'description': transaction.description,
            'created_at': transaction.created_at
        })

    return Response({
        'code': 200,
        'data': {
            'transactions': transaction_data,
            'has_more': len(transactions) == page_size
        }
    })


# 微信支付相关API
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def wechat_pay_create_order(request):
    """创建微信支付订单"""
    order_id = request.data.get('order_id')
    openid = request.data.get('openid')

    if not order_id or not openid:
        return Response({
            'code': 400,
            'message': '订单ID和OpenID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # 获取订单
        order = Order.objects.get(id=order_id, user=request.user, status='pending')
    except Order.DoesNotExist:
        return Response({
            'code': 404,
            'message': '订单不存在或状态异常'
        }, status=status.HTTP_404_NOT_FOUND)

    # 检查订单是否过期
    if order.expired_at and order.expired_at < timezone.now():
        order.status = 'expired'
        order.save()
        return Response({
            'code': 400,
            'message': '订单已过期'
        }, status=status.HTTP_400_BAD_REQUEST)

    # 准备微信支付参数
    order_data = {
        'body': order.product_name,
        'out_trade_no': order.order_no,
        'total_fee': float(order.final_amount),
        'openid': openid,
        'trade_type': 'JSAPI',
        'spbill_create_ip': request.META.get('REMOTE_ADDR', '127.0.0.1')
    }

    # 调用微信支付统一下单
    result = wechat_pay.unified_order(order_data)

    if result['success']:
        # 更新订单状态
        order.payment_method = 'wechat'
        order.save()

        return Response({
            'code': 200,
            'message': '订单创建成功',
            'data': {
                'order_id': order.id,
                'order_no': order.order_no,
                'pay_params': result['pay_params'],
                'prepay_id': result['prepay_id']
            }
        })
    else:
        return Response({
            'code': 500,
            'message': f'创建支付订单失败: {result["error"]}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def wechat_pay_notify(request):
    """微信支付回调"""
    try:
        # 解析XML数据
        xml_data = request.body.decode('utf-8')
        data = wechat_pay.xml_to_dict(xml_data)

        # 验证回调
        if not wechat_pay.verify_notify(data):
            return HttpResponse(
                wechat_pay.generate_notify_response(False, '签名验证失败'),
                content_type='application/xml'
            )

        # 获取订单信息
        out_trade_no = data.get('out_trade_no')
        transaction_id = data.get('transaction_id')
        total_fee = int(data.get('total_fee', 0)) / 100

        try:
            order = Order.objects.get(order_no=out_trade_no)
        except Order.DoesNotExist:
            logger.error(f'微信支付回调：订单不存在 {out_trade_no}')
            return HttpResponse(
                wechat_pay.generate_notify_response(False, '订单不存在'),
                content_type='application/xml'
            )

        # 检查订单状态
        if order.status == 'paid':
            # 订单已处理，直接返回成功
            return HttpResponse(
                wechat_pay.generate_notify_response(True),
                content_type='application/xml'
            )

        # 验证金额
        if abs(float(order.final_amount) - total_fee) > 0.01:
            logger.error(f'微信支付回调：金额不匹配 订单:{order.final_amount} 支付:{total_fee}')
            return HttpResponse(
                wechat_pay.generate_notify_response(False, '金额不匹配'),
                content_type='application/xml'
            )

        # 更新订单状态
        order.status = 'paid'
        order.paid_at = timezone.now()
        order.transaction_id = transaction_id
        order.save()

        # 创建支付记录
        PaymentRecord.objects.create(
            order=order,
            user=order.user,
            payment_method='wechat',
            amount=total_fee,
            transaction_id=transaction_id,
            status='success'
        )

        # 处理业务逻辑
        process_payment_success(order)

        return HttpResponse(
            wechat_pay.generate_notify_response(True),
            content_type='application/xml'
        )

    except Exception as e:
        logger.error(f'微信支付回调异常: {str(e)}')
        return HttpResponse(
            wechat_pay.generate_notify_response(False, '处理异常'),
            content_type='application/xml'
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def wechat_pay_query_order(request):
    """查询微信支付订单状态"""
    order_id = request.GET.get('order_id')

    if not order_id:
        return Response({
            'code': 400,
            'message': '订单ID不能为空'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        order = Order.objects.get(id=order_id, user=request.user)
    except Order.DoesNotExist:
        return Response({
            'code': 404,
            'message': '订单不存在'
        }, status=status.HTTP_404_NOT_FOUND)

    # 如果订单已支付，直接返回状态
    if order.status == 'paid':
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'order_status': 'paid',
                'trade_state': 'SUCCESS',
                'paid_at': order.paid_at
            }
        })

    # 查询微信支付状态
    result = wechat_pay.query_order(out_trade_no=order.order_no)

    if result['success']:
        trade_state = result['trade_state']

        # 如果微信显示已支付但本地未更新，同步状态
        if trade_state == 'SUCCESS' and order.status != 'paid':
            order.status = 'paid'
            order.paid_at = timezone.now()
            order.transaction_id = result.get('transaction_id')
            order.save()

            # 处理业务逻辑
            process_payment_success(order)

        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'order_status': order.status,
                'trade_state': trade_state,
                'transaction_id': result.get('transaction_id'),
                'paid_at': order.paid_at
            }
        })
    else:
        return Response({
            'code': 500,
            'message': f'查询失败: {result["error"]}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def process_payment_success(order):
    """处理支付成功后的业务逻辑"""
    try:
        if order.product_type == 'coins':
            # 金币充值
            coin_recharge = CoinRechargeRecord.objects.filter(order=order).first()
            if coin_recharge and not coin_recharge.is_processed:
                coin_recharge.process_recharge()

        elif order.product_type == 'vip':
            # VIP购买
            vip_package = VipPackage.objects.get(id=order.product_id)

            # 创建VIP记录
            UserVipRecord.objects.create(
                user=order.user,
                package=vip_package,
                order=order,
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timedelta(days=vip_package.duration),
                is_active=True
            )

            # 更新用户VIP状态
            order.user.is_vip = True
            order.user.vip_expired_at = timezone.now() + timedelta(days=vip_package.duration)
            order.user.save()

        logger.info(f'支付成功处理完成: 订单{order.order_no}')

    except Exception as e:
        logger.error(f'支付成功处理异常: 订单{order.order_no}, 错误:{str(e)}')
