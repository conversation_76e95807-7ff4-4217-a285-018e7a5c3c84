from django.contrib import admin
from django.utils.html import format_html
from .models import (VipPackage, CoinPackage, Order, PaymentRecord, 
                    UserVipRecord, CoinTransaction)


@admin.register(VipPackage)
class VipPackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'duration', 'price_display', 'discount_display', 
                   'is_active', 'is_popular', 'sort_order', 'created_at']
    list_filter = ['is_active', 'is_popular', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'duration')
        }),
        ('价格信息', {
            'fields': ('original_price', 'price')
        }),
        ('特权配置', {
            'fields': ('features', 'daily_likes_limit', 'daily_super_likes', 
                      'can_see_visitors', 'can_invisible_browse', 'can_undo_actions', 
                      'priority_matching')
        }),
        ('管理信息', {
            'fields': ('sort_order', 'is_active', 'is_popular')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def price_display(self, obj):
        if obj.original_price > obj.price:
            return format_html(
                '<span style="text-decoration: line-through; color: gray;">¥{}</span> '
                '<span style="color: red; font-weight: bold;">¥{}</span>',
                obj.original_price, obj.price
            )
        return f'¥{obj.price}'
    price_display.short_description = '价格'
    
    def discount_display(self, obj):
        if obj.discount_rate > 0:
            return format_html(
                '<span style="color: red;">{:.1%} OFF</span>',
                obj.discount_rate
            )
        return '-'
    discount_display.short_description = '折扣'


@admin.register(CoinPackage)
class CoinPackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'coin_amount', 'bonus_coins', 'total_coins_display', 
                   'price', 'is_active', 'is_popular', 'sort_order']
    list_filter = ['is_active', 'is_popular', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description')
        }),
        ('金币信息', {
            'fields': ('coin_amount', 'bonus_coins', 'price')
        }),
        ('管理信息', {
            'fields': ('sort_order', 'is_active', 'is_popular')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def total_coins_display(self, obj):
        if obj.bonus_coins > 0:
            return format_html(
                '{} <span style="color: green;">+{}</span>',
                obj.coin_amount, obj.bonus_coins
            )
        return str(obj.coin_amount)
    total_coins_display.short_description = '总金币'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_no', 'user', 'product_name', 'product_type_display', 
                   'final_amount', 'payment_method_display', 'status_display', 'created_at']
    list_filter = ['product_type', 'payment_method', 'status', 'created_at']
    search_fields = ['order_no', 'user__nickname', 'product_name', 'transaction_id']
    readonly_fields = ['order_no', 'created_at', 'paid_at', 'expired_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('order_no', 'user')
        }),
        ('产品信息', {
            'fields': ('product_type', 'product_id', 'product_name', 'product_data')
        }),
        ('价格信息', {
            'fields': ('original_amount', 'discount_amount', 'final_amount')
        }),
        ('支付信息', {
            'fields': ('payment_method', 'transaction_id', 'status')
        }),
        ('时间信息', {
            'fields': ('created_at', 'paid_at', 'expired_at')
        }),
        ('备注信息', {
            'fields': ('remark',)
        }),
    )
    
    def product_type_display(self, obj):
        return obj.get_product_type_display()
    product_type_display.short_description = '产品类型'
    
    def payment_method_display(self, obj):
        return obj.get_payment_method_display() if obj.payment_method else '-'
    payment_method_display.short_description = '支付方式'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'paid': 'green',
            'cancelled': 'gray',
            'refunded': 'blue',
            'expired': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '订单状态'


@admin.register(PaymentRecord)
class PaymentRecordAdmin(admin.ModelAdmin):
    list_display = ['payment_no', 'order', 'payment_method_display', 'amount', 
                   'status_display', 'created_at', 'paid_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['payment_no', 'order__order_no', 'third_party_order_no', 
                    'third_party_transaction_id']
    readonly_fields = ['payment_no', 'created_at', 'paid_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('payment_no', 'order', 'payment_method', 'amount')
        }),
        ('第三方信息', {
            'fields': ('third_party_order_no', 'third_party_transaction_id')
        }),
        ('状态信息', {
            'fields': ('status', 'callback_data')
        }),
        ('时间信息', {
            'fields': ('created_at', 'paid_at')
        }),
    )
    
    def payment_method_display(self, obj):
        return obj.get_payment_method_display()
    payment_method_display.short_description = '支付方式'
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'success': 'green',
            'failed': 'red',
            'cancelled': 'gray',
            'refunded': 'blue'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '支付状态'


@admin.register(UserVipRecord)
class UserVipRecordAdmin(admin.ModelAdmin):
    list_display = ['user', 'package', 'start_time', 'end_time', 'duration', 
                   'is_active', 'created_at']
    list_filter = ['is_active', 'package', 'created_at']
    search_fields = ['user__nickname', 'package__name']
    readonly_fields = ['created_at']


@admin.register(CoinTransaction)
class CoinTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'transaction_type_display', 'amount_display', 
                   'balance_before', 'balance_after', 'related_user', 'created_at']
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['user__nickname', 'related_user__nickname', 'description']
    readonly_fields = ['created_at']
    
    def transaction_type_display(self, obj):
        colors = {
            'purchase': 'green',
            'gift': 'purple',
            'reward': 'blue',
            'refund': 'orange',
            'deduct': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.transaction_type, 'black'),
            obj.get_transaction_type_display()
        )
    transaction_type_display.short_description = '交易类型'
    
    def amount_display(self, obj):
        if obj.transaction_type in ['purchase', 'reward', 'refund']:
            return format_html('<span style="color: green;">+{}</span>', obj.amount)
        else:
            return format_html('<span style="color: red;">-{}</span>', obj.amount)
    amount_display.short_description = '金币数量'
