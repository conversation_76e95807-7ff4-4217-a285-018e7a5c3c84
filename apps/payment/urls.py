from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views
from . import vip_views

router = DefaultRouter()
router.register(r'payment/coin-packages', views.CoinPackageViewSet, basename='coin-package')
router.register(r'payment/vip-packages', vip_views.VipPackageViewSet, basename='vip-package')
router.register(r'payment/orders', views.OrderViewSet, basename='order')
router.register(r'payment/coin-transactions', views.CoinTransactionViewSet, basename='coin-transaction')
router.register(r'payment/vip-records', vip_views.UserVipRecordViewSet, basename='vip-record')
router.register(r'payment/points-tasks', views.PointsTaskViewSet, basename='points-task')

urlpatterns = [
    path('', include(router.urls)),
    
    # 钱包相关
    path('payment/wallet/', views.wallet_info, name='wallet-info'),
    path('payment/use-coins/', views.use_coins, name='use-coins'),
    
    # 历史记录
    path('payment/recharge-history/', views.recharge_history, name='recharge-history'),
    path('payment/consumption-history/', views.consumption_history, name='consumption-history'),
    
    # 退款和发票
    path('payment/request-refund/', views.request_refund, name='request-refund'),
    path('payment/request-invoice/', views.request_invoice, name='request-invoice'),
    
    # 支付回调
    path('payment/callback/wechat/', views.PaymentCallbackView.wechat_callback, name='wechat-callback'),
    path('payment/callback/alipay/', views.PaymentCallbackView.alipay_callback, name='alipay-callback'),

    # 积分系统
    path('payment/points/balance/', views.points_balance, name='points-balance'),
    path('payment/points/transactions/', views.points_transactions, name='points-transactions'),
    path('payment/points/daily-tasks/', views.daily_tasks, name='daily-tasks'),
    path('payment/points/complete-task/', views.complete_task, name='complete-task'),

    # VIP系统
    path('payment/vip/privileges/', vip_views.vip_privileges, name='vip-privileges'),
    path('payment/vip/benefits/', vip_views.vip_benefits_comparison, name='vip-benefits'),
    path('payment/vip/purchase-with-coins/', vip_views.purchase_vip_with_coins, name='purchase-vip-coins'),
    path('payment/vip/create-order/', vip_views.create_vip_order, name='create-vip-order'),

    # 金币系统
    path('payment/coin-balance/', views.coin_balance, name='coin-balance'),
    path('payment/recharge-coins/', views.recharge_coins, name='recharge-coins'),
    path('payment/use-coins/', views.use_coins, name='use-coins'),
    path('payment/coin-transactions/', views.coin_transactions, name='coin-transactions'),

    # 微信支付
    path('payment/wechat/create-order/', views.wechat_pay_create_order, name='wechat-create-order'),
    path('payment/wechat/notify/', views.wechat_pay_notify, name='wechat-notify'),
    path('payment/wechat/query/', views.wechat_pay_query_order, name='wechat-query'),
]
