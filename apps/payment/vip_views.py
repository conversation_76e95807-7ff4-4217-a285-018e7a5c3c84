from rest_framework import status, permissions, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
from decimal import Decimal

from .models import VipPackage, UserVipRecord, Order, CoinBalance
from .vip_serializers import (
    VipPackageSerializer, UserVipRecordSerializer, PurchaseVipSerializer,
    VipPrivilegeSerializer, UserVipStatusSerializer, CoinBalanceSerializer,
    OrderSerializer, CreateOrderSerializer, VipBenefitsSerializer
)
from apps.users.models import User


class VipPackageViewSet(viewsets.ReadOnlyModelViewSet):
    """VIP套餐API"""
    queryset = VipPackage.objects.filter(is_active=True).order_by('sort_order', 'price')
    serializer_class = VipPackageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request):
        """获取VIP套餐列表"""
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def popular(self, request):
        """获取热门套餐"""
        popular_packages = self.get_queryset().filter(is_popular=True)
        serializer = self.get_serializer(popular_packages, many=True)
        
        return Response({
            'code': 200,
            'data': serializer.data
        })


class UserVipRecordViewSet(viewsets.ReadOnlyModelViewSet):
    """用户VIP记录API"""
    serializer_class = UserVipRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserVipRecord.objects.filter(
            user=self.request.user
        ).select_related('package').order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def current_status(self, request):
        """获取当前VIP状态"""
        user = request.user
        
        # 查找当前有效的VIP记录
        current_vip = UserVipRecord.objects.filter(
            user=user,
            end_time__gt=timezone.now()
        ).select_related('package').first()
        
        if current_vip:
            # 计算剩余天数
            days_left = (current_vip.end_time - timezone.now()).days
            
            # 获取今日使用情况（这里需要根据实际业务逻辑实现）
            daily_likes_left = current_vip.package.daily_likes_limit
            daily_super_likes_left = current_vip.package.daily_super_likes
            
            status_data = {
                'is_vip': True,
                'level_name': current_vip.package.name,
                'expire_date': current_vip.end_time,
                'days_left': days_left,
                'features': current_vip.package.features,
                'daily_likes_left': daily_likes_left,
                'daily_super_likes_left': daily_super_likes_left,
                'can_see_visitors': current_vip.package.can_see_visitors,
                'can_invisible_browse': current_vip.package.can_invisible_browse,
                'can_undo_actions': current_vip.package.can_undo_actions,
                'priority_matching': current_vip.package.priority_matching
            }
        else:
            status_data = {
                'is_vip': False,
                'level_name': '普通用户',
                'expire_date': None,
                'days_left': 0,
                'features': {},
                'daily_likes_left': 10,  # 普通用户默认限制
                'daily_super_likes_left': 1,
                'can_see_visitors': False,
                'can_invisible_browse': False,
                'can_undo_actions': False,
                'priority_matching': False
            }
        
        return Response({
            'code': 200,
            'data': status_data
        })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def vip_privileges(request):
    """获取VIP特权列表"""
    privileges = [
        {
            'id': 1,
            'icon': '👁️',
            'title': '无限查看',
            'description': '无限制查看喜欢你的人',
            'is_new': False
        },
        {
            'id': 2,
            'icon': '⭐',
            'title': '超级喜欢',
            'description': '每日5次超级喜欢机会',
            'is_new': False
        },
        {
            'id': 3,
            'icon': '👻',
            'title': '隐身模式',
            'description': '浏览他人资料时不留痕迹',
            'is_new': True
        },
        {
            'id': 4,
            'icon': '🎯',
            'title': '精准推荐',
            'description': '获得更精准的匹配推荐',
            'is_new': False
        },
        {
            'id': 5,
            'icon': '💬',
            'title': '专属客服',
            'description': '7x24小时专属客服支持',
            'is_new': False
        },
        {
            'id': 6,
            'icon': '🔄',
            'title': '撤回操作',
            'description': '可以撤回误操作的喜欢/跳过',
            'is_new': True
        },
        {
            'id': 7,
            'icon': '🚀',
            'title': '优先展示',
            'description': '您的资料将优先展示给其他用户',
            'is_new': False
        },
        {
            'id': 8,
            'icon': '📊',
            'title': '详细统计',
            'description': '查看详细的匹配和互动统计',
            'is_new': False
        }
    ]
    
    return Response({
        'code': 200,
        'data': privileges
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def vip_benefits_comparison(request):
    """VIP权益对比"""
    benefits = [
        {
            'feature': '每日喜欢次数',
            'free_user': '10次',
            'vip_user': '无限制',
            'is_highlight': True
        },
        {
            'feature': '超级喜欢',
            'free_user': '1次/天',
            'vip_user': '5次/天',
            'is_highlight': True
        },
        {
            'feature': '查看访客',
            'free_user': '最近3位',
            'vip_user': '无限制',
            'is_highlight': False
        },
        {
            'feature': '隐身浏览',
            'free_user': '不支持',
            'vip_user': '支持',
            'is_highlight': True
        },
        {
            'feature': '撤回操作',
            'free_user': '不支持',
            'vip_user': '支持',
            'is_highlight': False
        },
        {
            'feature': '优先推荐',
            'free_user': '普通',
            'vip_user': '优先',
            'is_highlight': True
        },
        {
            'feature': '客服支持',
            'free_user': '工作时间',
            'vip_user': '7x24小时',
            'is_highlight': False
        }
    ]
    
    return Response({
        'code': 200,
        'data': benefits
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def purchase_vip_with_coins(request):
    """使用金币购买VIP"""
    serializer = PurchaseVipSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    package_id = serializer.validated_data['package_id']
    
    try:
        with transaction.atomic():
            # 获取VIP套餐
            package = VipPackage.objects.get(id=package_id, is_active=True)
            
            # 检查用户金币余额
            coin_balance, created = CoinBalance.objects.get_or_create(
                user=request.user,
                defaults={'balance': 0}
            )
            
            # 计算金币价格（假设1元=10金币）
            coin_price = int(package.price * 10)
            
            if coin_balance.balance < coin_price:
                return Response({
                    'code': 400,
                    'message': f'金币余额不足，需要{coin_price}金币，当前余额{coin_balance.balance}金币'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查是否已有有效VIP
            existing_vip = UserVipRecord.objects.filter(
                user=request.user,
                end_time__gt=timezone.now()
            ).first()
            
            # 计算VIP开始和结束时间
            start_time = timezone.now()
            if existing_vip:
                start_time = existing_vip.end_time
            
            end_time = start_time + timedelta(days=package.duration)
            
            # 扣除金币
            coin_balance.balance -= coin_price
            coin_balance.total_spent += coin_price
            coin_balance.save()
            
            # 创建订单
            order = Order.objects.create(
                user=request.user,
                product_type='vip',
                product_id=package.id,
                product_name=package.name,
                product_data={'package_id': package.id, 'duration': package.duration},
                original_amount=package.price,
                final_amount=package.price,
                payment_method='coin',
                status='paid',
                paid_at=timezone.now()
            )

            # 创建VIP记录
            vip_record = UserVipRecord.objects.create(
                user=request.user,
                order=order,
                package=package,
                start_time=start_time,
                end_time=end_time,
                duration=package.duration
            )
            
            return Response({
                'code': 200,
                'message': 'VIP开通成功',
                'data': UserVipRecordSerializer(vip_record).data
            })
            
    except VipPackage.DoesNotExist:
        return Response({
            'code': 404,
            'message': 'VIP套餐不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'系统错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_vip_order(request):
    """创建VIP购买订单"""
    serializer = CreateOrderSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'code': 400,
            'message': '参数错误',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        product_type = serializer.validated_data['product_type']
        product_id = serializer.validated_data['product_id']
        payment_method = serializer.validated_data['payment_method']
        
        if product_type == 'vip':
            package = VipPackage.objects.get(id=product_id, is_active=True)
            product_name = package.name
            amount = package.price
            product_data = {
                'package_id': package.id,
                'duration': package.duration,
                'features': package.features
            }
        else:
            return Response({
                'code': 400,
                'message': '暂不支持该产品类型'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建订单
        order = Order.objects.create(
            user=request.user,
            product_type=product_type,
            product_id=product_id,
            product_name=product_name,
            product_data=product_data,
            original_amount=amount,
            final_amount=amount,
            payment_method=payment_method,
            expired_at=timezone.now() + timedelta(minutes=30)  # 30分钟过期
        )
        
        # 这里应该调用相应的支付API创建支付订单
        # 例如微信支付、支付宝等
        
        return Response({
            'code': 200,
            'message': '订单创建成功',
            'data': {
                'order_id': order.id,
                'order_no': order.order_no,
                'amount': order.final_amount,
                'expired_at': order.expired_at,
                'pay_params': {}  # 这里返回支付参数
            }
        })
        
    except Exception as e:
        return Response({
            'code': 500,
            'message': f'创建订单失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_coin_balance(request):
    """获取用户金币余额"""
    coin_balance, created = CoinBalance.objects.get_or_create(
        user=request.user,
        defaults={'balance': 0}
    )
    
    serializer = CoinBalanceSerializer(coin_balance)
    return Response({
        'code': 200,
        'data': serializer.data
    })
