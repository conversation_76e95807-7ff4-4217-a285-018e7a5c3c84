from rest_framework import serializers
from .models import (
    Order, PaymentRecord, CoinPackage, VipPackage,
    CoinTransaction, UserVipRecord, PointsTask,
    UserPointsAccount, PointsTransaction, UserTaskProgress
)
from apps.users.serializers import UserProfileSerializer


class CoinPackageSerializer(serializers.ModelSerializer):
    """金币套餐序列化器"""
    
    class Meta:
        model = CoinPackage
        fields = [
            'id', 'name', 'coin_amount', 'price', 'bonus_coins',
            'total_coins', 'description', 'is_popular', 'is_active',
            'created_at'
        ]
        read_only_fields = ['id', 'total_coins', 'created_at']


class VipPackageSerializer(serializers.ModelSerializer):
    """VIP套餐序列化器"""

    class Meta:
        model = VipPackage
        fields = [
            'id', 'name', 'duration', 'price', 'original_price',
            'features', 'description', 'daily_likes_limit', 'daily_super_likes',
            'can_see_visitors', 'can_invisible_browse', 'can_undo_actions',
            'priority_matching', 'sort_order', 'is_popular', 'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class OrderSerializer(serializers.ModelSerializer):
    """订单序列化器"""
    user = UserProfileSerializer(read_only=True)
    coin_package = CoinPackageSerializer(read_only=True)
    vip_package = VipPackageSerializer(read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'user', 'order_no', 'order_type', 'coin_package',
            'vip_package', 'amount', 'status', 'payment_method',
            'payment_time', 'expire_time', 'created_at'
        ]
        read_only_fields = [
            'id', 'user', 'order_no', 'payment_time', 'created_at'
        ]


class CreateOrderSerializer(serializers.Serializer):
    """创建订单序列化器"""
    ORDER_TYPES = [
        ('coin', '金币充值'),
        ('vip', 'VIP购买'),
    ]
    
    order_type = serializers.ChoiceField(choices=ORDER_TYPES)
    package_id = serializers.IntegerField()
    payment_method = serializers.ChoiceField(
        choices=[('wechat', '微信支付'), ('alipay', '支付宝')],
        default='wechat'
    )
    
    def validate(self, data):
        order_type = data.get('order_type')
        package_id = data.get('package_id')
        
        if order_type == 'coin':
            try:
                package = CoinPackage.objects.get(id=package_id, is_active=True)
                data['package'] = package
            except CoinPackage.DoesNotExist:
                raise serializers.ValidationError("金币套餐不存在或已下架")
        
        elif order_type == 'vip':
            try:
                package = VipPackage.objects.get(id=package_id, is_active=True)
                data['package'] = package
            except VipPackage.DoesNotExist:
                raise serializers.ValidationError("VIP套餐不存在或已下架")
        
        return data


class PaymentRecordSerializer(serializers.ModelSerializer):
    """支付记录序列化器"""
    user = UserProfileSerializer(read_only=True)
    order = OrderSerializer(read_only=True)
    
    class Meta:
        model = PaymentRecord
        fields = [
            'id', 'user', 'order', 'transaction_id', 'payment_method',
            'amount', 'status', 'paid_at', 'refunded_at', 'refund_reason',
            'created_at'
        ]
        read_only_fields = ['id', 'user', 'created_at']


class CoinTransactionSerializer(serializers.ModelSerializer):
    """金币交易记录序列化器"""
    user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = CoinTransaction
        fields = [
            'id', 'user', 'transaction_type', 'amount', 'balance_after',
            'description', 'related_order_id', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'balance_after', 'created_at']


class UserVipRecordSerializer(serializers.ModelSerializer):
    """用户VIP记录序列化器"""
    user = UserProfileSerializer(read_only=True)
    package = VipPackageSerializer(read_only=True)

    class Meta:
        model = UserVipRecord
        fields = [
            'id', 'user', 'package', 'start_time', 'end_time',
            'duration', 'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'user', 'is_active', 'created_at']


class PaymentCallbackSerializer(serializers.Serializer):
    """支付回调序列化器"""
    order_no = serializers.CharField(max_length=64)
    transaction_id = serializers.CharField(max_length=128)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    status = serializers.ChoiceField(choices=[('success', '成功'), ('failed', '失败')])
    sign = serializers.CharField(max_length=256)
    
    def validate_order_no(self, value):
        """验证订单号"""
        try:
            order = Order.objects.get(order_no=value)
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("订单不存在")


class CoinUseSerializer(serializers.Serializer):
    """金币使用序列化器"""
    amount = serializers.IntegerField(min_value=1)
    description = serializers.CharField(max_length=200)
    related_id = serializers.IntegerField(required=False)
    
    def validate_amount(self, value):
        """验证金币数量"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            if request.user.coin_balance < value:
                raise serializers.ValidationError("金币余额不足")
        return value


class VipStatusSerializer(serializers.Serializer):
    """VIP状态序列化器"""
    is_vip = serializers.BooleanField()
    vip_level = serializers.IntegerField()
    vip_name = serializers.CharField()
    expire_time = serializers.DateTimeField()
    days_left = serializers.IntegerField()
    features = serializers.ListField()
    
    
class WalletSerializer(serializers.Serializer):
    """钱包信息序列化器"""
    coin_balance = serializers.IntegerField()
    total_recharged = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_consumed_coins = serializers.IntegerField()
    vip_status = VipStatusSerializer()
    
    
class RechargeHistorySerializer(serializers.Serializer):
    """充值历史序列化器"""
    date = serializers.DateField()
    orders = OrderSerializer(many=True)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_coins = serializers.IntegerField()


class ConsumptionHistorySerializer(serializers.Serializer):
    """消费历史序列化器"""
    date = serializers.DateField()
    transactions = CoinTransactionSerializer(many=True)
    total_coins = serializers.IntegerField()


class RefundRequestSerializer(serializers.Serializer):
    """退款申请序列化器"""
    order_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=500)
    
    def validate_order_id(self, value):
        """验证订单ID"""
        request = self.context.get('request')
        try:
            order = Order.objects.get(
                id=value,
                user=request.user,
                status='paid'
            )
            
            # 检查是否在退款期限内（7天）
            from django.utils import timezone
            from datetime import timedelta
            
            if timezone.now() - order.payment_time > timedelta(days=7):
                raise serializers.ValidationError("超过退款期限")
            
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("订单不存在或状态不符合退款条件")


class InvoiceRequestSerializer(serializers.Serializer):
    """发票申请序列化器"""
    order_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    invoice_type = serializers.ChoiceField(
        choices=[('personal', '个人'), ('company', '企业')]
    )
    invoice_title = serializers.CharField(max_length=100)
    tax_number = serializers.CharField(max_length=50, required=False)
    email = serializers.EmailField()
    
    def validate_order_ids(self, value):
        """验证订单ID列表"""
        request = self.context.get('request')
        orders = Order.objects.filter(
            id__in=value,
            user=request.user,
            status='paid'
        )
        
        if orders.count() != len(value):
            raise serializers.ValidationError("部分订单不存在或状态不符合开票条件")
        
        return value
    
    def validate(self, data):
        """验证发票信息"""
        if data.get('invoice_type') == 'company' and not data.get('tax_number'):
            raise serializers.ValidationError("企业发票必须提供税号")
        
        return data


class PointsTaskSerializer(serializers.ModelSerializer):
    """积分任务序列化器"""

    class Meta:
        model = PointsTask
        fields = [
            'id', 'name', 'description', 'task_type', 'frequency',
            'points_reward', 'target_count', 'is_active', 'sort_order'
        ]


class UserPointsAccountSerializer(serializers.ModelSerializer):
    """用户积分账户序列化器"""

    class Meta:
        model = UserPointsAccount
        fields = [
            'total_points', 'available_points', 'used_points',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PointsTransactionSerializer(serializers.ModelSerializer):
    """积分交易记录序列化器"""
    task = PointsTaskSerializer(read_only=True)

    class Meta:
        model = PointsTransaction
        fields = [
            'id', 'transaction_type', 'points', 'earn_source', 'task',
            'description', 'balance_before', 'balance_after', 'created_at'
        ]
        read_only_fields = ['id', 'balance_before', 'balance_after', 'created_at']


class UserTaskProgressSerializer(serializers.ModelSerializer):
    """用户任务进度序列化器"""
    task = PointsTaskSerializer(read_only=True)

    class Meta:
        model = UserTaskProgress
        fields = [
            'id', 'task', 'current_count', 'is_completed', 'completed_at',
            'date_key', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CompleteTaskSerializer(serializers.Serializer):
    """完成任务序列化器"""
    task_id = serializers.IntegerField()

    def validate_task_id(self, value):
        try:
            task = PointsTask.objects.get(id=value, is_active=True)
            return value
        except PointsTask.DoesNotExist:
            raise serializers.ValidationError("任务不存在或已停用")
