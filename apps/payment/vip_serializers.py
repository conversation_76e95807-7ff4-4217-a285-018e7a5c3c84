from rest_framework import serializers
from .models import VipPackage, UserVipRecord, Order, CoinBalance
from apps.users.models import User


class VipPackageSerializer(serializers.ModelSerializer):
    """VIP套餐序列化器"""
    discount_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = VipPackage
        fields = [
            'id', 'name', 'description', 'duration', 'original_price', 'price',
            'features', 'daily_likes_limit', 'daily_super_likes', 'can_see_visitors',
            'can_invisible_browse', 'can_undo_actions', 'priority_matching',
            'is_popular', 'discount_rate'
        ]


class UserVipRecordSerializer(serializers.ModelSerializer):
    """用户VIP记录序列化器"""
    package_info = VipPackageSerializer(source='package', read_only=True)
    is_active = serializers.SerializerMethodField()
    days_left = serializers.SerializerMethodField()
    
    class Meta:
        model = UserVipRecord
        fields = [
            'id', 'package_info', 'start_time', 'end_time', 
            'is_active', 'days_left', 'created_at'
        ]
    
    def get_is_active(self, obj):
        from django.utils import timezone
        return obj.end_time > timezone.now()
    
    def get_days_left(self, obj):
        from django.utils import timezone
        if obj.end_time > timezone.now():
            return (obj.end_time - timezone.now()).days
        return 0


class PurchaseVipSerializer(serializers.Serializer):
    """购买VIP序列化器"""
    package_id = serializers.IntegerField()
    payment_method = serializers.ChoiceField(
        choices=['wechat', 'alipay', 'coin'], 
        default='wechat'
    )
    
    def validate_package_id(self, value):
        try:
            package = VipPackage.objects.get(id=value, is_active=True)
            return value
        except VipPackage.DoesNotExist:
            raise serializers.ValidationError("VIP套餐不存在")


class VipPrivilegeSerializer(serializers.Serializer):
    """VIP特权序列化器"""
    id = serializers.IntegerField()
    icon = serializers.CharField()
    title = serializers.CharField()
    description = serializers.CharField()
    is_new = serializers.BooleanField(default=False)


class UserVipStatusSerializer(serializers.Serializer):
    """用户VIP状态序列化器"""
    is_vip = serializers.BooleanField()
    level_name = serializers.CharField()
    expire_date = serializers.DateTimeField(allow_null=True)
    days_left = serializers.IntegerField()
    features = serializers.JSONField()
    
    # VIP特权状态
    daily_likes_left = serializers.IntegerField()
    daily_super_likes_left = serializers.IntegerField()
    can_see_visitors = serializers.BooleanField()
    can_invisible_browse = serializers.BooleanField()
    can_undo_actions = serializers.BooleanField()
    priority_matching = serializers.BooleanField()


class CoinBalanceSerializer(serializers.ModelSerializer):
    """金币余额序列化器"""
    
    class Meta:
        model = CoinBalance
        fields = [
            'balance', 'total_earned', 'total_spent', 'updated_at'
        ]
        read_only_fields = ['updated_at']


class OrderSerializer(serializers.ModelSerializer):
    """订单序列化器"""
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_no', 'product_type', 'product_name', 'original_amount',
            'discount_amount', 'final_amount', 'payment_method', 'status',
            'created_at', 'paid_at'
        ]
        read_only_fields = ['id', 'order_no', 'created_at', 'paid_at']


class CreateOrderSerializer(serializers.Serializer):
    """创建订单序列化器"""
    product_type = serializers.ChoiceField(choices=['vip', 'coins'])
    product_id = serializers.IntegerField()
    payment_method = serializers.ChoiceField(choices=['wechat', 'alipay'])
    
    def validate(self, data):
        product_type = data['product_type']
        product_id = data['product_id']
        
        if product_type == 'vip':
            try:
                VipPackage.objects.get(id=product_id, is_active=True)
            except VipPackage.DoesNotExist:
                raise serializers.ValidationError("VIP套餐不存在")
        elif product_type == 'coins':
            try:
                from .models import CoinPackage
                CoinPackage.objects.get(id=product_id, is_active=True)
            except CoinPackage.DoesNotExist:
                raise serializers.ValidationError("金币套餐不存在")
        
        return data


class VipBenefitsSerializer(serializers.Serializer):
    """VIP权益对比序列化器"""
    feature = serializers.CharField()
    free_user = serializers.CharField()
    vip_user = serializers.CharField()
    is_highlight = serializers.BooleanField(default=False)


class VipStatisticsSerializer(serializers.Serializer):
    """VIP统计序列化器"""
    total_vip_users = serializers.IntegerField()
    active_vip_users = serializers.IntegerField()
    vip_conversion_rate = serializers.FloatField()
    popular_package = VipPackageSerializer()
    monthly_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
