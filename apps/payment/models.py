from django.db import models
from apps.users.models import User
import uuid


class VipPackage(models.Model):
    """VIP套餐"""
    name = models.CharField(max_length=100, verbose_name='套餐名称')
    description = models.TextField(verbose_name='套餐描述')
    duration = models.IntegerField(verbose_name='时长(天)')
    original_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原价')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='现价')
    
    # 特权配置
    features = models.JSONField(default=dict, verbose_name='特权配置')
    
    # 限制配置
    daily_likes_limit = models.IntegerField(default=-1, verbose_name='每日喜欢限制(-1为无限)')
    daily_super_likes = models.IntegerField(default=5, verbose_name='每日超级喜欢次数')
    can_see_visitors = models.BooleanField(default=True, verbose_name='可查看访客')
    can_invisible_browse = models.BooleanField(default=True, verbose_name='可隐身浏览')
    can_undo_actions = models.BooleanField(default=True, verbose_name='可撤销操作')
    priority_matching = models.BooleanField(default=True, verbose_name='优先匹配')
    
    # 排序和状态
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_popular = models.BooleanField(default=False, verbose_name='是否热门')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'vip_packages'
        verbose_name = 'VIP套餐'
        verbose_name_plural = 'VIP套餐'
        ordering = ['sort_order', '-price']
    
    def __str__(self):
        return self.name
    
    @property
    def discount_rate(self):
        """折扣率"""
        if self.original_price > 0:
            return (self.original_price - self.price) / self.original_price
        return 0


class CoinPackage(models.Model):
    """金币套餐"""
    name = models.CharField(max_length=100, verbose_name='套餐名称')
    description = models.TextField(blank=True, verbose_name='套餐描述')
    coin_amount = models.IntegerField(verbose_name='金币数量')
    bonus_coins = models.IntegerField(default=0, verbose_name='赠送金币')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')
    
    # 排序和状态
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_popular = models.BooleanField(default=False, verbose_name='是否热门')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'coin_packages'
        verbose_name = '金币套餐'
        verbose_name_plural = '金币套餐'
        ordering = ['sort_order', 'price']
    
    def __str__(self):
        return f'{self.name} - {self.coin_amount}金币'
    
    @property
    def total_coins(self):
        """总金币数"""
        return self.coin_amount + self.bonus_coins


class Order(models.Model):
    """订单"""
    PRODUCT_TYPES = [
        ('vip', 'VIP会员'),
        ('coins', '金币'),
        ('gift', '礼物'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
        ('expired', '已过期'),
    ]
    
    PAYMENT_METHODS = [
        ('wechat', '微信支付'),
        ('alipay', '支付宝'),
        ('apple', 'Apple Pay'),
    ]
    
    # 基本信息
    order_no = models.CharField(max_length=64, unique=True, verbose_name='订单号')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders', verbose_name='用户')
    
    # 产品信息
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPES, verbose_name='产品类型')
    product_id = models.IntegerField(verbose_name='产品ID')
    product_name = models.CharField(max_length=200, verbose_name='产品名称')
    product_data = models.JSONField(default=dict, verbose_name='产品数据')
    
    # 价格信息
    original_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原价')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='优惠金额')
    final_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='实付金额')
    
    # 支付信息
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS, blank=True, 
                                     verbose_name='支付方式')
    transaction_id = models.CharField(max_length=200, blank=True, verbose_name='交易号')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='订单状态')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='支付时间')
    expired_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    # 备注信息
    remark = models.TextField(blank=True, verbose_name='备注')
    
    class Meta:
        db_table = 'orders'
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.order_no} - {self.user.nickname}'
    
    def save(self, *args, **kwargs):
        if not self.order_no:
            self.order_no = self.generate_order_no()
        super().save(*args, **kwargs)
    
    def generate_order_no(self):
        """生成订单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f'XQ{timestamp}{random_str}'.upper()


class PaymentRecord(models.Model):
    """支付记录"""
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('success', '支付成功'),
        ('failed', '支付失败'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payment_records', 
                             verbose_name='订单')
    payment_no = models.CharField(max_length=64, unique=True, verbose_name='支付单号')
    
    # 支付信息
    payment_method = models.CharField(max_length=20, choices=Order.PAYMENT_METHODS, 
                                     verbose_name='支付方式')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='支付金额')
    
    # 第三方支付信息
    third_party_order_no = models.CharField(max_length=200, blank=True, verbose_name='第三方订单号')
    third_party_transaction_id = models.CharField(max_length=200, blank=True, verbose_name='第三方交易号')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', 
                             verbose_name='支付状态')
    
    # 回调信息
    callback_data = models.JSONField(default=dict, verbose_name='回调数据')
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='支付时间')
    
    class Meta:
        db_table = 'payment_records'
        verbose_name = '支付记录'
        verbose_name_plural = '支付记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.payment_no} - {self.amount}元'
    
    def save(self, *args, **kwargs):
        if not self.payment_no:
            self.payment_no = self.generate_payment_no()
        super().save(*args, **kwargs)
    
    def generate_payment_no(self):
        """生成支付单号"""
        import time
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f'PAY{timestamp}{random_str}'.upper()


class UserVipRecord(models.Model):
    """用户VIP记录"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='vip_records', 
                            verbose_name='用户')
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='vip_records', 
                             verbose_name='订单')
    package = models.ForeignKey(VipPackage, on_delete=models.CASCADE, verbose_name='VIP套餐')
    
    # VIP信息
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    duration = models.IntegerField(verbose_name='时长(天)')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否有效')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'user_vip_records'
        verbose_name = '用户VIP记录'
        verbose_name_plural = '用户VIP记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.package.name}'


class CoinTransaction(models.Model):
    """金币交易记录"""
    TRANSACTION_TYPES = [
        ('purchase', '购买'),
        ('gift', '赠送礼物'),
        ('reward', '奖励'),
        ('refund', '退款'),
        ('deduct', '扣除'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='coin_transactions', 
                            verbose_name='用户')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, 
                                       verbose_name='交易类型')
    amount = models.IntegerField(verbose_name='金币数量')
    balance_before = models.IntegerField(verbose_name='交易前余额')
    balance_after = models.IntegerField(verbose_name='交易后余额')
    
    # 关联信息
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True, 
                             verbose_name='关联订单')
    related_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name='received_coin_transactions', 
                                    verbose_name='关联用户')
    
    description = models.CharField(max_length=200, verbose_name='描述')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'coin_transactions'
        verbose_name = '金币交易记录'
        verbose_name_plural = '金币交易记录'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user.nickname} {self.get_transaction_type_display()} {self.amount}金币'


class PointsTask(models.Model):
    """积分任务"""
    TASK_TYPES = [
        ('daily_login', '每日登录'),
        ('complete_profile', '完善资料'),
        ('upload_photo', '上传照片'),
        ('send_message', '发送消息'),
        ('publish_moment', '发布动态'),
        ('like_moment', '点赞动态'),
        ('invite_friend', '邀请好友'),
        ('verify_identity', '身份认证'),
        ('first_match', '首次匹配'),
        ('continuous_login', '连续登录'),
    ]

    TASK_FREQUENCIES = [
        ('once', '一次性'),
        ('daily', '每日'),
        ('weekly', '每周'),
        ('monthly', '每月'),
    ]

    name = models.CharField(max_length=100, verbose_name='任务名称')
    description = models.TextField(verbose_name='任务描述')
    task_type = models.CharField(max_length=20, choices=TASK_TYPES, verbose_name='任务类型')
    frequency = models.CharField(max_length=10, choices=TASK_FREQUENCIES, default='daily', verbose_name='任务频率')
    points_reward = models.IntegerField(verbose_name='积分奖励')

    # 任务条件
    target_count = models.IntegerField(default=1, verbose_name='目标次数')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.IntegerField(default=0, verbose_name='排序')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'points_tasks'
        verbose_name = '积分任务'
        verbose_name_plural = '积分任务'
        ordering = ['sort_order', '-points_reward']

    def __str__(self):
        return f"{self.name} (+{self.points_reward}积分)"


class UserPointsAccount(models.Model):
    """用户积分账户"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='points_account', verbose_name='用户')
    total_points = models.IntegerField(default=0, verbose_name='总积分')
    available_points = models.IntegerField(default=0, verbose_name='可用积分')
    used_points = models.IntegerField(default=0, verbose_name='已使用积分')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_points_accounts'
        verbose_name = '用户积分账户'
        verbose_name_plural = '用户积分账户'

    def __str__(self):
        return f"{self.user.nickname} - {self.available_points}积分"


class PointsTransaction(models.Model):
    """积分交易记录"""
    TRANSACTION_TYPES = [
        ('earn', '获得'),
        ('spend', '消费'),
        ('expire', '过期'),
        ('refund', '退还'),
    ]

    EARN_SOURCES = [
        ('task', '任务奖励'),
        ('invite', '邀请奖励'),
        ('activity', '活动奖励'),
        ('admin', '管理员发放'),
        ('compensation', '补偿'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='points_transactions', verbose_name='用户')
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES, verbose_name='交易类型')
    points = models.IntegerField(verbose_name='积分数量')

    # 获得积分相关
    earn_source = models.CharField(max_length=20, choices=EARN_SOURCES, blank=True, verbose_name='获得来源')
    task = models.ForeignKey(PointsTask, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联任务')

    description = models.CharField(max_length=200, verbose_name='描述')
    balance_before = models.IntegerField(verbose_name='交易前余额')
    balance_after = models.IntegerField(verbose_name='交易后余额')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'points_transactions'
        verbose_name = '积分交易记录'
        verbose_name_plural = '积分交易记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.nickname} {self.get_transaction_type_display()} {self.points}积分"


class UserTaskProgress(models.Model):
    """用户任务进度"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='task_progress', verbose_name='用户')
    task = models.ForeignKey(PointsTask, on_delete=models.CASCADE, related_name='user_progress', verbose_name='任务')

    current_count = models.IntegerField(default=0, verbose_name='当前进度')
    is_completed = models.BooleanField(default=False, verbose_name='是否完成')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    # 周期性任务的日期标识
    date_key = models.CharField(max_length=20, verbose_name='日期标识')  # 格式：2024-01-01 或 2024-W01

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_task_progress'
        verbose_name = '用户任务进度'
        verbose_name_plural = '用户任务进度'
        unique_together = ['user', 'task', 'date_key']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.nickname} - {self.task.name} ({self.current_count}/{self.task.target_count})"


class CoinBalance(models.Model):
    """用户金币余额"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='coin_balance',
                               verbose_name='用户')
    balance = models.IntegerField(default=0, verbose_name='当前余额')
    total_earned = models.IntegerField(default=0, verbose_name='累计获得')
    total_spent = models.IntegerField(default=0, verbose_name='累计消费')

    # 今日统计
    today_earned = models.IntegerField(default=0, verbose_name='今日获得')
    today_spent = models.IntegerField(default=0, verbose_name='今日消费')
    last_reset_date = models.DateField(auto_now_add=True, verbose_name='上次重置日期')

    # 冻结金币
    frozen_balance = models.IntegerField(default=0, verbose_name='冻结余额')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'coin_balances'
        verbose_name = '金币余额'
        verbose_name_plural = '金币余额'

    def __str__(self):
        return f'{self.user.nickname} - {self.balance}金币'

    def add_coins(self, amount, description=''):
        """增加金币"""
        if amount <= 0:
            return False

        self.balance += amount
        self.total_earned += amount
        self.today_earned += amount
        self.save()

        # 创建交易记录
        CoinTransaction.objects.create(
            user=self.user,
            transaction_type='reward',
            amount=amount,
            balance_before=self.balance - amount,
            balance_after=self.balance,
            description=description
        )
        return True

    def spend_coins(self, amount, description=''):
        """消费金币"""
        if amount <= 0 or self.balance < amount:
            return False

        self.balance -= amount
        self.total_spent += amount
        self.today_spent += amount
        self.save()

        # 创建交易记录
        CoinTransaction.objects.create(
            user=self.user,
            transaction_type='deduct',
            amount=-amount,
            balance_before=self.balance + amount,
            balance_after=self.balance,
            description=description
        )
        return True

    def reset_daily_stats(self):
        """重置每日统计"""
        today = timezone.now().date()
        if self.last_reset_date < today:
            self.today_earned = 0
            self.today_spent = 0
            self.last_reset_date = today
            self.save()


class CoinRechargeRecord(models.Model):
    """金币充值记录"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='coin_recharge_records',
                            verbose_name='用户')
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='coin_recharge_record',
                                verbose_name='订单')
    package = models.ForeignKey(CoinPackage, on_delete=models.CASCADE, verbose_name='金币套餐')

    # 充值信息
    coin_amount = models.IntegerField(verbose_name='金币数量')
    bonus_coins = models.IntegerField(default=0, verbose_name='赠送金币')
    total_coins = models.IntegerField(verbose_name='总金币数')
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='支付金额')

    # 状态
    is_processed = models.BooleanField(default=False, verbose_name='是否已处理')
    processed_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'coin_recharge_records'
        verbose_name = '金币充值记录'
        verbose_name_plural = '金币充值记录'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.user.nickname} 充值 {self.total_coins}金币'

    def process_recharge(self):
        """处理充值"""
        if self.is_processed:
            return False

        # 获取用户金币余额
        coin_balance, created = CoinBalance.objects.get_or_create(
            user=self.user,
            defaults={'balance': 0}
        )

        # 增加金币
        coin_balance.add_coins(
            self.total_coins,
            f'充值{self.package.name}'
        )

        # 标记为已处理
        self.is_processed = True
        self.processed_at = timezone.now()
        self.save()

        return True


class VipPrivilege(models.Model):
    """VIP特权配置模型"""
    # 特权信息
    privilege_code = models.CharField(max_length=50, unique=True, verbose_name='特权代码')
    privilege_name = models.CharField(max_length=100, verbose_name='特权名称')
    description = models.TextField(blank=True, verbose_name='特权描述')

    # 适用VIP等级
    min_vip_level = models.IntegerField(default=1, verbose_name='最低VIP等级')

    # 限制配置
    daily_limit = models.IntegerField(default=-1, verbose_name='每日限制(-1表示无限制)')
    monthly_limit = models.IntegerField(default=-1, verbose_name='每月限制(-1表示无限制)')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'vip_privileges'
        verbose_name = 'VIP特权配置'
        verbose_name_plural = 'VIP特权配置'
        ordering = ['min_vip_level', 'privilege_code']

    def __str__(self):
        return f"{self.privilege_name} (VIP{self.min_vip_level}+)"


class VipPrivilegeUsage(models.Model):
    """VIP特权使用记录模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='vip_usage_records', verbose_name='用户')
    privilege_code = models.CharField(max_length=50, verbose_name='特权代码')

    # 使用信息
    usage_date = models.DateField(verbose_name='使用日期')
    usage_count = models.IntegerField(default=1, verbose_name='使用次数')

    # 相关信息
    related_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='related_vip_usage', verbose_name='相关用户')
    related_content_id = models.BigIntegerField(null=True, blank=True, verbose_name='相关内容ID')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'vip_privilege_usage'
        verbose_name = 'VIP特权使用记录'
        verbose_name_plural = 'VIP特权使用记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} 使用 {self.privilege_code} ({self.usage_date})"
