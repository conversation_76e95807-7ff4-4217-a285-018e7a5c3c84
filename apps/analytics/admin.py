from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Sum, Avg
from django.urls import path
from django.shortcuts import render
from django.http import JsonResponse
from .models import PlatformStatistics, UserBehaviorLog, RevenueRecord, SystemAlert


@admin.register(PlatformStatistics)
class PlatformStatisticsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_users', 'new_users', 'active_users', 'vip_users',
                   'new_matches', 'new_messages', 'daily_revenue_display']
    list_filter = ['date']
    search_fields = ['date']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('date',)
        }),
        ('用户统计', {
            'fields': ('total_users', 'new_users', 'active_users', 'vip_users')
        }),
        ('匹配统计', {
            'fields': ('total_matches', 'new_matches', 'successful_matches')
        }),
        ('聊天统计', {
            'fields': ('total_messages', 'new_messages', 'active_conversations')
        }),
        ('动态统计', {
            'fields': ('total_moments', 'new_moments', 'moments_likes')
        }),
        ('收入统计', {
            'fields': ('daily_revenue', 'vip_revenue', 'gift_revenue')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def daily_revenue_display(self, obj):
        return format_html('<span style="color: green; font-weight: bold;">¥{}</span>', obj.daily_revenue)
    daily_revenue_display.short_description = '日收入'
    
    def changelist_view(self, request, extra_context=None):
        # 添加统计图表数据
        extra_context = extra_context or {}
        
        # 最近7天数据
        recent_stats = PlatformStatistics.objects.order_by('-date')[:7]
        chart_data = {
            'dates': [stat.date.strftime('%m-%d') for stat in reversed(recent_stats)],
            'users': [stat.new_users for stat in reversed(recent_stats)],
            'matches': [stat.new_matches for stat in reversed(recent_stats)],
            'revenue': [float(stat.daily_revenue) for stat in reversed(recent_stats)],
        }
        extra_context['chart_data'] = chart_data
        
        return super().changelist_view(request, extra_context)


@admin.register(UserBehaviorLog)
class UserBehaviorLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'action_type_display', 'target_user', 'location', 'created_at']
    list_filter = ['action_type', 'created_at', 'location']
    search_fields = ['user__nickname', 'target_user__nickname', 'ip_address']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'action_type', 'target_user')
        }),
        ('行为详情', {
            'fields': ('details',)
        }),
        ('技术信息', {
            'fields': ('ip_address', 'user_agent')
        }),
        ('地理位置', {
            'fields': ('location', 'latitude', 'longitude')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def action_type_display(self, obj):
        colors = {
            'login': 'green', 'logout': 'gray', 'like': 'red', 'super_like': 'purple',
            'match': 'blue', 'send_message': 'orange', 'purchase_vip': 'gold',
            'report': 'darkred', 'block': 'black'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.action_type, 'black'),
            obj.get_action_type_display()
        )
    action_type_display.short_description = '行为类型'


@admin.register(RevenueRecord)
class RevenueRecordAdmin(admin.ModelAdmin):
    list_display = ['user', 'revenue_type_display', 'product_name', 'amount_display',
                   'payment_method', 'payment_time']
    list_filter = ['revenue_type', 'payment_method', 'payment_time']
    search_fields = ['user__nickname', 'order_id', 'product_name']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'revenue_type', 'amount')
        }),
        ('订单信息', {
            'fields': ('order_id', 'payment_method', 'payment_time')
        }),
        ('商品信息', {
            'fields': ('product_name', 'product_details')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def revenue_type_display(self, obj):
        colors = {
            'vip': 'gold', 'gift': 'pink', 'recharge': 'green',
            'matchmaker': 'purple', 'activity': 'blue'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.revenue_type, 'black'),
            obj.get_revenue_type_display()
        )
    revenue_type_display.short_description = '收入类型'
    
    def amount_display(self, obj):
        return format_html('<span style="color: green; font-weight: bold;">¥{}</span>', obj.amount)
    amount_display.short_description = '金额'
    
    def changelist_view(self, request, extra_context=None):
        # 添加收入统计
        extra_context = extra_context or {}
        
        # 总收入统计
        total_revenue = RevenueRecord.objects.aggregate(total=Sum('amount'))['total'] or 0
        avg_revenue = RevenueRecord.objects.aggregate(avg=Avg('amount'))['avg'] or 0
        
        # 按类型统计
        revenue_by_type = {}
        for revenue_type, _ in RevenueRecord.REVENUE_TYPES:
            amount = RevenueRecord.objects.filter(revenue_type=revenue_type).aggregate(
                total=Sum('amount'))['total'] or 0
            revenue_by_type[revenue_type] = amount
        
        extra_context.update({
            'total_revenue': total_revenue,
            'avg_revenue': avg_revenue,
            'revenue_by_type': revenue_by_type,
        })
        
        return super().changelist_view(request, extra_context)


@admin.register(SystemAlert)
class SystemAlertAdmin(admin.ModelAdmin):
    list_display = ['title', 'alert_type_display', 'alert_level_display', 
                   'related_user', 'is_resolved_display', 'created_at']
    list_filter = ['alert_type', 'alert_level', 'is_resolved', 'created_at']
    search_fields = ['title', 'content', 'related_user__nickname']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'alert_type', 'alert_level')
        }),
        ('相关信息', {
            'fields': ('related_user', 'related_data')
        }),
        ('处理状态', {
            'fields': ('is_resolved', 'resolved_by', 'resolved_at', 'resolution_notes')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
    )
    
    def alert_type_display(self, obj):
        colors = {
            'user_behavior': 'orange', 'system_error': 'red', 'payment_issue': 'purple',
            'security_threat': 'darkred', 'performance': 'blue', 'data_anomaly': 'brown'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.alert_type, 'black'),
            obj.get_alert_type_display()
        )
    alert_type_display.short_description = '告警类型'
    
    def alert_level_display(self, obj):
        colors = {'info': 'blue', 'warning': 'orange', 'error': 'red', 'critical': 'darkred'}
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.alert_level, 'black'),
            obj.get_alert_level_display()
        )
    alert_level_display.short_description = '告警级别'
    
    def is_resolved_display(self, obj):
        if obj.is_resolved:
            return format_html('<span style="color: green;">✓ 已处理</span>')
        return format_html('<span style="color: red;">✗ 未处理</span>')
    is_resolved_display.short_description = '处理状态'
    
    actions = ['mark_as_resolved']
    
    def mark_as_resolved(self, request, queryset):
        updated = queryset.update(is_resolved=True, resolved_by=request.user.username)
        self.message_user(request, f'已标记 {updated} 条告警为已处理')
    mark_as_resolved.short_description = '标记为已处理'
