from django.db import models
from django.utils import timezone
from apps.users.models import User


class PlatformStatistics(models.Model):
    """平台统计数据"""
    date = models.DateField(verbose_name='统计日期')
    
    # 用户统计
    total_users = models.IntegerField(default=0, verbose_name='总用户数')
    new_users = models.IntegerField(default=0, verbose_name='新增用户')
    active_users = models.IntegerField(default=0, verbose_name='活跃用户')
    vip_users = models.IntegerField(default=0, verbose_name='VIP用户数')
    
    # 匹配统计
    total_matches = models.IntegerField(default=0, verbose_name='总匹配数')
    new_matches = models.IntegerField(default=0, verbose_name='新增匹配')
    successful_matches = models.IntegerField(default=0, verbose_name='成功匹配')
    
    # 聊天统计
    total_messages = models.IntegerField(default=0, verbose_name='总消息数')
    new_messages = models.IntegerField(default=0, verbose_name='新增消息')
    active_conversations = models.IntegerField(default=0, verbose_name='活跃对话')
    
    # 动态统计
    total_moments = models.IntegerField(default=0, verbose_name='总动态数')
    new_moments = models.IntegerField(default=0, verbose_name='新增动态')
    moments_likes = models.IntegerField(default=0, verbose_name='动态点赞数')
    
    # 收入统计
    daily_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='日收入')
    vip_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='VIP收入')
    gift_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='礼品收入')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'platform_statistics'
        verbose_name = '平台统计'
        verbose_name_plural = '平台统计'
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f'{self.date} - 用户:{self.total_users} 匹配:{self.total_matches}'


class UserBehaviorLog(models.Model):
    """用户行为日志"""
    ACTION_TYPES = [
        ('login', '登录'),
        ('logout', '登出'),
        ('view_profile', '查看资料'),
        ('like', '喜欢'),
        ('super_like', '超级喜欢'),
        ('pass', '跳过'),
        ('match', '匹配'),
        ('send_message', '发送消息'),
        ('send_gift', '发送礼品'),
        ('publish_moment', '发布动态'),
        ('purchase_vip', '购买VIP'),
        ('recharge', '充值'),
        ('report', '举报'),
        ('block', '拉黑'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name='行为类型')
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,
                                   related_name='analytics_received_actions', verbose_name='目标用户')
    
    # 行为详情
    details = models.JSONField(default=dict, verbose_name='行为详情')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')
    
    # 地理位置
    location = models.CharField(max_length=100, blank=True, verbose_name='地理位置')
    latitude = models.FloatField(null=True, blank=True, verbose_name='纬度')
    longitude = models.FloatField(null=True, blank=True, verbose_name='经度')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'user_behavior_logs'
        verbose_name = '用户行为日志'
        verbose_name_plural = '用户行为日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f'{self.user.nickname} - {self.get_action_type_display()}'


class RevenueRecord(models.Model):
    """收入记录"""
    REVENUE_TYPES = [
        ('vip', 'VIP会员'),
        ('gift', '礼品购买'),
        ('recharge', '金币充值'),
        ('matchmaker', '红娘服务'),
        ('activity', '活动费用'),
        ('other', '其他'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    revenue_type = models.CharField(max_length=20, choices=REVENUE_TYPES, verbose_name='收入类型')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='金额')
    
    # 订单信息
    order_id = models.CharField(max_length=100, unique=True, verbose_name='订单号')
    payment_method = models.CharField(max_length=50, verbose_name='支付方式')
    
    # 商品信息
    product_name = models.CharField(max_length=200, verbose_name='商品名称')
    product_details = models.JSONField(default=dict, verbose_name='商品详情')
    
    # 时间信息
    payment_time = models.DateTimeField(verbose_name='支付时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'revenue_records'
        verbose_name = '收入记录'
        verbose_name_plural = '收入记录'
        ordering = ['-payment_time']
        indexes = [
            models.Index(fields=['revenue_type', 'payment_time']),
            models.Index(fields=['user', 'payment_time']),
        ]
    
    def __str__(self):
        return f'{self.user.nickname} - {self.product_name} - ¥{self.amount}'


class SystemAlert(models.Model):
    """系统告警"""
    ALERT_LEVELS = [
        ('info', '信息'),
        ('warning', '警告'),
        ('error', '错误'),
        ('critical', '严重'),
    ]
    
    ALERT_TYPES = [
        ('user_behavior', '用户行为异常'),
        ('system_error', '系统错误'),
        ('payment_issue', '支付问题'),
        ('security_threat', '安全威胁'),
        ('performance', '性能问题'),
        ('data_anomaly', '数据异常'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='告警标题')
    content = models.TextField(verbose_name='告警内容')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES, verbose_name='告警类型')
    alert_level = models.CharField(max_length=10, choices=ALERT_LEVELS, verbose_name='告警级别')
    
    # 相关信息
    related_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='相关用户')
    related_data = models.JSONField(default=dict, verbose_name='相关数据')
    
    # 处理状态
    is_resolved = models.BooleanField(default=False, verbose_name='是否已处理')
    resolved_by = models.CharField(max_length=100, blank=True, verbose_name='处理人')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    resolution_notes = models.TextField(blank=True, verbose_name='处理说明')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'system_alerts'
        verbose_name = '系统告警'
        verbose_name_plural = '系统告警'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_level', 'is_resolved']),
            models.Index(fields=['alert_type', 'created_at']),
        ]
    
    def __str__(self):
        return f'[{self.get_alert_level_display()}] {self.title}'
