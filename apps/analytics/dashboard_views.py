"""
数据分析仪表板视图
提供高级数据分析和可视化功能
"""

from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.db.models import Count, Q, Avg, Sum, F
from django.utils import timezone
from datetime import datetime, timedelta, date
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
import json

from apps.users.models import User, UserStatistics
from apps.matching.models import Like, Match
from apps.chat.models import ChatMessage
from apps.moments.models import Moment, UserStory
from apps.payment.models import VipMembership, PaymentRecord
from .models import PlatformStatistics, UserBehaviorLog


@method_decorator(staff_member_required, name='dispatch')
class AnalyticsDashboardView(TemplateView):
    """数据分析仪表板主页"""
    template_name = 'analytics/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取基础数据
        today = timezone.now().date()
        context.update({
            'page_title': '数据分析仪表板',
            'current_date': today.strftime('%Y年%m月%d日'),
            'total_users': User.objects.filter(is_active=True).count(),
            'online_users': self.get_online_users_count(),
            'today_revenue': self.get_today_revenue(),
            'conversion_rate': self.get_conversion_rate(),
        })
        
        return context
    
    def get_online_users_count(self):
        """获取在线用户数（最近5分钟活跃）"""
        five_minutes_ago = timezone.now() - timedelta(minutes=5)
        return User.objects.filter(
            last_login__gte=five_minutes_ago
        ).count()
    
    def get_today_revenue(self):
        """获取今日收入"""
        today = timezone.now().date()
        return PaymentRecord.objects.filter(
            status='completed',
            created_at__date=today
        ).aggregate(total=Sum('amount'))['total'] or 0
    
    def get_conversion_rate(self):
        """获取转化率（VIP用户比例）"""
        total_users = User.objects.filter(is_active=True).count()
        vip_users = VipMembership.objects.filter(is_active=True).count()
        return (vip_users / total_users * 100) if total_users > 0 else 0


class AdvancedAnalyticsAPIView(APIView):
    """高级数据分析API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取高级分析数据"""
        analysis_type = request.GET.get('type', 'overview')
        date_range = int(request.GET.get('range', 30))
        
        if analysis_type == 'overview':
            return self.get_overview_analysis(date_range)
        elif analysis_type == 'user_lifecycle':
            return self.get_user_lifecycle_analysis(date_range)
        elif analysis_type == 'feature_usage':
            return self.get_feature_usage_analysis(date_range)
        elif analysis_type == 'cohort':
            return self.get_cohort_analysis()
        elif analysis_type == 'funnel':
            return self.get_funnel_analysis(date_range)
        elif analysis_type == 'realtime':
            return self.get_realtime_metrics()
        else:
            return Response({'error': '无效的分析类型'}, status=400)
    
    def get_overview_analysis(self, days):
        """获取概览分析"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 用户增长分析
        user_growth = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            new_users = User.objects.filter(date_joined__date=date).count()
            total_users = User.objects.filter(date_joined__date__lte=date).count()
            user_growth.append({
                'date': date.strftime('%Y-%m-%d'),
                'new_users': new_users,
                'total_users': total_users
            })
        
        # 活跃度分析
        activity_data = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            active_users = User.objects.filter(last_login__date=date).count()
            messages = ChatMessage.objects.filter(created_at__date=date).count()
            likes = Like.objects.filter(created_at__date=date).count()
            activity_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'active_users': active_users,
                'messages': messages,
                'likes': likes
            })
        
        # 收入分析
        revenue_data = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            revenue = PaymentRecord.objects.filter(
                status='completed',
                created_at__date=date
            ).aggregate(total=Sum('amount'))['total'] or 0
            revenue_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'revenue': float(revenue)
            })
        
        return Response({
            'code': 200,
            'data': {
                'user_growth': user_growth,
                'activity_data': activity_data,
                'revenue_data': revenue_data,
                'period': f'{start_date} 至 {end_date}'
            }
        })
    
    def get_user_lifecycle_analysis(self, days):
        """获取用户生命周期分析"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 用户生命周期阶段分析
        lifecycle_stages = {
            'new_users': User.objects.filter(
                date_joined__date__gte=start_date,
                date_joined__date__lte=end_date
            ).count(),
            'active_users': User.objects.filter(
                last_login__date__gte=start_date,
                date_joined__date__lt=start_date
            ).count(),
            'dormant_users': User.objects.filter(
                last_login__date__lt=start_date - timedelta(days=7),
                date_joined__date__lt=start_date
            ).count(),
            'churned_users': User.objects.filter(
                last_login__date__lt=start_date - timedelta(days=30),
                date_joined__date__lt=start_date
            ).count()
        }
        
        # 留存率分析
        retention_analysis = self.calculate_retention_rates(start_date, end_date)
        
        # 用户价值分析
        user_value_segments = self.analyze_user_value_segments()
        
        return Response({
            'code': 200,
            'data': {
                'lifecycle_stages': lifecycle_stages,
                'retention_analysis': retention_analysis,
                'user_value_segments': user_value_segments
            }
        })
    
    def get_feature_usage_analysis(self, days):
        """获取功能使用分析"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 核心功能使用统计
        feature_usage = {
            'profile_views': UserBehaviorLog.objects.filter(
                action='view_profile',
                created_at__date__gte=start_date
            ).count(),
            'likes_sent': Like.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'messages_sent': ChatMessage.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'moments_posted': Moment.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'stories_posted': UserStory.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'searches_performed': UserBehaviorLog.objects.filter(
                action='search',
                created_at__date__gte=start_date
            ).count()
        }
        
        # 功能使用趋势
        feature_trends = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            daily_usage = {
                'date': date.strftime('%Y-%m-%d'),
                'likes': Like.objects.filter(created_at__date=date).count(),
                'messages': ChatMessage.objects.filter(created_at__date=date).count(),
                'moments': Moment.objects.filter(created_at__date=date).count(),
                'stories': UserStory.objects.filter(created_at__date=date).count()
            }
            feature_trends.append(daily_usage)
        
        # 用户参与度分析
        engagement_metrics = self.calculate_engagement_metrics(start_date, end_date)
        
        return Response({
            'code': 200,
            'data': {
                'feature_usage': feature_usage,
                'feature_trends': feature_trends,
                'engagement_metrics': engagement_metrics
            }
        })
    
    def get_cohort_analysis(self):
        """获取群组分析"""
        # 按注册月份分组的用户群组分析
        cohort_data = []
        
        # 获取最近12个月的数据
        for i in range(12):
            cohort_month = timezone.now().date().replace(day=1) - timedelta(days=30*i)
            
            # 该月注册的用户
            cohort_users = User.objects.filter(
                date_joined__year=cohort_month.year,
                date_joined__month=cohort_month.month
            )
            
            if cohort_users.exists():
                cohort_size = cohort_users.count()
                
                # 计算各月的留存率
                retention_data = []
                for month in range(6):  # 计算6个月的留存
                    retention_month = cohort_month + timedelta(days=30*(month+1))
                    if retention_month <= timezone.now().date():
                        retained_users = cohort_users.filter(
                            last_login__year=retention_month.year,
                            last_login__month=retention_month.month
                        ).count()
                        retention_rate = (retained_users / cohort_size * 100) if cohort_size > 0 else 0
                        retention_data.append({
                            'month': month + 1,
                            'retained_users': retained_users,
                            'retention_rate': round(retention_rate, 2)
                        })
                
                cohort_data.append({
                    'cohort_month': cohort_month.strftime('%Y-%m'),
                    'cohort_size': cohort_size,
                    'retention_data': retention_data
                })
        
        return Response({
            'code': 200,
            'data': {
                'cohort_analysis': cohort_data
            }
        })
    
    def get_funnel_analysis(self, days):
        """获取漏斗分析"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 注册转化漏斗
        registration_funnel = {
            'visitors': User.objects.filter(
                date_joined__date__gte=start_date
            ).count() * 3,  # 假设访客是注册用户的3倍
            'registrations': User.objects.filter(
                date_joined__date__gte=start_date
            ).count(),
            'profile_completed': User.objects.filter(
                date_joined__date__gte=start_date,
                age__isnull=False,
                city__isnull=False
            ).count(),
            'first_like': User.objects.filter(
                date_joined__date__gte=start_date,
                sent_likes__isnull=False
            ).distinct().count(),
            'first_match': User.objects.filter(
                date_joined__date__gte=start_date,
                sent_likes__match__isnull=False
            ).distinct().count()
        }
        
        # 付费转化漏斗
        payment_funnel = {
            'active_users': User.objects.filter(
                last_login__date__gte=start_date
            ).count(),
            'viewed_vip_page': User.objects.filter(
                last_login__date__gte=start_date
            ).count() // 4,  # 假设25%的用户查看过VIP页面
            'initiated_payment': PaymentRecord.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'completed_payment': PaymentRecord.objects.filter(
                created_at__date__gte=start_date,
                status='completed'
            ).count()
        }
        
        return Response({
            'code': 200,
            'data': {
                'registration_funnel': registration_funnel,
                'payment_funnel': payment_funnel
            }
        })
    
    def get_realtime_metrics(self):
        """获取实时指标"""
        now = timezone.now()
        one_hour_ago = now - timedelta(hours=1)
        
        # 实时活跃数据
        realtime_data = {
            'current_online_users': User.objects.filter(
                last_login__gte=now - timedelta(minutes=5)
            ).count(),
            'last_hour_logins': User.objects.filter(
                last_login__gte=one_hour_ago
            ).count(),
            'last_hour_registrations': User.objects.filter(
                date_joined__gte=one_hour_ago
            ).count(),
            'last_hour_matches': Match.objects.filter(
                created_at__gte=one_hour_ago
            ).count(),
            'last_hour_messages': ChatMessage.objects.filter(
                created_at__gte=one_hour_ago
            ).count(),
            'last_hour_revenue': float(PaymentRecord.objects.filter(
                status='completed',
                created_at__gte=one_hour_ago
            ).aggregate(total=Sum('amount'))['total'] or 0)
        }
        
        # 系统性能指标（模拟数据）
        performance_metrics = {
            'avg_response_time': 120,  # ms
            'error_rate': 0.5,  # %
            'server_cpu_usage': 45,  # %
            'server_memory_usage': 68,  # %
            'database_connections': 25
        }
        
        return Response({
            'code': 200,
            'data': {
                'realtime_data': realtime_data,
                'performance_metrics': performance_metrics,
                'timestamp': now.isoformat()
            }
        })
    
    def calculate_retention_rates(self, start_date, end_date):
        """计算留存率"""
        # 简化的留存率计算
        new_users = User.objects.filter(
            date_joined__date__gte=start_date,
            date_joined__date__lte=end_date
        )
        
        if not new_users.exists():
            return {'day1': 0, 'day7': 0, 'day30': 0}
        
        total_new_users = new_users.count()
        
        # 1日留存
        day1_retained = new_users.filter(
            last_login__date__gt=F('date_joined__date')
        ).count()
        
        # 7日留存
        day7_retained = new_users.filter(
            last_login__date__gte=F('date_joined__date') + timedelta(days=7)
        ).count()
        
        # 30日留存
        day30_retained = new_users.filter(
            last_login__date__gte=F('date_joined__date') + timedelta(days=30)
        ).count()
        
        return {
            'day1': round((day1_retained / total_new_users * 100), 2),
            'day7': round((day7_retained / total_new_users * 100), 2),
            'day30': round((day30_retained / total_new_users * 100), 2)
        }
    
    def analyze_user_value_segments(self):
        """分析用户价值分群"""
        # 根据付费金额和活跃度分群
        segments = {
            'high_value': VipMembership.objects.filter(
                is_active=True,
                user__last_login__date__gte=timezone.now().date() - timedelta(days=7)
            ).count(),
            'medium_value': User.objects.filter(
                last_login__date__gte=timezone.now().date() - timedelta(days=7),
                vip_memberships__isnull=True
            ).count(),
            'low_value': User.objects.filter(
                last_login__date__gte=timezone.now().date() - timedelta(days=30),
                last_login__date__lt=timezone.now().date() - timedelta(days=7)
            ).count(),
            'at_risk': User.objects.filter(
                last_login__date__lt=timezone.now().date() - timedelta(days=30)
            ).count()
        }
        
        return segments
    
    def calculate_engagement_metrics(self, start_date, end_date):
        """计算用户参与度指标"""
        active_users = User.objects.filter(
            last_login__date__gte=start_date
        ).count()
        
        if active_users == 0:
            return {'dau_mau_ratio': 0, 'avg_session_duration': 0, 'feature_adoption_rate': 0}
        
        # DAU/MAU比率（简化计算）
        daily_active = User.objects.filter(
            last_login__date=timezone.now().date()
        ).count()
        dau_mau_ratio = (daily_active / active_users * 100) if active_users > 0 else 0
        
        # 平均会话时长（模拟数据）
        avg_session_duration = 15.5  # 分钟
        
        # 功能采用率
        users_with_likes = User.objects.filter(
            last_login__date__gte=start_date,
            sent_likes__isnull=False
        ).distinct().count()
        feature_adoption_rate = (users_with_likes / active_users * 100) if active_users > 0 else 0
        
        return {
            'dau_mau_ratio': round(dau_mau_ratio, 2),
            'avg_session_duration': avg_session_duration,
            'feature_adoption_rate': round(feature_adoption_rate, 2)
        }
