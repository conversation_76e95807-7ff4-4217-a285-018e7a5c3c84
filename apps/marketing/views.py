"""
市场推广工具视图
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from django.http import JsonResponse
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework import status
import uuid
import random
import string

from .models import (
    InvitationCode, InvitationRecord, PromotionCampaign, 
    CampaignParticipation, ShareLink, ShareRecord, 
    ReferralProgram, CouponCode, MarketingStatistics
)


class InvitationCodeAPIView(APIView):
    """邀请码API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户的邀请码"""
        user = request.user
        
        # 获取用户的邀请码
        invitation_codes = InvitationCode.objects.filter(
            inviter=user, 
            is_active=True
        ).order_by('-created_at')
        
        codes_data = []
        for code in invitation_codes:
            codes_data.append({
                'id': code.id,
                'code': code.code,
                'max_uses': code.max_uses,
                'current_uses': code.current_uses,
                'remaining_uses': code.max_uses - code.current_uses,
                'inviter_reward': float(code.inviter_reward),
                'invitee_reward': float(code.invitee_reward),
                'expires_at': code.expires_at.isoformat() if code.expires_at else None,
                'is_valid': code.is_valid,
                'created_at': code.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        # 获取邀请统计
        total_invitations = InvitationRecord.objects.filter(inviter=user).count()
        successful_invitations = InvitationRecord.objects.filter(
            inviter=user, 
            reward_status='completed'
        ).count()
        total_rewards = InvitationRecord.objects.filter(
            inviter=user, 
            reward_status='completed'
        ).aggregate(total=Sum('inviter_reward_amount'))['total'] or 0
        
        return Response({
            'code': 200,
            'data': {
                'invitation_codes': codes_data,
                'statistics': {
                    'total_invitations': total_invitations,
                    'successful_invitations': successful_invitations,
                    'total_rewards': float(total_rewards),
                    'success_rate': (successful_invitations / total_invitations * 100) if total_invitations > 0 else 0
                }
            }
        })
    
    def post(self, request):
        """生成新的邀请码"""
        user = request.user
        
        # 检查用户是否可以生成邀请码
        existing_codes = InvitationCode.objects.filter(
            inviter=user, 
            is_active=True
        ).count()
        
        if existing_codes >= 5:  # 限制每个用户最多5个有效邀请码
            return Response({
                'code': 400,
                'message': '您已达到邀请码数量上限'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 生成唯一邀请码
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            if not InvitationCode.objects.filter(code=code).exists():
                break
        
        # 创建邀请码
        invitation_code = InvitationCode.objects.create(
            code=code,
            inviter=user,
            max_uses=request.data.get('max_uses', 10),
            inviter_reward=request.data.get('inviter_reward', 10.0),
            invitee_reward=request.data.get('invitee_reward', 5.0),
            expires_at=timezone.now() + timedelta(days=30)  # 30天后过期
        )
        
        return Response({
            'code': 200,
            'message': '邀请码生成成功',
            'data': {
                'invitation_code': invitation_code.code,
                'expires_at': invitation_code.expires_at.isoformat()
            }
        })


class UseInvitationCodeAPIView(APIView):
    """使用邀请码API"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """使用邀请码"""
        user = request.user
        code = request.data.get('code', '').strip().upper()
        
        if not code:
            return Response({
                'code': 400,
                'message': '请输入邀请码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查用户是否已经使用过邀请码
        if InvitationRecord.objects.filter(invitee=user).exists():
            return Response({
                'code': 400,
                'message': '您已经使用过邀请码了'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            invitation_code = InvitationCode.objects.get(code=code)
        except InvitationCode.DoesNotExist:
            return Response({
                'code': 404,
                'message': '邀请码不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查邀请码是否有效
        if not invitation_code.is_valid:
            return Response({
                'code': 400,
                'message': '邀请码已失效或已用完'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查是否自己邀请自己
        if invitation_code.inviter == user:
            return Response({
                'code': 400,
                'message': '不能使用自己的邀请码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 创建邀请记录
        invitation_record = InvitationRecord.objects.create(
            invitation_code=invitation_code,
            inviter=invitation_code.inviter,
            invitee=user,
            inviter_reward_amount=invitation_code.inviter_reward,
            invitee_reward_amount=invitation_code.invitee_reward
        )
        
        # 更新邀请码使用次数
        invitation_code.current_uses += 1
        invitation_code.save()
        
        # 发放奖励（这里应该调用奖励发放服务）
        self.issue_rewards(invitation_record)
        
        return Response({
            'code': 200,
            'message': f'邀请码使用成功，您获得了 {invitation_code.invitee_reward} 元奖励',
            'data': {
                'reward_amount': float(invitation_code.invitee_reward),
                'inviter_username': invitation_code.inviter.username
            }
        })
    
    def issue_rewards(self, invitation_record):
        """发放奖励"""
        # 这里应该实现具体的奖励发放逻辑
        # 比如增加用户余额、发放VIP时长等
        invitation_record.reward_status = 'completed'
        invitation_record.reward_issued_at = timezone.now()
        invitation_record.save()


class ShareLinkAPIView(APIView):
    """分享链接API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户的分享链接"""
        user = request.user
        link_type = request.GET.get('type', 'app_download')
        
        # 获取或创建分享链接
        share_link, created = ShareLink.objects.get_or_create(
            user=user,
            link_type=link_type,
            defaults={
                'title': self.get_default_title(link_type),
                'description': self.get_default_description(link_type),
                'target_url': self.get_target_url(link_type, user),
                'image_url': self.get_default_image(link_type)
            }
        )
        
        # 生成分享URL
        share_url = f"https://yourdomain.com/share/{share_link.link_id}"
        
        return Response({
            'code': 200,
            'data': {
                'share_url': share_url,
                'title': share_link.title,
                'description': share_link.description,
                'image_url': share_link.image_url,
                'statistics': {
                    'view_count': share_link.view_count,
                    'click_count': share_link.click_count,
                    'conversion_count': share_link.conversion_count
                }
            }
        })
    
    def post(self, request):
        """创建自定义分享链接"""
        user = request.user
        
        share_link = ShareLink.objects.create(
            user=user,
            link_type='custom',
            title=request.data.get('title', ''),
            description=request.data.get('description', ''),
            image_url=request.data.get('image_url', ''),
            target_url=request.data.get('target_url', ''),
            target_data=request.data.get('target_data', {})
        )
        
        share_url = f"https://yourdomain.com/share/{share_link.link_id}"
        
        return Response({
            'code': 200,
            'message': '分享链接创建成功',
            'data': {
                'share_url': share_url,
                'link_id': str(share_link.link_id)
            }
        })
    
    def get_default_title(self, link_type):
        """获取默认标题"""
        titles = {
            'app_download': '相亲交友App - 找到你的另一半',
            'user_profile': '来看看我的资料吧',
            'moment': '分享我的精彩动态',
            'activity': '超值活动，不容错过'
        }
        return titles.get(link_type, '相亲交友App')
    
    def get_default_description(self, link_type):
        """获取默认描述"""
        descriptions = {
            'app_download': '专业的相亲交友平台，帮你找到真爱',
            'user_profile': '快来认识我吧，期待与你的相遇',
            'moment': '分享生活中的美好瞬间',
            'activity': '限时优惠活动，立即参与'
        }
        return descriptions.get(link_type, '相亲交友App')
    
    def get_target_url(self, link_type, user):
        """获取目标URL"""
        if link_type == 'app_download':
            return f"https://yourdomain.com/download?ref={user.id}"
        elif link_type == 'user_profile':
            return f"https://yourdomain.com/profile/{user.id}"
        else:
            return "https://yourdomain.com/"
    
    def get_default_image(self, link_type):
        """获取默认图片"""
        return "https://yourdomain.com/static/images/share_default.jpg"


class CouponCodeAPIView(APIView):
    """优惠券API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取可用优惠券"""
        user = request.user
        now = timezone.now()
        
        # 获取用户可用的优惠券
        available_coupons = CouponCode.objects.filter(
            is_active=True,
            start_time__lte=now,
            end_time__gte=now,
            current_uses__lt=models.F('max_uses')
        )
        
        # 过滤用户群体限制
        # 这里应该根据用户属性进行过滤
        
        coupon_data = []
        for coupon in available_coupons:
            coupon_data.append({
                'id': coupon.id,
                'code': coupon.code,
                'name': coupon.name,
                'description': coupon.description,
                'coupon_type': coupon.get_coupon_type_display(),
                'discount_value': float(coupon.discount_value),
                'min_amount': float(coupon.min_amount),
                'max_discount': float(coupon.max_discount) if coupon.max_discount else None,
                'end_time': coupon.end_time.strftime('%Y-%m-%d %H:%M'),
                'remaining_uses': coupon.max_uses - coupon.current_uses
            })
        
        return Response({
            'code': 200,
            'data': coupon_data
        })
    
    def post(self, request):
        """使用优惠券"""
        user = request.user
        coupon_code = request.data.get('coupon_code', '').strip()
        order_amount = float(request.data.get('order_amount', 0))
        
        if not coupon_code:
            return Response({
                'code': 400,
                'message': '请输入优惠券代码'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            coupon = CouponCode.objects.get(code=coupon_code)
        except CouponCode.DoesNotExist:
            return Response({
                'code': 404,
                'message': '优惠券不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查优惠券是否有效
        if not coupon.is_valid:
            return Response({
                'code': 400,
                'message': '优惠券已失效或已用完'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查最低消费金额
        if order_amount < coupon.min_amount:
            return Response({
                'code': 400,
                'message': f'订单金额不足，最低需要 {coupon.min_amount} 元'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 计算折扣金额
        discount_amount = self.calculate_discount(coupon, order_amount)
        final_amount = max(0, order_amount - discount_amount)
        
        return Response({
            'code': 200,
            'message': '优惠券验证成功',
            'data': {
                'coupon_name': coupon.name,
                'discount_amount': discount_amount,
                'final_amount': final_amount,
                'original_amount': order_amount
            }
        })
    
    def calculate_discount(self, coupon, order_amount):
        """计算折扣金额"""
        if coupon.coupon_type == 'discount_percent':
            discount = order_amount * (coupon.discount_value / 100)
            if coupon.max_discount:
                discount = min(discount, coupon.max_discount)
            return discount
        elif coupon.coupon_type == 'discount_amount':
            return min(coupon.discount_value, order_amount)
        else:
            return 0


class MarketingStatsAPIView(APIView):
    """营销统计API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取营销统计数据"""
        date_range = int(request.GET.get('range', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=date_range)
        
        # 邀请统计
        invitation_stats = {
            'total_codes': InvitationCode.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'total_invitations': InvitationRecord.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'successful_invitations': InvitationRecord.objects.filter(
                created_at__date__gte=start_date,
                reward_status='completed'
            ).count(),
            'total_rewards': float(InvitationRecord.objects.filter(
                created_at__date__gte=start_date,
                reward_status='completed'
            ).aggregate(total=Sum('inviter_reward_amount'))['total'] or 0)
        }
        
        # 分享统计
        share_stats = {
            'total_shares': ShareLink.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'total_views': ShareRecord.objects.filter(
                created_at__date__gte=start_date,
                action='view'
            ).count(),
            'total_clicks': ShareRecord.objects.filter(
                created_at__date__gte=start_date,
                action='click'
            ).count(),
            'total_conversions': ShareRecord.objects.filter(
                created_at__date__gte=start_date,
                action='register'
            ).count()
        }
        
        # 活动统计
        campaign_stats = {
            'active_campaigns': PromotionCampaign.objects.filter(
                status='active'
            ).count(),
            'total_participants': CampaignParticipation.objects.filter(
                created_at__date__gte=start_date
            ).count(),
            'total_completions': CampaignParticipation.objects.filter(
                created_at__date__gte=start_date,
                is_completed=True
            ).count()
        }
        
        # 优惠券统计
        coupon_stats = {
            'active_coupons': CouponCode.objects.filter(
                is_active=True,
                start_time__lte=timezone.now(),
                end_time__gte=timezone.now()
            ).count(),
            'total_coupon_uses': CouponCode.objects.filter(
                created_at__date__gte=start_date
            ).aggregate(total=Sum('current_uses'))['total'] or 0
        }
        
        return Response({
            'code': 200,
            'data': {
                'invitation_stats': invitation_stats,
                'share_stats': share_stats,
                'campaign_stats': campaign_stats,
                'coupon_stats': coupon_stats,
                'period': f'{start_date} 至 {end_date}'
            }
        })
