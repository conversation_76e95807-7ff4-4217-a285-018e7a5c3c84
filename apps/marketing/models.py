"""
市场推广工具模型
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class InvitationCode(models.Model):
    """邀请码"""
    code = models.CharField(max_length=20, unique=True, verbose_name='邀请码')
    inviter = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='invitation_codes',
        verbose_name='邀请人'
    )
    
    # 邀请码配置
    max_uses = models.IntegerField(default=1, verbose_name='最大使用次数')
    current_uses = models.IntegerField(default=0, verbose_name='当前使用次数')
    
    # 奖励配置
    inviter_reward = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='邀请人奖励')
    invitee_reward = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='被邀请人奖励')
    
    # 时间限制
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '邀请码'
        verbose_name_plural = '邀请码'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['inviter', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.inviter.username}"
    
    @property
    def is_valid(self):
        """检查邀请码是否有效"""
        if not self.is_active:
            return False
        
        if self.current_uses >= self.max_uses:
            return False
        
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        
        return True


class InvitationRecord(models.Model):
    """邀请记录"""
    invitation_code = models.ForeignKey(
        InvitationCode, 
        on_delete=models.CASCADE, 
        related_name='records',
        verbose_name='邀请码'
    )
    inviter = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='sent_invitations',
        verbose_name='邀请人'
    )
    invitee = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_invitations',
        verbose_name='被邀请人'
    )
    
    # 奖励信息
    inviter_reward_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='邀请人奖励金额')
    invitee_reward_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='被邀请人奖励金额')
    
    # 奖励状态
    REWARD_STATUS_CHOICES = [
        ('pending', '待发放'),
        ('processing', '处理中'),
        ('completed', '已发放'),
        ('failed', '发放失败'),
    ]
    reward_status = models.CharField(max_length=20, choices=REWARD_STATUS_CHOICES, default='pending', verbose_name='奖励状态')
    reward_issued_at = models.DateTimeField(null=True, blank=True, verbose_name='奖励发放时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '邀请记录'
        verbose_name_plural = '邀请记录'
        unique_together = ['invitation_code', 'invitee']
        indexes = [
            models.Index(fields=['inviter', 'created_at']),
            models.Index(fields=['invitee', 'created_at']),
            models.Index(fields=['reward_status']),
        ]
    
    def __str__(self):
        return f"{self.inviter.username} 邀请 {self.invitee.username}"


class PromotionCampaign(models.Model):
    """推广活动"""
    CAMPAIGN_TYPES = [
        ('registration', '注册活动'),
        ('recharge', '充值活动'),
        ('vip', 'VIP活动'),
        ('sharing', '分享活动'),
        ('check_in', '签到活动'),
        ('special', '特殊活动'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '进行中'),
        ('paused', '暂停'),
        ('ended', '已结束'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='活动名称')
    description = models.TextField(verbose_name='活动描述')
    campaign_type = models.CharField(max_length=20, choices=CAMPAIGN_TYPES, verbose_name='活动类型')
    
    # 活动配置
    config = models.JSONField(default=dict, verbose_name='活动配置')
    
    # 时间设置
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    
    # 参与条件
    target_users = models.JSONField(default=dict, verbose_name='目标用户条件')
    max_participants = models.IntegerField(null=True, blank=True, verbose_name='最大参与人数')
    
    # 奖励设置
    rewards = models.JSONField(default=list, verbose_name='奖励设置')
    
    # 状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    
    # 统计数据
    participant_count = models.IntegerField(default=0, verbose_name='参与人数')
    completion_count = models.IntegerField(default=0, verbose_name='完成人数')
    total_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='总成本')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '推广活动'
        verbose_name_plural = '推广活动'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    @property
    def is_active(self):
        """检查活动是否进行中"""
        now = timezone.now()
        return (
            self.status == 'active' and
            self.start_time <= now <= self.end_time
        )


class CampaignParticipation(models.Model):
    """活动参与记录"""
    campaign = models.ForeignKey(
        PromotionCampaign, 
        on_delete=models.CASCADE, 
        related_name='participations',
        verbose_name='活动'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 参与数据
    participation_data = models.JSONField(default=dict, verbose_name='参与数据')
    
    # 完成状态
    is_completed = models.BooleanField(default=False, verbose_name='是否完成')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    
    # 奖励信息
    rewards_received = models.JSONField(default=list, verbose_name='已获得奖励')
    total_reward_value = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='奖励总价值')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='参与时间')
    
    class Meta:
        verbose_name = '活动参与记录'
        verbose_name_plural = '活动参与记录'
        unique_together = ['campaign', 'user']
        indexes = [
            models.Index(fields=['campaign', 'is_completed']),
            models.Index(fields=['user', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.campaign.name}"


class ShareLink(models.Model):
    """分享链接"""
    LINK_TYPES = [
        ('app_download', '应用下载'),
        ('user_profile', '用户资料'),
        ('moment', '动态分享'),
        ('activity', '活动分享'),
        ('custom', '自定义'),
    ]
    
    link_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='链接ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='分享用户')
    
    link_type = models.CharField(max_length=20, choices=LINK_TYPES, verbose_name='链接类型')
    title = models.CharField(max_length=200, verbose_name='分享标题')
    description = models.TextField(blank=True, verbose_name='分享描述')
    image_url = models.URLField(blank=True, verbose_name='分享图片')
    
    # 目标信息
    target_url = models.URLField(verbose_name='目标链接')
    target_data = models.JSONField(default=dict, verbose_name='目标数据')
    
    # 统计数据
    view_count = models.IntegerField(default=0, verbose_name='查看次数')
    click_count = models.IntegerField(default=0, verbose_name='点击次数')
    conversion_count = models.IntegerField(default=0, verbose_name='转化次数')
    
    # 设置
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '分享链接'
        verbose_name_plural = '分享链接'
        indexes = [
            models.Index(fields=['link_id']),
            models.Index(fields=['user', 'link_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.user.username}"


class ShareRecord(models.Model):
    """分享记录"""
    share_link = models.ForeignKey(
        ShareLink, 
        on_delete=models.CASCADE, 
        related_name='records',
        verbose_name='分享链接'
    )
    
    # 访问信息
    visitor_ip = models.GenericIPAddressField(verbose_name='访问IP')
    user_agent = models.TextField(verbose_name='用户代理')
    referer = models.URLField(blank=True, verbose_name='来源页面')
    
    # 访问者信息
    visitor_user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='访问用户'
    )
    
    # 行为记录
    ACTION_CHOICES = [
        ('view', '查看'),
        ('click', '点击'),
        ('register', '注册'),
        ('download', '下载'),
        ('purchase', '购买'),
    ]
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name='行为类型')
    
    # 地理位置
    country = models.CharField(max_length=100, blank=True, verbose_name='国家')
    city = models.CharField(max_length=100, blank=True, verbose_name='城市')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='访问时间')
    
    class Meta:
        verbose_name = '分享记录'
        verbose_name_plural = '分享记录'
        indexes = [
            models.Index(fields=['share_link', 'action']),
            models.Index(fields=['visitor_user', 'created_at']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.share_link.title} - {self.get_action_display()}"


class ReferralProgram(models.Model):
    """推荐计划"""
    name = models.CharField(max_length=200, verbose_name='计划名称')
    description = models.TextField(verbose_name='计划描述')
    
    # 推荐奖励配置
    referrer_rewards = models.JSONField(default=list, verbose_name='推荐人奖励')
    referee_rewards = models.JSONField(default=list, verbose_name='被推荐人奖励')
    
    # 条件设置
    min_referee_actions = models.JSONField(default=dict, verbose_name='被推荐人最低行为要求')
    max_referrals_per_user = models.IntegerField(default=100, verbose_name='每用户最大推荐数')
    
    # 时间设置
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 统计
    total_referrals = models.IntegerField(default=0, verbose_name='总推荐数')
    total_rewards_paid = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='总奖励支出')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '推荐计划'
        verbose_name_plural = '推荐计划'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class MarketingStatistics(models.Model):
    """营销统计"""
    date = models.DateField(verbose_name='统计日期')
    
    # 邀请统计
    new_invitations = models.IntegerField(default=0, verbose_name='新增邀请')
    successful_invitations = models.IntegerField(default=0, verbose_name='成功邀请')
    invitation_conversion_rate = models.FloatField(default=0, verbose_name='邀请转化率')
    
    # 分享统计
    new_shares = models.IntegerField(default=0, verbose_name='新增分享')
    share_views = models.IntegerField(default=0, verbose_name='分享查看')
    share_clicks = models.IntegerField(default=0, verbose_name='分享点击')
    share_conversions = models.IntegerField(default=0, verbose_name='分享转化')
    
    # 活动统计
    active_campaigns = models.IntegerField(default=0, verbose_name='进行中活动')
    campaign_participants = models.IntegerField(default=0, verbose_name='活动参与人数')
    campaign_completions = models.IntegerField(default=0, verbose_name='活动完成人数')
    
    # 成本统计
    total_marketing_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='总营销成本')
    invitation_rewards_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='邀请奖励成本')
    campaign_rewards_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='活动奖励成本')
    
    # 效果统计
    new_users_from_marketing = models.IntegerField(default=0, verbose_name='营销带来新用户')
    revenue_from_marketing = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name='营销带来收入')
    roi = models.FloatField(default=0, verbose_name='投资回报率')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '营销统计'
        verbose_name_plural = '营销统计'
        unique_together = ['date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.date} - 营销统计"


class CouponCode(models.Model):
    """优惠券"""
    COUPON_TYPES = [
        ('discount_percent', '百分比折扣'),
        ('discount_amount', '固定金额折扣'),
        ('free_vip', '免费VIP'),
        ('free_privilege', '免费特权'),
    ]

    code = models.CharField(max_length=50, unique=True, verbose_name='优惠券代码')
    name = models.CharField(max_length=200, verbose_name='优惠券名称')
    description = models.TextField(blank=True, verbose_name='优惠券描述')

    coupon_type = models.CharField(max_length=20, choices=COUPON_TYPES, verbose_name='优惠券类型')

    # 折扣配置
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='折扣值')
    min_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='最低消费金额')
    max_discount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='最大折扣金额')

    # 使用限制
    max_uses = models.IntegerField(default=1, verbose_name='最大使用次数')
    max_uses_per_user = models.IntegerField(default=1, verbose_name='每用户最大使用次数')
    current_uses = models.IntegerField(default=0, verbose_name='当前使用次数')

    # 适用范围
    applicable_products = models.JSONField(default=list, verbose_name='适用产品')
    applicable_user_groups = models.JSONField(default=list, verbose_name='适用用户群体')

    # 时间设置
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '优惠券'
        verbose_name_plural = '优惠券'
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active', 'start_time', 'end_time']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def is_valid(self):
        """检查优惠券是否有效"""
        now = timezone.now()
        return (
            self.is_active and
            self.start_time <= now <= self.end_time and
            self.current_uses < self.max_uses
        )
