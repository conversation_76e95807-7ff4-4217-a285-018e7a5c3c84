from django.contrib import admin
from django.utils.html import format_html
from .models import (Match, RecommendationLog, MatchingAlgorithmConfig, 
                    UserCompatibility, DailyRecommendation, MatchingStatistics)


@admin.register(Match)
class MatchAdmin(admin.ModelAdmin):
    list_display = ['id', 'user1', 'user2', 'status_display', 'match_score', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user1__nickname', 'user2__nickname']
    readonly_fields = ['created_at', 'matched_at']
    list_per_page = 25  # 分页优化
    list_max_show_all = 100

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user1', 'user2')
    
    def status_display(self, obj):
        colors = {
            'pending': 'orange',
            'matched': 'green', 
            'unmatched': 'red',
            'expired': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'


@admin.register(RecommendationLog)
class RecommendationLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'recommended_user', 'algorithm_version', 
                   'recommendation_score', 'user_action', 'created_at']
    list_filter = ['algorithm_version', 'user_action', 'created_at']
    search_fields = ['user__nickname', 'recommended_user__nickname']
    readonly_fields = ['created_at']


@admin.register(MatchingAlgorithmConfig)
class MatchingAlgorithmConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'version', 'is_active', 'min_match_score', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'version']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserCompatibility)
class UserCompatibilityAdmin(admin.ModelAdmin):
    list_display = ['user1', 'user2', 'total_score', 'age_score', 
                   'location_score', 'created_at']
    search_fields = ['user1__nickname', 'user2__nickname']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(DailyRecommendation)
class DailyRecommendationAdmin(admin.ModelAdmin):
    list_display = ['user', 'date', 'total_count', 'viewed_count', 
                   'liked_count', 'matched_count']
    list_filter = ['date']
    search_fields = ['user__nickname']
    readonly_fields = ['created_at']


@admin.register(MatchingStatistics)
class MatchingStatisticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_matches', 'total_likes_sent', 
                   'total_likes_received', 'like_success_rate']
    search_fields = ['user__nickname']
    readonly_fields = ['updated_at']
