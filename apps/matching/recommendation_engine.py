"""
智能推荐引擎
基于用户行为、偏好和机器学习的推荐算法
"""

import math
import numpy as np
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg, F
from collections import defaultdict

from apps.users.models import User, UserAction, UserPreference
from .models import Match, RecommendationLog, UserCompatibility


class SmartRecommendationEngine:
    """智能推荐引擎"""
    
    def __init__(self):
        self.feature_weights = {
            'age_compatibility': 0.20,
            'location_proximity': 0.25,
            'education_match': 0.15,
            'income_compatibility': 0.10,
            'height_preference': 0.05,
            'activity_level': 0.15,
            'behavioral_similarity': 0.10
        }
    
    def get_recommendations(self, user, limit=10, page=1):
        """获取用户推荐列表"""
        # 获取候选用户
        candidates = self._get_candidate_users(user)
        
        # 计算推荐得分
        scored_candidates = []
        for candidate in candidates:
            score = self._calculate_comprehensive_score(user, candidate)
            reasons = self._generate_match_reasons(user, candidate, score)
            
            scored_candidates.append({
                'user': candidate,
                'score': score,
                'reasons': reasons,
                'compatibility_factors': self._get_compatibility_factors(user, candidate)
            })
        
        # 排序并应用多样性算法
        scored_candidates = self._apply_diversity_algorithm(scored_candidates)
        
        # 分页
        start = (page - 1) * limit
        end = start + limit
        
        # 记录推荐日志
        self._log_recommendations(user, scored_candidates[start:end])
        
        return scored_candidates[start:end]
    
    def _get_candidate_users(self, user):
        """获取候选用户"""
        # 获取用户偏好
        try:
            preference = user.preference
        except:
            preference = None
        
        # 排除已操作过的用户
        acted_user_ids = UserAction.objects.filter(user=user).values_list('target_user_id', flat=True)
        
        # 排除已匹配的用户
        matched_user_ids = Match.objects.filter(
            Q(user1=user) | Q(user2=user),
            status__in=['matched', 'pending']
        ).values_list('user1_id', 'user2_id')
        
        exclude_ids = set(acted_user_ids)
        for user1_id, user2_id in matched_user_ids:
            exclude_ids.add(user1_id if user1_id != user.id else user2_id)
        
        # 基础查询
        queryset = User.objects.filter(
            status=1,
            user_type='normal'  # 只推荐普通用户
        ).exclude(
            id=user.id
        ).exclude(
            id__in=exclude_ids
        )
        
        # 应用偏好筛选
        if preference:
            if preference.preferred_gender:
                queryset = queryset.filter(gender=preference.preferred_gender)
            
            if preference.min_age and preference.max_age:
                queryset = queryset.filter(
                    age__gte=preference.min_age,
                    age__lte=preference.max_age
                )
            
            if preference.preferred_education:
                queryset = queryset.filter(education=preference.preferred_education)
            
            if preference.preferred_income:
                queryset = queryset.filter(income=preference.preferred_income)
            
            if preference.only_verified:
                queryset = queryset.filter(is_verified=True)
            
            if preference.only_with_photos:
                queryset = queryset.exclude(photos__isnull=True).exclude(photos__exact='[]')
        
        # 优先返回活跃用户
        return queryset.order_by('-last_active', '-created_at')[:100]
    
    def _calculate_comprehensive_score(self, user, candidate):
        """计算综合推荐得分"""
        scores = {}
        
        # 年龄兼容性
        scores['age_compatibility'] = self._calculate_age_compatibility(user, candidate)
        
        # 位置接近度
        scores['location_proximity'] = self._calculate_location_proximity(user, candidate)
        
        # 学历匹配度
        scores['education_match'] = self._calculate_education_match(user, candidate)
        
        # 收入兼容性
        scores['income_compatibility'] = self._calculate_income_compatibility(user, candidate)
        
        # 身高偏好
        scores['height_preference'] = self._calculate_height_preference(user, candidate)
        
        # 活跃度
        scores['activity_level'] = self._calculate_activity_level(candidate)
        
        # 行为相似性
        scores['behavioral_similarity'] = self._calculate_behavioral_similarity(user, candidate)
        
        # 计算加权总分
        total_score = sum(
            scores[factor] * self.feature_weights[factor]
            for factor in scores
        )
        
        # 应用个性化调整
        total_score = self._apply_personalization(user, candidate, total_score)
        
        return round(total_score, 3)
    
    def _calculate_age_compatibility(self, user, candidate):
        """计算年龄兼容性"""
        if not user.age or not candidate.age:
            return 0.5
        
        age_diff = abs(user.age - candidate.age)
        
        # 使用高斯函数计算兼容性
        optimal_diff = 3  # 最佳年龄差
        sigma = 5  # 标准差
        
        score = math.exp(-((age_diff - optimal_diff) ** 2) / (2 * sigma ** 2))
        return min(1.0, score)
    
    def _calculate_location_proximity(self, user, candidate):
        """计算位置接近度"""
        if not all([user.latitude, user.longitude, candidate.latitude, candidate.longitude]):
            return 0.3  # 默认中等分数
        
        distance = self._calculate_distance(
            user.latitude, user.longitude,
            candidate.latitude, candidate.longitude
        )
        
        # 距离越近分数越高
        if distance <= 5:
            return 1.0
        elif distance <= 20:
            return 0.8
        elif distance <= 50:
            return 0.6
        elif distance <= 100:
            return 0.4
        else:
            return 0.2
    
    def _calculate_education_match(self, user, candidate):
        """计算学历匹配度"""
        if not user.education or not candidate.education:
            return 0.5
        
        edu_diff = abs(user.education - candidate.education)
        
        if edu_diff == 0:
            return 1.0
        elif edu_diff == 1:
            return 0.8
        elif edu_diff == 2:
            return 0.6
        else:
            return 0.4
    
    def _calculate_income_compatibility(self, user, candidate):
        """计算收入兼容性"""
        if not user.income or not candidate.income:
            return 0.5
        
        income_diff = abs(user.income - candidate.income)
        
        if income_diff <= 1:
            return 1.0
        elif income_diff <= 2:
            return 0.8
        elif income_diff <= 3:
            return 0.6
        else:
            return 0.4
    
    def _calculate_height_preference(self, user, candidate):
        """计算身高偏好匹配"""
        if not user.height or not candidate.height:
            return 0.5
        
        # 获取用户偏好
        try:
            preference = user.preference
            if preference.min_height and preference.max_height:
                if preference.min_height <= candidate.height <= preference.max_height:
                    return 1.0
                else:
                    return 0.3
        except:
            pass
        
        # 默认身高匹配逻辑
        height_diff = abs(user.height - candidate.height)
        return max(0.3, 1 - height_diff / 30)
    
    def _calculate_activity_level(self, candidate):
        """计算用户活跃度"""
        if not candidate.last_active:
            return 0.3
        
        days_since_active = (timezone.now() - candidate.last_active).days
        
        if days_since_active <= 1:
            return 1.0
        elif days_since_active <= 3:
            return 0.9
        elif days_since_active <= 7:
            return 0.7
        elif days_since_active <= 30:
            return 0.5
        else:
            return 0.2
    
    def _calculate_behavioral_similarity(self, user, candidate):
        """计算行为相似性"""
        # 获取用户行为数据
        user_actions = UserAction.objects.filter(user=user)
        candidate_actions = UserAction.objects.filter(user=candidate)
        
        if not user_actions.exists() or not candidate_actions.exists():
            return 0.5
        
        # 计算喜欢/不喜欢的比例相似性
        user_like_ratio = user_actions.filter(action_type='like').count() / user_actions.count()
        candidate_like_ratio = candidate_actions.filter(action_type='like').count() / candidate_actions.count()
        
        ratio_similarity = 1 - abs(user_like_ratio - candidate_like_ratio)
        
        return ratio_similarity
    
    def _apply_personalization(self, user, candidate, base_score):
        """应用个性化调整"""
        # VIP用户加权
        if candidate.is_vip:
            base_score *= 1.1
        
        # 认证用户加权
        if candidate.is_verified:
            base_score *= 1.05
        
        # 完整资料加权
        if candidate.bio and candidate.photos:
            base_score *= 1.03
        
        return min(1.0, base_score)
    
    def _apply_diversity_algorithm(self, scored_candidates):
        """应用多样性算法，避免推荐过于相似的用户"""
        if len(scored_candidates) <= 10:
            return sorted(scored_candidates, key=lambda x: x['score'], reverse=True)
        
        # 按得分排序
        scored_candidates.sort(key=lambda x: x['score'], reverse=True)
        
        # 选择多样化的推荐
        diverse_recommendations = []
        used_features = set()
        
        for candidate in scored_candidates:
            user = candidate['user']
            features = (user.age // 5, user.education, user.location)  # 特征组合
            
            if features not in used_features or len(diverse_recommendations) < 5:
                diverse_recommendations.append(candidate)
                used_features.add(features)
            
            if len(diverse_recommendations) >= 20:  # 限制数量
                break
        
        return diverse_recommendations
    
    def _generate_match_reasons(self, user, candidate, score):
        """生成匹配原因"""
        reasons = []
        
        if user.age and candidate.age and abs(user.age - candidate.age) <= 5:
            reasons.append('年龄相近')
        
        if user.education and candidate.education and user.education == candidate.education:
            reasons.append('学历相同')
        
        if candidate.is_verified:
            reasons.append('已认证用户')
        
        if candidate.photos:
            reasons.append('有照片')
        
        if score > 0.8:
            reasons.append('高度匹配')
        elif score > 0.6:
            reasons.append('较好匹配')
        
        return reasons
    
    def _get_compatibility_factors(self, user, candidate):
        """获取兼容性因子详情"""
        return {
            'age_diff': abs(user.age - candidate.age) if user.age and candidate.age else None,
            'education_match': user.education == candidate.education if user.education and candidate.education else None,
            'location_distance': self._calculate_distance(
                user.latitude, user.longitude,
                candidate.latitude, candidate.longitude
            ) if all([user.latitude, user.longitude, candidate.latitude, candidate.longitude]) else None,
            'activity_days': (timezone.now() - candidate.last_active).days if candidate.last_active else None
        }
    
    def _calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # 地球半径（公里）
        
        return c * r
    
    def _log_recommendations(self, user, recommendations):
        """记录推荐日志"""
        for rec in recommendations:
            RecommendationLog.objects.create(
                user=user,
                recommended_user=rec['user'],
                algorithm_version='smart_v2.0',
                match_score=rec['score'],
                recommendation_reasons=rec['reasons']
            )


# 全局推荐引擎实例
recommendation_engine = SmartRecommendationEngine()
