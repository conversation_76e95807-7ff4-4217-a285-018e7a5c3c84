from django.db import models
from django.utils import timezone
from apps.users.models import User


class Like(models.Model):
    """用户点赞模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_likes', verbose_name='点赞用户')
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_likes', verbose_name='被点赞用户')
    is_mutual = models.BooleanField(default=False, verbose_name='是否互相喜欢')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '用户点赞'
        verbose_name_plural = '用户点赞'
        unique_together = ['user', 'target_user']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['target_user', 'created_at']),
            models.Index(fields=['is_mutual']),
        ]

    def __str__(self):
        return f"{self.user.username} -> {self.target_user.username}"


class Match(models.Model):
    """匹配记录"""
    STATUS_CHOICES = [
        ('pending', '待确认'),
        ('matched', '已匹配'),
        ('unmatched', '已取消匹配'),
        ('expired', '已过期'),
    ]
    
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matches_as_user1', verbose_name='用户1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matches_as_user2', verbose_name='用户2')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    
    # 匹配得分
    match_score = models.FloatField(default=0.0, verbose_name='匹配得分')
    
    # 匹配原因
    match_reasons = models.JSONField(default=list, verbose_name='匹配原因')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    matched_at = models.DateTimeField(null=True, blank=True, verbose_name='匹配时间')
    expired_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    class Meta:
        db_table = 'matches'
        verbose_name = '匹配记录'
        verbose_name_plural = '匹配记录'
        unique_together = ['user1', 'user2']
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.user1.nickname} ❤️ {self.user2.nickname}'
    
    def get_other_user(self, user):
        """获取匹配的另一个用户"""
        return self.user2 if self.user1 == user else self.user1


class RecommendationLog(models.Model):
    """推荐日志"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendation_logs', verbose_name='用户')
    recommended_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommended_logs', 
                                       verbose_name='被推荐用户')
    
    # 推荐算法相关
    algorithm_version = models.CharField(max_length=50, default='v1.0', verbose_name='算法版本')
    recommendation_score = models.FloatField(verbose_name='推荐得分')
    factors = models.JSONField(default=dict, verbose_name='推荐因子')
    
    # 用户反馈
    user_action = models.CharField(max_length=20, blank=True, verbose_name='用户行为')
    feedback_score = models.FloatField(null=True, blank=True, verbose_name='反馈得分')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'recommendation_logs'
        verbose_name = '推荐日志'
        verbose_name_plural = '推荐日志'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'推荐 {self.recommended_user.nickname} 给 {self.user.nickname}'


class MatchingAlgorithmConfig(models.Model):
    """匹配算法配置"""
    name = models.CharField(max_length=100, unique=True, verbose_name='配置名称')
    version = models.CharField(max_length=20, verbose_name='版本')
    
    # 权重配置
    age_weight = models.FloatField(default=0.2, verbose_name='年龄权重')
    location_weight = models.FloatField(default=0.3, verbose_name='位置权重')
    education_weight = models.FloatField(default=0.15, verbose_name='学历权重')
    income_weight = models.FloatField(default=0.1, verbose_name='收入权重')
    height_weight = models.FloatField(default=0.05, verbose_name='身高权重')
    interest_weight = models.FloatField(default=0.2, verbose_name='兴趣权重')
    
    # 其他参数
    max_distance = models.IntegerField(default=50, verbose_name='最大距离(km)')
    min_match_score = models.FloatField(default=0.6, verbose_name='最小匹配得分')
    
    is_active = models.BooleanField(default=False, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'matching_algorithm_configs'
        verbose_name = '匹配算法配置'
        verbose_name_plural = '匹配算法配置'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.name} v{self.version}'


class UserCompatibility(models.Model):
    """用户兼容性分析"""
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='compatibility_as_user1', 
                             verbose_name='用户1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='compatibility_as_user2', 
                             verbose_name='用户2')
    
    # 各维度得分
    age_score = models.FloatField(default=0.0, verbose_name='年龄匹配得分')
    location_score = models.FloatField(default=0.0, verbose_name='位置匹配得分')
    education_score = models.FloatField(default=0.0, verbose_name='学历匹配得分')
    income_score = models.FloatField(default=0.0, verbose_name='收入匹配得分')
    height_score = models.FloatField(default=0.0, verbose_name='身高匹配得分')
    interest_score = models.FloatField(default=0.0, verbose_name='兴趣匹配得分')
    
    # 综合得分
    total_score = models.FloatField(default=0.0, verbose_name='综合得分')
    
    # 分析详情
    analysis_details = models.JSONField(default=dict, verbose_name='分析详情')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'user_compatibility'
        verbose_name = '用户兼容性'
        verbose_name_plural = '用户兼容性'
        unique_together = ['user1', 'user2']
        ordering = ['-total_score']
    
    def __str__(self):
        return f'{self.user1.nickname} & {self.user2.nickname} 兼容性: {self.total_score:.2f}'


class DailyRecommendation(models.Model):
    """每日推荐"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='daily_recommendations', 
                           verbose_name='用户')
    recommended_users = models.JSONField(default=list, verbose_name='推荐用户ID列表')
    date = models.DateField(verbose_name='日期')
    
    # 推荐统计
    total_count = models.IntegerField(default=0, verbose_name='推荐总数')
    viewed_count = models.IntegerField(default=0, verbose_name='查看数')
    liked_count = models.IntegerField(default=0, verbose_name='喜欢数')
    matched_count = models.IntegerField(default=0, verbose_name='匹配数')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'daily_recommendations'
        verbose_name = '每日推荐'
        verbose_name_plural = '每日推荐'
        unique_together = ['user', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.date} 推荐'


class MatchingStatistics(models.Model):
    """匹配统计"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='matching_stats', 
                               verbose_name='用户')
    
    # 行为统计
    total_likes_sent = models.IntegerField(default=0, verbose_name='发出喜欢总数')
    total_likes_received = models.IntegerField(default=0, verbose_name='收到喜欢总数')
    total_super_likes_sent = models.IntegerField(default=0, verbose_name='发出超级喜欢总数')
    total_super_likes_received = models.IntegerField(default=0, verbose_name='收到超级喜欢总数')
    total_passes = models.IntegerField(default=0, verbose_name='跳过总数')
    total_matches = models.IntegerField(default=0, verbose_name='匹配总数')
    total_profile_views = models.IntegerField(default=0, verbose_name='资料查看总数')
    
    # 今日统计
    today_likes_sent = models.IntegerField(default=0, verbose_name='今日发出喜欢')
    today_likes_received = models.IntegerField(default=0, verbose_name='今日收到喜欢')
    today_super_likes_sent = models.IntegerField(default=0, verbose_name='今日发出超级喜欢')
    today_matches = models.IntegerField(default=0, verbose_name='今日匹配')
    today_profile_views = models.IntegerField(default=0, verbose_name='今日资料查看')
    
    # 成功率统计
    like_success_rate = models.FloatField(default=0.0, verbose_name='喜欢成功率')
    super_like_success_rate = models.FloatField(default=0.0, verbose_name='超级喜欢成功率')
    
    # 时间统计
    last_like_time = models.DateTimeField(null=True, blank=True, verbose_name='最后喜欢时间')
    last_match_time = models.DateTimeField(null=True, blank=True, verbose_name='最后匹配时间')
    
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'matching_statistics'
        verbose_name = '匹配统计'
        verbose_name_plural = '匹配统计'
    
    def __str__(self):
        return f'{self.user.nickname} 匹配统计'
    
    def reset_daily_stats(self):
        """重置每日统计"""
        self.today_likes_sent = 0
        self.today_likes_received = 0
        self.today_super_likes_sent = 0
        self.today_matches = 0
        self.today_profile_views = 0
        self.save()
    
    def calculate_success_rates(self):
        """计算成功率"""
        if self.total_likes_sent > 0:
            self.like_success_rate = self.total_matches / self.total_likes_sent
        if self.total_super_likes_sent > 0:
            self.super_like_success_rate = self.total_matches / self.total_super_likes_sent
        self.save()


class RecommendationConfig(models.Model):
    """推荐算法配置模型"""
    ALGORITHM_TYPE_CHOICES = [
        ('latest', '最新用户'),
        ('activity', '活跃用户'),
        ('nearby', '附近用户'),
        ('match', '高匹配度'),
        ('video', '视频用户'),
    ]

    # 算法类型
    algorithm_type = models.CharField(max_length=20, choices=ALGORITHM_TYPE_CHOICES, verbose_name='算法类型')

    # 配置参数
    config_params = models.JSONField(default=dict, verbose_name='算法参数配置')
    weight = models.DecimalField(max_digits=3, decimal_places=2, default=1.0, verbose_name='权重')

    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    # 描述
    name = models.CharField(max_length=100, blank=True, verbose_name='配置名称')
    description = models.TextField(blank=True, verbose_name='配置描述')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'recommendation_configs'
        verbose_name = '推荐算法配置'
        verbose_name_plural = '推荐算法配置'
        ordering = ['algorithm_type']

    def __str__(self):
        return f"{self.get_algorithm_type_display()} - {self.name}"


class UserRecommendation(models.Model):
    """用户推荐记录模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations', verbose_name='用户')
    recommended_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommended_to', verbose_name='被推荐用户')

    # 推荐信息
    algorithm_type = models.CharField(max_length=20, verbose_name='推荐算法类型')
    score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0, verbose_name='推荐得分')
    reason = models.CharField(max_length=200, blank=True, verbose_name='推荐理由')

    # 用户行为
    is_viewed = models.BooleanField(default=False, verbose_name='是否已查看')
    is_liked = models.BooleanField(default=False, verbose_name='是否已点赞')
    is_passed = models.BooleanField(default=False, verbose_name='是否已跳过')

    # 时间信息
    recommended_at = models.DateTimeField(auto_now_add=True, verbose_name='推荐时间')
    viewed_at = models.DateTimeField(null=True, blank=True, verbose_name='查看时间')
    action_at = models.DateTimeField(null=True, blank=True, verbose_name='操作时间')

    class Meta:
        db_table = 'user_recommendations'
        verbose_name = '用户推荐记录'
        verbose_name_plural = '用户推荐记录'
        ordering = ['-recommended_at']

    def __str__(self):
        return f"{self.user.nickname} <- {self.recommended_user.nickname} ({self.algorithm_type})"
