from rest_framework import serializers
from .models import (
    Match, RecommendationLog, MatchingAlgorithmConfig, 
    UserCompatibility, DailyRecommendation, MatchingStatistics
)
from apps.users.serializers import UserProfileSerializer


class MatchSerializer(serializers.ModelSerializer):
    """匹配记录序列化器"""
    user1 = UserProfileSerializer(read_only=True)
    user2 = UserProfileSerializer(read_only=True)
    other_user = serializers.SerializerMethodField()
    
    class Meta:
        model = Match
        fields = [
            'id', 'user1', 'user2', 'other_user', 'status', 'match_score',
            'match_reasons', 'created_at', 'matched_at'
        ]
        read_only_fields = ['id', 'created_at', 'matched_at']
    
    def get_other_user(self, obj):
        """获取匹配的另一个用户"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_user(request.user)
            return UserProfileSerializer(other_user, context=self.context).data
        return None


class RecommendationSerializer(serializers.ModelSerializer):
    """推荐用户序列化器"""
    user = UserProfileSerializer(read_only=True)
    match_score = serializers.FloatField()
    match_reasons = serializers.ListField()
    
    class Meta:
        model = None  # 这是一个虚拟序列化器
        fields = ['user', 'match_score', 'match_reasons']


class UserActionSerializer(serializers.Serializer):
    """用户行为序列化器"""
    ACTION_CHOICES = [
        ('like', '喜欢'),
        ('pass', '跳过'),
        ('super_like', '超级喜欢'),
    ]
    
    target_user_id = serializers.IntegerField()
    action = serializers.ChoiceField(choices=ACTION_CHOICES)
    message = serializers.CharField(max_length=200, required=False, allow_blank=True)
    
    def validate_target_user_id(self, value):
        from apps.users.models import User
        try:
            user = User.objects.get(id=value, status=1)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("目标用户不存在")


class MatchingStatisticsSerializer(serializers.ModelSerializer):
    """匹配统计序列化器"""
    
    class Meta:
        model = MatchingStatistics
        fields = [
            'total_likes_sent', 'total_likes_received', 'total_super_likes_sent',
            'total_super_likes_received', 'total_passes', 'total_matches',
            'total_profile_views', 'today_likes_sent', 'today_likes_received',
            'today_super_likes_sent', 'today_matches', 'today_profile_views',
            'like_success_rate', 'super_like_success_rate'
        ]


class RecommendationLogSerializer(serializers.ModelSerializer):
    """推荐日志序列化器"""
    recommended_user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = RecommendationLog
        fields = [
            'id', 'recommended_user', 'algorithm_version', 'recommendation_score',
            'factors', 'user_action', 'feedback_score', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserCompatibilitySerializer(serializers.ModelSerializer):
    """用户兼容性序列化器"""
    user1 = UserProfileSerializer(read_only=True)
    user2 = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = UserCompatibility
        fields = [
            'id', 'user1', 'user2', 'age_score', 'location_score',
            'education_score', 'income_score', 'height_score', 'interest_score',
            'total_score', 'analysis_details', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class DailyRecommendationSerializer(serializers.ModelSerializer):
    """每日推荐序列化器"""
    
    class Meta:
        model = DailyRecommendation
        fields = [
            'id', 'date', 'total_count', 'viewed_count', 'liked_count',
            'matched_count', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class MatchingAlgorithmConfigSerializer(serializers.ModelSerializer):
    """匹配算法配置序列化器"""
    
    class Meta:
        model = MatchingAlgorithmConfig
        fields = [
            'id', 'name', 'version', 'age_weight', 'location_weight',
            'education_weight', 'income_weight', 'height_weight', 'interest_weight',
            'max_distance', 'min_match_score', 'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class FilterSettingsSerializer(serializers.Serializer):
    """筛选设置序列化器"""
    min_age = serializers.IntegerField(min_value=18, max_value=100, required=False)
    max_age = serializers.IntegerField(min_value=18, max_value=100, required=False)
    gender = serializers.ChoiceField(choices=[(1, '男'), (2, '女')], required=False)
    location = serializers.CharField(max_length=100, required=False, allow_blank=True)
    max_distance = serializers.IntegerField(min_value=1, max_value=1000, required=False)
    min_education = serializers.IntegerField(min_value=1, max_value=5, required=False)
    min_income = serializers.IntegerField(min_value=1, max_value=6, required=False)
    min_height = serializers.IntegerField(min_value=100, max_value=250, required=False)
    max_height = serializers.IntegerField(min_value=100, max_value=250, required=False)
    only_verified = serializers.BooleanField(required=False)
    only_with_photos = serializers.BooleanField(required=False)
    
    def validate(self, data):
        if 'min_age' in data and 'max_age' in data:
            if data['min_age'] > data['max_age']:
                raise serializers.ValidationError("最小年龄不能大于最大年龄")
        
        if 'min_height' in data and 'max_height' in data:
            if data['min_height'] > data['max_height']:
                raise serializers.ValidationError("最小身高不能大于最大身高")
        
        return data
