from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.decorators import action as drf_action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from django.db.models import Q, F
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
import random
import math

from .models import (
    Match, RecommendationLog, MatchingAlgorithmConfig,
    UserCompatibility, DailyRecommendation, MatchingStatistics
)
from .recommendation_engine import recommendation_engine
from .analytics import analytics
from utils.performance_monitor import monitor_performance
from .serializers import (
    MatchSerializer, RecommendationSerializer, UserActionSerializer,
    MatchingStatisticsSerializer, RecommendationLogSerializer,
    UserCompatibilitySerializer, DailyRecommendationSerializer,
    FilterSettingsSerializer
)
from apps.users.models import User, UserAction, UserPreference
from apps.users.serializers import UserProfileSerializer


class MatchingViewSet(ReadOnlyModelViewSet):
    """匹配系统视图集"""
    permission_classes = [permissions.IsAuthenticated]
    
    @drf_action(detail=False, methods=['get'])
    @monitor_performance
    def recommendations(self, request):
        """获取推荐用户"""
        page = int(request.GET.get('page', 1))
        limit = min(int(request.GET.get('limit', 10)), 50)

        try:
            # 使用智能推荐引擎
            recommendations = recommendation_engine.get_recommendations(
                user=request.user,
                limit=limit,
                page=page
            )

            # 构建响应数据
            users_data = []
            for rec in recommendations:
                user_data = UserProfileSerializer(rec['user'], context={'request': request}).data
                user_data.update({
                    'match_score': rec['score'],
                    'match_reasons': rec['reasons'],
                    'compatibility_factors': rec['compatibility_factors']
                })
                users_data.append(user_data)

            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'users': users_data,
                    'has_more': len(recommendations) == limit,
                    'algorithm_version': 'smart_v2.0'
                }
            })

        except Exception as e:
            # 降级到原始算法
            print(f"智能推荐引擎错误，降级到原始算法: {str(e)}")
            recommended_users = self.get_recommendations_for_user(request.user, page, limit)
            serializer = RecommendationSerializer(recommended_users, many=True, context={'request': request})

            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'users': serializer.data,
                    'has_more': len(recommended_users) == limit,
                    'algorithm_version': 'fallback_v1.0'
                }
            })
    
    @drf_action(detail=False, methods=['post'])
    def user_action(self, request):
        """用户行为（喜欢/跳过/超级喜欢）"""
        serializer = UserActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        target_user_id = serializer.validated_data['target_user_id']
        action_type = serializer.validated_data['action']
        message = serializer.validated_data.get('message', '')
        
        try:
            target_user = User.objects.get(id=target_user_id, status=1)
        except User.DoesNotExist:
            return Response({
                'code': 404,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查是否已经有行为记录
        existing_action = UserAction.objects.filter(
            user=request.user,
            target_user=target_user,
            action_type__in=['like', 'super_like']
        ).first()
        
        if existing_action:
            return Response({
                'code': 400,
                'message': '您已经对该用户执行过操作'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 记录用户行为
        user_action = UserAction.objects.create(
            user=request.user,
            target_user=target_user,
            action_type=action_type,
            message=message
        )
        
        # 检查是否匹配
        is_match = False
        match_id = None
        
        if action_type in ['like', 'super_like']:
            # 检查对方是否也喜欢了当前用户
            reverse_action = UserAction.objects.filter(
                user=target_user,
                target_user=request.user,
                action_type__in=['like', 'super_like']
            ).first()
            
            if reverse_action:
                # 创建匹配记录
                match = Match.objects.create(
                    user1=request.user,
                    user2=target_user,
                    status='matched',
                    match_score=self.calculate_match_score(request.user, target_user),
                    matched_at=timezone.now()
                )
                is_match = True
                match_id = match.id
                
                # 更新统计
                self.update_matching_stats(request.user, action_type, is_match)
                self.update_matching_stats(target_user, 'received', is_match)
        
        return Response({
            'code': 200,
            'message': '操作成功',
            'data': {
                'is_match': is_match,
                'match_id': match_id
            }
        })
    
    @drf_action(detail=False, methods=['get'])
    def matches(self, request):
        """获取匹配列表"""
        matches = Match.objects.filter(
            Q(user1=request.user) | Q(user2=request.user),
            status='matched'
        ).order_by('-matched_at')
        
        serializer = MatchSerializer(matches, many=True, context={'request': request})
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'matches': serializer.data
            }
        })
    
    @drf_action(detail=False, methods=['get'])
    def stats(self, request):
        """获取匹配统计"""
        stats, created = MatchingStatistics.objects.get_or_create(user=request.user)
        serializer = MatchingStatisticsSerializer(stats)
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': serializer.data
        })
    
    @drf_action(detail=False, methods=['post'])
    def filter(self, request):
        """根据条件筛选用户"""
        serializer = FilterSettingsSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'code': 400,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        filters = serializer.validated_data
        users = self.filter_users(request.user, filters)
        
        # 分页
        page = int(request.GET.get('page', 1))
        limit = min(int(request.GET.get('limit', 20)), 50)
        start = (page - 1) * limit
        end = start + limit
        
        paginated_users = users[start:end]
        
        user_serializer = UserProfileSerializer(paginated_users, many=True, context={'request': request})
        
        return Response({
            'code': 200,
            'message': 'success',
            'data': {
                'users': user_serializer.data,
                'total': users.count(),
                'has_more': end < users.count()
            }
        })
    
    def get_recommendations_for_user(self, user, page=1, limit=10):
        """为用户获取推荐列表（优化版本）"""
        # 使用缓存
        cache_key = f'user_recommendations_{user.id}_{page}_{limit}'
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result

        # 获取用户偏好
        try:
            preference = user.preference
        except:
            preference = None

        # 优化查询：使用子查询排除已操作的用户
        acted_user_subquery = UserAction.objects.filter(user=user).values('target_user_id')

        # 基础查询：使用select_related优化
        queryset = User.objects.select_related('preference').filter(
            status=1
        ).exclude(
            id=user.id
        ).exclude(
            id__in=acted_user_subquery
        ).only(
            'id', 'nickname', 'age', 'gender', 'location', 'education',
            'height', 'bio', 'avatar', 'last_active', 'is_verified', 'is_vip',
            'latitude', 'longitude', 'photos'
        )
        
        # 应用偏好筛选
        if preference:
            if preference.preferred_gender:
                queryset = queryset.filter(gender=preference.preferred_gender)
            
            if preference.min_age and preference.max_age:
                queryset = queryset.filter(age__gte=preference.min_age, age__lte=preference.max_age)
            
            if preference.only_verified:
                queryset = queryset.filter(is_verified=True)
            
            if preference.only_with_photos:
                queryset = queryset.exclude(photos__isnull=True).exclude(photos__exact='[]')
        
        # 获取用户列表（优化：减少数据库查询）
        users = list(queryset.order_by('-last_active', '-is_vip')[:limit * 2])

        # 计算匹配得分并排序
        scored_users = []
        for target_user in users:
            score = self.calculate_match_score(user, target_user)
            reasons = self.get_match_reasons(user, target_user)
            scored_users.append({
                'user': target_user,
                'match_score': score,
                'match_reasons': reasons
            })

        # 按得分排序
        scored_users.sort(key=lambda x: x['match_score'], reverse=True)

        # 分页
        start = (page - 1) * limit
        end = start + limit

        result = scored_users[start:end]

        # 缓存结果（5分钟）
        cache.set(cache_key, result, 300)

        return result
    
    def calculate_match_score(self, user1, user2):
        """计算匹配得分"""
        score = 0.0
        
        # 年龄匹配 (权重: 0.2)
        if user1.age and user2.age:
            age_diff = abs(user1.age - user2.age)
            age_score = max(0, 1 - age_diff / 20)  # 年龄差20岁以内
            score += age_score * 0.2
        
        # 位置匹配 (权重: 0.3)
        if user1.latitude and user1.longitude and user2.latitude and user2.longitude:
            distance = self.calculate_distance(
                user1.latitude, user1.longitude,
                user2.latitude, user2.longitude
            )
            location_score = max(0, 1 - distance / 100)  # 100公里以内
            score += location_score * 0.3
        
        # 学历匹配 (权重: 0.15)
        if user1.education and user2.education:
            edu_diff = abs(user1.education - user2.education)
            edu_score = max(0, 1 - edu_diff / 4)
            score += edu_score * 0.15
        
        # 收入匹配 (权重: 0.1)
        if user1.income and user2.income:
            income_diff = abs(user1.income - user2.income)
            income_score = max(0, 1 - income_diff / 5)
            score += income_score * 0.1
        
        # 身高匹配 (权重: 0.05)
        if user1.height and user2.height:
            height_diff = abs(user1.height - user2.height)
            height_score = max(0, 1 - height_diff / 30)
            score += height_score * 0.05
        
        # 活跃度加分 (权重: 0.2)
        activity_score = self.calculate_activity_score(user2)
        score += activity_score * 0.2
        
        return round(score, 2)
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # 地球半径（公里）
        
        return c * r
    
    def calculate_activity_score(self, user):
        """计算用户活跃度得分"""
        now = timezone.now()
        
        # 最近登录时间
        if user.last_active:
            days_since_active = (now - user.last_active).days
            if days_since_active <= 1:
                return 1.0
            elif days_since_active <= 7:
                return 0.8
            elif days_since_active <= 30:
                return 0.5
            else:
                return 0.2
        
        return 0.1
    
    def get_match_reasons(self, user1, user2):
        """获取匹配原因"""
        reasons = []
        
        if user1.location and user2.location and user1.location == user2.location:
            reasons.append("同城")
        
        if user1.age and user2.age and abs(user1.age - user2.age) <= 3:
            reasons.append("年龄相近")
        
        if user1.education and user2.education and user1.education == user2.education:
            reasons.append("学历相同")
        
        if user1.profession and user2.profession and user1.profession == user2.profession:
            reasons.append("职业相同")
        
        if user2.is_verified:
            reasons.append("已认证用户")
        
        if not reasons:
            reasons.append("系统推荐")
        
        return reasons
    
    def filter_users(self, user, filters):
        """根据条件筛选用户"""
        queryset = User.objects.filter(status=1).exclude(id=user.id)
        
        if 'min_age' in filters:
            queryset = queryset.filter(age__gte=filters['min_age'])
        
        if 'max_age' in filters:
            queryset = queryset.filter(age__lte=filters['max_age'])
        
        if 'gender' in filters:
            queryset = queryset.filter(gender=filters['gender'])
        
        if 'location' in filters and filters['location']:
            queryset = queryset.filter(location__icontains=filters['location'])
        
        if 'min_education' in filters:
            queryset = queryset.filter(education__gte=filters['min_education'])
        
        if 'min_income' in filters:
            queryset = queryset.filter(income__gte=filters['min_income'])
        
        if 'min_height' in filters:
            queryset = queryset.filter(height__gte=filters['min_height'])
        
        if 'max_height' in filters:
            queryset = queryset.filter(height__lte=filters['max_height'])
        
        if filters.get('only_verified'):
            queryset = queryset.filter(is_verified=True)
        
        if filters.get('only_with_photos'):
            queryset = queryset.exclude(photos__isnull=True).exclude(photos__exact='[]')
        
        return queryset.order_by('-last_active')

    @drf_action(detail=False, methods=['get'])
    def algorithm_analytics(self, request):
        """获取推荐算法分析数据"""
        days = int(request.GET.get('days', 30))

        try:
            # 获取算法性能分析
            performance_data = analytics.analyze_algorithm_performance(days)

            # 获取用户行为洞察
            behavior_insights = analytics.get_user_behavior_insights(days)

            # 生成优化建议
            suggestions = analytics.generate_optimization_suggestions(performance_data)

            return Response({
                'code': 200,
                'message': 'success',
                'data': {
                    'performance': performance_data,
                    'behavior_insights': behavior_insights,
                    'optimization_suggestions': suggestions,
                    'generated_at': timezone.now().isoformat()
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'分析失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update_matching_stats(self, user, action_type, is_match=False):
        """更新匹配统计"""
        stats, created = MatchingStatistics.objects.get_or_create(user=user)
        
        if action_type == 'like':
            stats.total_likes_sent += 1
            stats.today_likes_sent += 1
        elif action_type == 'super_like':
            stats.total_super_likes_sent += 1
            stats.today_super_likes_sent += 1
        elif action_type == 'received':
            stats.total_likes_received += 1
            stats.today_likes_received += 1
        
        if is_match:
            stats.total_matches += 1
            stats.today_matches += 1
        
        # 计算成功率
        if stats.total_likes_sent > 0:
            stats.like_success_rate = stats.total_matches / stats.total_likes_sent
        
        stats.save()


# ==================== 新增智能推荐功能API ====================

from rest_framework.views import APIView

class RecommendationsByTypeView(APIView):
    """分类推荐API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取分类推荐用户"""
        user = request.user
        recommendation_type = request.GET.get('type', 'latest')
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 10))

        try:
            # 根据类型获取推荐用户
            if recommendation_type == 'latest':
                users = self.get_latest_users(user, page, limit)
            elif recommendation_type == 'activity':
                users = self.get_active_users(user, page, limit)
            elif recommendation_type == 'nearby':
                users = self.get_nearby_users(user, page, limit)
            elif recommendation_type == 'match':
                users = self.get_high_match_users(user, page, limit)
            elif recommendation_type == 'video':
                users = self.get_video_users(user, page, limit)
            else:
                users = self.get_latest_users(user, page, limit)

            # 记录推荐日志
            self.log_recommendations(user, users, recommendation_type)

            return Response({
                'code': 200,
                'data': {
                    'results': users,
                    'pagination': {
                        'page': page,
                        'limit': limit,
                        'total': self.get_total_count(user, recommendation_type),
                        'has_next': len(users) == limit
                    }
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取推荐失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_latest_users(self, user, page, limit):
        """获取最新用户"""
        offset = (page - 1) * limit
        users = User.objects.filter(
            is_active=True,
            status=1
        ).exclude(
            id=user.id
        ).order_by('-created_at')[offset:offset + limit]

        return self.format_user_data(users, user)

    def get_active_users(self, user, page, limit):
        """获取活跃用户"""
        offset = (page - 1) * limit
        users = User.objects.filter(
            is_active=True,
            status=1,
            online_status='online'
        ).exclude(
            id=user.id
        ).order_by('-last_active')[offset:offset + limit]

        return self.format_user_data(users, user)

    def get_nearby_users(self, user, page, limit):
        """获取附近用户"""
        offset = (page - 1) * limit
        # 简化版本，实际应该使用地理位置计算
        users = User.objects.filter(
            is_active=True,
            status=1,
            latitude__isnull=False,
            longitude__isnull=False
        ).exclude(
            id=user.id
        ).order_by('-last_active')[offset:offset + limit]

        return self.format_user_data(users, user)

    def get_high_match_users(self, user, page, limit):
        """获取高匹配度用户"""
        offset = (page - 1) * limit
        # 简化版本，实际应该使用匹配算法
        users = User.objects.filter(
            is_active=True,
            status=1
        ).exclude(
            id=user.id
        ).order_by('?')[offset:offset + limit]  # 随机排序模拟匹配算法

        return self.format_user_data(users, user)

    def get_video_users(self, user, page, limit):
        """获取有视频的用户"""
        offset = (page - 1) * limit
        # 简化版本，实际应该检查用户是否有视频
        users = User.objects.filter(
            is_active=True,
            status=1
        ).exclude(
            id=user.id
        ).order_by('-created_at')[offset:offset + limit]

        return self.format_user_data(users, user)

    def format_user_data(self, users, current_user):
        """格式化用户数据"""
        result = []
        for user in users:
            # 计算距离（模拟）
            distance = f"{random.uniform(0.5, 50.0):.1f}km"

            # 计算匹配度（模拟）
            match_score = random.randint(75, 98)

            user_data = {
                'id': user.id,
                'nickname': user.nickname or user.username,
                'age': user.age or 25,
                'avatar': user.avatar or '',
                'occupation': user.occupation or '在校学生',
                'is_online': user.online_status == 'online',
                'match_score': match_score,
                'distance': distance,
                'location': f"{user.city or '玉林市'}{user.district or '北流市'}",
                'education': user.education_level or '本科',
                'height': user.height or 175,
                'income': user.income_range or '3-5千元/月',
                'interests': user.interests or ['读书', '旅行', '健身'],
                'is_verified': user.is_verified,
                'is_vip': user.is_vip,
                'photo_count': len(user.get_photos_list()) if hasattr(user, 'get_photos_list') else 6,
                'last_active': '1小时前',
                'recommendation_reason': self.get_recommendation_reason(match_score, distance)
            }
            result.append(user_data)

        return result

    def get_recommendation_reason(self, match_score, distance):
        """获取推荐理由"""
        reasons = [
            '兴趣相投，地理位置相近',
            '年龄匹配，职业相似',
            '学历相当，价值观一致',
            '活跃用户，回复及时',
            '新用户，值得关注'
        ]
        return random.choice(reasons)

    def log_recommendations(self, user, users, algorithm_type):
        """记录推荐日志"""
        # 这里应该记录到UserRecommendation表
        pass

    def get_total_count(self, user, recommendation_type):
        """获取总数量"""
        return User.objects.filter(
            is_active=True,
            status=1
        ).exclude(id=user.id).count()
