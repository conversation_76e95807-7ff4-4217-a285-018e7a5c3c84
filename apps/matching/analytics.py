"""
推荐算法分析工具
用于评估和优化推荐算法的性能
"""

from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Avg, Q, F
from collections import defaultdict
import json

from apps.users.models import User, UserAction
from .models import Match, RecommendationLog


class RecommendationAnalytics:
    """推荐算法分析器"""
    
    def __init__(self):
        self.metrics = {}
    
    def analyze_algorithm_performance(self, days=30):
        """分析算法性能"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # 获取推荐日志
        logs = RecommendationLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # 基础统计
        total_recommendations = logs.count()
        unique_users = logs.values('user').distinct().count()
        
        # 计算各种指标
        metrics = {
            'period': f'{days}天',
            'total_recommendations': total_recommendations,
            'unique_users': unique_users,
            'avg_recommendations_per_user': total_recommendations / max(unique_users, 1),
            'click_through_rate': self._calculate_ctr(logs),
            'match_rate': self._calculate_match_rate(logs),
            'algorithm_distribution': self._get_algorithm_distribution(logs),
            'score_distribution': self._get_score_distribution(logs),
            'user_satisfaction': self._calculate_user_satisfaction(logs),
            'diversity_score': self._calculate_diversity_score(logs)
        }
        
        return metrics
    
    def _calculate_ctr(self, logs):
        """计算点击率"""
        total_recommendations = logs.count()
        if total_recommendations == 0:
            return 0
        
        # 统计被点击的推荐（即产生了用户行为）
        clicked_recommendations = 0
        for log in logs:
            # 检查是否有后续的用户行为
            has_action = UserAction.objects.filter(
                user=log.user,
                target_user=log.recommended_user,
                created_at__gte=log.created_at,
                created_at__lte=log.created_at + timedelta(hours=24)
            ).exists()
            
            if has_action:
                clicked_recommendations += 1
        
        return round(clicked_recommendations / total_recommendations * 100, 2)
    
    def _calculate_match_rate(self, logs):
        """计算匹配率"""
        total_recommendations = logs.count()
        if total_recommendations == 0:
            return 0
        
        # 统计产生匹配的推荐
        matched_recommendations = 0
        for log in logs:
            # 检查是否产生了匹配
            has_match = Match.objects.filter(
                Q(user1=log.user, user2=log.recommended_user) |
                Q(user1=log.recommended_user, user2=log.user),
                status='matched',
                created_at__gte=log.created_at
            ).exists()
            
            if has_match:
                matched_recommendations += 1
        
        return round(matched_recommendations / total_recommendations * 100, 2)
    
    def _get_algorithm_distribution(self, logs):
        """获取算法版本分布"""
        distribution = logs.values('algorithm_version').annotate(
            count=Count('id')
        ).order_by('-count')
        
        total = logs.count()
        result = {}
        for item in distribution:
            version = item['algorithm_version'] or 'unknown'
            count = item['count']
            percentage = round(count / max(total, 1) * 100, 2)
            result[version] = {
                'count': count,
                'percentage': percentage
            }
        
        return result
    
    def _get_score_distribution(self, logs):
        """获取匹配得分分布"""
        scores = [log.match_score for log in logs if log.match_score is not None]
        
        if not scores:
            return {}
        
        # 分段统计
        ranges = [
            (0.0, 0.2, '很低'),
            (0.2, 0.4, '低'),
            (0.4, 0.6, '中等'),
            (0.6, 0.8, '高'),
            (0.8, 1.0, '很高')
        ]
        
        distribution = {}
        total = len(scores)
        
        for min_score, max_score, label in ranges:
            count = sum(1 for score in scores if min_score <= score < max_score)
            percentage = round(count / total * 100, 2)
            distribution[label] = {
                'range': f'{min_score}-{max_score}',
                'count': count,
                'percentage': percentage
            }
        
        # 添加统计信息
        distribution['statistics'] = {
            'avg_score': round(sum(scores) / len(scores), 3),
            'min_score': round(min(scores), 3),
            'max_score': round(max(scores), 3),
            'total_count': total
        }
        
        return distribution
    
    def _calculate_user_satisfaction(self, logs):
        """计算用户满意度"""
        # 基于用户的后续行为来评估满意度
        satisfaction_scores = []
        
        for log in logs:
            # 检查用户的后续行为
            actions = UserAction.objects.filter(
                user=log.user,
                target_user=log.recommended_user,
                created_at__gte=log.created_at,
                created_at__lte=log.created_at + timedelta(days=7)
            )
            
            if not actions.exists():
                # 没有行为，中性评分
                satisfaction_scores.append(0.5)
            else:
                # 根据行为类型评分
                action_scores = []
                for action in actions:
                    if action.action_type == 'like':
                        action_scores.append(1.0)
                    elif action.action_type == 'super_like':
                        action_scores.append(1.0)
                    elif action.action_type == 'dislike':
                        action_scores.append(0.0)
                    elif action.action_type == 'block':
                        action_scores.append(0.0)
                
                if action_scores:
                    satisfaction_scores.append(sum(action_scores) / len(action_scores))
                else:
                    satisfaction_scores.append(0.5)
        
        if not satisfaction_scores:
            return 0
        
        avg_satisfaction = sum(satisfaction_scores) / len(satisfaction_scores)
        return round(avg_satisfaction * 100, 2)  # 转换为百分比
    
    def _calculate_diversity_score(self, logs):
        """计算推荐多样性得分"""
        user_recommendations = defaultdict(list)
        
        # 按用户分组推荐
        for log in logs:
            user_recommendations[log.user_id].append(log.recommended_user)
        
        diversity_scores = []
        
        for user_id, recommended_users in user_recommendations.items():
            if len(recommended_users) < 2:
                continue
            
            # 计算推荐用户的多样性
            users = User.objects.filter(id__in=[u.id for u in recommended_users])
            
            # 年龄多样性
            ages = [u.age for u in users if u.age]
            age_diversity = len(set(ages)) / max(len(ages), 1) if ages else 0
            
            # 学历多样性
            educations = [u.education for u in users if u.education]
            edu_diversity = len(set(educations)) / max(len(educations), 1) if educations else 0
            
            # 位置多样性
            locations = [u.location for u in users if u.location]
            loc_diversity = len(set(locations)) / max(len(locations), 1) if locations else 0
            
            # 综合多样性得分
            user_diversity = (age_diversity + edu_diversity + loc_diversity) / 3
            diversity_scores.append(user_diversity)
        
        if not diversity_scores:
            return 0
        
        avg_diversity = sum(diversity_scores) / len(diversity_scores)
        return round(avg_diversity * 100, 2)  # 转换为百分比
    
    def get_user_behavior_insights(self, days=30):
        """获取用户行为洞察"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        actions = UserAction.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # 行为类型分布
        action_distribution = actions.values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        total_actions = actions.count()
        behavior_stats = {}
        
        for item in action_distribution:
            action_type = item['action_type']
            count = item['count']
            percentage = round(count / max(total_actions, 1) * 100, 2)
            behavior_stats[action_type] = {
                'count': count,
                'percentage': percentage
            }
        
        # 用户活跃度分析
        active_users = actions.values('user').distinct().count()
        avg_actions_per_user = total_actions / max(active_users, 1)
        
        # 匹配成功率
        matches = Match.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date,
            status='matched'
        ).count()
        
        likes = actions.filter(action_type='like').count()
        match_success_rate = round(matches / max(likes, 1) * 100, 2)
        
        return {
            'period': f'{days}天',
            'total_actions': total_actions,
            'active_users': active_users,
            'avg_actions_per_user': round(avg_actions_per_user, 2),
            'behavior_distribution': behavior_stats,
            'total_matches': matches,
            'match_success_rate': match_success_rate
        }
    
    def generate_optimization_suggestions(self, performance_data):
        """生成优化建议"""
        suggestions = []
        
        # 基于点击率的建议
        ctr = performance_data.get('click_through_rate', 0)
        if ctr < 10:
            suggestions.append({
                'type': 'low_ctr',
                'message': '点击率较低，建议优化推荐算法的相关性',
                'priority': 'high',
                'actions': [
                    '调整特征权重',
                    '增加个性化因子',
                    '优化用户画像'
                ]
            })
        
        # 基于匹配率的建议
        match_rate = performance_data.get('match_rate', 0)
        if match_rate < 5:
            suggestions.append({
                'type': 'low_match_rate',
                'message': '匹配率较低，建议提高推荐质量',
                'priority': 'high',
                'actions': [
                    '增强兼容性算法',
                    '优化筛选条件',
                    '提高用户活跃度'
                ]
            })
        
        # 基于多样性的建议
        diversity = performance_data.get('diversity_score', 0)
        if diversity < 60:
            suggestions.append({
                'type': 'low_diversity',
                'message': '推荐多样性不足，可能导致用户疲劳',
                'priority': 'medium',
                'actions': [
                    '增加多样性算法',
                    '平衡不同类型用户的推荐',
                    '避免过度个性化'
                ]
            })
        
        # 基于用户满意度的建议
        satisfaction = performance_data.get('user_satisfaction', 0)
        if satisfaction < 70:
            suggestions.append({
                'type': 'low_satisfaction',
                'message': '用户满意度有待提升',
                'priority': 'high',
                'actions': [
                    '收集用户反馈',
                    '优化推荐解释',
                    '提供更多控制选项'
                ]
            })
        
        return suggestions


# 全局分析器实例
analytics = RecommendationAnalytics()
