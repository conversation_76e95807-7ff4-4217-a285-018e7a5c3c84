"""
安全工具类
提供各种安全检测和防护功能
"""

import re
import time
import hashlib
import hmac
import base64
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .models import RateLimitRecord, SecurityEvent, IPBlacklist

logger = logging.getLogger(__name__)


class SecurityAnalyzer:
    """安全分析器"""
    
    def __init__(self):
        self.suspicious_patterns = [
            # SQL注入模式
            r'(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)',
            r'(\bor\b.*=.*\bor\b)|(\band\b.*=.*\band\b)',
            r'(\bdrop\b.*\btable\b)|(\bdelete\b.*\bfrom\b)',
            r'(\binsert\b.*\binto\b)|(\bupdate\b.*\bset\b)',
            
            # XSS模式
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'<iframe[^>]*>.*?</iframe>',
            
            # 路径遍历模式
            r'\.\./',
            r'\.\.\\',
            r'/etc/passwd',
            r'/proc/self/environ',
            
            # 命令注入模式
            r';\s*(cat|ls|pwd|whoami|id)',
            r'\|\s*(cat|ls|pwd|whoami|id)',
            r'`.*`',
            r'\$\(.*\)',
        ]
        
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
    
    def analyze_request(self, request):
        """分析请求的安全风险"""
        risk_score = 0
        threats = []
        
        # 分析URL
        url_risk = self.analyze_url(request.path)
        risk_score += url_risk['score']
        threats.extend(url_risk['threats'])
        
        # 分析参数
        params_risk = self.analyze_parameters(request)
        risk_score += params_risk['score']
        threats.extend(params_risk['threats'])
        
        # 分析头部
        headers_risk = self.analyze_headers(request)
        risk_score += headers_risk['score']
        threats.extend(headers_risk['threats'])
        
        return {
            'risk_score': risk_score,
            'threats': threats,
            'is_suspicious': risk_score > 50
        }
    
    def analyze_url(self, url):
        """分析URL"""
        risk_score = 0
        threats = []
        
        # 检查可疑模式
        for pattern in self.compiled_patterns:
            if pattern.search(url):
                risk_score += 30
                threats.append('suspicious_url_pattern')
                break
        
        # 检查URL长度
        if len(url) > 2000:
            risk_score += 20
            threats.append('long_url')
        
        # 检查编码攻击
        if '%' in url and self.detect_encoding_attack(url):
            risk_score += 40
            threats.append('encoding_attack')
        
        return {'score': risk_score, 'threats': threats}
    
    def analyze_parameters(self, request):
        """分析请求参数"""
        risk_score = 0
        threats = []
        
        # 分析GET参数
        for key, value in request.GET.items():
            param_risk = self.analyze_parameter_value(value)
            risk_score += param_risk['score']
            threats.extend(param_risk['threats'])
        
        # 分析POST参数
        if hasattr(request, 'POST'):
            for key, value in request.POST.items():
                param_risk = self.analyze_parameter_value(value)
                risk_score += param_risk['score']
                threats.extend(param_risk['threats'])
        
        return {'score': risk_score, 'threats': threats}
    
    def analyze_parameter_value(self, value):
        """分析参数值"""
        risk_score = 0
        threats = []
        
        if not isinstance(value, str):
            return {'score': 0, 'threats': []}
        
        # 检查可疑模式
        for pattern in self.compiled_patterns:
            if pattern.search(value):
                risk_score += 25
                threats.append('suspicious_parameter')
                break
        
        # 检查长度
        if len(value) > 10000:
            risk_score += 15
            threats.append('long_parameter')
        
        return {'score': risk_score, 'threats': threats}
    
    def analyze_headers(self, request):
        """分析请求头"""
        risk_score = 0
        threats = []
        
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # 检查用户代理
        if not user_agent:
            risk_score += 30
            threats.append('missing_user_agent')
        elif self.is_suspicious_user_agent(user_agent):
            risk_score += 40
            threats.append('suspicious_user_agent')
        
        # 检查Referer
        referer = request.META.get('HTTP_REFERER', '')
        if referer and self.is_suspicious_referer(referer):
            risk_score += 20
            threats.append('suspicious_referer')
        
        return {'score': risk_score, 'threats': threats}
    
    def detect_encoding_attack(self, text):
        """检测编码攻击"""
        # 检查多重编码
        if text.count('%') > len(text) * 0.3:
            return True
        
        # 检查Unicode编码攻击
        unicode_patterns = [r'%u[0-9a-fA-F]{4}', r'\\u[0-9a-fA-F]{4}']
        for pattern in unicode_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def is_suspicious_user_agent(self, user_agent):
        """检查可疑用户代理"""
        suspicious_ua = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap',
            'burp', 'w3af', 'acunetix', 'nessus'
        ]
        
        ua_lower = user_agent.lower()
        return any(sus in ua_lower for sus in suspicious_ua)
    
    def is_suspicious_referer(self, referer):
        """检查可疑来源"""
        # 检查是否包含可疑域名
        suspicious_domains = ['malware.com', 'phishing.com']
        return any(domain in referer for domain in suspicious_domains)


class RateLimiter:
    """频率限制器"""
    
    def __init__(self):
        self.cache_prefix = 'rate_limit'
    
    def check_limit(self, identifier, limit_type, limit_count, time_window):
        """检查频率限制"""
        cache_key = f"{self.cache_prefix}:{identifier}:{limit_type}"
        
        # 获取当前计数
        current_data = cache.get(cache_key, {'count': 0, 'window_start': time.time()})
        current_time = time.time()
        
        # 检查时间窗口是否需要重置
        if current_time - current_data['window_start'] > time_window:
            current_data = {'count': 0, 'window_start': current_time}
        
        # 增加计数
        current_data['count'] += 1
        
        # 计算重置时间
        reset_time = current_data['window_start'] + time_window
        
        # 检查是否超限
        exceeded = current_data['count'] > limit_count
        
        # 更新缓存
        cache.set(cache_key, current_data, time_window)
        
        # 记录到数据库（如果超限）
        if exceeded:
            self.record_rate_limit_violation(identifier, limit_type, limit_count, time_window)
        
        return {
            'exceeded': exceeded,
            'current_count': current_data['count'],
            'limit_count': limit_count,
            'reset_time': reset_time,
            'remaining': max(0, limit_count - current_data['count'])
        }
    
    def record_rate_limit_violation(self, identifier, limit_type, limit_count, time_window):
        """记录频率限制违规"""
        try:
            identifier_parts = identifier.split('_', 1)
            identifier_type = identifier_parts[0]
            identifier_value = identifier_parts[1] if len(identifier_parts) > 1 else identifier
            
            record, created = RateLimitRecord.objects.get_or_create(
                identifier_type=identifier_type,
                identifier_value=identifier_value,
                limit_type=limit_type,
                defaults={
                    'limit_count': limit_count,
                    'time_window': time_window,
                    'current_count': 1,
                    'is_exceeded': True,
                    'exceeded_at': timezone.now(),
                    'reset_at': timezone.now() + timedelta(seconds=time_window)
                }
            )
            
            if not created:
                record.current_count += 1
                record.is_exceeded = True
                record.exceeded_at = timezone.now()
                record.save()
                
        except Exception as e:
            logger.error(f"记录频率限制违规失败: {str(e)}")
    
    def reset_limit(self, identifier, limit_type):
        """重置频率限制"""
        cache_key = f"{self.cache_prefix}:{identifier}:{limit_type}"
        cache.delete(cache_key)
    
    def get_remaining_time(self, identifier, limit_type):
        """获取剩余重置时间"""
        cache_key = f"{self.cache_prefix}:{identifier}:{limit_type}"
        current_data = cache.get(cache_key)
        
        if not current_data:
            return 0
        
        current_time = time.time()
        return max(0, current_data['window_start'] + current_data.get('time_window', 3600) - current_time)


class ThreatDetector:
    """威胁检测器"""
    
    def __init__(self):
        self.sql_injection_patterns = [
            r"(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)",
            r"(\bor\b\s+\d+\s*=\s*\d+)|(\band\b\s+\d+\s*=\s*\d+)",
            r"(\bdrop\b.*\btable\b)|(\bdelete\b.*\bfrom\b)",
            r"(\binsert\b.*\binto\b)|(\bupdate\b.*\bset\b)",
            r"'.*(\bor\b|\band\b).*'",
            r";\s*(drop|delete|insert|update|create|alter)",
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=\s*['\"].*['\"]",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
        ]
        
        self.path_traversal_patterns = [
            r"\.\.[\\/]",
            r"[\\/]etc[\\/]passwd",
            r"[\\/]proc[\\/]",
            r"[\\/]windows[\\/]system32",
            r"[\\/]boot\.ini",
        ]
    
    def detect_sql_injection(self, request):
        """检测SQL注入"""
        return self._check_patterns(request, self.sql_injection_patterns)
    
    def detect_xss(self, request):
        """检测XSS攻击"""
        return self._check_patterns(request, self.xss_patterns)
    
    def detect_path_traversal(self, request):
        """检测路径遍历"""
        return self._check_patterns(request, self.path_traversal_patterns)
    
    def detect_brute_force(self, request):
        """检测暴力破解"""
        # 检查登录端点的频繁访问
        if request.path.endswith('/login') or 'login' in request.path:
            client_ip = self._get_client_ip(request)
            cache_key = f"brute_force:{client_ip}"
            
            attempts = cache.get(cache_key, 0)
            if attempts > 10:  # 10次尝试
                return True
            
            cache.set(cache_key, attempts + 1, 3600)  # 1小时窗口
        
        return False
    
    def _check_patterns(self, request, patterns):
        """检查模式匹配"""
        # 检查URL
        for pattern in patterns:
            if re.search(pattern, request.path, re.IGNORECASE):
                return True
        
        # 检查GET参数
        for value in request.GET.values():
            for pattern in patterns:
                if re.search(pattern, str(value), re.IGNORECASE):
                    return True
        
        # 检查POST参数
        if hasattr(request, 'POST'):
            for value in request.POST.values():
                for pattern in patterns:
                    if re.search(pattern, str(value), re.IGNORECASE):
                        return True
        
        # 检查请求体
        if hasattr(request, 'body') and request.body:
            try:
                body_str = request.body.decode('utf-8')
                for pattern in patterns:
                    if re.search(pattern, body_str, re.IGNORECASE):
                        return True
            except UnicodeDecodeError:
                pass
        
        return False
    
    def _get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class DataEncryptor:
    """数据加密器"""
    
    def __init__(self):
        self.key = self._get_encryption_key()
        self.cipher = Fernet(self.key)
    
    def _get_encryption_key(self):
        """获取加密密钥"""
        # 从设置中获取密钥，如果没有则生成新的
        secret_key = getattr(settings, 'ENCRYPTION_SECRET_KEY', None)
        if not secret_key:
            secret_key = Fernet.generate_key()
            logger.warning("未配置加密密钥，使用临时密钥")
        
        if isinstance(secret_key, str):
            secret_key = secret_key.encode()
        
        # 使用PBKDF2派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'xiangqin_salt',  # 在生产环境中应该使用随机盐
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(secret_key))
        return key
    
    def encrypt(self, data):
        """加密数据"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encrypted_data = self.cipher.encrypt(data)
        return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        if isinstance(encrypted_data, str):
            encrypted_data = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
        
        decrypted_data = self.cipher.decrypt(encrypted_data)
        return decrypted_data.decode('utf-8')
    
    def hash_password(self, password, salt=None):
        """哈希密码"""
        if salt is None:
            salt = hashlib.sha256(str(time.time()).encode()).hexdigest()[:16]
        
        hashed = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}${base64.b64encode(hashed).decode()}"
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        try:
            salt, hashed = hashed_password.split('$')
            return self.hash_password(password, salt) == hashed_password
        except ValueError:
            return False


class SecurityTokenManager:
    """安全令牌管理器"""
    
    def __init__(self):
        self.secret_key = getattr(settings, 'SECRET_KEY', 'default_secret')
    
    def generate_token(self, user_id, expires_in=3600):
        """生成安全令牌"""
        timestamp = int(time.time())
        expires_at = timestamp + expires_in
        
        payload = f"{user_id}:{expires_at}:{timestamp}"
        signature = self._sign_payload(payload)
        
        token = base64.urlsafe_b64encode(f"{payload}:{signature}".encode()).decode()
        return token
    
    def verify_token(self, token):
        """验证安全令牌"""
        try:
            decoded = base64.urlsafe_b64decode(token.encode()).decode()
            parts = decoded.split(':')
            
            if len(parts) != 4:
                return None
            
            user_id, expires_at, timestamp, signature = parts
            payload = f"{user_id}:{expires_at}:{timestamp}"
            
            # 验证签名
            if not self._verify_signature(payload, signature):
                return None
            
            # 检查过期时间
            if int(time.time()) > int(expires_at):
                return None
            
            return {
                'user_id': int(user_id),
                'expires_at': int(expires_at),
                'timestamp': int(timestamp)
            }
            
        except Exception:
            return None
    
    def _sign_payload(self, payload):
        """签名载荷"""
        return hmac.new(
            self.secret_key.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def _verify_signature(self, payload, signature):
        """验证签名"""
        expected_signature = self._sign_payload(payload)
        return hmac.compare_digest(expected_signature, signature)
