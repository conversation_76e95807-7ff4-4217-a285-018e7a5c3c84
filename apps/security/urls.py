"""
安全防护模块URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# API URL模式
urlpatterns = [
    # 安全事件
    path('security/events/', views.SecurityEventAPIView.as_view(), name='security_events'),
    
    # IP黑名单
    path('security/blacklist/', views.IPBlacklistAPIView.as_view(), name='ip_blacklist'),
    
    # 用户安全档案
    path('security/user-profiles/', views.UserSecurityProfileAPIView.as_view(), name='user_security_profiles'),
    
    # 安全配置
    path('security/config/', views.SecurityConfigAPIView.as_view(), name='security_config'),
    
    # 包含路由器URL
    path('', include(router.urls)),
]
