"""
安全防护系统模型
包括防刷、防攻击、数据加密等安全机制
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import validate_ipv4_address, validate_ipv6_address
import uuid

User = get_user_model()


class SecurityEvent(models.Model):
    """安全事件记录"""
    EVENT_TYPES = [
        ('login_attempt', '登录尝试'),
        ('failed_login', '登录失败'),
        ('suspicious_activity', '可疑活动'),
        ('rate_limit_exceeded', '频率限制超出'),
        ('sql_injection_attempt', 'SQL注入尝试'),
        ('xss_attempt', 'XSS攻击尝试'),
        ('csrf_attack', 'CSRF攻击'),
        ('brute_force_attack', '暴力破解攻击'),
        ('ddos_attack', 'DDoS攻击'),
        ('data_breach_attempt', '数据泄露尝试'),
        ('unauthorized_access', '未授权访问'),
        ('malicious_upload', '恶意文件上传'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]
    
    event_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='事件ID')
    event_type = models.CharField(max_length=30, choices=EVENT_TYPES, verbose_name='事件类型')
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, verbose_name='严重程度')
    
    # 用户信息
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='相关用户'
    )
    
    # 网络信息
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    referer = models.URLField(blank=True, verbose_name='来源页面')
    
    # 请求信息
    request_method = models.CharField(max_length=10, verbose_name='请求方法')
    request_path = models.CharField(max_length=500, verbose_name='请求路径')
    request_data = models.JSONField(default=dict, verbose_name='请求数据')
    
    # 事件详情
    description = models.TextField(verbose_name='事件描述')
    details = models.JSONField(default=dict, verbose_name='事件详情')
    
    # 处理状态
    is_blocked = models.BooleanField(default=False, verbose_name='是否已阻止')
    is_resolved = models.BooleanField(default=False, verbose_name='是否已处理')
    resolution_notes = models.TextField(blank=True, verbose_name='处理备注')
    
    # 地理位置
    country = models.CharField(max_length=100, blank=True, verbose_name='国家')
    region = models.CharField(max_length=100, blank=True, verbose_name='地区')
    city = models.CharField(max_length=100, blank=True, verbose_name='城市')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='发生时间')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='处理时间')
    
    class Meta:
        verbose_name = '安全事件'
        verbose_name_plural = '安全事件'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['event_type', 'severity']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['user', 'event_type']),
            models.Index(fields=['is_resolved', 'severity']),
        ]
    
    def __str__(self):
        return f"{self.get_event_type_display()} - {self.ip_address} - {self.created_at}"


class IPBlacklist(models.Model):
    """IP黑名单"""
    BLOCK_REASONS = [
        ('manual', '手动添加'),
        ('auto_brute_force', '自动检测-暴力破解'),
        ('auto_ddos', '自动检测-DDoS攻击'),
        ('auto_suspicious', '自动检测-可疑活动'),
        ('reported', '用户举报'),
        ('third_party', '第三方情报'),
    ]
    
    ip_address = models.GenericIPAddressField(unique=True, verbose_name='IP地址')
    reason = models.CharField(max_length=20, choices=BLOCK_REASONS, verbose_name='封禁原因')
    description = models.TextField(blank=True, verbose_name='详细描述')
    
    # 封禁信息
    blocked_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='封禁操作员'
    )
    blocked_at = models.DateTimeField(auto_now_add=True, verbose_name='封禁时间')
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    # 统计信息
    violation_count = models.IntegerField(default=1, verbose_name='违规次数')
    last_violation_at = models.DateTimeField(auto_now_add=True, verbose_name='最后违规时间')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否生效')
    
    # 地理位置
    country = models.CharField(max_length=100, blank=True, verbose_name='国家')
    region = models.CharField(max_length=100, blank=True, verbose_name='地区')
    
    class Meta:
        verbose_name = 'IP黑名单'
        verbose_name_plural = 'IP黑名单'
        ordering = ['-blocked_at']
        indexes = [
            models.Index(fields=['ip_address', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"{self.ip_address} - {self.get_reason_display()}"
    
    @property
    def is_expired(self):
        """检查是否已过期"""
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at


class RateLimitRecord(models.Model):
    """频率限制记录"""
    LIMIT_TYPES = [
        ('api_request', 'API请求'),
        ('login_attempt', '登录尝试'),
        ('message_send', '发送消息'),
        ('like_action', '点赞操作'),
        ('search_query', '搜索查询'),
        ('file_upload', '文件上传'),
        ('password_reset', '密码重置'),
    ]
    
    # 标识信息
    identifier_type = models.CharField(max_length=20, verbose_name='标识类型')  # ip, user, device
    identifier_value = models.CharField(max_length=255, verbose_name='标识值')
    limit_type = models.CharField(max_length=20, choices=LIMIT_TYPES, verbose_name='限制类型')
    
    # 限制配置
    limit_count = models.IntegerField(verbose_name='限制次数')
    time_window = models.IntegerField(verbose_name='时间窗口(秒)')
    
    # 当前状态
    current_count = models.IntegerField(default=0, verbose_name='当前次数')
    window_start = models.DateTimeField(auto_now_add=True, verbose_name='窗口开始时间')
    
    # 超限信息
    is_exceeded = models.BooleanField(default=False, verbose_name='是否超限')
    exceeded_at = models.DateTimeField(null=True, blank=True, verbose_name='超限时间')
    reset_at = models.DateTimeField(null=True, blank=True, verbose_name='重置时间')
    
    class Meta:
        verbose_name = '频率限制记录'
        verbose_name_plural = '频率限制记录'
        unique_together = ['identifier_type', 'identifier_value', 'limit_type']
        indexes = [
            models.Index(fields=['identifier_value', 'limit_type']),
            models.Index(fields=['is_exceeded', 'reset_at']),
        ]
    
    def __str__(self):
        return f"{self.identifier_value} - {self.get_limit_type_display()}"


class UserSecurityProfile(models.Model):
    """用户安全档案"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 风险评分
    risk_score = models.FloatField(default=0.0, verbose_name='风险评分')
    trust_level = models.CharField(
        max_length=20,
        choices=[
            ('very_low', '极低'),
            ('low', '低'),
            ('medium', '中等'),
            ('high', '高'),
            ('very_high', '极高'),
        ],
        default='medium',
        verbose_name='信任等级'
    )
    
    # 登录安全
    failed_login_count = models.IntegerField(default=0, verbose_name='登录失败次数')
    last_failed_login = models.DateTimeField(null=True, blank=True, verbose_name='最后登录失败时间')
    account_locked_until = models.DateTimeField(null=True, blank=True, verbose_name='账户锁定至')
    
    # 设备信息
    known_devices = models.JSONField(default=list, verbose_name='已知设备')
    suspicious_devices = models.JSONField(default=list, verbose_name='可疑设备')
    
    # 行为模式
    normal_login_hours = models.JSONField(default=list, verbose_name='正常登录时段')
    normal_locations = models.JSONField(default=list, verbose_name='正常登录地点')
    
    # 安全事件统计
    security_events_count = models.IntegerField(default=0, verbose_name='安全事件次数')
    last_security_event = models.DateTimeField(null=True, blank=True, verbose_name='最后安全事件时间')
    
    # 验证状态
    phone_verified = models.BooleanField(default=False, verbose_name='手机已验证')
    email_verified = models.BooleanField(default=False, verbose_name='邮箱已验证')
    identity_verified = models.BooleanField(default=False, verbose_name='身份已验证')
    
    # 安全设置
    two_factor_enabled = models.BooleanField(default=False, verbose_name='双因子认证已启用')
    login_notifications = models.BooleanField(default=True, verbose_name='登录通知已启用')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户安全档案'
        verbose_name_plural = '用户安全档案'
        indexes = [
            models.Index(fields=['risk_score']),
            models.Index(fields=['trust_level']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - 风险评分: {self.risk_score}"


class DataEncryption(models.Model):
    """数据加密记录"""
    ENCRYPTION_TYPES = [
        ('user_data', '用户数据'),
        ('chat_message', '聊天消息'),
        ('file_upload', '上传文件'),
        ('payment_info', '支付信息'),
        ('sensitive_log', '敏感日志'),
    ]
    
    ENCRYPTION_ALGORITHMS = [
        ('AES-256', 'AES-256'),
        ('RSA-2048', 'RSA-2048'),
        ('ChaCha20', 'ChaCha20'),
    ]
    
    record_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='记录ID')
    data_type = models.CharField(max_length=20, choices=ENCRYPTION_TYPES, verbose_name='数据类型')
    
    # 加密信息
    algorithm = models.CharField(max_length=20, choices=ENCRYPTION_ALGORITHMS, verbose_name='加密算法')
    key_id = models.CharField(max_length=100, verbose_name='密钥ID')
    encrypted_data = models.BinaryField(verbose_name='加密数据')
    
    # 元数据
    original_size = models.IntegerField(verbose_name='原始大小')
    encrypted_size = models.IntegerField(verbose_name='加密后大小')
    checksum = models.CharField(max_length=64, verbose_name='校验和')
    
    # 访问控制
    access_level = models.CharField(
        max_length=20,
        choices=[
            ('public', '公开'),
            ('internal', '内部'),
            ('confidential', '机密'),
            ('top_secret', '绝密'),
        ],
        default='internal',
        verbose_name='访问级别'
    )
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name='过期时间')
    
    class Meta:
        verbose_name = '数据加密记录'
        verbose_name_plural = '数据加密记录'
        indexes = [
            models.Index(fields=['data_type', 'created_at']),
            models.Index(fields=['key_id']),
        ]
    
    def __str__(self):
        return f"{self.get_data_type_display()} - {self.algorithm}"


class SecurityAuditLog(models.Model):
    """安全审计日志"""
    ACTION_TYPES = [
        ('user_login', '用户登录'),
        ('user_logout', '用户登出'),
        ('password_change', '密码修改'),
        ('profile_update', '资料更新'),
        ('permission_change', '权限变更'),
        ('data_access', '数据访问'),
        ('data_export', '数据导出'),
        ('admin_action', '管理员操作'),
        ('system_config', '系统配置'),
        ('security_setting', '安全设置'),
    ]
    
    log_id = models.UUIDField(default=uuid.uuid4, unique=True, verbose_name='日志ID')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name='操作类型')
    
    # 操作者信息
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='操作用户'
    )
    session_id = models.CharField(max_length=100, blank=True, verbose_name='会话ID')
    
    # 操作详情
    action_description = models.TextField(verbose_name='操作描述')
    target_object_type = models.CharField(max_length=50, blank=True, verbose_name='目标对象类型')
    target_object_id = models.CharField(max_length=100, blank=True, verbose_name='目标对象ID')
    
    # 操作结果
    is_successful = models.BooleanField(verbose_name='是否成功')
    error_message = models.TextField(blank=True, verbose_name='错误信息')
    
    # 环境信息
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    
    # 数据变更
    old_values = models.JSONField(default=dict, verbose_name='变更前值')
    new_values = models.JSONField(default=dict, verbose_name='变更后值')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')
    
    class Meta:
        verbose_name = '安全审计日志'
        verbose_name_plural = '安全审计日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action_type']),
            models.Index(fields=['action_type', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.get_action_type_display()} - {self.user.username if self.user else 'Anonymous'}"


class SecurityConfiguration(models.Model):
    """安全配置"""
    CONFIG_CATEGORIES = [
        ('rate_limiting', '频率限制'),
        ('authentication', '身份认证'),
        ('encryption', '数据加密'),
        ('monitoring', '安全监控'),
        ('access_control', '访问控制'),
    ]
    
    category = models.CharField(max_length=20, choices=CONFIG_CATEGORIES, verbose_name='配置类别')
    config_key = models.CharField(max_length=100, verbose_name='配置键')
    config_value = models.JSONField(verbose_name='配置值')
    description = models.TextField(verbose_name='配置描述')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 变更记录
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '安全配置'
        verbose_name_plural = '安全配置'
        unique_together = ['category', 'config_key']
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.config_key}"
