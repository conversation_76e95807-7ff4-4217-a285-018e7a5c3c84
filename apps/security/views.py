"""
安全防护模块视图
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework import status
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from .models import SecurityEvent, IPBlacklist, UserSecurityProfile, SecurityConfiguration


class SecurityEventAPIView(APIView):
    """安全事件API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取安全事件"""
        try:
            # 查询参数
            event_type = request.GET.get('event_type')
            severity = request.GET.get('severity')
            days = int(request.GET.get('days', 7))
            limit = int(request.GET.get('limit', 50))
            
            # 构建查询
            queryset = SecurityEvent.objects.all()
            
            # 时间过滤
            start_date = timezone.now() - timedelta(days=days)
            queryset = queryset.filter(created_at__gte=start_date)
            
            # 类型过滤
            if event_type:
                queryset = queryset.filter(event_type=event_type)
            
            # 严重程度过滤
            if severity:
                queryset = queryset.filter(severity=severity)
            
            # 获取事件列表
            events = queryset.order_by('-created_at')[:limit]
            
            event_data = []
            for event in events:
                event_data.append({
                    'event_id': str(event.event_id),
                    'event_type': event.event_type,
                    'severity': event.severity,
                    'ip_address': event.ip_address,
                    'user_id': event.user.id if event.user else None,
                    'description': event.description,
                    'is_blocked': event.is_blocked,
                    'is_resolved': event.is_resolved,
                    'created_at': event.created_at.isoformat()
                })
            
            # 统计信息
            stats = {
                'total_events': queryset.count(),
                'high_severity': queryset.filter(severity='high').count(),
                'unresolved': queryset.filter(is_resolved=False).count(),
                'blocked': queryset.filter(is_blocked=True).count()
            }
            
            return Response({
                'code': 200,
                'message': '获取安全事件成功',
                'data': {
                    'events': event_data,
                    'stats': stats,
                    'total': len(event_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取安全事件失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class IPBlacklistAPIView(APIView):
    """IP黑名单API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取IP黑名单"""
        try:
            # 查询参数
            is_active = request.GET.get('is_active', 'true').lower() == 'true'
            reason = request.GET.get('reason')
            limit = int(request.GET.get('limit', 100))
            
            # 构建查询
            queryset = IPBlacklist.objects.filter(is_active=is_active)
            
            if reason:
                queryset = queryset.filter(reason=reason)
            
            # 获取黑名单列表
            blacklist = queryset.order_by('-blocked_at')[:limit]
            
            blacklist_data = []
            for item in blacklist:
                blacklist_data.append({
                    'id': item.id,
                    'ip_address': item.ip_address,
                    'reason': item.reason,
                    'description': item.description,
                    'blocked_at': item.blocked_at.isoformat(),
                    'expires_at': item.expires_at.isoformat() if item.expires_at else None,
                    'violation_count': item.violation_count,
                    'is_expired': item.is_expired
                })
            
            return Response({
                'code': 200,
                'message': '获取IP黑名单成功',
                'data': {
                    'blacklist': blacklist_data,
                    'total': len(blacklist_data)
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取IP黑名单失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """添加IP到黑名单"""
        try:
            ip_address = request.data.get('ip_address')
            reason = request.data.get('reason', 'manual')
            description = request.data.get('description', '')
            expires_hours = request.data.get('expires_hours')
            
            if not ip_address:
                return Response({
                    'code': 400,
                    'message': 'IP地址不能为空',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 计算过期时间
            expires_at = None
            if expires_hours:
                expires_at = timezone.now() + timedelta(hours=int(expires_hours))
            
            # 创建或更新黑名单记录
            blacklist_item, created = IPBlacklist.objects.get_or_create(
                ip_address=ip_address,
                defaults={
                    'reason': reason,
                    'description': description,
                    'expires_at': expires_at,
                    'blocked_by': request.user
                }
            )
            
            if not created:
                blacklist_item.violation_count += 1
                blacklist_item.last_violation_at = timezone.now()
                blacklist_item.is_active = True
                blacklist_item.save()
            
            return Response({
                'code': 200,
                'message': 'IP已添加到黑名单',
                'data': None
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'添加IP黑名单失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserSecurityProfileAPIView(APIView):
    """用户安全档案API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取用户安全档案"""
        try:
            user = request.user
            
            # 获取或创建用户安全档案
            profile, created = UserSecurityProfile.objects.get_or_create(
                user=user,
                defaults={
                    'risk_score': 0.0,
                    'trust_level': 'medium'
                }
            )
            
            return Response({
                'code': 200,
                'message': '获取用户安全档案成功',
                'data': {
                    'risk_score': profile.risk_score,
                    'trust_level': profile.trust_level,
                    'failed_login_count': profile.failed_login_count,
                    'last_failed_login': profile.last_failed_login.isoformat() if profile.last_failed_login else None,
                    'account_locked_until': profile.account_locked_until.isoformat() if profile.account_locked_until else None,
                    'security_events_count': profile.security_events_count,
                    'phone_verified': profile.phone_verified,
                    'email_verified': profile.email_verified,
                    'identity_verified': profile.identity_verified,
                    'two_factor_enabled': profile.two_factor_enabled
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取用户安全档案失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SecurityConfigAPIView(APIView):
    """安全配置API"""
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取安全配置"""
        try:
            category = request.GET.get('category')
            
            # 构建查询
            queryset = SecurityConfiguration.objects.filter(is_active=True)
            
            if category:
                queryset = queryset.filter(category=category)
            
            # 获取配置列表
            configs = queryset.order_by('category', 'config_key')
            
            config_data = {}
            for config in configs:
                if config.category not in config_data:
                    config_data[config.category] = {}
                config_data[config.category][config.config_key] = config.config_value
            
            return Response({
                'code': 200,
                'message': '获取安全配置成功',
                'data': {
                    'configs': config_data,
                    'total': configs.count()
                }
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取安全配置失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """更新安全配置"""
        try:
            category = request.data.get('category')
            config_key = request.data.get('config_key')
            config_value = request.data.get('config_value')
            description = request.data.get('description', '')
            
            if not all([category, config_key, config_value]):
                return Response({
                    'code': 400,
                    'message': '缺少必要参数',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建或更新配置
            config, created = SecurityConfiguration.objects.get_or_create(
                category=category,
                config_key=config_key,
                defaults={
                    'config_value': config_value,
                    'description': description,
                    'created_by': request.user
                }
            )
            
            if not created:
                config.config_value = config_value
                config.description = description
                config.save()
            
            return Response({
                'code': 200,
                'message': '安全配置更新成功',
                'data': None
            })
            
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'更新安全配置失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
