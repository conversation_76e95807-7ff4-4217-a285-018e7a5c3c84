"""
安全防护中间件
实现防刷、防攻击、频率限制等安全机制
"""

import re
import json
import time
import hashlib
import logging
from datetime import datetime, timedelta
from django.http import JsonResponse, HttpResponseForbidden
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.deprecation import MiddlewareMixin

from .models import SecurityEvent, IPBlacklist, RateLimitRecord, UserSecurityProfile
from .security_utils import SecurityAnalyzer, RateLimiter, ThreatDetector

User = get_user_model()
logger = logging.getLogger(__name__)


class SecurityMiddleware(MiddlewareMixin):
    """安全防护主中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.security_analyzer = SecurityAnalyzer()
        self.rate_limiter = RateLimiter()
        self.threat_detector = ThreatDetector()
        
        # 安全配置
        self.config = {
            'enable_ip_blocking': getattr(settings, 'SECURITY_ENABLE_IP_BLOCKING', True),
            'enable_rate_limiting': getattr(settings, 'SECURITY_ENABLE_RATE_LIMITING', True),
            'enable_threat_detection': getattr(settings, 'SECURITY_ENABLE_THREAT_DETECTION', True),
            'max_request_size': getattr(settings, 'SECURITY_MAX_REQUEST_SIZE', 10 * 1024 * 1024),  # 10MB
            'blocked_user_agents': getattr(settings, 'SECURITY_BLOCKED_USER_AGENTS', []),
            'suspicious_patterns': getattr(settings, 'SECURITY_SUSPICIOUS_PATTERNS', []),
        }
        
        super().__init__(get_response)
    
    def __call__(self, request):
        """处理请求"""
        # 获取客户端信息
        client_ip = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # 1. IP黑名单检查
        if self.config['enable_ip_blocking'] and self.is_ip_blocked(client_ip):
            return self.create_blocked_response('IP地址已被封禁')
        
        # 2. 用户代理检查
        if self.is_user_agent_blocked(user_agent):
            self.log_security_event(
                request, 'suspicious_activity', 'medium',
                f'被阻止的用户代理: {user_agent}'
            )
            return self.create_blocked_response('请求被拒绝')
        
        # 3. 请求大小检查
        if self.is_request_too_large(request):
            self.log_security_event(
                request, 'suspicious_activity', 'medium',
                f'请求过大: {len(request.body)} bytes'
            )
            return self.create_blocked_response('请求过大')
        
        # 4. 频率限制检查
        if self.config['enable_rate_limiting']:
            rate_limit_result = self.check_rate_limits(request)
            if rate_limit_result['blocked']:
                return self.create_rate_limit_response(rate_limit_result)
        
        # 5. 威胁检测
        if self.config['enable_threat_detection']:
            threat_result = self.detect_threats(request)
            if threat_result['blocked']:
                return self.create_threat_response(threat_result)
        
        # 处理请求
        response = self.get_response(request)
        
        # 6. 响应后处理
        self.post_process_request(request, response)
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端真实IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_ip_blocked(self, ip):
        """检查IP是否被封禁"""
        try:
            blocked_ip = IPBlacklist.objects.get(
                ip_address=ip,
                is_active=True
            )
            
            # 检查是否过期
            if blocked_ip.expires_at and timezone.now() > blocked_ip.expires_at:
                blocked_ip.is_active = False
                blocked_ip.save()
                return False
            
            return True
        except IPBlacklist.DoesNotExist:
            return False
    
    def is_user_agent_blocked(self, user_agent):
        """检查用户代理是否被阻止"""
        if not user_agent:
            return True
        
        # 检查已知的恶意用户代理
        blocked_patterns = [
            r'bot', r'crawler', r'spider', r'scraper',
            r'curl', r'wget', r'python-requests',
            r'sqlmap', r'nikto', r'nmap'
        ]
        
        user_agent_lower = user_agent.lower()
        for pattern in blocked_patterns:
            if re.search(pattern, user_agent_lower):
                return True
        
        # 检查自定义阻止列表
        for blocked_ua in self.config['blocked_user_agents']:
            if blocked_ua.lower() in user_agent_lower:
                return True
        
        return False
    
    def is_request_too_large(self, request):
        """检查请求是否过大"""
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length:
            try:
                size = int(content_length)
                return size > self.config['max_request_size']
            except ValueError:
                return False
        return False
    
    def check_rate_limits(self, request):
        """检查频率限制"""
        client_ip = self.get_client_ip(request)
        user = getattr(request, 'user', None)
        
        # 定义不同类型的频率限制
        limits = [
            {
                'identifier': f'ip_{client_ip}',
                'type': 'api_request',
                'limit': 1000,  # 每小时1000次请求
                'window': 3600
            }
        ]
        
        # 如果是认证用户，添加用户级别的限制
        if user and user.is_authenticated:
            limits.extend([
                {
                    'identifier': f'user_{user.id}',
                    'type': 'api_request',
                    'limit': 5000,  # 认证用户每小时5000次
                    'window': 3600
                },
                {
                    'identifier': f'user_{user.id}',
                    'type': 'login_attempt',
                    'limit': 10,  # 每小时10次登录尝试
                    'window': 3600
                }
            ])
        
        # 检查特定端点的限制
        if request.path.startswith('/api/v1/auth/login'):
            limits.append({
                'identifier': f'ip_{client_ip}',
                'type': 'login_attempt',
                'limit': 5,  # 每15分钟5次登录尝试
                'window': 900
            })
        
        # 执行频率限制检查
        for limit_config in limits:
            result = self.rate_limiter.check_limit(
                limit_config['identifier'],
                limit_config['type'],
                limit_config['limit'],
                limit_config['window']
            )
            
            if result['exceeded']:
                self.log_security_event(
                    request, 'rate_limit_exceeded', 'medium',
                    f"频率限制超出: {limit_config['type']}"
                )
                return {
                    'blocked': True,
                    'limit_type': limit_config['type'],
                    'reset_time': result['reset_time']
                }
        
        return {'blocked': False}
    
    def detect_threats(self, request):
        """威胁检测"""
        threats = []
        
        # 1. SQL注入检测
        if self.threat_detector.detect_sql_injection(request):
            threats.append('sql_injection')
            self.log_security_event(
                request, 'sql_injection_attempt', 'high',
                '检测到SQL注入尝试'
            )
        
        # 2. XSS攻击检测
        if self.threat_detector.detect_xss(request):
            threats.append('xss')
            self.log_security_event(
                request, 'xss_attempt', 'high',
                '检测到XSS攻击尝试'
            )
        
        # 3. 路径遍历检测
        if self.threat_detector.detect_path_traversal(request):
            threats.append('path_traversal')
            self.log_security_event(
                request, 'suspicious_activity', 'high',
                '检测到路径遍历尝试'
            )
        
        # 4. 暴力破解检测
        if self.threat_detector.detect_brute_force(request):
            threats.append('brute_force')
            self.log_security_event(
                request, 'brute_force_attack', 'high',
                '检测到暴力破解攻击'
            )
        
        # 如果检测到高危威胁，阻止请求
        high_risk_threats = ['sql_injection', 'xss', 'brute_force']
        if any(threat in high_risk_threats for threat in threats):
            return {
                'blocked': True,
                'threats': threats,
                'reason': '检测到安全威胁'
            }
        
        return {'blocked': False, 'threats': threats}
    
    def post_process_request(self, request, response):
        """请求后处理"""
        # 记录访问日志
        self.log_access(request, response)
        
        # 更新用户安全档案
        if hasattr(request, 'user') and request.user.is_authenticated:
            self.update_user_security_profile(request.user, request)
    
    def log_security_event(self, request, event_type, severity, description):
        """记录安全事件"""
        try:
            SecurityEvent.objects.create(
                event_type=event_type,
                severity=severity,
                user=getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                referer=request.META.get('HTTP_REFERER', ''),
                request_method=request.method,
                request_path=request.path,
                request_data=self.sanitize_request_data(request),
                description=description
            )
        except Exception as e:
            logger.error(f"记录安全事件失败: {str(e)}")
    
    def log_access(self, request, response):
        """记录访问日志"""
        # 这里可以实现详细的访问日志记录
        pass
    
    def update_user_security_profile(self, user, request):
        """更新用户安全档案"""
        try:
            profile, created = UserSecurityProfile.objects.get_or_create(user=user)
            
            # 更新设备信息
            device_info = {
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'ip_address': self.get_client_ip(request),
                'last_seen': timezone.now().isoformat()
            }
            
            # 检查是否是新设备
            device_hash = hashlib.md5(device_info['user_agent'].encode()).hexdigest()
            known_devices = profile.known_devices or []
            
            if not any(d.get('hash') == device_hash for d in known_devices):
                device_info['hash'] = device_hash
                known_devices.append(device_info)
                profile.known_devices = known_devices[-10:]  # 只保留最近10个设备
            
            profile.save()
            
        except Exception as e:
            logger.error(f"更新用户安全档案失败: {str(e)}")
    
    def sanitize_request_data(self, request):
        """清理请求数据（移除敏感信息）"""
        data = {}
        
        # GET参数
        if request.GET:
            data['GET'] = dict(request.GET)
        
        # POST数据（移除密码等敏感字段）
        if request.POST:
            post_data = dict(request.POST)
            sensitive_fields = ['password', 'token', 'secret', 'key']
            for field in sensitive_fields:
                if field in post_data:
                    post_data[field] = '***'
            data['POST'] = post_data
        
        return data
    
    def create_blocked_response(self, message):
        """创建阻止响应"""
        return JsonResponse({
            'error': 'Access Denied',
            'message': message,
            'code': 403
        }, status=403)
    
    def create_rate_limit_response(self, result):
        """创建频率限制响应"""
        return JsonResponse({
            'error': 'Rate Limit Exceeded',
            'message': f'请求过于频繁，请稍后再试',
            'limit_type': result['limit_type'],
            'reset_time': result.get('reset_time'),
            'code': 429
        }, status=429)
    
    def create_threat_response(self, result):
        """创建威胁检测响应"""
        return JsonResponse({
            'error': 'Security Threat Detected',
            'message': result['reason'],
            'code': 403
        }, status=403)


class CSRFProtectionMiddleware(MiddlewareMixin):
    """CSRF保护中间件"""
    
    def process_request(self, request):
        """处理请求"""
        # 对于API请求，检查CSRF token
        if request.path.startswith('/api/') and request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            csrf_token = request.META.get('HTTP_X_CSRFTOKEN') or request.POST.get('csrfmiddlewaretoken')
            
            if not csrf_token:
                return JsonResponse({
                    'error': 'CSRF Token Missing',
                    'message': '缺少CSRF令牌',
                    'code': 403
                }, status=403)
            
            # 验证CSRF token
            if not self.validate_csrf_token(csrf_token, request):
                return JsonResponse({
                    'error': 'CSRF Token Invalid',
                    'message': 'CSRF令牌无效',
                    'code': 403
                }, status=403)
    
    def validate_csrf_token(self, token, request):
        """验证CSRF令牌"""
        # 这里应该实现CSRF令牌验证逻辑
        # 简化实现，实际应该使用Django的CSRF验证
        return True


class SecurityHeadersMiddleware(MiddlewareMixin):
    """安全头中间件"""
    
    def process_response(self, request, response):
        """添加安全头"""
        # X-Content-Type-Options
        response['X-Content-Type-Options'] = 'nosniff'
        
        # X-Frame-Options
        response['X-Frame-Options'] = 'DENY'
        
        # X-XSS-Protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Strict-Transport-Security
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Content-Security-Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://cdn.jsdelivr.net; "
            "connect-src 'self' wss: ws:; "
            "frame-ancestors 'none';"
        )
        response['Content-Security-Policy'] = csp
        
        # Referrer-Policy
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Permissions-Policy
        response['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        """记录请求日志"""
        start_time = time.time()
        
        # 记录请求开始
        self.log_request_start(request)
        
        response = self.get_response(request)
        
        # 记录请求结束
        end_time = time.time()
        duration = end_time - start_time
        self.log_request_end(request, response, duration)
        
        return response
    
    def log_request_start(self, request):
        """记录请求开始"""
        logger.info(f"Request started: {request.method} {request.path}")
    
    def log_request_end(self, request, response, duration):
        """记录请求结束"""
        logger.info(
            f"Request completed: {request.method} {request.path} "
            f"- Status: {response.status_code} - Duration: {duration:.3f}s"
        )


class IPWhitelistMiddleware(MiddlewareMixin):
    """IP白名单中间件（用于管理员接口）"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.admin_whitelist = getattr(settings, 'ADMIN_IP_WHITELIST', [])
        super().__init__(get_response)

    def __call__(self, request):
        """检查管理员IP白名单"""
        # 只对管理员路径进行检查
        if request.path.startswith('/admin/') or request.path.startswith('/admin-dashboard/'):
            client_ip = self.get_client_ip(request)

            if self.admin_whitelist and client_ip not in self.admin_whitelist:
                logger.warning(f"Unauthorized admin access attempt from IP: {client_ip}")
                return JsonResponse({
                    'error': 'Access Denied',
                    'message': '管理员访问受限',
                    'code': 403
                }, status=403)

        return self.get_response(request)

    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
