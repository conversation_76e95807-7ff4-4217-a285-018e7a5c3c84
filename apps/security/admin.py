"""
安全防护模块 - Django Admin配置
"""

from django.contrib import admin
from django.utils.html import format_html

from .models import (
    SecurityEvent, IPBlacklist, UserSecurityProfile, SecurityConfiguration,
    SecurityAuditLog, DataEncryption, RateLimitRecord
)


@admin.register(SecurityEvent)
class SecurityEventAdmin(admin.ModelAdmin):
    """安全事件管理"""
    list_display = ['event_type', 'severity_display', 'ip_address', 'user', 'is_blocked', 'is_resolved', 'created_at']
    list_filter = ['event_type', 'severity', 'is_blocked', 'is_resolved', 'created_at']
    search_fields = ['ip_address', 'user__username', 'description']
    readonly_fields = ['event_id', 'created_at', 'resolved_at']
    actions = ['mark_as_resolved', 'block_events']
    
    fieldsets = (
        ('事件信息', {
            'fields': ('event_id', 'event_type', 'severity', 'description')
        }),
        ('来源信息', {
            'fields': ('ip_address', 'user_agent', 'user')
        }),
        ('处理状态', {
            'fields': ('is_blocked', 'is_resolved', 'resolution_notes')
        }),
        ('时间信息', {
            'fields': ('created_at', 'resolved_at')
        }),
    )
    
    def severity_display(self, obj):
        """显示严重程度"""
        colors = {
            'low': 'green',
            'medium': 'orange', 
            'high': 'red',
            'critical': 'darkred'
        }
        color = colors.get(obj.severity, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_severity_display()
        )
    severity_display.short_description = '严重程度'
    
    def mark_as_resolved(self, request, queryset):
        """标记为已解决"""
        updated = queryset.update(is_resolved=True)
        self.message_user(request, f'已标记 {updated} 个事件为已解决')
    mark_as_resolved.short_description = '标记为已解决'
    
    def block_events(self, request, queryset):
        """阻止事件"""
        updated = queryset.update(is_blocked=True)
        self.message_user(request, f'已阻止 {updated} 个事件')
    block_events.short_description = '阻止事件'


@admin.register(IPBlacklist)
class IPBlacklistAdmin(admin.ModelAdmin):
    """IP黑名单管理"""
    list_display = ['ip_address', 'reason', 'violation_count', 'is_active', 'is_expired', 'blocked_at']
    list_filter = ['reason', 'is_active', 'blocked_at']
    search_fields = ['ip_address', 'description']
    readonly_fields = ['blocked_at', 'last_violation_at']
    actions = ['activate_ips', 'deactivate_ips']
    
    fieldsets = (
        ('IP信息', {
            'fields': ('ip_address', 'reason', 'description')
        }),
        ('状态信息', {
            'fields': ('is_active', 'violation_count', 'blocked_by')
        }),
        ('时间信息', {
            'fields': ('blocked_at', 'expires_at', 'last_violation_at')
        }),
    )
    
    def activate_ips(self, request, queryset):
        """激活IP"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'已激活 {updated} 个IP')
    activate_ips.short_description = '激活选中的IP'
    
    def deactivate_ips(self, request, queryset):
        """停用IP"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'已停用 {updated} 个IP')
    deactivate_ips.short_description = '停用选中的IP'


@admin.register(UserSecurityProfile)
class UserSecurityProfileAdmin(admin.ModelAdmin):
    """用户安全档案管理"""
    list_display = ['user', 'risk_score_display', 'trust_level', 'failed_login_count', 'account_status', 'updated_at']
    list_filter = ['trust_level', 'phone_verified', 'email_verified', 'identity_verified', 'two_factor_enabled']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['last_failed_login', 'account_locked_until', 'updated_at']
    
    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'risk_score', 'trust_level')
        }),
        ('登录安全', {
            'fields': ('failed_login_count', 'last_failed_login', 'account_locked_until')
        }),
        ('验证状态', {
            'fields': ('phone_verified', 'email_verified', 'identity_verified', 'two_factor_enabled')
        }),
        ('统计信息', {
            'fields': ('security_events_count', 'updated_at')
        }),
    )
    
    def risk_score_display(self, obj):
        """显示风险评分"""
        if obj.risk_score >= 70:
            color = 'red'
        elif obj.risk_score >= 40:
            color = 'orange'
        else:
            color = 'green'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color,
            obj.risk_score
        )
    risk_score_display.short_description = '风险评分'
    
    def account_status(self, obj):
        """显示账户状态"""
        if obj.account_locked_until:
            return format_html('<span style="color: red;">已锁定</span>')
        elif obj.two_factor_enabled:
            return format_html('<span style="color: green;">安全</span>')
        else:
            return format_html('<span style="color: orange;">普通</span>')
    account_status.short_description = '账户状态'


@admin.register(SecurityConfiguration)
class SecurityConfigurationAdmin(admin.ModelAdmin):
    """安全配置管理"""
    list_display = ['category', 'config_key', 'config_value_display', 'is_active', 'updated_at']
    list_filter = ['category', 'is_active', 'updated_at']
    search_fields = ['config_key', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('配置信息', {
            'fields': ('category', 'config_key', 'config_value', 'description')
        }),
        ('状态信息', {
            'fields': ('is_active', 'created_by')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def config_value_display(self, obj):
        """显示配置值"""
        if len(obj.config_value) > 50:
            return obj.config_value[:50] + '...'
        return obj.config_value
    config_value_display.short_description = '配置值'


@admin.register(SecurityAuditLog)
class SecurityAuditLogAdmin(admin.ModelAdmin):
    """安全审计日志管理"""
    list_display = ['action_type', 'user', 'ip_address', 'is_successful', 'created_at']
    list_filter = ['action_type', 'is_successful', 'created_at']
    search_fields = ['user__username', 'ip_address', 'action_description']
    readonly_fields = ['created_at']
    
    def has_add_permission(self, request):
        """禁止添加审计日志"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改审计日志"""
        return False


@admin.register(DataEncryption)
class DataEncryptionAdmin(admin.ModelAdmin):
    """数据加密管理"""
    list_display = ['data_type', 'algorithm', 'access_level', 'created_at']
    list_filter = ['data_type', 'algorithm', 'access_level']
    search_fields = ['data_type', 'key_id']
    readonly_fields = ['record_id', 'created_at']


@admin.register(RateLimitRecord)
class RateLimitRecordAdmin(admin.ModelAdmin):
    """访问频率限制记录管理"""
    list_display = ['identifier_value', 'limit_type', 'current_count', 'is_exceeded', 'window_start']
    list_filter = ['limit_type', 'identifier_type', 'is_exceeded', 'window_start']
    search_fields = ['identifier_value']
    readonly_fields = ['window_start']
    
    def has_add_permission(self, request):
        """禁止手动添加频率限制记录"""
        return False
