from django.db import models
from django.utils import timezone
from django.conf import settings
from apps.users.models import User


class MarketingCampaign(models.Model):
    """营销活动"""
    CAMPAIGN_TYPES = [
        ('registration', '注册活动'),
        ('vip_promotion', 'VIP促销'),
        ('gift_discount', '礼品折扣'),
        ('matching_boost', '匹配加速'),
        ('referral', '推荐奖励'),
        ('seasonal', '节日活动'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('scheduled', '已安排'),
        ('active', '进行中'),
        ('paused', '已暂停'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='活动名称')
    description = models.TextField(verbose_name='活动描述')
    campaign_type = models.CharField(max_length=20, choices=CAMPAIGN_TYPES, verbose_name='活动类型')
    
    # 活动设置
    banner_image = models.URLField(blank=True, verbose_name='活动横幅')
    rules = models.TextField(verbose_name='活动规则')
    rewards = models.JSONField(default=dict, verbose_name='奖励设置')
    
    # 时间设置
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    
    # 目标设置
    target_participants = models.IntegerField(default=0, verbose_name='目标参与人数')
    budget = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='活动预算')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    
    # 统计信息
    participant_count = models.IntegerField(default=0, verbose_name='参与人数')
    conversion_count = models.IntegerField(default=0, verbose_name='转化人数')
    total_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='总成本')
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                  related_name='created_campaigns', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'marketing_campaigns'
        verbose_name = '营销活动'
        verbose_name_plural = '营销活动'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    @property
    def conversion_rate(self):
        """转化率"""
        if self.participant_count == 0:
            return 0
        return round(self.conversion_count / self.participant_count * 100, 2)


class CampaignParticipant(models.Model):
    """活动参与者"""
    campaign = models.ForeignKey(MarketingCampaign, on_delete=models.CASCADE, 
                               related_name='participants', verbose_name='活动')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    
    # 参与信息
    participation_data = models.JSONField(default=dict, verbose_name='参与数据')
    is_converted = models.BooleanField(default=False, verbose_name='是否转化')
    reward_received = models.JSONField(default=dict, verbose_name='已获得奖励')
    
    participated_at = models.DateTimeField(auto_now_add=True, verbose_name='参与时间')
    
    class Meta:
        db_table = 'campaign_participants'
        verbose_name = '活动参与者'
        verbose_name_plural = '活动参与者'
        unique_together = ['campaign', 'user']
    
    def __str__(self):
        return f'{self.user.nickname} - {self.campaign.name}'


class PushNotification(models.Model):
    """推送通知"""
    NOTIFICATION_TYPES = [
        ('system', '系统通知'),
        ('marketing', '营销推送'),
        ('reminder', '提醒通知'),
        ('activity', '活动通知'),
        ('personal', '个人消息'),
    ]
    
    SEND_TYPES = [
        ('all', '全部用户'),
        ('vip', 'VIP用户'),
        ('active', '活跃用户'),
        ('inactive', '非活跃用户'),
        ('custom', '自定义用户'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('scheduled', '已安排'),
        ('sending', '发送中'),
        ('sent', '已发送'),
        ('failed', '发送失败'),
    ]
    
    title = models.CharField(max_length=100, verbose_name='通知标题')
    content = models.TextField(verbose_name='通知内容')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name='通知类型')
    
    # 发送设置
    send_type = models.CharField(max_length=20, choices=SEND_TYPES, verbose_name='发送对象')
    target_users = models.ManyToManyField(User, blank=True,
                                        related_name='received_notifications', verbose_name='目标用户')
    
    # 时间设置
    send_time = models.DateTimeField(default=timezone.now, verbose_name='发送时间')
    
    # 跳转设置
    action_type = models.CharField(max_length=50, blank=True, verbose_name='跳转类型')
    action_data = models.JSONField(default=dict, verbose_name='跳转数据')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    
    # 统计信息
    target_count = models.IntegerField(default=0, verbose_name='目标用户数')
    sent_count = models.IntegerField(default=0, verbose_name='发送成功数')
    read_count = models.IntegerField(default=0, verbose_name='阅读数')
    click_count = models.IntegerField(default=0, verbose_name='点击数')
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                  related_name='created_notifications', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='发送时间')
    
    class Meta:
        db_table = 'push_notifications'
        verbose_name = '推送通知'
        verbose_name_plural = '推送通知'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    @property
    def open_rate(self):
        """打开率"""
        if self.sent_count == 0:
            return 0
        return round(self.read_count / self.sent_count * 100, 2)
    
    @property
    def click_rate(self):
        """点击率"""
        if self.read_count == 0:
            return 0
        return round(self.click_count / self.read_count * 100, 2)


class UserSegment(models.Model):
    """用户分群"""
    name = models.CharField(max_length=100, verbose_name='分群名称')
    description = models.TextField(blank=True, verbose_name='分群描述')
    
    # 分群条件
    conditions = models.JSONField(default=dict, verbose_name='分群条件')
    
    # 统计信息
    user_count = models.IntegerField(default=0, verbose_name='用户数量')
    
    # 自动更新
    is_auto_update = models.BooleanField(default=False, verbose_name='是否自动更新')
    last_updated = models.DateTimeField(null=True, blank=True, verbose_name='最后更新时间')
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                  related_name='created_segments', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'user_segments'
        verbose_name = '用户分群'
        verbose_name_plural = '用户分群'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.name} ({self.user_count}人)'


class ABTest(models.Model):
    """A/B测试"""
    TEST_TYPES = [
        ('ui', '界面测试'),
        ('feature', '功能测试'),
        ('algorithm', '算法测试'),
        ('content', '内容测试'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('running', '运行中'),
        ('paused', '已暂停'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='测试名称')
    description = models.TextField(verbose_name='测试描述')
    test_type = models.CharField(max_length=20, choices=TEST_TYPES, verbose_name='测试类型')
    
    # 测试设置
    hypothesis = models.TextField(verbose_name='测试假设')
    success_metric = models.CharField(max_length=100, verbose_name='成功指标')
    
    # 版本设置
    control_version = models.JSONField(default=dict, verbose_name='对照版本')
    test_version = models.JSONField(default=dict, verbose_name='测试版本')
    
    # 流量分配
    traffic_allocation = models.FloatField(default=50.0, verbose_name='测试版本流量比例')
    
    # 时间设置
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    
    # 状态信息
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    
    # 统计信息
    control_users = models.IntegerField(default=0, verbose_name='对照组用户数')
    test_users = models.IntegerField(default=0, verbose_name='测试组用户数')
    control_conversions = models.IntegerField(default=0, verbose_name='对照组转化数')
    test_conversions = models.IntegerField(default=0, verbose_name='测试组转化数')
    
    # 结果
    is_significant = models.BooleanField(default=False, verbose_name='是否显著')
    confidence_level = models.FloatField(default=0, verbose_name='置信度')
    winner = models.CharField(max_length=20, blank=True, verbose_name='获胜版本')
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE,
                                  related_name='created_abtests', verbose_name='创建人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'ab_tests'
        verbose_name = 'A/B测试'
        verbose_name_plural = 'A/B测试'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    @property
    def control_conversion_rate(self):
        """对照组转化率"""
        if self.control_users == 0:
            return 0
        return round(self.control_conversions / self.control_users * 100, 2)
    
    @property
    def test_conversion_rate(self):
        """测试组转化率"""
        if self.test_users == 0:
            return 0
        return round(self.test_conversions / self.test_users * 100, 2)
