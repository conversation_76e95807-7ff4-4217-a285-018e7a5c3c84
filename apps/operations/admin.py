from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import (MarketingCampaign, CampaignParticipant, PushNotification, 
                    UserSegment, ABTest)


@admin.register(MarketingCampaign)
class MarketingCampaignAdmin(admin.ModelAdmin):
    list_display = ['name', 'campaign_type_display', 'status_display', 'participant_count',
                   'conversion_rate_display', 'start_time', 'end_time']
    list_filter = ['campaign_type', 'status', 'start_time']
    search_fields = ['name', 'description']
    readonly_fields = ['participant_count', 'conversion_count', 'total_cost', 
                      'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'campaign_type')
        }),
        ('活动设置', {
            'fields': ('banner_image', 'rules', 'rewards')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('目标设置', {
            'fields': ('target_participants', 'budget')
        }),
        ('状态信息', {
            'fields': ('status',)
        }),
        ('统计信息', {
            'fields': ('participant_count', 'conversion_count', 'total_cost')
        }),
        ('管理信息', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )
    
    def campaign_type_display(self, obj):
        colors = {
            'registration': 'green', 'vip_promotion': 'gold', 'gift_discount': 'pink',
            'matching_boost': 'blue', 'referral': 'purple', 'seasonal': 'orange'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.campaign_type, 'black'),
            obj.get_campaign_type_display()
        )
    campaign_type_display.short_description = '活动类型'
    
    def status_display(self, obj):
        colors = {
            'draft': 'gray', 'scheduled': 'blue', 'active': 'green',
            'paused': 'orange', 'completed': 'purple', 'cancelled': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def conversion_rate_display(self, obj):
        rate = obj.conversion_rate
        color = 'green' if rate > 10 else 'orange' if rate > 5 else 'red'
        return format_html('<span style="color: {};">{:.2f}%</span>', color, rate)
    conversion_rate_display.short_description = '转化率'
    
    actions = ['activate_campaigns', 'pause_campaigns', 'complete_campaigns']
    
    def activate_campaigns(self, request, queryset):
        updated = queryset.update(status='active')
        self.message_user(request, f'已激活 {updated} 个活动')
    activate_campaigns.short_description = '激活活动'
    
    def pause_campaigns(self, request, queryset):
        updated = queryset.update(status='paused')
        self.message_user(request, f'已暂停 {updated} 个活动')
    pause_campaigns.short_description = '暂停活动'
    
    def complete_campaigns(self, request, queryset):
        updated = queryset.update(status='completed')
        self.message_user(request, f'已完成 {updated} 个活动')
    complete_campaigns.short_description = '完成活动'


@admin.register(CampaignParticipant)
class CampaignParticipantAdmin(admin.ModelAdmin):
    list_display = ['user', 'campaign', 'is_converted', 'participated_at']
    list_filter = ['is_converted', 'participated_at', 'campaign']
    search_fields = ['user__nickname', 'campaign__name']
    readonly_fields = ['participated_at']


@admin.register(PushNotification)
class PushNotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'notification_type_display', 'send_type_display', 
                   'status_display', 'target_count', 'sent_count', 'open_rate_display', 'send_time']
    list_filter = ['notification_type', 'send_type', 'status', 'send_time']
    search_fields = ['title', 'content']
    readonly_fields = ['target_count', 'sent_count', 'read_count', 'click_count', 
                      'created_at', 'sent_at']
    filter_horizontal = ['target_users']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'notification_type')
        }),
        ('发送设置', {
            'fields': ('send_type', 'target_users', 'send_time')
        }),
        ('跳转设置', {
            'fields': ('action_type', 'action_data')
        }),
        ('状态信息', {
            'fields': ('status',)
        }),
        ('统计信息', {
            'fields': ('target_count', 'sent_count', 'read_count', 'click_count')
        }),
        ('管理信息', {
            'fields': ('created_by', 'created_at', 'sent_at')
        }),
    )
    
    def notification_type_display(self, obj):
        colors = {
            'system': 'blue', 'marketing': 'green', 'reminder': 'orange',
            'activity': 'purple', 'personal': 'gray'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.notification_type, 'black'),
            obj.get_notification_type_display()
        )
    notification_type_display.short_description = '通知类型'
    
    def send_type_display(self, obj):
        return obj.get_send_type_display()
    send_type_display.short_description = '发送对象'
    
    def status_display(self, obj):
        colors = {
            'draft': 'gray', 'scheduled': 'blue', 'sending': 'orange',
            'sent': 'green', 'failed': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def open_rate_display(self, obj):
        rate = obj.open_rate
        color = 'green' if rate > 20 else 'orange' if rate > 10 else 'red'
        return format_html('<span style="color: {};">{:.2f}%</span>', color, rate)
    open_rate_display.short_description = '打开率'
    
    actions = ['send_notifications', 'cancel_notifications']
    
    def send_notifications(self, request, queryset):
        updated = queryset.filter(status='scheduled').update(
            status='sending', 
            sent_at=timezone.now()
        )
        self.message_user(request, f'已发送 {updated} 条通知')
    send_notifications.short_description = '发送通知'
    
    def cancel_notifications(self, request, queryset):
        updated = queryset.filter(status__in=['draft', 'scheduled']).update(status='failed')
        self.message_user(request, f'已取消 {updated} 条通知')
    cancel_notifications.short_description = '取消通知'


@admin.register(UserSegment)
class UserSegmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'user_count', 'is_auto_update', 'last_updated', 'created_at']
    list_filter = ['is_auto_update', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['user_count', 'last_updated', 'created_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description')
        }),
        ('分群条件', {
            'fields': ('conditions',)
        }),
        ('统计信息', {
            'fields': ('user_count',)
        }),
        ('自动更新', {
            'fields': ('is_auto_update', 'last_updated')
        }),
        ('管理信息', {
            'fields': ('created_by', 'created_at')
        }),
    )


@admin.register(ABTest)
class ABTestAdmin(admin.ModelAdmin):
    list_display = ['name', 'test_type_display', 'status_display', 'control_conversion_rate_display',
                   'test_conversion_rate_display', 'is_significant', 'winner', 'start_time']
    list_filter = ['test_type', 'status', 'is_significant', 'start_time']
    search_fields = ['name', 'description', 'hypothesis']
    readonly_fields = ['control_users', 'test_users', 'control_conversions', 'test_conversions',
                      'is_significant', 'confidence_level', 'winner', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'test_type')
        }),
        ('测试设置', {
            'fields': ('hypothesis', 'success_metric')
        }),
        ('版本设置', {
            'fields': ('control_version', 'test_version', 'traffic_allocation')
        }),
        ('时间设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('状态信息', {
            'fields': ('status',)
        }),
        ('统计信息', {
            'fields': ('control_users', 'test_users', 'control_conversions', 'test_conversions')
        }),
        ('结果', {
            'fields': ('is_significant', 'confidence_level', 'winner')
        }),
        ('管理信息', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )
    
    def test_type_display(self, obj):
        colors = {'ui': 'blue', 'feature': 'green', 'algorithm': 'purple', 'content': 'orange'}
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.test_type, 'black'),
            obj.get_test_type_display()
        )
    test_type_display.short_description = '测试类型'
    
    def status_display(self, obj):
        colors = {
            'draft': 'gray', 'running': 'green', 'paused': 'orange',
            'completed': 'blue', 'cancelled': 'red'
        }
        return format_html(
            '<span style="color: {};">{}</span>',
            colors.get(obj.status, 'black'),
            obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def control_conversion_rate_display(self, obj):
        rate = obj.control_conversion_rate
        return format_html('<span style="color: blue;">{:.2f}%</span>', rate)
    control_conversion_rate_display.short_description = '对照组转化率'
    
    def test_conversion_rate_display(self, obj):
        rate = obj.test_conversion_rate
        control_rate = obj.control_conversion_rate
        color = 'green' if rate > control_rate else 'red' if rate < control_rate else 'orange'
        return format_html('<span style="color: {};">{:.2f}%</span>', color, rate)
    test_conversion_rate_display.short_description = '测试组转化率'
    
    actions = ['start_tests', 'pause_tests', 'complete_tests']
    
    def start_tests(self, request, queryset):
        updated = queryset.update(status='running')
        self.message_user(request, f'已启动 {updated} 个测试')
    start_tests.short_description = '启动测试'
    
    def pause_tests(self, request, queryset):
        updated = queryset.update(status='paused')
        self.message_user(request, f'已暂停 {updated} 个测试')
    pause_tests.short_description = '暂停测试'
    
    def complete_tests(self, request, queryset):
        updated = queryset.update(status='completed')
        self.message_user(request, f'已完成 {updated} 个测试')
    complete_tests.short_description = '完成测试'
